export default {
    labelIdle: 'Ser<PERSON> & Jatuhkan berkas Anda atau <span class="filepond--label-action">Je<PERSON><PERSON><PERSON></span>',
    labelInvalidField: '<PERSON><PERSON> berisi berkas yang tidak valid',
    labelFileWaitingForSize: 'Menunggu ukuran berkas',
    labelFileSizeNotAvailable: 'Ukuran berkas tidak tersedia',
    labelFileLoading: 'Memuat',
    labelFileLoadError: 'Kesalahan saat memuat',
    labelFileProcessing: 'Mengunggah',
    labelFileProcessingComplete: 'Pengunggahan selesai',
    labelFileProcessingAborted: 'Pengunggahan dibatalkan',
    labelFileProcessingError: 'Kesalahan saat pengunggahan',
    labelFileProcessingRevertError: 'Kesalahan saat pemulihan',
    labelFileRemoveError: 'Kesalahan saat penghapusan',
    labelTapToCancel: 'ketuk untuk membatalkan',
    labelTapToRetry: 'ketuk untuk mencoba lagi',
    labelTapToUndo: 'ketuk untuk mengurungkan',
    labelButtonRemoveItem: 'Hapus',
    labelButtonAbortItemLoad: 'Batalkan',
    labelButtonRetryItemLoad: 'Coba Kembali',
    labelButtonAbortItemProcessing: 'Batalkan',
    labelButtonUndoItemProcessing: 'Urungkan',
    labelButtonRetryItemProcessing: 'Coba Kembali',
    labelButtonProcessItem: 'Unggah',
    labelMaxFileSizeExceeded: 'Berkas terlalu besar',
    labelMaxFileSize: 'Ukuran berkas maksimum adalah {filesize}',
    labelMaxTotalFileSizeExceeded: 'Jumlah berkas maksimum terlampaui',
    labelMaxTotalFileSize: 'Jumlah berkas maksimum adalah {filesize}',
    labelFileTypeNotAllowed: 'Jenis berkas tidak valid',
    fileValidateTypeLabelExpectedTypes: 'Mengharapkan {allButLastType} atau {lastType}',
    imageValidateSizeLabelFormatError: 'Jenis citra tidak didukung',
    imageValidateSizeLabelImageSizeTooSmall: 'Citra terlalu kecil',
    imageValidateSizeLabelImageSizeTooBig: 'Citra terlalu besar',
    imageValidateSizeLabelExpectedMinSize: 'Ukuran minimum adalah {minWidth} × {minHeight}',
    imageValidateSizeLabelExpectedMaxSize: 'Ukuran maksimum adalah {minWidth} × {minHeight}',
    imageValidateSizeLabelImageResolutionTooLow: 'Resolusi terlalu rendah',
    imageValidateSizeLabelImageResolutionTooHigh: 'Resolusi terlalu tinggi',
    imageValidateSizeLabelExpectedMinResolution: 'Resolusi minimum adalah {minResolution}',
    imageValidateSizeLabelExpectedMaxResolution: 'Resolusi maksimum adalah {maxResolution}'
  };
