import { Head } from '@inertiajs/react';
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useTranslation } from '@/hooks/use-translation';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import AppearanceToggleDropdown from '@/components/appearance-dropdown';
import FilePondUpload from '@/components/FilePondUpload';

export default function TestFilePond() {
    const { translate } = useTranslation();
    const [files, setFiles] = useState<any[]>([]);

    return (
        <>
            <Head title="Test FilePond" />
            
            <div className="min-h-screen bg-background text-foreground">
                {/* Header avec contrôles */}
                <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                    <div className="container mx-auto px-4">
                        <nav className="flex h-16 items-center justify-between">
                            <div className="flex items-center space-x-2">
                                <span className="text-2xl font-bold text-primary">Lorelei</span>
                                <span className="text-xl font-medium text-muted-foreground">Test FilePond</span>
                            </div>
                            <div className="flex items-center space-x-2">
                                <LanguageSwitcher />
                                <AppearanceToggleDropdown />
                            </div>
                        </nav>
                    </div>
                </header>

                <div className="container mx-auto px-4 py-8 max-w-4xl">
                    <Card className="border-border bg-card">
                        <CardHeader>
                            <CardTitle className="text-card-foreground">Test FilePond Upload</CardTitle>
                            <CardDescription className="text-muted-foreground">
                                Testez le composant FilePond avec le nouveau design
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            {/* Test upload d'images */}
                            <div className="space-y-4">
                                <h3 className="text-lg font-semibold text-foreground">Upload d'images</h3>
                                <FilePondUpload
                                    files={files}
                                    onUpdateFiles={setFiles}
                                    acceptedFileTypes={['image/*']}
                                    maxFileSize="5MB"
                                    maxFiles={3}
                                    allowMultiple={true}
                                    labelIdle='Glissez-déposez vos images ou <span class="filepond--label-action">Parcourir</span>'
                                    imageResizeTargetWidth={800}
                                    imageResizeTargetHeight={600}
                                />
                            </div>

                            {/* Test upload de documents */}
                            <div className="space-y-4">
                                <h3 className="text-lg font-semibold text-foreground">Upload de documents</h3>
                                <FilePondUpload
                                    files={[]}
                                    onUpdateFiles={() => {}}
                                    acceptedFileTypes={['application/pdf', '.doc', '.docx']}
                                    maxFileSize="10MB"
                                    maxFiles={1}
                                    allowMultiple={false}
                                    labelIdle='Glissez-déposez votre document ou <span class="filepond--label-action">Parcourir</span>'
                                />
                            </div>

                            {/* Informations sur les fichiers sélectionnés */}
                            {files.length > 0 && (
                                <div className="space-y-4">
                                    <h3 className="text-lg font-semibold text-foreground">Fichiers sélectionnés</h3>
                                    <div className="space-y-2">
                                        {files.map((file, index) => (
                                            <div key={index} className="p-3 bg-muted/50 rounded-md">
                                                <div className="text-sm font-medium text-foreground">
                                                    {file.filename || file.file?.name}
                                                </div>
                                                <div className="text-xs text-muted-foreground">
                                                    {file.fileSize && `Taille: ${Math.round(file.fileSize / 1024)} KB`}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </>
    );
}
