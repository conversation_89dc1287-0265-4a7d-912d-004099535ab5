# Structure des Routes - Projet Marchand

Ce document décrit l'organisation des routes dans le projet marchand séparé.

## Vue d'ensemble

Le projet marchand contient plusieurs interfaces :

1. **Interface d'inscription** (Inertia + React)
2. **Dashboard moderne** (Inertia + React) 
3. **Panel Filament Marchand** (Interface d'administration pour marchands)
4. **Panel Filament Admin** (Interface d'administration pour administrateurs)

## Structure des URLs

### 1. Interface d'inscription et dashboard moderne (Inertia)

```
/ - Page d'accueil
/seller/welcome - Page de bienvenue inscription
/seller/business-info - Formulaire informations business
/seller/documents - Upload des documents
/seller/finalize - Finalisation inscription
/seller/rejected - Page marchand rejeté
/seller/suspended - Page marchand suspendu

/dashboard/ - Dashboard principal marchand
/dashboard/profile - Profil marchand
/dashboard/subscriptions - Gestion abonnements
/dashboard/documents - Gestion documents
```

### 2. Panel Filament Marchand

```
/marchand - Dashboard Filament pour marchands
/marchand/login - Connexion Filament marchand
/marchand/produits - Gestion des produits
/marchand/commandes - Gestion des commandes
/marchand/abonnements - Consultation abonnements
```

### 3. Panel Filament Admin

```
/admin - Dashboard Filament pour administrateurs
/admin/login - Connexion Filament admin
/admin/users - Gestion des utilisateurs
/admin/marchands - Gestion des marchands
/admin/commandes - Toutes les commandes
/admin/produits - Tous les produits
```

## Middlewares de sécurité

### SellerMiddleware
- Vérifie que l'utilisateur est connecté
- Vérifie qu'il a un profil marchand
- Vérifie que son statut est validé
- Redirige vers l'inscription si nécessaire

### AdminMiddleware  
- Vérifie que l'utilisateur est connecté
- Vérifie qu'il est administrateur (is_admin = true)
- Bloque l'accès si non autorisé

### FilamentMarchandMiddleware
- Middleware spécifique pour Filament
- Vérifie le statut marchand avant accès au panel

## Fichiers de routes

- `routes/web.php` - Routes principales (inscription, dashboard Inertia)
- `routes/auth.php` - Routes d'authentification
- `routes/admin.php` - Routes spécifiques admin (si nécessaire)
- `routes/settings.php` - Routes de configuration

## Panels Filament

### MarchandPanelProvider
- ID: 'marchand'
- Path: '/marchand'
- Ressources: app/Filament/Marchand/Resources
- Middleware: FilamentMarchandMiddleware

### AdminPanelProvider  
- ID: 'admin'
- Path: '/admin'
- Ressources: app/Filament/Resources
- Middleware: AdminMiddleware

## Redirections automatiques

- `/dashboard` → `/dashboard/` (dashboard Inertia)
- Marchand non validé → `/seller/welcome`
- Non-admin tentant d'accéder à `/admin` → Erreur 403
- Utilisateur sans profil marchand → `/seller/welcome`

## Configuration des domaines

En production, les domaines seront :
- `seller.lorelei.com` - Toutes les routes de ce projet
- `admin.lorelei.com` - Redirection vers `seller.lorelei.com/admin`
- `lorelei.com` - Site client principal (projet séparé)

## Notes importantes

1. Les panels Filament sont automatiquement découverts par Laravel
2. Les middlewares sont enregistrés dans `bootstrap/app.php`
3. Chaque panel a ses propres ressources et widgets
4. La base de données est partagée entre tous les projets
5. L'authentification est unifiée (même table users)
