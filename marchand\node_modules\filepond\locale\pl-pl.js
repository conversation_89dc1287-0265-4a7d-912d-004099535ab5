export default {
    labelIdle: 'P<PERSON><PERSON><PERSON><PERSON><PERSON>j i upuść lub <span class="filepond--label-action">wy<PERSON><PERSON></span> pliki',
    labelInvalidField: 'Nieprawidłowe pliki',
    labelFileWaitingForSize: 'Pobieranie rozmiaru',
    labelFileSizeNotAvailable: 'Nieznany rozmiar',
    labelFileLoading: 'W<PERSON><PERSON>ywanie',
    labelFileLoadError: 'Błąd wczytywania',
    labelFileProcessing: 'Prz<PERSON>y<PERSON><PERSON>',
    labelFileProcessingComplete: 'Przesłano',
    labelFileProcessingAborted: 'Przerwano',
    labelFileProcessingError: 'Przes<PERSON><PERSON>nie nie powiodło się',
    labelFileProcessingRevertError: 'Coś poszło nie tak',
    labelFileRemoveError: 'Nieudane usunięcie',
    labelTapToCancel: 'Anuluj',
    labelTapToRetry: 'Ponów',
    labelTapToUndo: 'Cofnij',
    labelButtonRemoveItem: 'Usu<PERSON>',
    labelButtonAbortItemLoad: 'Przerwij',
    labelButtonRetryItemLoad: 'Ponów',
    labelButtonAbortItemProcessing: 'Anuluj',
    labelButtonUndoItemProcessing: 'Cofnij',
    labelButtonRetryItemProcessing: 'Ponów',
    labelButtonProcessItem: 'Prześlij',
    labelMaxFileSizeExceeded: 'Plik jest zbyt duży',
    labelMaxFileSize: 'Dopuszczalna wielkość pliku to {filesize}',
    labelMaxTotalFileSizeExceeded: 'Przekroczono łączny rozmiar plików',
    labelMaxTotalFileSize: 'Łączny rozmiar plików nie może przekroczyć {filesize}',
    labelFileTypeNotAllowed: 'Niedozwolony rodzaj pliku',
    fileValidateTypeLabelExpectedTypes: 'Oczekiwano {allButLastType} lub {lastType}',
    imageValidateSizeLabelFormatError: 'Nieobsługiwany format obrazu',
    imageValidateSizeLabelImageSizeTooSmall: 'Obraz jest zbyt mały',
    imageValidateSizeLabelImageSizeTooBig: 'Obraz jest zbyt duży',
    imageValidateSizeLabelExpectedMinSize: 'Minimalne wymiary obrazu to {minWidth}×{minHeight}',
    imageValidateSizeLabelExpectedMaxSize: 'Maksymalna wymiary obrazu to {maxWidth}×{maxHeight}',
    imageValidateSizeLabelImageResolutionTooLow: 'Rozdzielczość jest zbyt niska',
    imageValidateSizeLabelImageResolutionTooHigh: 'Rozdzielczość jest zbyt wysoka',
    imageValidateSizeLabelExpectedMinResolution: 'Minimalna rozdzielczość to {minResolution}',
    imageValidateSizeLabelExpectedMaxResolution: 'Maksymalna rozdzielczość to {maxResolution}'
};
