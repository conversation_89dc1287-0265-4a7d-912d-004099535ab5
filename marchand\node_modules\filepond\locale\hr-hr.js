export default {
    labelIdle: 'Ovdje "ispusti" datoteku ili <span class="filepond--label-action"> Pretraži </span>',
    labelInvalidField: '<PERSON><PERSON> sadrži neispravne datoteke',
    labelFileWaitingForSize: '<PERSON><PERSON>n<PERSON> na veličinu datoteke',
    labelFileSizeNotAvailable: '<PERSON><PERSON><PERSON><PERSON> datoteke nije dostupna',
    labelFileLoading: 'Učitavanje',
    labelFileLoadError: 'Greška tijekom učitavanja',
    labelFileProcessing: 'Prijenos',
    labelFileProcessingComplete: '<PERSON>rije<PERSON> završen',
    labelFileProcessingAborted: 'Prijenos otkazan',
    labelFileProcessingError: 'Greška tijekom prijenosa',
    labelFileProcessingRevertError: 'Greška tijekom vraćanja',
    labelFileRemoveError: 'Greška tijekom uklananja datoteke',
    labelTapToCancel: 'Dodirni za prekid',
    labelTapToRetry: 'Dodirni za ponovno',
    labelTapToUndo: 'Dodirni za vraćanje',
    labelButtonRemoveItem: 'Ukloni',
    labelButtonAbortItemLoad: 'Odbaci',
    labelButtonRetryItemLoad: 'Ponovi',
    labelButtonAbortItemProcessing: 'Prekini',
    labelButtonUndoItemProcessing: 'Vrati',
    labelButtonRetryItemProcessing: 'Ponovi',
    labelButtonProcessItem: 'Prijenos',
    labelMaxFileSizeExceeded: 'Datoteka je prevelika',
    labelMaxFileSize: 'Maksimalna veličina datoteke je {filesize}',
    labelMaxTotalFileSizeExceeded: 'Maksimalna ukupna veličina datoteke prekoračena',
    labelMaxTotalFileSize: 'Maksimalna ukupna veličina datoteke je {filesize}',
    labelFileTypeNotAllowed: 'Tip datoteke nije podržan',
    fileValidateTypeLabelExpectedTypes: 'Očekivan {allButLastType} ili {lastType}',
    imageValidateSizeLabelFormatError: 'Tip slike nije podržan',
    imageValidateSizeLabelImageSizeTooSmall: 'Slika je premala',
    imageValidateSizeLabelImageSizeTooBig: 'Slika je prevelika',
    imageValidateSizeLabelExpectedMinSize: 'Minimalna veličina je {minWidth} × {minHeight}',
    imageValidateSizeLabelExpectedMaxSize: 'Maksimalna veličina je {maxWidth} × {maxHeight}',
    imageValidateSizeLabelImageResolutionTooLow: 'Rezolucija je preniska',
    imageValidateSizeLabelImageResolutionTooHigh: 'Rezolucija je previsoka',
    imageValidateSizeLabelExpectedMinResolution: 'Minimalna rezolucija je {minResolution}',
    imageValidateSizeLabelExpectedMaxResolution: 'Maksimalna rezolucija je {maxResolution}'
};