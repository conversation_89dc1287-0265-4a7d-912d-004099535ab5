export default {
    labelIdle: 'اسحب و ادرج ملفاتك أو <span class="filepond--label-action"> تصفح </span>',
    labelInvalidField: 'الحقل يحتوي على ملفات غير صالحة',
    labelFileWaitingForSize: 'بانتظار الحجم',
    labelFileSizeNotAvailable: 'الحجم غير متاح',
    labelFileLoading: 'بالإنتظار',
    labelFileLoadError: 'حدث خطأ أثناء التحميل',
    labelFileProcessing: 'يتم الرفع',
    labelFileProcessingComplete: 'تم الرفع',
    labelFileProcessingAborted: 'تم إلغاء الرفع',
    labelFileProcessingError: 'حدث خطأ أثناء الرفع',
    labelFileProcessingRevertError: 'حدث خطأ أثناء التراجع',
    labelFileRemoveError: 'حدث خطأ أثناء الحذف',
    labelTapToCancel: 'انقر للإلغاء',
    labelTapToRetry: 'انقر لإعادة المحاولة',
    labelTapToUndo: 'انقر للتراجع',
    labelButtonRemoveItem: 'مسح',
    labelButtonAbortItemLoad: 'إلغاء',
    labelButtonRetryItemLoad: 'إعادة',
    labelButtonAbortItemProcessing: 'إلغاء',
    labelButtonUndoItemProcessing: 'تراجع',
    labelButtonRetryItemProcessing: 'إعادة',
    labelButtonProcessItem: 'رفع',
    labelMaxFileSizeExceeded: 'الملف كبير جدا',
    labelMaxFileSize: 'حجم الملف الأقصى: {filesize}',
    labelMaxTotalFileSizeExceeded: 'تم تجاوز الحد الأقصى للحجم الإجمالي',
    labelMaxTotalFileSize: 'الحد الأقصى لحجم الملف: {filesize}',
    labelFileTypeNotAllowed: 'ملف من نوع غير صالح',
    fileValidateTypeLabelExpectedTypes: 'تتوقع {allButLastType} من {lastType}',
    imageValidateSizeLabelFormatError: 'نوع الصورة غير مدعوم',
    imageValidateSizeLabelImageSizeTooSmall: 'الصورة صغير جدا',
    imageValidateSizeLabelImageSizeTooBig: 'الصورة كبيرة جدا',
    imageValidateSizeLabelExpectedMinSize: 'الحد الأدنى للأبعاد هو: {minWidth} × {minHeight}',
    imageValidateSizeLabelExpectedMaxSize: 'الحد الأقصى للأبعاد هو: {maxWidth} × {maxHeight}',
    imageValidateSizeLabelImageResolutionTooLow: 'الدقة ضعيفة جدا',
    imageValidateSizeLabelImageResolutionTooHigh: 'الدقة مرتفعة جدا',
    imageValidateSizeLabelExpectedMinResolution: 'أقل دقة: {minResolution}',
    imageValidateSizeLabelExpectedMaxResolution: 'أقصى دقة: {maxResolution}'
};
