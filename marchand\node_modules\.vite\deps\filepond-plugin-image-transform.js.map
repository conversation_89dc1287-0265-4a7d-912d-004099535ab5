{"version": 3, "sources": ["../../filepond-plugin-image-transform/dist/filepond-plugin-image-transform.esm.js"], "sourcesContent": ["/*!\n * FilePondPluginImageTransform 3.8.7\n * Licensed under MIT, https://opensource.org/licenses/MIT/\n * Please visit https://pqina.nl/filepond/ for details.\n */\n\n/* eslint-disable */\n\n// test if file is of type image\nconst isImage = file => /^image/.test(file.type);\n\nconst getFilenameWithoutExtension = name => name.substr(0, name.lastIndexOf('.')) || name;\n\n// only handles image/jpg, image/jpeg, image/png, and image/svg+xml for now\nconst ExtensionMap = {\n    jpeg: 'jpg',\n    'svg+xml': 'svg',\n};\n\nconst renameFileToMatchMimeType = (filename, mimeType) => {\n    const name = getFilenameWithoutExtension(filename);\n    const type = mimeType.split('/')[1];\n    const extension = ExtensionMap[type] || type;\n    return `${name}.${extension}`;\n};\n\n// returns all the valid output formats we can encode towards\nconst getValidOutputMimeType = type => (/jpeg|png|svg\\+xml/.test(type) ? type : 'image/jpeg');\n\n// test if file is of type image\nconst isImage$1 = file => /^image/.test(file.type);\n\nconst MATRICES = {\n    1: () => [1, 0, 0, 1, 0, 0],\n    2: width => [-1, 0, 0, 1, width, 0],\n    3: (width, height) => [-1, 0, 0, -1, width, height],\n    4: (width, height) => [1, 0, 0, -1, 0, height],\n    5: () => [0, 1, 1, 0, 0, 0],\n    6: (width, height) => [0, 1, -1, 0, height, 0],\n    7: (width, height) => [0, -1, -1, 0, height, width],\n    8: width => [0, -1, 1, 0, 0, width],\n};\n\nconst getImageOrientationMatrix = (width, height, orientation) => {\n    if (orientation === -1) {\n        orientation = 1;\n    }\n    return MATRICES[orientation](width, height);\n};\n\nconst createVector = (x, y) => ({ x, y });\n\nconst vectorDot = (a, b) => a.x * b.x + a.y * b.y;\n\nconst vectorSubtract = (a, b) => createVector(a.x - b.x, a.y - b.y);\n\nconst vectorDistanceSquared = (a, b) => vectorDot(vectorSubtract(a, b), vectorSubtract(a, b));\n\nconst vectorDistance = (a, b) => Math.sqrt(vectorDistanceSquared(a, b));\n\nconst getOffsetPointOnEdge = (length, rotation) => {\n    const a = length;\n\n    const A = 1.5707963267948966;\n    const B = rotation;\n    const C = 1.5707963267948966 - rotation;\n\n    const sinA = Math.sin(A);\n    const sinB = Math.sin(B);\n    const sinC = Math.sin(C);\n    const cosC = Math.cos(C);\n    const ratio = a / sinA;\n    const b = ratio * sinB;\n    const c = ratio * sinC;\n\n    return createVector(cosC * b, cosC * c);\n};\n\nconst getRotatedRectSize = (rect, rotation) => {\n    const w = rect.width;\n    const h = rect.height;\n\n    const hor = getOffsetPointOnEdge(w, rotation);\n    const ver = getOffsetPointOnEdge(h, rotation);\n\n    const tl = createVector(rect.x + Math.abs(hor.x), rect.y - Math.abs(hor.y));\n\n    const tr = createVector(rect.x + rect.width + Math.abs(ver.y), rect.y + Math.abs(ver.x));\n\n    const bl = createVector(rect.x - Math.abs(ver.y), rect.y + rect.height - Math.abs(ver.x));\n\n    return {\n        width: vectorDistance(tl, tr),\n        height: vectorDistance(tl, bl),\n    };\n};\n\nconst getImageRectZoomFactor = (imageRect, cropRect, rotation = 0, center = { x: 0.5, y: 0.5 }) => {\n    // calculate available space round image center position\n    const cx = center.x > 0.5 ? 1 - center.x : center.x;\n    const cy = center.y > 0.5 ? 1 - center.y : center.y;\n    const imageWidth = cx * 2 * imageRect.width;\n    const imageHeight = cy * 2 * imageRect.height;\n\n    // calculate rotated crop rectangle size\n    const rotatedCropSize = getRotatedRectSize(cropRect, rotation);\n\n    return Math.max(rotatedCropSize.width / imageWidth, rotatedCropSize.height / imageHeight);\n};\n\nconst getCenteredCropRect = (container, aspectRatio) => {\n    let width = container.width;\n    let height = width * aspectRatio;\n    if (height > container.height) {\n        height = container.height;\n        width = height / aspectRatio;\n    }\n    const x = (container.width - width) * 0.5;\n    const y = (container.height - height) * 0.5;\n\n    return {\n        x,\n        y,\n        width,\n        height,\n    };\n};\n\nconst calculateCanvasSize = (image, canvasAspectRatio, zoom = 1) => {\n    const imageAspectRatio = image.height / image.width;\n\n    // determine actual pixels on x and y axis\n    let canvasWidth = 1;\n    let canvasHeight = canvasAspectRatio;\n    let imgWidth = 1;\n    let imgHeight = imageAspectRatio;\n    if (imgHeight > canvasHeight) {\n        imgHeight = canvasHeight;\n        imgWidth = imgHeight / imageAspectRatio;\n    }\n\n    const scalar = Math.max(canvasWidth / imgWidth, canvasHeight / imgHeight);\n    const width = image.width / (zoom * scalar * imgWidth);\n    const height = width * canvasAspectRatio;\n\n    return {\n        width: width,\n        height: height,\n    };\n};\n\nconst canvasRelease = canvas => {\n    canvas.width = 1;\n    canvas.height = 1;\n    const ctx = canvas.getContext('2d');\n    ctx.clearRect(0, 0, 1, 1);\n};\n\nconst isFlipped = flip => flip && (flip.horizontal || flip.vertical);\n\nconst getBitmap = (image, orientation, flip) => {\n    if (orientation <= 1 && !isFlipped(flip)) {\n        image.width = image.naturalWidth;\n        image.height = image.naturalHeight;\n        return image;\n    }\n\n    const canvas = document.createElement('canvas');\n    const width = image.naturalWidth;\n    const height = image.naturalHeight;\n\n    // if is rotated incorrectly swap width and height\n    const swapped = orientation >= 5 && orientation <= 8;\n    if (swapped) {\n        canvas.width = height;\n        canvas.height = width;\n    } else {\n        canvas.width = width;\n        canvas.height = height;\n    }\n\n    // draw the image but first fix orientation and set correct flip\n    const ctx = canvas.getContext('2d');\n\n    // get base transformation matrix\n    if (orientation) {\n        ctx.transform.apply(ctx, getImageOrientationMatrix(width, height, orientation));\n    }\n\n    if (isFlipped(flip)) {\n        // flip horizontal\n        // [-1, 0, 0, 1, width, 0]\n        const matrix = [1, 0, 0, 1, 0, 0];\n        if ((!swapped && flip.horizontal) || swapped & flip.vertical) {\n            matrix[0] = -1;\n            matrix[4] = width;\n        }\n\n        // flip vertical\n        // [1, 0, 0, -1, 0, height]\n        if ((!swapped && flip.vertical) || (swapped && flip.horizontal)) {\n            matrix[3] = -1;\n            matrix[5] = height;\n        }\n\n        ctx.transform(...matrix);\n    }\n\n    ctx.drawImage(image, 0, 0, width, height);\n\n    return canvas;\n};\n\nconst imageToImageData = (imageElement, orientation, crop = {}, options = {}) => {\n    const { canvasMemoryLimit, background = null } = options;\n\n    const zoom = crop.zoom || 1;\n\n    // fixes possible image orientation problems by drawing the image on the correct canvas\n    const bitmap = getBitmap(imageElement, orientation, crop.flip);\n    const imageSize = {\n        width: bitmap.width,\n        height: bitmap.height,\n    };\n\n    const aspectRatio = crop.aspectRatio || imageSize.height / imageSize.width;\n\n    let canvasSize = calculateCanvasSize(imageSize, aspectRatio, zoom);\n\n    if (canvasMemoryLimit) {\n        const requiredMemory = canvasSize.width * canvasSize.height;\n        if (requiredMemory > canvasMemoryLimit) {\n            const scalar = Math.sqrt(canvasMemoryLimit) / Math.sqrt(requiredMemory);\n            imageSize.width = Math.floor(imageSize.width * scalar);\n            imageSize.height = Math.floor(imageSize.height * scalar);\n            canvasSize = calculateCanvasSize(imageSize, aspectRatio, zoom);\n        }\n    }\n\n    const canvas = document.createElement('canvas');\n\n    const canvasCenter = {\n        x: canvasSize.width * 0.5,\n        y: canvasSize.height * 0.5,\n    };\n\n    const stage = {\n        x: 0,\n        y: 0,\n        width: canvasSize.width,\n        height: canvasSize.height,\n        center: canvasCenter,\n    };\n\n    const shouldLimit = typeof crop.scaleToFit === 'undefined' || crop.scaleToFit;\n\n    const scale =\n        zoom *\n        getImageRectZoomFactor(\n            imageSize,\n            getCenteredCropRect(stage, aspectRatio),\n            crop.rotation,\n            shouldLimit ? crop.center : { x: 0.5, y: 0.5 }\n        );\n\n    // start drawing\n    canvas.width = Math.round(canvasSize.width / scale);\n    canvas.height = Math.round(canvasSize.height / scale);\n\n    canvasCenter.x /= scale;\n    canvasCenter.y /= scale;\n\n    const imageOffset = {\n        x: canvasCenter.x - imageSize.width * (crop.center ? crop.center.x : 0.5),\n        y: canvasCenter.y - imageSize.height * (crop.center ? crop.center.y : 0.5),\n    };\n\n    const ctx = canvas.getContext('2d');\n    if (background) {\n        ctx.fillStyle = background;\n        ctx.fillRect(0, 0, canvas.width, canvas.height);\n    }\n\n    // move to draw offset\n    ctx.translate(canvasCenter.x, canvasCenter.y);\n    ctx.rotate(crop.rotation || 0);\n\n    // draw the image\n    ctx.drawImage(\n        bitmap,\n        imageOffset.x - canvasCenter.x,\n        imageOffset.y - canvasCenter.y,\n        imageSize.width,\n        imageSize.height\n    );\n\n    // get data from canvas\n    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n\n    // release canvas\n    canvasRelease(canvas);\n\n    // return data\n    return imageData;\n};\n\n/**\n * Polyfill toBlob for Edge\n */\nconst IS_BROWSER = (() =>\n    typeof window !== 'undefined' && typeof window.document !== 'undefined')();\nif (IS_BROWSER) {\n    if (!HTMLCanvasElement.prototype.toBlob) {\n        Object.defineProperty(HTMLCanvasElement.prototype, 'toBlob', {\n            value: function(callback, type, quality) {\n                var dataURL = this.toDataURL(type, quality).split(',')[1];\n                setTimeout(function() {\n                    var binStr = atob(dataURL);\n                    var len = binStr.length;\n                    var arr = new Uint8Array(len);\n                    for (var i = 0; i < len; i++) {\n                        arr[i] = binStr.charCodeAt(i);\n                    }\n                    callback(new Blob([arr], { type: type || 'image/png' }));\n                });\n            },\n        });\n    }\n}\n\nconst canvasToBlob = (canvas, options, beforeCreateBlob = null) =>\n    new Promise(resolve => {\n        const promisedImage = beforeCreateBlob ? beforeCreateBlob(canvas) : canvas;\n        Promise.resolve(promisedImage).then(canvas => {\n            canvas.toBlob(resolve, options.type, options.quality);\n        });\n    });\n\nconst vectorMultiply = (v, amount) => createVector$1(v.x * amount, v.y * amount);\n\nconst vectorAdd = (a, b) => createVector$1(a.x + b.x, a.y + b.y);\n\nconst vectorNormalize = v => {\n    const l = Math.sqrt(v.x * v.x + v.y * v.y);\n    if (l === 0) {\n        return {\n            x: 0,\n            y: 0,\n        };\n    }\n    return createVector$1(v.x / l, v.y / l);\n};\n\nconst vectorRotate = (v, radians, origin) => {\n    const cos = Math.cos(radians);\n    const sin = Math.sin(radians);\n    const t = createVector$1(v.x - origin.x, v.y - origin.y);\n    return createVector$1(origin.x + cos * t.x - sin * t.y, origin.y + sin * t.x + cos * t.y);\n};\n\nconst createVector$1 = (x = 0, y = 0) => ({ x, y });\n\nconst getMarkupValue = (value, size, scalar = 1, axis) => {\n    if (typeof value === 'string') {\n        return parseFloat(value) * scalar;\n    }\n    if (typeof value === 'number') {\n        return value * (axis ? size[axis] : Math.min(size.width, size.height));\n    }\n    return;\n};\n\nconst getMarkupStyles = (markup, size, scale) => {\n    const lineStyle = markup.borderStyle || markup.lineStyle || 'solid';\n    const fill = markup.backgroundColor || markup.fontColor || 'transparent';\n    const stroke = markup.borderColor || markup.lineColor || 'transparent';\n    const strokeWidth = getMarkupValue(markup.borderWidth || markup.lineWidth, size, scale);\n    const lineCap = markup.lineCap || 'round';\n    const lineJoin = markup.lineJoin || 'round';\n    const dashes =\n        typeof lineStyle === 'string'\n            ? ''\n            : lineStyle.map(v => getMarkupValue(v, size, scale)).join(',');\n    const opacity = markup.opacity || 1;\n    return {\n        'stroke-linecap': lineCap,\n        'stroke-linejoin': lineJoin,\n        'stroke-width': strokeWidth || 0,\n        'stroke-dasharray': dashes,\n        stroke,\n        fill,\n        opacity,\n    };\n};\n\nconst isDefined = value => value != null;\n\nconst getMarkupRect = (rect, size, scalar = 1) => {\n    let left =\n        getMarkupValue(rect.x, size, scalar, 'width') ||\n        getMarkupValue(rect.left, size, scalar, 'width');\n    let top =\n        getMarkupValue(rect.y, size, scalar, 'height') ||\n        getMarkupValue(rect.top, size, scalar, 'height');\n    let width = getMarkupValue(rect.width, size, scalar, 'width');\n    let height = getMarkupValue(rect.height, size, scalar, 'height');\n    let right = getMarkupValue(rect.right, size, scalar, 'width');\n    let bottom = getMarkupValue(rect.bottom, size, scalar, 'height');\n\n    if (!isDefined(top)) {\n        if (isDefined(height) && isDefined(bottom)) {\n            top = size.height - height - bottom;\n        } else {\n            top = bottom;\n        }\n    }\n\n    if (!isDefined(left)) {\n        if (isDefined(width) && isDefined(right)) {\n            left = size.width - width - right;\n        } else {\n            left = right;\n        }\n    }\n\n    if (!isDefined(width)) {\n        if (isDefined(left) && isDefined(right)) {\n            width = size.width - left - right;\n        } else {\n            width = 0;\n        }\n    }\n\n    if (!isDefined(height)) {\n        if (isDefined(top) && isDefined(bottom)) {\n            height = size.height - top - bottom;\n        } else {\n            height = 0;\n        }\n    }\n\n    return {\n        x: left || 0,\n        y: top || 0,\n        width: width || 0,\n        height: height || 0,\n    };\n};\n\nconst pointsToPathShape = points =>\n    points.map((point, index) => `${index === 0 ? 'M' : 'L'} ${point.x} ${point.y}`).join(' ');\n\nconst setAttributes = (element, attr) =>\n    Object.keys(attr).forEach(key => element.setAttribute(key, attr[key]));\n\nconst ns = 'http://www.w3.org/2000/svg';\nconst svg = (tag, attr) => {\n    const element = document.createElementNS(ns, tag);\n    if (attr) {\n        setAttributes(element, attr);\n    }\n    return element;\n};\n\nconst updateRect = element =>\n    setAttributes(element, {\n        ...element.rect,\n        ...element.styles,\n    });\n\nconst updateEllipse = element => {\n    const cx = element.rect.x + element.rect.width * 0.5;\n    const cy = element.rect.y + element.rect.height * 0.5;\n    const rx = element.rect.width * 0.5;\n    const ry = element.rect.height * 0.5;\n    return setAttributes(element, {\n        cx,\n        cy,\n        rx,\n        ry,\n        ...element.styles,\n    });\n};\n\nconst IMAGE_FIT_STYLE = {\n    contain: 'xMidYMid meet',\n    cover: 'xMidYMid slice',\n};\n\nconst updateImage = (element, markup) => {\n    setAttributes(element, {\n        ...element.rect,\n        ...element.styles,\n        preserveAspectRatio: IMAGE_FIT_STYLE[markup.fit] || 'none',\n    });\n};\n\nconst TEXT_ANCHOR = {\n    left: 'start',\n    center: 'middle',\n    right: 'end',\n};\n\nconst updateText = (element, markup, size, scale) => {\n    const fontSize = getMarkupValue(markup.fontSize, size, scale);\n    const fontFamily = markup.fontFamily || 'sans-serif';\n    const fontWeight = markup.fontWeight || 'normal';\n    const textAlign = TEXT_ANCHOR[markup.textAlign] || 'start';\n\n    setAttributes(element, {\n        ...element.rect,\n        ...element.styles,\n        'stroke-width': 0,\n        'font-weight': fontWeight,\n        'font-size': fontSize,\n        'font-family': fontFamily,\n        'text-anchor': textAlign,\n    });\n\n    // update text\n    if (element.text !== markup.text) {\n        element.text = markup.text;\n        element.textContent = markup.text.length ? markup.text : ' ';\n    }\n};\n\nconst updateLine = (element, markup, size, scale) => {\n    setAttributes(element, {\n        ...element.rect,\n        ...element.styles,\n        fill: 'none',\n    });\n\n    const line = element.childNodes[0];\n    const begin = element.childNodes[1];\n    const end = element.childNodes[2];\n\n    const origin = element.rect;\n\n    const target = {\n        x: element.rect.x + element.rect.width,\n        y: element.rect.y + element.rect.height,\n    };\n\n    setAttributes(line, {\n        x1: origin.x,\n        y1: origin.y,\n        x2: target.x,\n        y2: target.y,\n    });\n\n    if (!markup.lineDecoration) return;\n\n    begin.style.display = 'none';\n    end.style.display = 'none';\n\n    const v = vectorNormalize({\n        x: target.x - origin.x,\n        y: target.y - origin.y,\n    });\n\n    const l = getMarkupValue(0.05, size, scale);\n\n    if (markup.lineDecoration.indexOf('arrow-begin') !== -1) {\n        const arrowBeginRotationPoint = vectorMultiply(v, l);\n        const arrowBeginCenter = vectorAdd(origin, arrowBeginRotationPoint);\n        const arrowBeginA = vectorRotate(origin, 2, arrowBeginCenter);\n        const arrowBeginB = vectorRotate(origin, -2, arrowBeginCenter);\n\n        setAttributes(begin, {\n            style: 'display:block;',\n            d: `M${arrowBeginA.x},${arrowBeginA.y} L${origin.x},${origin.y} L${arrowBeginB.x},${\n                arrowBeginB.y\n            }`,\n        });\n    }\n\n    if (markup.lineDecoration.indexOf('arrow-end') !== -1) {\n        const arrowEndRotationPoint = vectorMultiply(v, -l);\n        const arrowEndCenter = vectorAdd(target, arrowEndRotationPoint);\n        const arrowEndA = vectorRotate(target, 2, arrowEndCenter);\n        const arrowEndB = vectorRotate(target, -2, arrowEndCenter);\n\n        setAttributes(end, {\n            style: 'display:block;',\n            d: `M${arrowEndA.x},${arrowEndA.y} L${target.x},${target.y} L${arrowEndB.x},${\n                arrowEndB.y\n            }`,\n        });\n    }\n};\n\nconst updatePath = (element, markup, size, scale) => {\n    setAttributes(element, {\n        ...element.styles,\n        fill: 'none',\n        d: pointsToPathShape(\n            markup.points.map(point => ({\n                x: getMarkupValue(point.x, size, scale, 'width'),\n                y: getMarkupValue(point.y, size, scale, 'height'),\n            }))\n        ),\n    });\n};\n\nconst createShape = node => markup => svg(node, { id: markup.id });\n\nconst createImage = markup => {\n    const shape = svg('image', {\n        id: markup.id,\n        'stroke-linecap': 'round',\n        'stroke-linejoin': 'round',\n        opacity: '0',\n    });\n    shape.onload = () => {\n        shape.setAttribute('opacity', markup.opacity || 1);\n    };\n    shape.setAttributeNS('http://www.w3.org/1999/xlink', 'xlink:href', markup.src);\n    return shape;\n};\n\nconst createLine = markup => {\n    const shape = svg('g', {\n        id: markup.id,\n        'stroke-linecap': 'round',\n        'stroke-linejoin': 'round',\n    });\n\n    const line = svg('line');\n    shape.appendChild(line);\n\n    const begin = svg('path');\n    shape.appendChild(begin);\n\n    const end = svg('path');\n    shape.appendChild(end);\n\n    return shape;\n};\n\nconst CREATE_TYPE_ROUTES = {\n    image: createImage,\n    rect: createShape('rect'),\n    ellipse: createShape('ellipse'),\n    text: createShape('text'),\n    path: createShape('path'),\n    line: createLine,\n};\n\nconst UPDATE_TYPE_ROUTES = {\n    rect: updateRect,\n    ellipse: updateEllipse,\n    image: updateImage,\n    text: updateText,\n    path: updatePath,\n    line: updateLine,\n};\n\nconst createMarkupByType = (type, markup) => CREATE_TYPE_ROUTES[type](markup);\n\nconst updateMarkupByType = (element, type, markup, size, scale) => {\n    if (type !== 'path') {\n        element.rect = getMarkupRect(markup, size, scale);\n    }\n    element.styles = getMarkupStyles(markup, size, scale);\n    UPDATE_TYPE_ROUTES[type](element, markup, size, scale);\n};\n\nconst sortMarkupByZIndex = (a, b) => {\n    if (a[1].zIndex > b[1].zIndex) {\n        return 1;\n    }\n    if (a[1].zIndex < b[1].zIndex) {\n        return -1;\n    }\n    return 0;\n};\n\nconst cropSVG = (blob, crop = {}, markup, options) =>\n    new Promise(resolve => {\n        const { background = null } = options;\n\n        // load blob contents and wrap in crop svg\n        const fr = new FileReader();\n        fr.onloadend = () => {\n            // get svg text\n            const text = fr.result;\n\n            // create element with svg and get size\n            const original = document.createElement('div');\n            original.style.cssText = `position:absolute;pointer-events:none;width:0;height:0;visibility:hidden;`;\n            original.innerHTML = text;\n            const originalNode = original.querySelector('svg');\n            document.body.appendChild(original);\n\n            // request bounding box dimensions\n            const bBox = originalNode.getBBox();\n            original.parentNode.removeChild(original);\n\n            // get title\n            const titleNode = original.querySelector('title');\n\n            // calculate new heights and widths\n            const viewBoxAttribute = originalNode.getAttribute('viewBox') || '';\n            const widthAttribute = originalNode.getAttribute('width') || '';\n            const heightAttribute = originalNode.getAttribute('height') || '';\n            let width = parseFloat(widthAttribute) || null;\n            let height = parseFloat(heightAttribute) || null;\n            const widthUnits = (widthAttribute.match(/[a-z]+/) || [])[0] || '';\n            const heightUnits = (heightAttribute.match(/[a-z]+/) || [])[0] || '';\n\n            // create new size\n            const viewBoxList = viewBoxAttribute.split(' ').map(parseFloat);\n            const viewBox = viewBoxList.length\n                ? {\n                      x: viewBoxList[0],\n                      y: viewBoxList[1],\n                      width: viewBoxList[2],\n                      height: viewBoxList[3],\n                  }\n                : bBox;\n\n            let imageWidth = width != null ? width : viewBox.width;\n            let imageHeight = height != null ? height : viewBox.height;\n\n            originalNode.style.overflow = 'visible';\n            originalNode.setAttribute('width', imageWidth);\n            originalNode.setAttribute('height', imageHeight);\n\n            // markup\n            let markupSVG = '';\n            if (markup && markup.length) {\n                const size = {\n                    width: imageWidth,\n                    height: imageHeight,\n                };\n                markupSVG = markup.sort(sortMarkupByZIndex).reduce((prev, shape) => {\n                    const el = createMarkupByType(shape[0], shape[1]);\n                    updateMarkupByType(el, shape[0], shape[1], size);\n                    el.removeAttribute('id');\n                    if (el.getAttribute('opacity') === 1) {\n                        el.removeAttribute('opacity');\n                    }\n                    return prev + '\\n' + el.outerHTML + '\\n';\n                }, '');\n                markupSVG = `\\n\\n<g>${markupSVG.replace(/&nbsp;/g, ' ')}</g>\\n\\n`;\n            }\n\n            const aspectRatio = crop.aspectRatio || imageHeight / imageWidth;\n\n            const canvasWidth = imageWidth;\n            const canvasHeight = canvasWidth * aspectRatio;\n\n            const shouldLimit = typeof crop.scaleToFit === 'undefined' || crop.scaleToFit;\n\n            const cropCenterX = crop.center ? crop.center.x : 0.5;\n            const cropCenterY = crop.center ? crop.center.y : 0.5;\n\n            const canvasZoomFactor = getImageRectZoomFactor(\n                {\n                    width: imageWidth,\n                    height: imageHeight,\n                },\n                getCenteredCropRect(\n                    {\n                        width: canvasWidth,\n                        height: canvasHeight,\n                    },\n                    aspectRatio\n                ),\n                crop.rotation,\n                shouldLimit\n                    ? { x: cropCenterX, y: cropCenterY }\n                    : {\n                          x: 0.5,\n                          y: 0.5,\n                      }\n            );\n\n            const scale = crop.zoom * canvasZoomFactor;\n\n            const rotation = crop.rotation * (180 / Math.PI);\n\n            const canvasCenter = {\n                x: canvasWidth * 0.5,\n                y: canvasHeight * 0.5,\n            };\n\n            const imageOffset = {\n                x: canvasCenter.x - imageWidth * cropCenterX,\n                y: canvasCenter.y - imageHeight * cropCenterY,\n            };\n\n            const cropTransforms = [\n                // rotate\n                `rotate(${rotation} ${canvasCenter.x} ${canvasCenter.y})`,\n\n                // scale\n                `translate(${canvasCenter.x} ${canvasCenter.y})`,\n                `scale(${scale})`,\n                `translate(${-canvasCenter.x} ${-canvasCenter.y})`,\n\n                // offset\n                `translate(${imageOffset.x} ${imageOffset.y})`,\n            ];\n\n            const cropFlipHorizontal = crop.flip && crop.flip.horizontal;\n            const cropFlipVertical = crop.flip && crop.flip.vertical;\n\n            const flipTransforms = [\n                `scale(${cropFlipHorizontal ? -1 : 1} ${cropFlipVertical ? -1 : 1})`,\n                `translate(${cropFlipHorizontal ? -imageWidth : 0} ${\n                    cropFlipVertical ? -imageHeight : 0\n                })`,\n            ];\n\n            // crop\n            const transformed = `<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<svg width=\"${canvasWidth}${widthUnits}\" height=\"${canvasHeight}${heightUnits}\" \nviewBox=\"0 0 ${canvasWidth} ${canvasHeight}\" ${\n                background ? 'style=\"background:' + background + '\" ' : ''\n            }\npreserveAspectRatio=\"xMinYMin\"\nxmlns:xlink=\"http://www.w3.org/1999/xlink\"\nxmlns=\"http://www.w3.org/2000/svg\">\n<!-- Generated by PQINA - https://pqina.nl/ -->\n<title>${titleNode ? titleNode.textContent : ''}</title>\n<g transform=\"${cropTransforms.join(' ')}\">\n<g transform=\"${flipTransforms.join(' ')}\">\n${originalNode.outerHTML}${markupSVG}\n</g>\n</g>\n</svg>`;\n\n            // create new svg file\n            resolve(transformed);\n        };\n\n        fr.readAsText(blob);\n    });\n\nconst objectToImageData = obj => {\n    let imageData;\n    try {\n        imageData = new ImageData(obj.width, obj.height);\n    } catch (e) {\n        // IE + Old EDGE (tested on 12)\n        const canvas = document.createElement('canvas');\n        imageData = canvas.getContext('2d').createImageData(obj.width, obj.height);\n    }\n    imageData.data.set(obj.data);\n    return imageData;\n};\n\n/* javascript-obfuscator:disable */\nconst TransformWorker = () => {\n    // maps transform types to transform functions\n    const TRANSFORMS = { resize, filter };\n\n    // applies all image transforms to the image data array\n    const applyTransforms = (transforms, imageData) => {\n        transforms.forEach(transform => {\n            imageData = TRANSFORMS[transform.type](imageData, transform.data);\n        });\n        return imageData;\n    };\n\n    // transform image hub\n    const transform = (data, cb) => {\n        let transforms = data.transforms;\n\n        // if has filter and has resize, move filter to resize operation\n        let filterTransform = null;\n        transforms.forEach(transform => {\n            if (transform.type === 'filter') {\n                filterTransform = transform;\n            }\n        });\n        if (filterTransform) {\n            // find resize\n            let resizeTransform = null;\n            transforms.forEach(transform => {\n                if (transform.type === 'resize') {\n                    resizeTransform = transform;\n                }\n            });\n\n            if (resizeTransform) {\n                // update resize operation\n                resizeTransform.data.matrix = filterTransform.data;\n\n                // remove filter\n                transforms = transforms.filter(transform => transform.type !== 'filter');\n            }\n        }\n\n        cb(applyTransforms(transforms, data.imageData));\n    };\n\n    // eslint-disable-next-line no-restricted-globals\n    self.onmessage = e => {\n        transform(e.data.message, response => {\n            // eslint-disable-next-line no-restricted-globals\n            self.postMessage({ id: e.data.id, message: response }, [response.data.buffer]);\n        });\n    };\n\n    const br = 1.0;\n    const bg = 1.0;\n    const bb = 1.0;\n    function applyFilterMatrix(index, data, m) {\n        const ir = data[index] / 255;\n        const ig = data[index + 1] / 255;\n        const ib = data[index + 2] / 255;\n        const ia = data[index + 3] / 255;\n\n        const mr = ir * m[0] + ig * m[1] + ib * m[2] + ia * m[3] + m[4];\n        const mg = ir * m[5] + ig * m[6] + ib * m[7] + ia * m[8] + m[9];\n        const mb = ir * m[10] + ig * m[11] + ib * m[12] + ia * m[13] + m[14];\n        const ma = ir * m[15] + ig * m[16] + ib * m[17] + ia * m[18] + m[19];\n\n        const or = Math.max(0, mr * ma) + br * (1.0 - ma);\n        const og = Math.max(0, mg * ma) + bg * (1.0 - ma);\n        const ob = Math.max(0, mb * ma) + bb * (1.0 - ma);\n\n        data[index] = Math.max(0.0, Math.min(1.0, or)) * 255;\n        data[index + 1] = Math.max(0.0, Math.min(1.0, og)) * 255;\n        data[index + 2] = Math.max(0.0, Math.min(1.0, ob)) * 255;\n    }\n\n    const identityMatrix = self.JSON.stringify([\n        1,\n        0,\n        0,\n        0,\n        0,\n        0,\n        1,\n        0,\n        0,\n        0,\n        0,\n        0,\n        1,\n        0,\n        0,\n        0,\n        0,\n        0,\n        1,\n        0,\n    ]);\n    function isIdentityMatrix(filter) {\n        return self.JSON.stringify(filter || []) === identityMatrix;\n    }\n\n    function filter(imageData, matrix) {\n        if (!matrix || isIdentityMatrix(matrix)) return imageData;\n\n        const data = imageData.data;\n        const l = data.length;\n\n        const m11 = matrix[0];\n        const m12 = matrix[1];\n        const m13 = matrix[2];\n        const m14 = matrix[3];\n        const m15 = matrix[4];\n\n        const m21 = matrix[5];\n        const m22 = matrix[6];\n        const m23 = matrix[7];\n        const m24 = matrix[8];\n        const m25 = matrix[9];\n\n        const m31 = matrix[10];\n        const m32 = matrix[11];\n        const m33 = matrix[12];\n        const m34 = matrix[13];\n        const m35 = matrix[14];\n\n        const m41 = matrix[15];\n        const m42 = matrix[16];\n        const m43 = matrix[17];\n        const m44 = matrix[18];\n        const m45 = matrix[19];\n\n        let index = 0,\n            r = 0.0,\n            g = 0.0,\n            b = 0.0,\n            a = 0.0,\n            mr = 0.0,\n            mg = 0.0,\n            mb = 0.0,\n            ma = 0.0,\n            or = 0.0,\n            og = 0.0,\n            ob = 0.0;\n\n        for (; index < l; index += 4) {\n            r = data[index] / 255;\n            g = data[index + 1] / 255;\n            b = data[index + 2] / 255;\n            a = data[index + 3] / 255;\n\n            mr = r * m11 + g * m12 + b * m13 + a * m14 + m15;\n            mg = r * m21 + g * m22 + b * m23 + a * m24 + m25;\n            mb = r * m31 + g * m32 + b * m33 + a * m34 + m35;\n            ma = r * m41 + g * m42 + b * m43 + a * m44 + m45;\n\n            or = Math.max(0, mr * ma) + br * (1.0 - ma);\n            og = Math.max(0, mg * ma) + bg * (1.0 - ma);\n            ob = Math.max(0, mb * ma) + bb * (1.0 - ma);\n\n            data[index] = Math.max(0.0, Math.min(1.0, or)) * 255;\n            data[index + 1] = Math.max(0.0, Math.min(1.0, og)) * 255;\n            data[index + 2] = Math.max(0.0, Math.min(1.0, ob)) * 255;\n            // don't update alpha value\n        }\n\n        return imageData;\n    }\n\n    function resize(imageData, data) {\n        let { mode = 'contain', upscale = false, width, height, matrix } = data;\n\n        // test if is identity matrix\n        matrix = !matrix || isIdentityMatrix(matrix) ? null : matrix;\n\n        // need at least a width or a height\n        // also 0 is not a valid width or height\n        if (!width && !height) {\n            return filter(imageData, matrix);\n        }\n\n        // make sure all bounds are set\n        if (width === null) {\n            width = height;\n        } else if (height === null) {\n            height = width;\n        }\n\n        if (mode !== 'force') {\n            let scalarWidth = width / imageData.width;\n            let scalarHeight = height / imageData.height;\n            let scalar = 1;\n\n            if (mode === 'cover') {\n                scalar = Math.max(scalarWidth, scalarHeight);\n            } else if (mode === 'contain') {\n                scalar = Math.min(scalarWidth, scalarHeight);\n            }\n\n            // if image is too small, exit here with original image\n            if (scalar > 1 && upscale === false) {\n                return filter(imageData, matrix);\n            }\n\n            width = imageData.width * scalar;\n            height = imageData.height * scalar;\n        }\n\n        const originWidth = imageData.width;\n        const originHeight = imageData.height;\n        const targetWidth = Math.round(width);\n        const targetHeight = Math.round(height);\n        const inputData = imageData.data;\n        const outputData = new Uint8ClampedArray(targetWidth * targetHeight * 4);\n        const ratioWidth = originWidth / targetWidth;\n        const ratioHeight = originHeight / targetHeight;\n        const ratioWidthHalf = Math.ceil(ratioWidth * 0.5);\n        const ratioHeightHalf = Math.ceil(ratioHeight * 0.5);\n\n        for (let j = 0; j < targetHeight; j++) {\n            for (let i = 0; i < targetWidth; i++) {\n                let x2 = (i + j * targetWidth) * 4;\n                let weight = 0;\n                let weights = 0;\n                let weightsAlpha = 0;\n                let r = 0;\n                let g = 0;\n                let b = 0;\n                let a = 0;\n                let centerY = (j + 0.5) * ratioHeight;\n\n                for (let yy = Math.floor(j * ratioHeight); yy < (j + 1) * ratioHeight; yy++) {\n                    let dy = Math.abs(centerY - (yy + 0.5)) / ratioHeightHalf;\n                    let centerX = (i + 0.5) * ratioWidth;\n                    let w0 = dy * dy;\n\n                    for (let xx = Math.floor(i * ratioWidth); xx < (i + 1) * ratioWidth; xx++) {\n                        let dx = Math.abs(centerX - (xx + 0.5)) / ratioWidthHalf;\n                        let w = Math.sqrt(w0 + dx * dx);\n\n                        if (w >= -1 && w <= 1) {\n                            weight = 2 * w * w * w - 3 * w * w + 1;\n\n                            if (weight > 0) {\n                                dx = 4 * (xx + yy * originWidth);\n\n                                let ref = inputData[dx + 3];\n                                a += weight * ref;\n                                weightsAlpha += weight;\n\n                                if (ref < 255) {\n                                    weight = (weight * ref) / 250;\n                                }\n\n                                r += weight * inputData[dx];\n                                g += weight * inputData[dx + 1];\n                                b += weight * inputData[dx + 2];\n                                weights += weight;\n                            }\n                        }\n                    }\n                }\n\n                outputData[x2] = r / weights;\n                outputData[x2 + 1] = g / weights;\n                outputData[x2 + 2] = b / weights;\n                outputData[x2 + 3] = a / weightsAlpha;\n\n                matrix && applyFilterMatrix(x2, outputData, matrix);\n            }\n        }\n\n        return {\n            data: outputData,\n            width: targetWidth,\n            height: targetHeight,\n        };\n    }\n};\n/* javascript-obfuscator:enable */\n\nconst correctOrientation = (view, offset) => {\n    // Missing 0x45786966 Marker? No Exif Header, stop here\n    if (view.getUint32(offset + 4, false) !== 0x45786966) return;\n\n    // next byte!\n    offset += 4;\n\n    // First 2bytes defines byte align of TIFF data.\n    // If it is 0x4949=\"I I\", it means \"Intel\" type byte align\n    const intelByteAligned = view.getUint16((offset += 6), false) === 0x4949;\n    offset += view.getUint32(offset + 4, intelByteAligned);\n\n    const tags = view.getUint16(offset, intelByteAligned);\n    offset += 2;\n\n    // find Orientation tag\n    for (let i = 0; i < tags; i++) {\n        if (view.getUint16(offset + i * 12, intelByteAligned) === 0x0112) {\n            view.setUint16(offset + i * 12 + 8, 1, intelByteAligned);\n            return true;\n        }\n    }\n    return false;\n};\n\nconst readData = data => {\n    const view = new DataView(data);\n\n    // Every JPEG file starts from binary value '0xFFD8'\n    // If it's not present, exit here\n    if (view.getUint16(0) !== 0xffd8) return null;\n\n    let offset = 2; // Start at 2 as we skipped two bytes (FFD8)\n    let marker;\n    let markerLength;\n    let orientationCorrected = false;\n\n    while (offset < view.byteLength) {\n        marker = view.getUint16(offset, false);\n        markerLength = view.getUint16(offset + 2, false) + 2;\n\n        // Test if is APP and COM markers\n        const isData = (marker >= 0xffe0 && marker <= 0xffef) || marker === 0xfffe;\n        if (!isData) {\n            break;\n        }\n\n        if (!orientationCorrected) {\n            orientationCorrected = correctOrientation(view, offset, markerLength);\n        }\n\n        if (offset + markerLength > view.byteLength) {\n            break;\n        }\n\n        offset += markerLength;\n    }\n    return data.slice(0, offset);\n};\n\nconst getImageHead = file =>\n    new Promise(resolve => {\n        const reader = new FileReader();\n        reader.onload = () => resolve(readData(reader.result) || null);\n        reader.readAsArrayBuffer(file.slice(0, 256 * 1024));\n    });\n\nconst getBlobBuilder = () => {\n    return (window.BlobBuilder =\n        window.BlobBuilder ||\n        window.WebKitBlobBuilder ||\n        window.MozBlobBuilder ||\n        window.MSBlobBuilder);\n};\n\nconst createBlob = (arrayBuffer, mimeType) => {\n    const BB = getBlobBuilder();\n\n    if (BB) {\n        const bb = new BB();\n        bb.append(arrayBuffer);\n        return bb.getBlob(mimeType);\n    }\n\n    return new Blob([arrayBuffer], {\n        type: mimeType,\n    });\n};\n\nconst getUniqueId = () =>\n    Math.random()\n        .toString(36)\n        .substr(2, 9);\n\nconst createWorker = fn => {\n    const workerBlob = new Blob(['(', fn.toString(), ')()'], { type: 'application/javascript' });\n    const workerURL = URL.createObjectURL(workerBlob);\n    const worker = new Worker(workerURL);\n\n    const trips = [];\n\n    return {\n        transfer: () => {}, // (message, cb) => {}\n        post: (message, cb, transferList) => {\n            const id = getUniqueId();\n            trips[id] = cb;\n\n            worker.onmessage = e => {\n                const cb = trips[e.data.id];\n                if (!cb) return;\n                cb(e.data.message);\n                delete trips[e.data.id];\n            };\n\n            worker.postMessage(\n                {\n                    id,\n                    message,\n                },\n                transferList\n            );\n        },\n        terminate: () => {\n            worker.terminate();\n            URL.revokeObjectURL(workerURL);\n        },\n    };\n};\n\nconst loadImage = url =>\n    new Promise((resolve, reject) => {\n        const img = new Image();\n        img.onload = () => {\n            resolve(img);\n        };\n        img.onerror = e => {\n            reject(e);\n        };\n        img.src = url;\n    });\n\nconst chain = funcs =>\n    funcs.reduce(\n        (promise, func) => promise.then(result => func().then(Array.prototype.concat.bind(result))),\n        Promise.resolve([])\n    );\n\nconst canvasApplyMarkup = (canvas, markup) =>\n    new Promise(resolve => {\n        const size = {\n            width: canvas.width,\n            height: canvas.height,\n        };\n\n        const ctx = canvas.getContext('2d');\n\n        const drawers = markup.sort(sortMarkupByZIndex).map(item => () =>\n            new Promise(resolve => {\n                const result = TYPE_DRAW_ROUTES[item[0]](ctx, size, item[1], resolve);\n                if (result) resolve();\n            })\n        );\n\n        chain(drawers).then(() => resolve(canvas));\n    });\n\nconst applyMarkupStyles = (ctx, styles) => {\n    ctx.beginPath();\n    ctx.lineCap = styles['stroke-linecap'];\n    ctx.lineJoin = styles['stroke-linejoin'];\n    ctx.lineWidth = styles['stroke-width'];\n    if (styles['stroke-dasharray'].length) {\n        ctx.setLineDash(styles['stroke-dasharray'].split(','));\n    }\n    ctx.fillStyle = styles['fill'];\n    ctx.strokeStyle = styles['stroke'];\n    ctx.globalAlpha = styles.opacity || 1;\n};\n\nconst drawMarkupStyles = ctx => {\n    ctx.fill();\n    ctx.stroke();\n    ctx.globalAlpha = 1;\n};\n\nconst drawRect = (ctx, size, markup) => {\n    const rect = getMarkupRect(markup, size);\n    const styles = getMarkupStyles(markup, size);\n    applyMarkupStyles(ctx, styles);\n    ctx.rect(rect.x, rect.y, rect.width, rect.height);\n    drawMarkupStyles(ctx, styles);\n    return true;\n};\n\nconst drawEllipse = (ctx, size, markup) => {\n    const rect = getMarkupRect(markup, size);\n    const styles = getMarkupStyles(markup, size);\n    applyMarkupStyles(ctx, styles);\n\n    const x = rect.x,\n        y = rect.y,\n        w = rect.width,\n        h = rect.height,\n        kappa = 0.5522848,\n        ox = (w / 2) * kappa,\n        oy = (h / 2) * kappa,\n        xe = x + w,\n        ye = y + h,\n        xm = x + w / 2,\n        ym = y + h / 2;\n\n    ctx.moveTo(x, ym);\n    ctx.bezierCurveTo(x, ym - oy, xm - ox, y, xm, y);\n    ctx.bezierCurveTo(xm + ox, y, xe, ym - oy, xe, ym);\n    ctx.bezierCurveTo(xe, ym + oy, xm + ox, ye, xm, ye);\n    ctx.bezierCurveTo(xm - ox, ye, x, ym + oy, x, ym);\n\n    drawMarkupStyles(ctx, styles);\n    return true;\n};\n\nconst drawImage = (ctx, size, markup, done) => {\n    const rect = getMarkupRect(markup, size);\n    const styles = getMarkupStyles(markup, size);\n    applyMarkupStyles(ctx, styles);\n\n    const image = new Image();\n\n    // if is cross origin image add cross origin attribute\n    const isCrossOriginImage =\n        new URL(markup.src, window.location.href).origin !== window.location.origin;\n    if (isCrossOriginImage) image.crossOrigin = '';\n\n    image.onload = () => {\n        if (markup.fit === 'cover') {\n            const ar = rect.width / rect.height;\n            const width = ar > 1 ? image.width : image.height * ar;\n            const height = ar > 1 ? image.width / ar : image.height;\n            const x = image.width * 0.5 - width * 0.5;\n            const y = image.height * 0.5 - height * 0.5;\n            ctx.drawImage(image, x, y, width, height, rect.x, rect.y, rect.width, rect.height);\n        } else if (markup.fit === 'contain') {\n            const scalar = Math.min(rect.width / image.width, rect.height / image.height);\n            const width = scalar * image.width;\n            const height = scalar * image.height;\n            const x = rect.x + rect.width * 0.5 - width * 0.5;\n            const y = rect.y + rect.height * 0.5 - height * 0.5;\n            ctx.drawImage(image, 0, 0, image.width, image.height, x, y, width, height);\n        } else {\n            ctx.drawImage(\n                image,\n                0,\n                0,\n                image.width,\n                image.height,\n                rect.x,\n                rect.y,\n                rect.width,\n                rect.height\n            );\n        }\n\n        drawMarkupStyles(ctx, styles);\n        done();\n    };\n    image.src = markup.src;\n};\n\nconst drawText = (ctx, size, markup) => {\n    const rect = getMarkupRect(markup, size);\n    const styles = getMarkupStyles(markup, size);\n    applyMarkupStyles(ctx, styles);\n\n    const fontSize = getMarkupValue(markup.fontSize, size);\n    const fontFamily = markup.fontFamily || 'sans-serif';\n    const fontWeight = markup.fontWeight || 'normal';\n    const textAlign = markup.textAlign || 'left';\n\n    ctx.font = `${fontWeight} ${fontSize}px ${fontFamily}`;\n    ctx.textAlign = textAlign;\n    ctx.fillText(markup.text, rect.x, rect.y);\n\n    drawMarkupStyles(ctx, styles);\n    return true;\n};\n\nconst drawPath = (ctx, size, markup) => {\n    const styles = getMarkupStyles(markup, size);\n    applyMarkupStyles(ctx, styles);\n    ctx.beginPath();\n\n    const points = markup.points.map(point => ({\n        x: getMarkupValue(point.x, size, 1, 'width'),\n        y: getMarkupValue(point.y, size, 1, 'height'),\n    }));\n\n    ctx.moveTo(points[0].x, points[0].y);\n    const l = points.length;\n    for (let i = 1; i < l; i++) {\n        ctx.lineTo(points[i].x, points[i].y);\n    }\n\n    drawMarkupStyles(ctx, styles);\n    return true;\n};\n\nconst drawLine = (ctx, size, markup) => {\n    const rect = getMarkupRect(markup, size);\n    const styles = getMarkupStyles(markup, size);\n    applyMarkupStyles(ctx, styles);\n\n    ctx.beginPath();\n\n    const origin = {\n        x: rect.x,\n        y: rect.y,\n    };\n\n    const target = {\n        x: rect.x + rect.width,\n        y: rect.y + rect.height,\n    };\n\n    ctx.moveTo(origin.x, origin.y);\n    ctx.lineTo(target.x, target.y);\n\n    const v = vectorNormalize({\n        x: target.x - origin.x,\n        y: target.y - origin.y,\n    });\n\n    const l = 0.04 * Math.min(size.width, size.height);\n\n    if (markup.lineDecoration.indexOf('arrow-begin') !== -1) {\n        const arrowBeginRotationPoint = vectorMultiply(v, l);\n        const arrowBeginCenter = vectorAdd(origin, arrowBeginRotationPoint);\n        const arrowBeginA = vectorRotate(origin, 2, arrowBeginCenter);\n        const arrowBeginB = vectorRotate(origin, -2, arrowBeginCenter);\n\n        ctx.moveTo(arrowBeginA.x, arrowBeginA.y);\n        ctx.lineTo(origin.x, origin.y);\n        ctx.lineTo(arrowBeginB.x, arrowBeginB.y);\n    }\n    if (markup.lineDecoration.indexOf('arrow-end') !== -1) {\n        const arrowEndRotationPoint = vectorMultiply(v, -l);\n        const arrowEndCenter = vectorAdd(target, arrowEndRotationPoint);\n        const arrowEndA = vectorRotate(target, 2, arrowEndCenter);\n        const arrowEndB = vectorRotate(target, -2, arrowEndCenter);\n\n        ctx.moveTo(arrowEndA.x, arrowEndA.y);\n        ctx.lineTo(target.x, target.y);\n        ctx.lineTo(arrowEndB.x, arrowEndB.y);\n    }\n\n    drawMarkupStyles(ctx, styles);\n    return true;\n};\n\nconst TYPE_DRAW_ROUTES = {\n    rect: drawRect,\n    ellipse: drawEllipse,\n    image: drawImage,\n    text: drawText,\n    line: drawLine,\n    path: drawPath,\n};\n\nconst imageDataToCanvas = imageData => {\n    const image = document.createElement('canvas');\n    image.width = imageData.width;\n    image.height = imageData.height;\n    const ctx = image.getContext('2d');\n    ctx.putImageData(imageData, 0, 0);\n    return image;\n};\n\nconst transformImage = (file, instructions, options = {}) =>\n    new Promise((resolve, reject) => {\n        // if the file is not an image we do not have any business transforming it\n        if (!file || !isImage$1(file)) return reject({ status: 'not an image file', file });\n\n        // get separate options for easier use\n        const { stripImageHead, beforeCreateBlob, afterCreateBlob, canvasMemoryLimit } = options;\n\n        // get crop\n        const { crop, size, filter, markup, output } = instructions;\n\n        // get exif orientation\n        const orientation =\n            instructions.image && instructions.image.orientation\n                ? Math.max(1, Math.min(8, instructions.image.orientation))\n                : null;\n\n        // compression quality 0 => 100\n        const qualityAsPercentage = output && output.quality;\n        const quality = qualityAsPercentage === null ? null : qualityAsPercentage / 100;\n\n        // output format\n        const type = (output && output.type) || null;\n\n        // background color\n        const background = (output && output.background) || null;\n\n        // get transforms\n        const transforms = [];\n\n        // add resize transforms if set\n        if (size && (typeof size.width === 'number' || typeof size.height === 'number')) {\n            transforms.push({ type: 'resize', data: size });\n        }\n\n        // add filters\n        if (filter && filter.length === 20) {\n            transforms.push({ type: 'filter', data: filter });\n        }\n\n        // resolves with supplied blob\n        const resolveWithBlob = blob => {\n            const promisedBlob = afterCreateBlob ? afterCreateBlob(blob) : blob;\n            Promise.resolve(promisedBlob).then(resolve);\n        };\n\n        // done\n        const toBlob = (imageData, options) => {\n            const canvas = imageDataToCanvas(imageData);\n            const promisedCanvas = markup.length ? canvasApplyMarkup(canvas, markup) : canvas;\n            Promise.resolve(promisedCanvas).then(canvas => {\n                canvasToBlob(canvas, options, beforeCreateBlob)\n                    .then(blob => {\n                        // force release of canvas memory\n                        canvasRelease(canvas);\n\n                        // remove image head (default)\n                        if (stripImageHead) return resolveWithBlob(blob);\n\n                        // try to copy image head from original file to generated blob\n                        getImageHead(file).then(imageHead => {\n                            // re-inject image head in case of JPEG, as the image head is removed by canvas export\n                            if (imageHead !== null) {\n                                blob = new Blob([imageHead, blob.slice(20)], { type: blob.type });\n                            }\n\n                            // done!\n                            resolveWithBlob(blob);\n                        });\n                    })\n                    .catch(reject);\n            });\n        };\n\n        // if this is an svg and we want it to stay an svg\n        if (/svg/.test(file.type) && type === null) {\n            return cropSVG(file, crop, markup, { background }).then(text => {\n                resolve(createBlob(text, 'image/svg+xml'));\n            });\n        }\n\n        // get file url\n        const url = URL.createObjectURL(file);\n\n        // turn the file into an image\n        loadImage(url)\n            .then(image => {\n                // url is no longer needed\n                URL.revokeObjectURL(url);\n\n                // draw to canvas and start transform chain\n                const imageData = imageToImageData(image, orientation, crop, {\n                    canvasMemoryLimit,\n                    background,\n                });\n\n                // determine the format of the blob that we will output\n                const outputFormat = {\n                    quality,\n                    type: type || file.type,\n                };\n\n                // no transforms necessary, we done!\n                if (!transforms.length) {\n                    return toBlob(imageData, outputFormat);\n                }\n\n                // send to the transform worker to transform the blob on a separate thread\n                const worker = createWorker(TransformWorker);\n                worker.post(\n                    {\n                        transforms,\n                        imageData,\n                    },\n                    response => {\n                        // finish up\n                        toBlob(objectToImageData(response), outputFormat);\n\n                        // stop worker\n                        worker.terminate();\n                    },\n                    [imageData.data.buffer]\n                );\n            })\n            .catch(reject);\n    });\n\nconst MARKUP_RECT = ['x', 'y', 'left', 'top', 'right', 'bottom', 'width', 'height'];\n\nconst toOptionalFraction = value =>\n    typeof value === 'string' && /%/.test(value) ? parseFloat(value) / 100 : value;\n\n// adds default markup properties, clones markup\nconst prepareMarkup = markup => {\n    const [type, props] = markup;\n\n    const rect = props.points\n        ? {}\n        : MARKUP_RECT.reduce((prev, curr) => {\n              prev[curr] = toOptionalFraction(props[curr]);\n              return prev;\n          }, {});\n\n    return [\n        type,\n        {\n            zIndex: 0,\n            ...props,\n            ...rect,\n        },\n    ];\n};\n\nconst getImageSize = file =>\n    new Promise((resolve, reject) => {\n        const imageElement = new Image();\n        imageElement.src = URL.createObjectURL(file);\n\n        // start testing size\n        const measure = () => {\n            const width = imageElement.naturalWidth;\n            const height = imageElement.naturalHeight;\n            const hasSize = width && height;\n            if (!hasSize) return;\n\n            URL.revokeObjectURL(imageElement.src);\n            clearInterval(intervalId);\n            resolve({ width, height });\n        };\n\n        imageElement.onerror = err => {\n            URL.revokeObjectURL(imageElement.src);\n            clearInterval(intervalId);\n            reject(err);\n        };\n\n        const intervalId = setInterval(measure, 1);\n\n        measure();\n    });\n\n/**\n * Polyfill Edge and IE when in Browser\n */\nif (typeof window !== 'undefined' && typeof window.document !== 'undefined') {\n    if (!HTMLCanvasElement.prototype.toBlob) {\n        Object.defineProperty(HTMLCanvasElement.prototype, 'toBlob', {\n            value: function(cb, type, quality) {\n                const canvas = this;\n                setTimeout(() => {\n                    const dataURL = canvas.toDataURL(type, quality).split(',')[1];\n                    const binStr = atob(dataURL);\n                    let index = binStr.length;\n                    const data = new Uint8Array(index);\n                    while (index--) {\n                        data[index] = binStr.charCodeAt(index);\n                    }\n                    cb(new Blob([data], { type: type || 'image/png' }));\n                });\n            },\n        });\n    }\n}\n\nconst isBrowser = typeof window !== 'undefined' && typeof window.document !== 'undefined';\nconst isIOS = isBrowser && /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;\n\n/**\n * Image Transform Plugin\n */\nconst plugin = ({ addFilter, utils }) => {\n    const { Type, forin, getFileFromBlob, isFile } = utils;\n\n    /**\n     * Helper functions\n     */\n\n    // valid transforms (in correct order)\n    const TRANSFORM_LIST = ['crop', 'resize', 'filter', 'markup', 'output'];\n\n    const createVariantCreator = updateMetadata => (transform, file, metadata) =>\n        transform(file, updateMetadata ? updateMetadata(metadata) : metadata);\n\n    const isDefaultCrop = crop =>\n        crop.aspectRatio === null &&\n        crop.rotation === 0 &&\n        crop.zoom === 1 &&\n        crop.center &&\n        crop.center.x === 0.5 &&\n        crop.center.y === 0.5 &&\n        crop.flip &&\n        crop.flip.horizontal === false &&\n        crop.flip.vertical === false;\n\n    /**\n     * Filters\n     */\n    addFilter(\n        'SHOULD_PREPARE_OUTPUT',\n        (shouldPrepareOutput, { query }) =>\n            new Promise(resolve => {\n                // If is not async should prepare now\n                resolve(!query('IS_ASYNC'));\n            })\n    );\n\n    const shouldTransformFile = (query, file, item) =>\n        new Promise(resolve => {\n            if (\n                !query('GET_ALLOW_IMAGE_TRANSFORM') ||\n                item.archived ||\n                !isFile(file) ||\n                !isImage(file)\n            ) {\n                return resolve(false);\n            }\n\n            // if size can't be read this browser doesn't support image\n            getImageSize(file)\n                .then(() => {\n                    const fn = query('GET_IMAGE_TRANSFORM_IMAGE_FILTER');\n                    if (fn) {\n                        const filterResult = fn(file);\n                        if (filterResult == null) {\n                            // undefined or null\n                            return handleRevert(true);\n                        }\n                        if (typeof filterResult === 'boolean') {\n                            return resolve(filterResult);\n                        }\n                        if (typeof filterResult.then === 'function') {\n                            return filterResult.then(resolve);\n                        }\n                    }\n\n                    resolve(true);\n                })\n                .catch(err => {\n                    resolve(false);\n                });\n        });\n\n    addFilter('DID_CREATE_ITEM', (item, { query, dispatch }) => {\n        if (!query('GET_ALLOW_IMAGE_TRANSFORM')) return;\n\n        item.extend(\n            'requestPrepare',\n            () =>\n                new Promise((resolve, reject) => {\n                    dispatch(\n                        'REQUEST_PREPARE_OUTPUT',\n                        {\n                            query: item.id,\n                            item,\n                            success: resolve,\n                            failure: reject,\n                        },\n                        true\n                    );\n                })\n        );\n    });\n\n    // subscribe to file transformations\n    addFilter(\n        'PREPARE_OUTPUT',\n        (file, { query, item }) =>\n            new Promise(resolve => {\n                shouldTransformFile(query, file, item).then(shouldTransform => {\n                    // no need to transform, exit\n                    if (!shouldTransform) return resolve(file);\n\n                    // get variants\n                    const variants = [];\n\n                    // add original file\n                    if (query('GET_IMAGE_TRANSFORM_VARIANTS_INCLUDE_ORIGINAL')) {\n                        variants.push(\n                            () =>\n                                new Promise(resolve => {\n                                    resolve({\n                                        name: query('GET_IMAGE_TRANSFORM_VARIANTS_ORIGINAL_NAME'),\n                                        file,\n                                    });\n                                })\n                        );\n                    }\n\n                    // add default output version if output default set to true or if no variants defined\n                    if (query('GET_IMAGE_TRANSFORM_VARIANTS_INCLUDE_DEFAULT')) {\n                        variants.push(\n                            (transform, file, metadata) =>\n                                new Promise(resolve => {\n                                    transform(file, metadata).then(file =>\n                                        resolve({\n                                            name: query(\n                                                'GET_IMAGE_TRANSFORM_VARIANTS_DEFAULT_NAME'\n                                            ),\n                                            file,\n                                        })\n                                    );\n                                })\n                        );\n                    }\n\n                    // get other variants\n                    const variantsDefinition = query('GET_IMAGE_TRANSFORM_VARIANTS') || {};\n                    forin(variantsDefinition, (key, fn) => {\n                        const createVariant = createVariantCreator(fn);\n                        variants.push(\n                            (transform, file, metadata) =>\n                                new Promise(resolve => {\n                                    createVariant(transform, file, metadata).then(file =>\n                                        resolve({ name: key, file })\n                                    );\n                                })\n                        );\n                    });\n\n                    // output format (quality 0 => 100)\n                    const qualityAsPercentage = query('GET_IMAGE_TRANSFORM_OUTPUT_QUALITY');\n                    const qualityMode = query('GET_IMAGE_TRANSFORM_OUTPUT_QUALITY_MODE');\n                    const quality = qualityAsPercentage === null ? null : qualityAsPercentage / 100;\n                    const type = query('GET_IMAGE_TRANSFORM_OUTPUT_MIME_TYPE');\n                    const clientTransforms =\n                        query('GET_IMAGE_TRANSFORM_CLIENT_TRANSFORMS') || TRANSFORM_LIST;\n\n                    // update transform metadata object\n                    item.setMetadata(\n                        'output',\n                        {\n                            type,\n                            quality,\n                            client: clientTransforms,\n                        },\n                        true\n                    );\n\n                    // the function that is used to apply the file transformations\n                    const transform = (file, metadata) =>\n                        new Promise((resolve, reject) => {\n                            const filteredMetadata = { ...metadata };\n\n                            Object.keys(filteredMetadata)\n                                .filter(instruction => instruction !== 'exif')\n                                .forEach(instruction => {\n                                    // if not in list, remove from object, the instruction will be handled by the server\n                                    if (clientTransforms.indexOf(instruction) === -1) {\n                                        delete filteredMetadata[instruction];\n                                    }\n                                });\n\n                            const { resize, exif, output, crop, filter, markup } = filteredMetadata;\n\n                            const instructions = {\n                                image: {\n                                    orientation: exif ? exif.orientation : null,\n                                },\n                                output:\n                                    output &&\n                                    (output.type ||\n                                        typeof output.quality === 'number' ||\n                                        output.background)\n                                        ? {\n                                              type: output.type,\n                                              quality:\n                                                  typeof output.quality === 'number'\n                                                      ? output.quality * 100\n                                                      : null,\n                                              background:\n                                                  output.background ||\n                                                  query(\n                                                      'GET_IMAGE_TRANSFORM_CANVAS_BACKGROUND_COLOR'\n                                                  ) ||\n                                                  null,\n                                          }\n                                        : undefined,\n                                size:\n                                    resize && (resize.size.width || resize.size.height)\n                                        ? {\n                                              mode: resize.mode,\n                                              upscale: resize.upscale,\n                                              ...resize.size,\n                                          }\n                                        : undefined,\n                                crop:\n                                    crop && !isDefaultCrop(crop)\n                                        ? {\n                                              ...crop,\n                                          }\n                                        : undefined,\n                                markup: markup && markup.length ? markup.map(prepareMarkup) : [],\n                                filter,\n                            };\n\n                            if (instructions.output) {\n                                // determine if file type will change\n                                const willChangeType = output.type\n                                    ? // type set\n                                      output.type !== file.type\n                                    : // type not set\n                                      false;\n\n                                const canChangeQuality = /\\/jpe?g$/.test(file.type);\n                                const willChangeQuality =\n                                    output.quality !== null\n                                        ? // quality set\n                                          canChangeQuality && qualityMode === 'always'\n                                        : // quality not set\n                                          false;\n\n                                // determine if file data will be modified\n                                const willModifyImageData = !!(\n                                    instructions.size ||\n                                    instructions.crop ||\n                                    instructions.filter ||\n                                    willChangeType ||\n                                    willChangeQuality\n                                );\n\n                                // if we're not modifying the image data then we don't have to modify the output\n                                if (!willModifyImageData) return resolve(file);\n                            }\n\n                            const options = {\n                                beforeCreateBlob: query('GET_IMAGE_TRANSFORM_BEFORE_CREATE_BLOB'),\n                                afterCreateBlob: query('GET_IMAGE_TRANSFORM_AFTER_CREATE_BLOB'),\n                                canvasMemoryLimit: query('GET_IMAGE_TRANSFORM_CANVAS_MEMORY_LIMIT'),\n                                stripImageHead: query(\n                                    'GET_IMAGE_TRANSFORM_OUTPUT_STRIP_IMAGE_HEAD'\n                                ),\n                            };\n\n                            transformImage(file, instructions, options)\n                                .then(blob => {\n                                    // set file object\n                                    const out = getFileFromBlob(\n                                        blob,\n                                        // rename the original filename to match the mime type of the output image\n                                        renameFileToMatchMimeType(\n                                            file.name,\n                                            getValidOutputMimeType(blob.type)\n                                        )\n                                    );\n\n                                    resolve(out);\n                                })\n                                .catch(reject);\n                        });\n\n                    // start creating variants\n                    const variantPromises = variants.map(create =>\n                        create(transform, file, item.getMetadata())\n                    );\n\n                    // wait for results\n                    Promise.all(variantPromises).then(files => {\n                        // if single file object in array, return the single file object else, return array of\n                        resolve(\n                            files.length === 1 && files[0].name === null\n                                ? // return the File object\n                                  files[0].file\n                                : // return an array of files { name:'name', file:File }\n                                  files\n                        );\n                    });\n                });\n            })\n    );\n\n    // Expose plugin options\n    return {\n        options: {\n            allowImageTransform: [true, Type.BOOLEAN],\n\n            // filter images to transform\n            imageTransformImageFilter: [null, Type.FUNCTION],\n\n            // null, 'image/jpeg', 'image/png'\n            imageTransformOutputMimeType: [null, Type.STRING],\n\n            // null, 0 - 100\n            imageTransformOutputQuality: [null, Type.INT],\n\n            // set to false to copy image exif data to output\n            imageTransformOutputStripImageHead: [true, Type.BOOLEAN],\n\n            // only apply transforms in this list\n            imageTransformClientTransforms: [null, Type.ARRAY],\n\n            // only apply output quality when a transform is required\n            imageTransformOutputQualityMode: ['always', Type.STRING],\n            // 'always'\n            // 'optional'\n            // 'mismatch' (future feature, only applied if quality differs from input)\n\n            // get image transform variants\n            imageTransformVariants: [null, Type.OBJECT],\n\n            // should we post the default transformed file\n            imageTransformVariantsIncludeDefault: [true, Type.BOOLEAN],\n\n            // which name to prefix the default transformed file with\n            imageTransformVariantsDefaultName: [null, Type.STRING],\n\n            // should we post the original file\n            imageTransformVariantsIncludeOriginal: [false, Type.BOOLEAN],\n\n            // which name to prefix the original file with\n            imageTransformVariantsOriginalName: ['original_', Type.STRING],\n\n            // called before creating the blob, receives canvas, expects promise resolve with canvas\n            imageTransformBeforeCreateBlob: [null, Type.FUNCTION],\n\n            // expects promise resolved with blob\n            imageTransformAfterCreateBlob: [null, Type.FUNCTION],\n\n            // canvas memory limit\n            imageTransformCanvasMemoryLimit: [isBrowser && isIOS ? 4096 * 4096 : null, Type.INT],\n\n            // background image of the output canvas\n            imageTransformCanvasBackgroundColor: [null, Type.STRING],\n        },\n    };\n};\n\n// fire pluginloaded event if running in browser, this allows registering the plugin when using async script tags\nif (isBrowser) {\n    document.dispatchEvent(new CustomEvent('FilePond:pluginloaded', { detail: plugin }));\n}\n\nexport default plugin;\n"], "mappings": ";;;AASA,IAAM,UAAU,UAAQ,SAAS,KAAK,KAAK,IAAI;AAE/C,IAAM,8BAA8B,UAAQ,KAAK,OAAO,GAAG,KAAK,YAAY,GAAG,CAAC,KAAK;AAGrF,IAAM,eAAe;AAAA,EACjB,MAAM;AAAA,EACN,WAAW;AACf;AAEA,IAAM,4BAA4B,CAAC,UAAU,aAAa;AACtD,QAAM,OAAO,4BAA4B,QAAQ;AACjD,QAAM,OAAO,SAAS,MAAM,GAAG,EAAE,CAAC;AAClC,QAAM,YAAY,aAAa,IAAI,KAAK;AACxC,SAAO,GAAG,IAAI,IAAI,SAAS;AAC/B;AAGA,IAAM,yBAAyB,UAAS,oBAAoB,KAAK,IAAI,IAAI,OAAO;AAGhF,IAAM,YAAY,UAAQ,SAAS,KAAK,KAAK,IAAI;AAEjD,IAAM,WAAW;AAAA,EACb,GAAG,MAAM,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,EAC1B,GAAG,WAAS,CAAC,IAAI,GAAG,GAAG,GAAG,OAAO,CAAC;AAAA,EAClC,GAAG,CAAC,OAAO,WAAW,CAAC,IAAI,GAAG,GAAG,IAAI,OAAO,MAAM;AAAA,EAClD,GAAG,CAAC,OAAO,WAAW,CAAC,GAAG,GAAG,GAAG,IAAI,GAAG,MAAM;AAAA,EAC7C,GAAG,MAAM,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,EAC1B,GAAG,CAAC,OAAO,WAAW,CAAC,GAAG,GAAG,IAAI,GAAG,QAAQ,CAAC;AAAA,EAC7C,GAAG,CAAC,OAAO,WAAW,CAAC,GAAG,IAAI,IAAI,GAAG,QAAQ,KAAK;AAAA,EAClD,GAAG,WAAS,CAAC,GAAG,IAAI,GAAG,GAAG,GAAG,KAAK;AACtC;AAEA,IAAM,4BAA4B,CAAC,OAAO,QAAQ,gBAAgB;AAC9D,MAAI,gBAAgB,IAAI;AACpB,kBAAc;AAAA,EAClB;AACA,SAAO,SAAS,WAAW,EAAE,OAAO,MAAM;AAC9C;AAEA,IAAM,eAAe,CAAC,GAAG,OAAO,EAAE,GAAG,EAAE;AAEvC,IAAM,YAAY,CAAC,GAAG,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAEhD,IAAM,iBAAiB,CAAC,GAAG,MAAM,aAAa,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;AAElE,IAAM,wBAAwB,CAAC,GAAG,MAAM,UAAU,eAAe,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC,CAAC;AAE5F,IAAM,iBAAiB,CAAC,GAAG,MAAM,KAAK,KAAK,sBAAsB,GAAG,CAAC,CAAC;AAEtE,IAAM,uBAAuB,CAAC,QAAQ,aAAa;AAC/C,QAAM,IAAI;AAEV,QAAM,IAAI;AACV,QAAM,IAAI;AACV,QAAM,IAAI,qBAAqB;AAE/B,QAAM,OAAO,KAAK,IAAI,CAAC;AACvB,QAAM,OAAO,KAAK,IAAI,CAAC;AACvB,QAAM,OAAO,KAAK,IAAI,CAAC;AACvB,QAAM,OAAO,KAAK,IAAI,CAAC;AACvB,QAAM,QAAQ,IAAI;AAClB,QAAM,IAAI,QAAQ;AAClB,QAAM,IAAI,QAAQ;AAElB,SAAO,aAAa,OAAO,GAAG,OAAO,CAAC;AAC1C;AAEA,IAAM,qBAAqB,CAAC,MAAM,aAAa;AAC3C,QAAM,IAAI,KAAK;AACf,QAAM,IAAI,KAAK;AAEf,QAAM,MAAM,qBAAqB,GAAG,QAAQ;AAC5C,QAAM,MAAM,qBAAqB,GAAG,QAAQ;AAE5C,QAAM,KAAK,aAAa,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC;AAE1E,QAAM,KAAK,aAAa,KAAK,IAAI,KAAK,QAAQ,KAAK,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC;AAEvF,QAAM,KAAK,aAAa,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,KAAK,SAAS,KAAK,IAAI,IAAI,CAAC,CAAC;AAExF,SAAO;AAAA,IACH,OAAO,eAAe,IAAI,EAAE;AAAA,IAC5B,QAAQ,eAAe,IAAI,EAAE;AAAA,EACjC;AACJ;AAEA,IAAM,yBAAyB,CAAC,WAAW,UAAU,WAAW,GAAG,SAAS,EAAE,GAAG,KAAK,GAAG,IAAI,MAAM;AAE/F,QAAM,KAAK,OAAO,IAAI,MAAM,IAAI,OAAO,IAAI,OAAO;AAClD,QAAM,KAAK,OAAO,IAAI,MAAM,IAAI,OAAO,IAAI,OAAO;AAClD,QAAM,aAAa,KAAK,IAAI,UAAU;AACtC,QAAM,cAAc,KAAK,IAAI,UAAU;AAGvC,QAAM,kBAAkB,mBAAmB,UAAU,QAAQ;AAE7D,SAAO,KAAK,IAAI,gBAAgB,QAAQ,YAAY,gBAAgB,SAAS,WAAW;AAC5F;AAEA,IAAM,sBAAsB,CAAC,WAAW,gBAAgB;AACpD,MAAI,QAAQ,UAAU;AACtB,MAAI,SAAS,QAAQ;AACrB,MAAI,SAAS,UAAU,QAAQ;AAC3B,aAAS,UAAU;AACnB,YAAQ,SAAS;AAAA,EACrB;AACA,QAAM,KAAK,UAAU,QAAQ,SAAS;AACtC,QAAM,KAAK,UAAU,SAAS,UAAU;AAExC,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AAEA,IAAM,sBAAsB,CAAC,OAAO,mBAAmB,OAAO,MAAM;AAChE,QAAM,mBAAmB,MAAM,SAAS,MAAM;AAG9C,MAAI,cAAc;AAClB,MAAI,eAAe;AACnB,MAAI,WAAW;AACf,MAAI,YAAY;AAChB,MAAI,YAAY,cAAc;AAC1B,gBAAY;AACZ,eAAW,YAAY;AAAA,EAC3B;AAEA,QAAM,SAAS,KAAK,IAAI,cAAc,UAAU,eAAe,SAAS;AACxE,QAAM,QAAQ,MAAM,SAAS,OAAO,SAAS;AAC7C,QAAM,SAAS,QAAQ;AAEvB,SAAO;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AACJ;AAEA,IAAM,gBAAgB,YAAU;AAC5B,SAAO,QAAQ;AACf,SAAO,SAAS;AAChB,QAAM,MAAM,OAAO,WAAW,IAAI;AAClC,MAAI,UAAU,GAAG,GAAG,GAAG,CAAC;AAC5B;AAEA,IAAM,YAAY,UAAQ,SAAS,KAAK,cAAc,KAAK;AAE3D,IAAM,YAAY,CAAC,OAAO,aAAa,SAAS;AAC5C,MAAI,eAAe,KAAK,CAAC,UAAU,IAAI,GAAG;AACtC,UAAM,QAAQ,MAAM;AACpB,UAAM,SAAS,MAAM;AACrB,WAAO;AAAA,EACX;AAEA,QAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,QAAM,QAAQ,MAAM;AACpB,QAAM,SAAS,MAAM;AAGrB,QAAM,UAAU,eAAe,KAAK,eAAe;AACnD,MAAI,SAAS;AACT,WAAO,QAAQ;AACf,WAAO,SAAS;AAAA,EACpB,OAAO;AACH,WAAO,QAAQ;AACf,WAAO,SAAS;AAAA,EACpB;AAGA,QAAM,MAAM,OAAO,WAAW,IAAI;AAGlC,MAAI,aAAa;AACb,QAAI,UAAU,MAAM,KAAK,0BAA0B,OAAO,QAAQ,WAAW,CAAC;AAAA,EAClF;AAEA,MAAI,UAAU,IAAI,GAAG;AAGjB,UAAM,SAAS,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAChC,QAAK,CAAC,WAAW,KAAK,cAAe,UAAU,KAAK,UAAU;AAC1D,aAAO,CAAC,IAAI;AACZ,aAAO,CAAC,IAAI;AAAA,IAChB;AAIA,QAAK,CAAC,WAAW,KAAK,YAAc,WAAW,KAAK,YAAa;AAC7D,aAAO,CAAC,IAAI;AACZ,aAAO,CAAC,IAAI;AAAA,IAChB;AAEA,QAAI,UAAU,GAAG,MAAM;AAAA,EAC3B;AAEA,MAAI,UAAU,OAAO,GAAG,GAAG,OAAO,MAAM;AAExC,SAAO;AACX;AAEA,IAAM,mBAAmB,CAAC,cAAc,aAAa,OAAO,CAAC,GAAG,UAAU,CAAC,MAAM;AAC7E,QAAM,EAAE,mBAAmB,aAAa,KAAK,IAAI;AAEjD,QAAM,OAAO,KAAK,QAAQ;AAG1B,QAAM,SAAS,UAAU,cAAc,aAAa,KAAK,IAAI;AAC7D,QAAM,YAAY;AAAA,IACd,OAAO,OAAO;AAAA,IACd,QAAQ,OAAO;AAAA,EACnB;AAEA,QAAM,cAAc,KAAK,eAAe,UAAU,SAAS,UAAU;AAErE,MAAI,aAAa,oBAAoB,WAAW,aAAa,IAAI;AAEjE,MAAI,mBAAmB;AACnB,UAAM,iBAAiB,WAAW,QAAQ,WAAW;AACrD,QAAI,iBAAiB,mBAAmB;AACpC,YAAM,SAAS,KAAK,KAAK,iBAAiB,IAAI,KAAK,KAAK,cAAc;AACtE,gBAAU,QAAQ,KAAK,MAAM,UAAU,QAAQ,MAAM;AACrD,gBAAU,SAAS,KAAK,MAAM,UAAU,SAAS,MAAM;AACvD,mBAAa,oBAAoB,WAAW,aAAa,IAAI;AAAA,IACjE;AAAA,EACJ;AAEA,QAAM,SAAS,SAAS,cAAc,QAAQ;AAE9C,QAAM,eAAe;AAAA,IACjB,GAAG,WAAW,QAAQ;AAAA,IACtB,GAAG,WAAW,SAAS;AAAA,EAC3B;AAEA,QAAM,QAAQ;AAAA,IACV,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO,WAAW;AAAA,IAClB,QAAQ,WAAW;AAAA,IACnB,QAAQ;AAAA,EACZ;AAEA,QAAM,cAAc,OAAO,KAAK,eAAe,eAAe,KAAK;AAEnE,QAAM,QACF,OACA;AAAA,IACI;AAAA,IACA,oBAAoB,OAAO,WAAW;AAAA,IACtC,KAAK;AAAA,IACL,cAAc,KAAK,SAAS,EAAE,GAAG,KAAK,GAAG,IAAI;AAAA,EACjD;AAGJ,SAAO,QAAQ,KAAK,MAAM,WAAW,QAAQ,KAAK;AAClD,SAAO,SAAS,KAAK,MAAM,WAAW,SAAS,KAAK;AAEpD,eAAa,KAAK;AAClB,eAAa,KAAK;AAElB,QAAM,cAAc;AAAA,IAChB,GAAG,aAAa,IAAI,UAAU,SAAS,KAAK,SAAS,KAAK,OAAO,IAAI;AAAA,IACrE,GAAG,aAAa,IAAI,UAAU,UAAU,KAAK,SAAS,KAAK,OAAO,IAAI;AAAA,EAC1E;AAEA,QAAM,MAAM,OAAO,WAAW,IAAI;AAClC,MAAI,YAAY;AACZ,QAAI,YAAY;AAChB,QAAI,SAAS,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;AAAA,EAClD;AAGA,MAAI,UAAU,aAAa,GAAG,aAAa,CAAC;AAC5C,MAAI,OAAO,KAAK,YAAY,CAAC;AAG7B,MAAI;AAAA,IACA;AAAA,IACA,YAAY,IAAI,aAAa;AAAA,IAC7B,YAAY,IAAI,aAAa;AAAA,IAC7B,UAAU;AAAA,IACV,UAAU;AAAA,EACd;AAGA,QAAM,YAAY,IAAI,aAAa,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;AAGpE,gBAAc,MAAM;AAGpB,SAAO;AACX;AAKA,IAAM,cAAc,MAChB,OAAO,WAAW,eAAe,OAAO,OAAO,aAAa,aAAa;AAC7E,IAAI,YAAY;AACZ,MAAI,CAAC,kBAAkB,UAAU,QAAQ;AACrC,WAAO,eAAe,kBAAkB,WAAW,UAAU;AAAA,MACzD,OAAO,SAAS,UAAU,MAAM,SAAS;AACrC,YAAI,UAAU,KAAK,UAAU,MAAM,OAAO,EAAE,MAAM,GAAG,EAAE,CAAC;AACxD,mBAAW,WAAW;AAClB,cAAI,SAAS,KAAK,OAAO;AACzB,cAAI,MAAM,OAAO;AACjB,cAAI,MAAM,IAAI,WAAW,GAAG;AAC5B,mBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC1B,gBAAI,CAAC,IAAI,OAAO,WAAW,CAAC;AAAA,UAChC;AACA,mBAAS,IAAI,KAAK,CAAC,GAAG,GAAG,EAAE,MAAM,QAAQ,YAAY,CAAC,CAAC;AAAA,QAC3D,CAAC;AAAA,MACL;AAAA,IACJ,CAAC;AAAA,EACL;AACJ;AAEA,IAAM,eAAe,CAAC,QAAQ,SAAS,mBAAmB,SACtD,IAAI,QAAQ,aAAW;AACnB,QAAM,gBAAgB,mBAAmB,iBAAiB,MAAM,IAAI;AACpE,UAAQ,QAAQ,aAAa,EAAE,KAAK,CAAAA,YAAU;AAC1C,IAAAA,QAAO,OAAO,SAAS,QAAQ,MAAM,QAAQ,OAAO;AAAA,EACxD,CAAC;AACL,CAAC;AAEL,IAAM,iBAAiB,CAAC,GAAG,WAAW,eAAe,EAAE,IAAI,QAAQ,EAAE,IAAI,MAAM;AAE/E,IAAM,YAAY,CAAC,GAAG,MAAM,eAAe,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;AAE/D,IAAM,kBAAkB,OAAK;AACzB,QAAM,IAAI,KAAK,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AACzC,MAAI,MAAM,GAAG;AACT,WAAO;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,IACP;AAAA,EACJ;AACA,SAAO,eAAe,EAAE,IAAI,GAAG,EAAE,IAAI,CAAC;AAC1C;AAEA,IAAM,eAAe,CAAC,GAAG,SAAS,WAAW;AACzC,QAAM,MAAM,KAAK,IAAI,OAAO;AAC5B,QAAM,MAAM,KAAK,IAAI,OAAO;AAC5B,QAAM,IAAI,eAAe,EAAE,IAAI,OAAO,GAAG,EAAE,IAAI,OAAO,CAAC;AACvD,SAAO,eAAe,OAAO,IAAI,MAAM,EAAE,IAAI,MAAM,EAAE,GAAG,OAAO,IAAI,MAAM,EAAE,IAAI,MAAM,EAAE,CAAC;AAC5F;AAEA,IAAM,iBAAiB,CAAC,IAAI,GAAG,IAAI,OAAO,EAAE,GAAG,EAAE;AAEjD,IAAM,iBAAiB,CAAC,OAAO,MAAM,SAAS,GAAG,SAAS;AACtD,MAAI,OAAO,UAAU,UAAU;AAC3B,WAAO,WAAW,KAAK,IAAI;AAAA,EAC/B;AACA,MAAI,OAAO,UAAU,UAAU;AAC3B,WAAO,SAAS,OAAO,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,OAAO,KAAK,MAAM;AAAA,EACxE;AACA;AACJ;AAEA,IAAM,kBAAkB,CAAC,QAAQ,MAAM,UAAU;AAC7C,QAAM,YAAY,OAAO,eAAe,OAAO,aAAa;AAC5D,QAAM,OAAO,OAAO,mBAAmB,OAAO,aAAa;AAC3D,QAAM,SAAS,OAAO,eAAe,OAAO,aAAa;AACzD,QAAM,cAAc,eAAe,OAAO,eAAe,OAAO,WAAW,MAAM,KAAK;AACtF,QAAM,UAAU,OAAO,WAAW;AAClC,QAAM,WAAW,OAAO,YAAY;AACpC,QAAM,SACF,OAAO,cAAc,WACf,KACA,UAAU,IAAI,OAAK,eAAe,GAAG,MAAM,KAAK,CAAC,EAAE,KAAK,GAAG;AACrE,QAAM,UAAU,OAAO,WAAW;AAClC,SAAO;AAAA,IACH,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,gBAAgB,eAAe;AAAA,IAC/B,oBAAoB;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AAEA,IAAM,YAAY,WAAS,SAAS;AAEpC,IAAM,gBAAgB,CAAC,MAAM,MAAM,SAAS,MAAM;AAC9C,MAAI,OACA,eAAe,KAAK,GAAG,MAAM,QAAQ,OAAO,KAC5C,eAAe,KAAK,MAAM,MAAM,QAAQ,OAAO;AACnD,MAAI,MACA,eAAe,KAAK,GAAG,MAAM,QAAQ,QAAQ,KAC7C,eAAe,KAAK,KAAK,MAAM,QAAQ,QAAQ;AACnD,MAAI,QAAQ,eAAe,KAAK,OAAO,MAAM,QAAQ,OAAO;AAC5D,MAAI,SAAS,eAAe,KAAK,QAAQ,MAAM,QAAQ,QAAQ;AAC/D,MAAI,QAAQ,eAAe,KAAK,OAAO,MAAM,QAAQ,OAAO;AAC5D,MAAI,SAAS,eAAe,KAAK,QAAQ,MAAM,QAAQ,QAAQ;AAE/D,MAAI,CAAC,UAAU,GAAG,GAAG;AACjB,QAAI,UAAU,MAAM,KAAK,UAAU,MAAM,GAAG;AACxC,YAAM,KAAK,SAAS,SAAS;AAAA,IACjC,OAAO;AACH,YAAM;AAAA,IACV;AAAA,EACJ;AAEA,MAAI,CAAC,UAAU,IAAI,GAAG;AAClB,QAAI,UAAU,KAAK,KAAK,UAAU,KAAK,GAAG;AACtC,aAAO,KAAK,QAAQ,QAAQ;AAAA,IAChC,OAAO;AACH,aAAO;AAAA,IACX;AAAA,EACJ;AAEA,MAAI,CAAC,UAAU,KAAK,GAAG;AACnB,QAAI,UAAU,IAAI,KAAK,UAAU,KAAK,GAAG;AACrC,cAAQ,KAAK,QAAQ,OAAO;AAAA,IAChC,OAAO;AACH,cAAQ;AAAA,IACZ;AAAA,EACJ;AAEA,MAAI,CAAC,UAAU,MAAM,GAAG;AACpB,QAAI,UAAU,GAAG,KAAK,UAAU,MAAM,GAAG;AACrC,eAAS,KAAK,SAAS,MAAM;AAAA,IACjC,OAAO;AACH,eAAS;AAAA,IACb;AAAA,EACJ;AAEA,SAAO;AAAA,IACH,GAAG,QAAQ;AAAA,IACX,GAAG,OAAO;AAAA,IACV,OAAO,SAAS;AAAA,IAChB,QAAQ,UAAU;AAAA,EACtB;AACJ;AAEA,IAAM,oBAAoB,YACtB,OAAO,IAAI,CAAC,OAAO,UAAU,GAAG,UAAU,IAAI,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,MAAM,CAAC,EAAE,EAAE,KAAK,GAAG;AAE7F,IAAM,gBAAgB,CAAC,SAAS,SAC5B,OAAO,KAAK,IAAI,EAAE,QAAQ,SAAO,QAAQ,aAAa,KAAK,KAAK,GAAG,CAAC,CAAC;AAEzE,IAAM,KAAK;AACX,IAAM,MAAM,CAAC,KAAK,SAAS;AACvB,QAAM,UAAU,SAAS,gBAAgB,IAAI,GAAG;AAChD,MAAI,MAAM;AACN,kBAAc,SAAS,IAAI;AAAA,EAC/B;AACA,SAAO;AACX;AAEA,IAAM,aAAa,aACf,cAAc,SAAS;AAAA,EACnB,GAAG,QAAQ;AAAA,EACX,GAAG,QAAQ;AACf,CAAC;AAEL,IAAM,gBAAgB,aAAW;AAC7B,QAAM,KAAK,QAAQ,KAAK,IAAI,QAAQ,KAAK,QAAQ;AACjD,QAAM,KAAK,QAAQ,KAAK,IAAI,QAAQ,KAAK,SAAS;AAClD,QAAM,KAAK,QAAQ,KAAK,QAAQ;AAChC,QAAM,KAAK,QAAQ,KAAK,SAAS;AACjC,SAAO,cAAc,SAAS;AAAA,IAC1B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG,QAAQ;AAAA,EACf,CAAC;AACL;AAEA,IAAM,kBAAkB;AAAA,EACpB,SAAS;AAAA,EACT,OAAO;AACX;AAEA,IAAM,cAAc,CAAC,SAAS,WAAW;AACrC,gBAAc,SAAS;AAAA,IACnB,GAAG,QAAQ;AAAA,IACX,GAAG,QAAQ;AAAA,IACX,qBAAqB,gBAAgB,OAAO,GAAG,KAAK;AAAA,EACxD,CAAC;AACL;AAEA,IAAM,cAAc;AAAA,EAChB,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACX;AAEA,IAAM,aAAa,CAAC,SAAS,QAAQ,MAAM,UAAU;AACjD,QAAM,WAAW,eAAe,OAAO,UAAU,MAAM,KAAK;AAC5D,QAAM,aAAa,OAAO,cAAc;AACxC,QAAM,aAAa,OAAO,cAAc;AACxC,QAAM,YAAY,YAAY,OAAO,SAAS,KAAK;AAEnD,gBAAc,SAAS;AAAA,IACnB,GAAG,QAAQ;AAAA,IACX,GAAG,QAAQ;AAAA,IACX,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,aAAa;AAAA,IACb,eAAe;AAAA,IACf,eAAe;AAAA,EACnB,CAAC;AAGD,MAAI,QAAQ,SAAS,OAAO,MAAM;AAC9B,YAAQ,OAAO,OAAO;AACtB,YAAQ,cAAc,OAAO,KAAK,SAAS,OAAO,OAAO;AAAA,EAC7D;AACJ;AAEA,IAAM,aAAa,CAAC,SAAS,QAAQ,MAAM,UAAU;AACjD,gBAAc,SAAS;AAAA,IACnB,GAAG,QAAQ;AAAA,IACX,GAAG,QAAQ;AAAA,IACX,MAAM;AAAA,EACV,CAAC;AAED,QAAM,OAAO,QAAQ,WAAW,CAAC;AACjC,QAAM,QAAQ,QAAQ,WAAW,CAAC;AAClC,QAAM,MAAM,QAAQ,WAAW,CAAC;AAEhC,QAAM,SAAS,QAAQ;AAEvB,QAAM,SAAS;AAAA,IACX,GAAG,QAAQ,KAAK,IAAI,QAAQ,KAAK;AAAA,IACjC,GAAG,QAAQ,KAAK,IAAI,QAAQ,KAAK;AAAA,EACrC;AAEA,gBAAc,MAAM;AAAA,IAChB,IAAI,OAAO;AAAA,IACX,IAAI,OAAO;AAAA,IACX,IAAI,OAAO;AAAA,IACX,IAAI,OAAO;AAAA,EACf,CAAC;AAED,MAAI,CAAC,OAAO,eAAgB;AAE5B,QAAM,MAAM,UAAU;AACtB,MAAI,MAAM,UAAU;AAEpB,QAAM,IAAI,gBAAgB;AAAA,IACtB,GAAG,OAAO,IAAI,OAAO;AAAA,IACrB,GAAG,OAAO,IAAI,OAAO;AAAA,EACzB,CAAC;AAED,QAAM,IAAI,eAAe,MAAM,MAAM,KAAK;AAE1C,MAAI,OAAO,eAAe,QAAQ,aAAa,MAAM,IAAI;AACrD,UAAM,0BAA0B,eAAe,GAAG,CAAC;AACnD,UAAM,mBAAmB,UAAU,QAAQ,uBAAuB;AAClE,UAAM,cAAc,aAAa,QAAQ,GAAG,gBAAgB;AAC5D,UAAM,cAAc,aAAa,QAAQ,IAAI,gBAAgB;AAE7D,kBAAc,OAAO;AAAA,MACjB,OAAO;AAAA,MACP,GAAG,IAAI,YAAY,CAAC,IAAI,YAAY,CAAC,KAAK,OAAO,CAAC,IAAI,OAAO,CAAC,KAAK,YAAY,CAAC,IAC5E,YAAY,CAChB;AAAA,IACJ,CAAC;AAAA,EACL;AAEA,MAAI,OAAO,eAAe,QAAQ,WAAW,MAAM,IAAI;AACnD,UAAM,wBAAwB,eAAe,GAAG,CAAC,CAAC;AAClD,UAAM,iBAAiB,UAAU,QAAQ,qBAAqB;AAC9D,UAAM,YAAY,aAAa,QAAQ,GAAG,cAAc;AACxD,UAAM,YAAY,aAAa,QAAQ,IAAI,cAAc;AAEzD,kBAAc,KAAK;AAAA,MACf,OAAO;AAAA,MACP,GAAG,IAAI,UAAU,CAAC,IAAI,UAAU,CAAC,KAAK,OAAO,CAAC,IAAI,OAAO,CAAC,KAAK,UAAU,CAAC,IACtE,UAAU,CACd;AAAA,IACJ,CAAC;AAAA,EACL;AACJ;AAEA,IAAM,aAAa,CAAC,SAAS,QAAQ,MAAM,UAAU;AACjD,gBAAc,SAAS;AAAA,IACnB,GAAG,QAAQ;AAAA,IACX,MAAM;AAAA,IACN,GAAG;AAAA,MACC,OAAO,OAAO,IAAI,YAAU;AAAA,QACxB,GAAG,eAAe,MAAM,GAAG,MAAM,OAAO,OAAO;AAAA,QAC/C,GAAG,eAAe,MAAM,GAAG,MAAM,OAAO,QAAQ;AAAA,MACpD,EAAE;AAAA,IACN;AAAA,EACJ,CAAC;AACL;AAEA,IAAM,cAAc,UAAQ,YAAU,IAAI,MAAM,EAAE,IAAI,OAAO,GAAG,CAAC;AAEjE,IAAM,cAAc,YAAU;AAC1B,QAAM,QAAQ,IAAI,SAAS;AAAA,IACvB,IAAI,OAAO;AAAA,IACX,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,SAAS;AAAA,EACb,CAAC;AACD,QAAM,SAAS,MAAM;AACjB,UAAM,aAAa,WAAW,OAAO,WAAW,CAAC;AAAA,EACrD;AACA,QAAM,eAAe,gCAAgC,cAAc,OAAO,GAAG;AAC7E,SAAO;AACX;AAEA,IAAM,aAAa,YAAU;AACzB,QAAM,QAAQ,IAAI,KAAK;AAAA,IACnB,IAAI,OAAO;AAAA,IACX,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,EACvB,CAAC;AAED,QAAM,OAAO,IAAI,MAAM;AACvB,QAAM,YAAY,IAAI;AAEtB,QAAM,QAAQ,IAAI,MAAM;AACxB,QAAM,YAAY,KAAK;AAEvB,QAAM,MAAM,IAAI,MAAM;AACtB,QAAM,YAAY,GAAG;AAErB,SAAO;AACX;AAEA,IAAM,qBAAqB;AAAA,EACvB,OAAO;AAAA,EACP,MAAM,YAAY,MAAM;AAAA,EACxB,SAAS,YAAY,SAAS;AAAA,EAC9B,MAAM,YAAY,MAAM;AAAA,EACxB,MAAM,YAAY,MAAM;AAAA,EACxB,MAAM;AACV;AAEA,IAAM,qBAAqB;AAAA,EACvB,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AACV;AAEA,IAAM,qBAAqB,CAAC,MAAM,WAAW,mBAAmB,IAAI,EAAE,MAAM;AAE5E,IAAM,qBAAqB,CAAC,SAAS,MAAM,QAAQ,MAAM,UAAU;AAC/D,MAAI,SAAS,QAAQ;AACjB,YAAQ,OAAO,cAAc,QAAQ,MAAM,KAAK;AAAA,EACpD;AACA,UAAQ,SAAS,gBAAgB,QAAQ,MAAM,KAAK;AACpD,qBAAmB,IAAI,EAAE,SAAS,QAAQ,MAAM,KAAK;AACzD;AAEA,IAAM,qBAAqB,CAAC,GAAG,MAAM;AACjC,MAAI,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,QAAQ;AAC3B,WAAO;AAAA,EACX;AACA,MAAI,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,QAAQ;AAC3B,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAEA,IAAM,UAAU,CAAC,MAAM,OAAO,CAAC,GAAG,QAAQ,YACtC,IAAI,QAAQ,aAAW;AACnB,QAAM,EAAE,aAAa,KAAK,IAAI;AAG9B,QAAM,KAAK,IAAI,WAAW;AAC1B,KAAG,YAAY,MAAM;AAEjB,UAAM,OAAO,GAAG;AAGhB,UAAM,WAAW,SAAS,cAAc,KAAK;AAC7C,aAAS,MAAM,UAAU;AACzB,aAAS,YAAY;AACrB,UAAM,eAAe,SAAS,cAAc,KAAK;AACjD,aAAS,KAAK,YAAY,QAAQ;AAGlC,UAAM,OAAO,aAAa,QAAQ;AAClC,aAAS,WAAW,YAAY,QAAQ;AAGxC,UAAM,YAAY,SAAS,cAAc,OAAO;AAGhD,UAAM,mBAAmB,aAAa,aAAa,SAAS,KAAK;AACjE,UAAM,iBAAiB,aAAa,aAAa,OAAO,KAAK;AAC7D,UAAM,kBAAkB,aAAa,aAAa,QAAQ,KAAK;AAC/D,QAAI,QAAQ,WAAW,cAAc,KAAK;AAC1C,QAAI,SAAS,WAAW,eAAe,KAAK;AAC5C,UAAM,cAAc,eAAe,MAAM,QAAQ,KAAK,CAAC,GAAG,CAAC,KAAK;AAChE,UAAM,eAAe,gBAAgB,MAAM,QAAQ,KAAK,CAAC,GAAG,CAAC,KAAK;AAGlE,UAAM,cAAc,iBAAiB,MAAM,GAAG,EAAE,IAAI,UAAU;AAC9D,UAAM,UAAU,YAAY,SACtB;AAAA,MACI,GAAG,YAAY,CAAC;AAAA,MAChB,GAAG,YAAY,CAAC;AAAA,MAChB,OAAO,YAAY,CAAC;AAAA,MACpB,QAAQ,YAAY,CAAC;AAAA,IACzB,IACA;AAEN,QAAI,aAAa,SAAS,OAAO,QAAQ,QAAQ;AACjD,QAAI,cAAc,UAAU,OAAO,SAAS,QAAQ;AAEpD,iBAAa,MAAM,WAAW;AAC9B,iBAAa,aAAa,SAAS,UAAU;AAC7C,iBAAa,aAAa,UAAU,WAAW;AAG/C,QAAI,YAAY;AAChB,QAAI,UAAU,OAAO,QAAQ;AACzB,YAAM,OAAO;AAAA,QACT,OAAO;AAAA,QACP,QAAQ;AAAA,MACZ;AACA,kBAAY,OAAO,KAAK,kBAAkB,EAAE,OAAO,CAAC,MAAM,UAAU;AAChE,cAAM,KAAK,mBAAmB,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAChD,2BAAmB,IAAI,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AAC/C,WAAG,gBAAgB,IAAI;AACvB,YAAI,GAAG,aAAa,SAAS,MAAM,GAAG;AAClC,aAAG,gBAAgB,SAAS;AAAA,QAChC;AACA,eAAO,OAAO,OAAO,GAAG,YAAY;AAAA,MACxC,GAAG,EAAE;AACL,kBAAY;AAAA;AAAA,KAAU,UAAU,QAAQ,WAAW,GAAG,CAAC;AAAA;AAAA;AAAA,IAC3D;AAEA,UAAM,cAAc,KAAK,eAAe,cAAc;AAEtD,UAAM,cAAc;AACpB,UAAM,eAAe,cAAc;AAEnC,UAAM,cAAc,OAAO,KAAK,eAAe,eAAe,KAAK;AAEnE,UAAM,cAAc,KAAK,SAAS,KAAK,OAAO,IAAI;AAClD,UAAM,cAAc,KAAK,SAAS,KAAK,OAAO,IAAI;AAElD,UAAM,mBAAmB;AAAA,MACrB;AAAA,QACI,OAAO;AAAA,QACP,QAAQ;AAAA,MACZ;AAAA,MACA;AAAA,QACI;AAAA,UACI,OAAO;AAAA,UACP,QAAQ;AAAA,QACZ;AAAA,QACA;AAAA,MACJ;AAAA,MACA,KAAK;AAAA,MACL,cACM,EAAE,GAAG,aAAa,GAAG,YAAY,IACjC;AAAA,QACI,GAAG;AAAA,QACH,GAAG;AAAA,MACP;AAAA,IACV;AAEA,UAAM,QAAQ,KAAK,OAAO;AAE1B,UAAM,WAAW,KAAK,YAAY,MAAM,KAAK;AAE7C,UAAM,eAAe;AAAA,MACjB,GAAG,cAAc;AAAA,MACjB,GAAG,eAAe;AAAA,IACtB;AAEA,UAAM,cAAc;AAAA,MAChB,GAAG,aAAa,IAAI,aAAa;AAAA,MACjC,GAAG,aAAa,IAAI,cAAc;AAAA,IACtC;AAEA,UAAM,iBAAiB;AAAA;AAAA,MAEnB,UAAU,QAAQ,IAAI,aAAa,CAAC,IAAI,aAAa,CAAC;AAAA;AAAA,MAGtD,aAAa,aAAa,CAAC,IAAI,aAAa,CAAC;AAAA,MAC7C,SAAS,KAAK;AAAA,MACd,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC;AAAA;AAAA,MAG/C,aAAa,YAAY,CAAC,IAAI,YAAY,CAAC;AAAA,IAC/C;AAEA,UAAM,qBAAqB,KAAK,QAAQ,KAAK,KAAK;AAClD,UAAM,mBAAmB,KAAK,QAAQ,KAAK,KAAK;AAEhD,UAAM,iBAAiB;AAAA,MACnB,SAAS,qBAAqB,KAAK,CAAC,IAAI,mBAAmB,KAAK,CAAC;AAAA,MACjE,aAAa,qBAAqB,CAAC,aAAa,CAAC,IAC7C,mBAAmB,CAAC,cAAc,CACtC;AAAA,IACJ;AAGA,UAAM,cAAc;AAAA,cAClB,WAAW,GAAG,UAAU,aAAa,YAAY,GAAG,WAAW;AAAA,eAC9D,WAAW,IAAI,YAAY,KAC1B,aAAa,uBAAuB,aAAa,OAAO,EAC5D;AAAA;AAAA;AAAA;AAAA;AAAA,SAKH,YAAY,UAAU,cAAc,EAAE;AAAA,gBAC/B,eAAe,KAAK,GAAG,CAAC;AAAA,gBACxB,eAAe,KAAK,GAAG,CAAC;AAAA,EACtC,aAAa,SAAS,GAAG,SAAS;AAAA;AAAA;AAAA;AAMxB,YAAQ,WAAW;AAAA,EACvB;AAEA,KAAG,WAAW,IAAI;AACtB,CAAC;AAEL,IAAM,oBAAoB,SAAO;AAC7B,MAAI;AACJ,MAAI;AACA,gBAAY,IAAI,UAAU,IAAI,OAAO,IAAI,MAAM;AAAA,EACnD,SAAS,GAAG;AAER,UAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,gBAAY,OAAO,WAAW,IAAI,EAAE,gBAAgB,IAAI,OAAO,IAAI,MAAM;AAAA,EAC7E;AACA,YAAU,KAAK,IAAI,IAAI,IAAI;AAC3B,SAAO;AACX;AAGA,IAAM,kBAAkB,MAAM;AAE1B,QAAM,aAAa,EAAE,QAAQ,OAAO;AAGpC,QAAM,kBAAkB,CAAC,YAAY,cAAc;AAC/C,eAAW,QAAQ,CAAAC,eAAa;AAC5B,kBAAY,WAAWA,WAAU,IAAI,EAAE,WAAWA,WAAU,IAAI;AAAA,IACpE,CAAC;AACD,WAAO;AAAA,EACX;AAGA,QAAM,YAAY,CAAC,MAAM,OAAO;AAC5B,QAAI,aAAa,KAAK;AAGtB,QAAI,kBAAkB;AACtB,eAAW,QAAQ,CAAAA,eAAa;AAC5B,UAAIA,WAAU,SAAS,UAAU;AAC7B,0BAAkBA;AAAA,MACtB;AAAA,IACJ,CAAC;AACD,QAAI,iBAAiB;AAEjB,UAAI,kBAAkB;AACtB,iBAAW,QAAQ,CAAAA,eAAa;AAC5B,YAAIA,WAAU,SAAS,UAAU;AAC7B,4BAAkBA;AAAA,QACtB;AAAA,MACJ,CAAC;AAED,UAAI,iBAAiB;AAEjB,wBAAgB,KAAK,SAAS,gBAAgB;AAG9C,qBAAa,WAAW,OAAO,CAAAA,eAAaA,WAAU,SAAS,QAAQ;AAAA,MAC3E;AAAA,IACJ;AAEA,OAAG,gBAAgB,YAAY,KAAK,SAAS,CAAC;AAAA,EAClD;AAGA,OAAK,YAAY,OAAK;AAClB,cAAU,EAAE,KAAK,SAAS,cAAY;AAElC,WAAK,YAAY,EAAE,IAAI,EAAE,KAAK,IAAI,SAAS,SAAS,GAAG,CAAC,SAAS,KAAK,MAAM,CAAC;AAAA,IACjF,CAAC;AAAA,EACL;AAEA,QAAM,KAAK;AACX,QAAM,KAAK;AACX,QAAM,KAAK;AACX,WAAS,kBAAkB,OAAO,MAAM,GAAG;AACvC,UAAM,KAAK,KAAK,KAAK,IAAI;AACzB,UAAM,KAAK,KAAK,QAAQ,CAAC,IAAI;AAC7B,UAAM,KAAK,KAAK,QAAQ,CAAC,IAAI;AAC7B,UAAM,KAAK,KAAK,QAAQ,CAAC,IAAI;AAE7B,UAAM,KAAK,KAAK,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;AAC9D,UAAM,KAAK,KAAK,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;AAC9D,UAAM,KAAK,KAAK,EAAE,EAAE,IAAI,KAAK,EAAE,EAAE,IAAI,KAAK,EAAE,EAAE,IAAI,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE;AACnE,UAAM,KAAK,KAAK,EAAE,EAAE,IAAI,KAAK,EAAE,EAAE,IAAI,KAAK,EAAE,EAAE,IAAI,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE;AAEnE,UAAM,KAAK,KAAK,IAAI,GAAG,KAAK,EAAE,IAAI,MAAM,IAAM;AAC9C,UAAM,KAAK,KAAK,IAAI,GAAG,KAAK,EAAE,IAAI,MAAM,IAAM;AAC9C,UAAM,KAAK,KAAK,IAAI,GAAG,KAAK,EAAE,IAAI,MAAM,IAAM;AAE9C,SAAK,KAAK,IAAI,KAAK,IAAI,GAAK,KAAK,IAAI,GAAK,EAAE,CAAC,IAAI;AACjD,SAAK,QAAQ,CAAC,IAAI,KAAK,IAAI,GAAK,KAAK,IAAI,GAAK,EAAE,CAAC,IAAI;AACrD,SAAK,QAAQ,CAAC,IAAI,KAAK,IAAI,GAAK,KAAK,IAAI,GAAK,EAAE,CAAC,IAAI;AAAA,EACzD;AAEA,QAAM,iBAAiB,KAAK,KAAK,UAAU;AAAA,IACvC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACD,WAAS,iBAAiBC,SAAQ;AAC9B,WAAO,KAAK,KAAK,UAAUA,WAAU,CAAC,CAAC,MAAM;AAAA,EACjD;AAEA,WAAS,OAAO,WAAW,QAAQ;AAC/B,QAAI,CAAC,UAAU,iBAAiB,MAAM,EAAG,QAAO;AAEhD,UAAM,OAAO,UAAU;AACvB,UAAM,IAAI,KAAK;AAEf,UAAM,MAAM,OAAO,CAAC;AACpB,UAAM,MAAM,OAAO,CAAC;AACpB,UAAM,MAAM,OAAO,CAAC;AACpB,UAAM,MAAM,OAAO,CAAC;AACpB,UAAM,MAAM,OAAO,CAAC;AAEpB,UAAM,MAAM,OAAO,CAAC;AACpB,UAAM,MAAM,OAAO,CAAC;AACpB,UAAM,MAAM,OAAO,CAAC;AACpB,UAAM,MAAM,OAAO,CAAC;AACpB,UAAM,MAAM,OAAO,CAAC;AAEpB,UAAM,MAAM,OAAO,EAAE;AACrB,UAAM,MAAM,OAAO,EAAE;AACrB,UAAM,MAAM,OAAO,EAAE;AACrB,UAAM,MAAM,OAAO,EAAE;AACrB,UAAM,MAAM,OAAO,EAAE;AAErB,UAAM,MAAM,OAAO,EAAE;AACrB,UAAM,MAAM,OAAO,EAAE;AACrB,UAAM,MAAM,OAAO,EAAE;AACrB,UAAM,MAAM,OAAO,EAAE;AACrB,UAAM,MAAM,OAAO,EAAE;AAErB,QAAI,QAAQ,GACR,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,KAAK,GACL,KAAK,GACL,KAAK,GACL,KAAK,GACL,KAAK,GACL,KAAK,GACL,KAAK;AAET,WAAO,QAAQ,GAAG,SAAS,GAAG;AAC1B,UAAI,KAAK,KAAK,IAAI;AAClB,UAAI,KAAK,QAAQ,CAAC,IAAI;AACtB,UAAI,KAAK,QAAQ,CAAC,IAAI;AACtB,UAAI,KAAK,QAAQ,CAAC,IAAI;AAEtB,WAAK,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM;AAC7C,WAAK,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM;AAC7C,WAAK,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM;AAC7C,WAAK,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM;AAE7C,WAAK,KAAK,IAAI,GAAG,KAAK,EAAE,IAAI,MAAM,IAAM;AACxC,WAAK,KAAK,IAAI,GAAG,KAAK,EAAE,IAAI,MAAM,IAAM;AACxC,WAAK,KAAK,IAAI,GAAG,KAAK,EAAE,IAAI,MAAM,IAAM;AAExC,WAAK,KAAK,IAAI,KAAK,IAAI,GAAK,KAAK,IAAI,GAAK,EAAE,CAAC,IAAI;AACjD,WAAK,QAAQ,CAAC,IAAI,KAAK,IAAI,GAAK,KAAK,IAAI,GAAK,EAAE,CAAC,IAAI;AACrD,WAAK,QAAQ,CAAC,IAAI,KAAK,IAAI,GAAK,KAAK,IAAI,GAAK,EAAE,CAAC,IAAI;AAAA,IAEzD;AAEA,WAAO;AAAA,EACX;AAEA,WAAS,OAAO,WAAW,MAAM;AAC7B,QAAI,EAAE,OAAO,WAAW,UAAU,OAAO,OAAO,QAAQ,OAAO,IAAI;AAGnE,aAAS,CAAC,UAAU,iBAAiB,MAAM,IAAI,OAAO;AAItD,QAAI,CAAC,SAAS,CAAC,QAAQ;AACnB,aAAO,OAAO,WAAW,MAAM;AAAA,IACnC;AAGA,QAAI,UAAU,MAAM;AAChB,cAAQ;AAAA,IACZ,WAAW,WAAW,MAAM;AACxB,eAAS;AAAA,IACb;AAEA,QAAI,SAAS,SAAS;AAClB,UAAI,cAAc,QAAQ,UAAU;AACpC,UAAI,eAAe,SAAS,UAAU;AACtC,UAAI,SAAS;AAEb,UAAI,SAAS,SAAS;AAClB,iBAAS,KAAK,IAAI,aAAa,YAAY;AAAA,MAC/C,WAAW,SAAS,WAAW;AAC3B,iBAAS,KAAK,IAAI,aAAa,YAAY;AAAA,MAC/C;AAGA,UAAI,SAAS,KAAK,YAAY,OAAO;AACjC,eAAO,OAAO,WAAW,MAAM;AAAA,MACnC;AAEA,cAAQ,UAAU,QAAQ;AAC1B,eAAS,UAAU,SAAS;AAAA,IAChC;AAEA,UAAM,cAAc,UAAU;AAC9B,UAAM,eAAe,UAAU;AAC/B,UAAM,cAAc,KAAK,MAAM,KAAK;AACpC,UAAM,eAAe,KAAK,MAAM,MAAM;AACtC,UAAM,YAAY,UAAU;AAC5B,UAAM,aAAa,IAAI,kBAAkB,cAAc,eAAe,CAAC;AACvE,UAAM,aAAa,cAAc;AACjC,UAAM,cAAc,eAAe;AACnC,UAAM,iBAAiB,KAAK,KAAK,aAAa,GAAG;AACjD,UAAM,kBAAkB,KAAK,KAAK,cAAc,GAAG;AAEnD,aAAS,IAAI,GAAG,IAAI,cAAc,KAAK;AACnC,eAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AAClC,YAAI,MAAM,IAAI,IAAI,eAAe;AACjC,YAAI,SAAS;AACb,YAAI,UAAU;AACd,YAAI,eAAe;AACnB,YAAI,IAAI;AACR,YAAI,IAAI;AACR,YAAI,IAAI;AACR,YAAI,IAAI;AACR,YAAI,WAAW,IAAI,OAAO;AAE1B,iBAAS,KAAK,KAAK,MAAM,IAAI,WAAW,GAAG,MAAM,IAAI,KAAK,aAAa,MAAM;AACzE,cAAI,KAAK,KAAK,IAAI,WAAW,KAAK,IAAI,IAAI;AAC1C,cAAI,WAAW,IAAI,OAAO;AAC1B,cAAI,KAAK,KAAK;AAEd,mBAAS,KAAK,KAAK,MAAM,IAAI,UAAU,GAAG,MAAM,IAAI,KAAK,YAAY,MAAM;AACvE,gBAAI,KAAK,KAAK,IAAI,WAAW,KAAK,IAAI,IAAI;AAC1C,gBAAI,IAAI,KAAK,KAAK,KAAK,KAAK,EAAE;AAE9B,gBAAI,KAAK,MAAM,KAAK,GAAG;AACnB,uBAAS,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAErC,kBAAI,SAAS,GAAG;AACZ,qBAAK,KAAK,KAAK,KAAK;AAEpB,oBAAI,MAAM,UAAU,KAAK,CAAC;AAC1B,qBAAK,SAAS;AACd,gCAAgB;AAEhB,oBAAI,MAAM,KAAK;AACX,2BAAU,SAAS,MAAO;AAAA,gBAC9B;AAEA,qBAAK,SAAS,UAAU,EAAE;AAC1B,qBAAK,SAAS,UAAU,KAAK,CAAC;AAC9B,qBAAK,SAAS,UAAU,KAAK,CAAC;AAC9B,2BAAW;AAAA,cACf;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAEA,mBAAW,EAAE,IAAI,IAAI;AACrB,mBAAW,KAAK,CAAC,IAAI,IAAI;AACzB,mBAAW,KAAK,CAAC,IAAI,IAAI;AACzB,mBAAW,KAAK,CAAC,IAAI,IAAI;AAEzB,kBAAU,kBAAkB,IAAI,YAAY,MAAM;AAAA,MACtD;AAAA,IACJ;AAEA,WAAO;AAAA,MACH,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,IACZ;AAAA,EACJ;AACJ;AAGA,IAAM,qBAAqB,CAAC,MAAM,WAAW;AAEzC,MAAI,KAAK,UAAU,SAAS,GAAG,KAAK,MAAM,WAAY;AAGtD,YAAU;AAIV,QAAM,mBAAmB,KAAK,UAAW,UAAU,GAAI,KAAK,MAAM;AAClE,YAAU,KAAK,UAAU,SAAS,GAAG,gBAAgB;AAErD,QAAM,OAAO,KAAK,UAAU,QAAQ,gBAAgB;AACpD,YAAU;AAGV,WAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC3B,QAAI,KAAK,UAAU,SAAS,IAAI,IAAI,gBAAgB,MAAM,KAAQ;AAC9D,WAAK,UAAU,SAAS,IAAI,KAAK,GAAG,GAAG,gBAAgB;AACvD,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AAEA,IAAM,WAAW,UAAQ;AACrB,QAAM,OAAO,IAAI,SAAS,IAAI;AAI9B,MAAI,KAAK,UAAU,CAAC,MAAM,MAAQ,QAAO;AAEzC,MAAI,SAAS;AACb,MAAI;AACJ,MAAI;AACJ,MAAI,uBAAuB;AAE3B,SAAO,SAAS,KAAK,YAAY;AAC7B,aAAS,KAAK,UAAU,QAAQ,KAAK;AACrC,mBAAe,KAAK,UAAU,SAAS,GAAG,KAAK,IAAI;AAGnD,UAAM,SAAU,UAAU,SAAU,UAAU,SAAW,WAAW;AACpE,QAAI,CAAC,QAAQ;AACT;AAAA,IACJ;AAEA,QAAI,CAAC,sBAAsB;AACvB,6BAAuB,mBAAmB,MAAM,QAAQ,YAAY;AAAA,IACxE;AAEA,QAAI,SAAS,eAAe,KAAK,YAAY;AACzC;AAAA,IACJ;AAEA,cAAU;AAAA,EACd;AACA,SAAO,KAAK,MAAM,GAAG,MAAM;AAC/B;AAEA,IAAM,eAAe,UACjB,IAAI,QAAQ,aAAW;AACnB,QAAM,SAAS,IAAI,WAAW;AAC9B,SAAO,SAAS,MAAM,QAAQ,SAAS,OAAO,MAAM,KAAK,IAAI;AAC7D,SAAO,kBAAkB,KAAK,MAAM,GAAG,MAAM,IAAI,CAAC;AACtD,CAAC;AAEL,IAAM,iBAAiB,MAAM;AACzB,SAAQ,OAAO,cACX,OAAO,eACP,OAAO,qBACP,OAAO,kBACP,OAAO;AACf;AAEA,IAAM,aAAa,CAAC,aAAa,aAAa;AAC1C,QAAM,KAAK,eAAe;AAE1B,MAAI,IAAI;AACJ,UAAM,KAAK,IAAI,GAAG;AAClB,OAAG,OAAO,WAAW;AACrB,WAAO,GAAG,QAAQ,QAAQ;AAAA,EAC9B;AAEA,SAAO,IAAI,KAAK,CAAC,WAAW,GAAG;AAAA,IAC3B,MAAM;AAAA,EACV,CAAC;AACL;AAEA,IAAM,cAAc,MAChB,KAAK,OAAO,EACP,SAAS,EAAE,EACX,OAAO,GAAG,CAAC;AAEpB,IAAM,eAAe,QAAM;AACvB,QAAM,aAAa,IAAI,KAAK,CAAC,KAAK,GAAG,SAAS,GAAG,KAAK,GAAG,EAAE,MAAM,yBAAyB,CAAC;AAC3F,QAAM,YAAY,IAAI,gBAAgB,UAAU;AAChD,QAAM,SAAS,IAAI,OAAO,SAAS;AAEnC,QAAM,QAAQ,CAAC;AAEf,SAAO;AAAA,IACH,UAAU,MAAM;AAAA,IAAC;AAAA;AAAA,IACjB,MAAM,CAAC,SAAS,IAAI,iBAAiB;AACjC,YAAM,KAAK,YAAY;AACvB,YAAM,EAAE,IAAI;AAEZ,aAAO,YAAY,OAAK;AACpB,cAAMC,MAAK,MAAM,EAAE,KAAK,EAAE;AAC1B,YAAI,CAACA,IAAI;AACT,QAAAA,IAAG,EAAE,KAAK,OAAO;AACjB,eAAO,MAAM,EAAE,KAAK,EAAE;AAAA,MAC1B;AAEA,aAAO;AAAA,QACH;AAAA,UACI;AAAA,UACA;AAAA,QACJ;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,WAAW,MAAM;AACb,aAAO,UAAU;AACjB,UAAI,gBAAgB,SAAS;AAAA,IACjC;AAAA,EACJ;AACJ;AAEA,IAAM,YAAY,SACd,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC7B,QAAM,MAAM,IAAI,MAAM;AACtB,MAAI,SAAS,MAAM;AACf,YAAQ,GAAG;AAAA,EACf;AACA,MAAI,UAAU,OAAK;AACf,WAAO,CAAC;AAAA,EACZ;AACA,MAAI,MAAM;AACd,CAAC;AAEL,IAAM,QAAQ,WACV,MAAM;AAAA,EACF,CAAC,SAAS,SAAS,QAAQ,KAAK,YAAU,KAAK,EAAE,KAAK,MAAM,UAAU,OAAO,KAAK,MAAM,CAAC,CAAC;AAAA,EAC1F,QAAQ,QAAQ,CAAC,CAAC;AACtB;AAEJ,IAAM,oBAAoB,CAAC,QAAQ,WAC/B,IAAI,QAAQ,aAAW;AACnB,QAAM,OAAO;AAAA,IACT,OAAO,OAAO;AAAA,IACd,QAAQ,OAAO;AAAA,EACnB;AAEA,QAAM,MAAM,OAAO,WAAW,IAAI;AAElC,QAAM,UAAU,OAAO,KAAK,kBAAkB,EAAE;AAAA,IAAI,UAAQ,MACxD,IAAI,QAAQ,CAAAC,aAAW;AACnB,YAAM,SAAS,iBAAiB,KAAK,CAAC,CAAC,EAAE,KAAK,MAAM,KAAK,CAAC,GAAGA,QAAO;AACpE,UAAI,OAAQ,CAAAA,SAAQ;AAAA,IACxB,CAAC;AAAA,EACL;AAEA,QAAM,OAAO,EAAE,KAAK,MAAM,QAAQ,MAAM,CAAC;AAC7C,CAAC;AAEL,IAAM,oBAAoB,CAAC,KAAK,WAAW;AACvC,MAAI,UAAU;AACd,MAAI,UAAU,OAAO,gBAAgB;AACrC,MAAI,WAAW,OAAO,iBAAiB;AACvC,MAAI,YAAY,OAAO,cAAc;AACrC,MAAI,OAAO,kBAAkB,EAAE,QAAQ;AACnC,QAAI,YAAY,OAAO,kBAAkB,EAAE,MAAM,GAAG,CAAC;AAAA,EACzD;AACA,MAAI,YAAY,OAAO,MAAM;AAC7B,MAAI,cAAc,OAAO,QAAQ;AACjC,MAAI,cAAc,OAAO,WAAW;AACxC;AAEA,IAAM,mBAAmB,SAAO;AAC5B,MAAI,KAAK;AACT,MAAI,OAAO;AACX,MAAI,cAAc;AACtB;AAEA,IAAM,WAAW,CAAC,KAAK,MAAM,WAAW;AACpC,QAAM,OAAO,cAAc,QAAQ,IAAI;AACvC,QAAM,SAAS,gBAAgB,QAAQ,IAAI;AAC3C,oBAAkB,KAAK,MAAM;AAC7B,MAAI,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,OAAO,KAAK,MAAM;AAChD,mBAAiB,KAAK,MAAM;AAC5B,SAAO;AACX;AAEA,IAAM,cAAc,CAAC,KAAK,MAAM,WAAW;AACvC,QAAM,OAAO,cAAc,QAAQ,IAAI;AACvC,QAAM,SAAS,gBAAgB,QAAQ,IAAI;AAC3C,oBAAkB,KAAK,MAAM;AAE7B,QAAM,IAAI,KAAK,GACX,IAAI,KAAK,GACT,IAAI,KAAK,OACT,IAAI,KAAK,QACT,QAAQ,WACR,KAAM,IAAI,IAAK,OACf,KAAM,IAAI,IAAK,OACf,KAAK,IAAI,GACT,KAAK,IAAI,GACT,KAAK,IAAI,IAAI,GACb,KAAK,IAAI,IAAI;AAEjB,MAAI,OAAO,GAAG,EAAE;AAChB,MAAI,cAAc,GAAG,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC;AAC/C,MAAI,cAAc,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI,EAAE;AACjD,MAAI,cAAc,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,EAAE;AAClD,MAAI,cAAc,KAAK,IAAI,IAAI,GAAG,KAAK,IAAI,GAAG,EAAE;AAEhD,mBAAiB,KAAK,MAAM;AAC5B,SAAO;AACX;AAEA,IAAM,YAAY,CAAC,KAAK,MAAM,QAAQ,SAAS;AAC3C,QAAM,OAAO,cAAc,QAAQ,IAAI;AACvC,QAAM,SAAS,gBAAgB,QAAQ,IAAI;AAC3C,oBAAkB,KAAK,MAAM;AAE7B,QAAM,QAAQ,IAAI,MAAM;AAGxB,QAAM,qBACF,IAAI,IAAI,OAAO,KAAK,OAAO,SAAS,IAAI,EAAE,WAAW,OAAO,SAAS;AACzE,MAAI,mBAAoB,OAAM,cAAc;AAE5C,QAAM,SAAS,MAAM;AACjB,QAAI,OAAO,QAAQ,SAAS;AACxB,YAAM,KAAK,KAAK,QAAQ,KAAK;AAC7B,YAAM,QAAQ,KAAK,IAAI,MAAM,QAAQ,MAAM,SAAS;AACpD,YAAM,SAAS,KAAK,IAAI,MAAM,QAAQ,KAAK,MAAM;AACjD,YAAM,IAAI,MAAM,QAAQ,MAAM,QAAQ;AACtC,YAAM,IAAI,MAAM,SAAS,MAAM,SAAS;AACxC,UAAI,UAAU,OAAO,GAAG,GAAG,OAAO,QAAQ,KAAK,GAAG,KAAK,GAAG,KAAK,OAAO,KAAK,MAAM;AAAA,IACrF,WAAW,OAAO,QAAQ,WAAW;AACjC,YAAM,SAAS,KAAK,IAAI,KAAK,QAAQ,MAAM,OAAO,KAAK,SAAS,MAAM,MAAM;AAC5E,YAAM,QAAQ,SAAS,MAAM;AAC7B,YAAM,SAAS,SAAS,MAAM;AAC9B,YAAM,IAAI,KAAK,IAAI,KAAK,QAAQ,MAAM,QAAQ;AAC9C,YAAM,IAAI,KAAK,IAAI,KAAK,SAAS,MAAM,SAAS;AAChD,UAAI,UAAU,OAAO,GAAG,GAAG,MAAM,OAAO,MAAM,QAAQ,GAAG,GAAG,OAAO,MAAM;AAAA,IAC7E,OAAO;AACH,UAAI;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,MAAM;AAAA,QACN,MAAM;AAAA,QACN,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACT;AAAA,IACJ;AAEA,qBAAiB,KAAK,MAAM;AAC5B,SAAK;AAAA,EACT;AACA,QAAM,MAAM,OAAO;AACvB;AAEA,IAAM,WAAW,CAAC,KAAK,MAAM,WAAW;AACpC,QAAM,OAAO,cAAc,QAAQ,IAAI;AACvC,QAAM,SAAS,gBAAgB,QAAQ,IAAI;AAC3C,oBAAkB,KAAK,MAAM;AAE7B,QAAM,WAAW,eAAe,OAAO,UAAU,IAAI;AACrD,QAAM,aAAa,OAAO,cAAc;AACxC,QAAM,aAAa,OAAO,cAAc;AACxC,QAAM,YAAY,OAAO,aAAa;AAEtC,MAAI,OAAO,GAAG,UAAU,IAAI,QAAQ,MAAM,UAAU;AACpD,MAAI,YAAY;AAChB,MAAI,SAAS,OAAO,MAAM,KAAK,GAAG,KAAK,CAAC;AAExC,mBAAiB,KAAK,MAAM;AAC5B,SAAO;AACX;AAEA,IAAM,WAAW,CAAC,KAAK,MAAM,WAAW;AACpC,QAAM,SAAS,gBAAgB,QAAQ,IAAI;AAC3C,oBAAkB,KAAK,MAAM;AAC7B,MAAI,UAAU;AAEd,QAAM,SAAS,OAAO,OAAO,IAAI,YAAU;AAAA,IACvC,GAAG,eAAe,MAAM,GAAG,MAAM,GAAG,OAAO;AAAA,IAC3C,GAAG,eAAe,MAAM,GAAG,MAAM,GAAG,QAAQ;AAAA,EAChD,EAAE;AAEF,MAAI,OAAO,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC;AACnC,QAAM,IAAI,OAAO;AACjB,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,QAAI,OAAO,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC;AAAA,EACvC;AAEA,mBAAiB,KAAK,MAAM;AAC5B,SAAO;AACX;AAEA,IAAM,WAAW,CAAC,KAAK,MAAM,WAAW;AACpC,QAAM,OAAO,cAAc,QAAQ,IAAI;AACvC,QAAM,SAAS,gBAAgB,QAAQ,IAAI;AAC3C,oBAAkB,KAAK,MAAM;AAE7B,MAAI,UAAU;AAEd,QAAM,SAAS;AAAA,IACX,GAAG,KAAK;AAAA,IACR,GAAG,KAAK;AAAA,EACZ;AAEA,QAAM,SAAS;AAAA,IACX,GAAG,KAAK,IAAI,KAAK;AAAA,IACjB,GAAG,KAAK,IAAI,KAAK;AAAA,EACrB;AAEA,MAAI,OAAO,OAAO,GAAG,OAAO,CAAC;AAC7B,MAAI,OAAO,OAAO,GAAG,OAAO,CAAC;AAE7B,QAAM,IAAI,gBAAgB;AAAA,IACtB,GAAG,OAAO,IAAI,OAAO;AAAA,IACrB,GAAG,OAAO,IAAI,OAAO;AAAA,EACzB,CAAC;AAED,QAAM,IAAI,OAAO,KAAK,IAAI,KAAK,OAAO,KAAK,MAAM;AAEjD,MAAI,OAAO,eAAe,QAAQ,aAAa,MAAM,IAAI;AACrD,UAAM,0BAA0B,eAAe,GAAG,CAAC;AACnD,UAAM,mBAAmB,UAAU,QAAQ,uBAAuB;AAClE,UAAM,cAAc,aAAa,QAAQ,GAAG,gBAAgB;AAC5D,UAAM,cAAc,aAAa,QAAQ,IAAI,gBAAgB;AAE7D,QAAI,OAAO,YAAY,GAAG,YAAY,CAAC;AACvC,QAAI,OAAO,OAAO,GAAG,OAAO,CAAC;AAC7B,QAAI,OAAO,YAAY,GAAG,YAAY,CAAC;AAAA,EAC3C;AACA,MAAI,OAAO,eAAe,QAAQ,WAAW,MAAM,IAAI;AACnD,UAAM,wBAAwB,eAAe,GAAG,CAAC,CAAC;AAClD,UAAM,iBAAiB,UAAU,QAAQ,qBAAqB;AAC9D,UAAM,YAAY,aAAa,QAAQ,GAAG,cAAc;AACxD,UAAM,YAAY,aAAa,QAAQ,IAAI,cAAc;AAEzD,QAAI,OAAO,UAAU,GAAG,UAAU,CAAC;AACnC,QAAI,OAAO,OAAO,GAAG,OAAO,CAAC;AAC7B,QAAI,OAAO,UAAU,GAAG,UAAU,CAAC;AAAA,EACvC;AAEA,mBAAiB,KAAK,MAAM;AAC5B,SAAO;AACX;AAEA,IAAM,mBAAmB;AAAA,EACrB,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AACV;AAEA,IAAM,oBAAoB,eAAa;AACnC,QAAM,QAAQ,SAAS,cAAc,QAAQ;AAC7C,QAAM,QAAQ,UAAU;AACxB,QAAM,SAAS,UAAU;AACzB,QAAM,MAAM,MAAM,WAAW,IAAI;AACjC,MAAI,aAAa,WAAW,GAAG,CAAC;AAChC,SAAO;AACX;AAEA,IAAM,iBAAiB,CAAC,MAAM,cAAc,UAAU,CAAC,MACnD,IAAI,QAAQ,CAAC,SAAS,WAAW;AAE7B,MAAI,CAAC,QAAQ,CAAC,UAAU,IAAI,EAAG,QAAO,OAAO,EAAE,QAAQ,qBAAqB,KAAK,CAAC;AAGlF,QAAM,EAAE,gBAAgB,kBAAkB,iBAAiB,kBAAkB,IAAI;AAGjF,QAAM,EAAE,MAAM,MAAM,QAAQ,QAAQ,OAAO,IAAI;AAG/C,QAAM,cACF,aAAa,SAAS,aAAa,MAAM,cACnC,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,aAAa,MAAM,WAAW,CAAC,IACvD;AAGV,QAAM,sBAAsB,UAAU,OAAO;AAC7C,QAAM,UAAU,wBAAwB,OAAO,OAAO,sBAAsB;AAG5E,QAAM,OAAQ,UAAU,OAAO,QAAS;AAGxC,QAAM,aAAc,UAAU,OAAO,cAAe;AAGpD,QAAM,aAAa,CAAC;AAGpB,MAAI,SAAS,OAAO,KAAK,UAAU,YAAY,OAAO,KAAK,WAAW,WAAW;AAC7E,eAAW,KAAK,EAAE,MAAM,UAAU,MAAM,KAAK,CAAC;AAAA,EAClD;AAGA,MAAI,UAAU,OAAO,WAAW,IAAI;AAChC,eAAW,KAAK,EAAE,MAAM,UAAU,MAAM,OAAO,CAAC;AAAA,EACpD;AAGA,QAAM,kBAAkB,UAAQ;AAC5B,UAAM,eAAe,kBAAkB,gBAAgB,IAAI,IAAI;AAC/D,YAAQ,QAAQ,YAAY,EAAE,KAAK,OAAO;AAAA,EAC9C;AAGA,QAAM,SAAS,CAAC,WAAWC,aAAY;AACnC,UAAM,SAAS,kBAAkB,SAAS;AAC1C,UAAM,iBAAiB,OAAO,SAAS,kBAAkB,QAAQ,MAAM,IAAI;AAC3E,YAAQ,QAAQ,cAAc,EAAE,KAAK,CAAAL,YAAU;AAC3C,mBAAaA,SAAQK,UAAS,gBAAgB,EACzC,KAAK,UAAQ;AAEV,sBAAcL,OAAM;AAGpB,YAAI,eAAgB,QAAO,gBAAgB,IAAI;AAG/C,qBAAa,IAAI,EAAE,KAAK,eAAa;AAEjC,cAAI,cAAc,MAAM;AACpB,mBAAO,IAAI,KAAK,CAAC,WAAW,KAAK,MAAM,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,KAAK,CAAC;AAAA,UACpE;AAGA,0BAAgB,IAAI;AAAA,QACxB,CAAC;AAAA,MACL,CAAC,EACA,MAAM,MAAM;AAAA,IACrB,CAAC;AAAA,EACL;AAGA,MAAI,MAAM,KAAK,KAAK,IAAI,KAAK,SAAS,MAAM;AACxC,WAAO,QAAQ,MAAM,MAAM,QAAQ,EAAE,WAAW,CAAC,EAAE,KAAK,UAAQ;AAC5D,cAAQ,WAAW,MAAM,eAAe,CAAC;AAAA,IAC7C,CAAC;AAAA,EACL;AAGA,QAAM,MAAM,IAAI,gBAAgB,IAAI;AAGpC,YAAU,GAAG,EACR,KAAK,WAAS;AAEX,QAAI,gBAAgB,GAAG;AAGvB,UAAM,YAAY,iBAAiB,OAAO,aAAa,MAAM;AAAA,MACzD;AAAA,MACA;AAAA,IACJ,CAAC;AAGD,UAAM,eAAe;AAAA,MACjB;AAAA,MACA,MAAM,QAAQ,KAAK;AAAA,IACvB;AAGA,QAAI,CAAC,WAAW,QAAQ;AACpB,aAAO,OAAO,WAAW,YAAY;AAAA,IACzC;AAGA,UAAM,SAAS,aAAa,eAAe;AAC3C,WAAO;AAAA,MACH;AAAA,QACI;AAAA,QACA;AAAA,MACJ;AAAA,MACA,cAAY;AAER,eAAO,kBAAkB,QAAQ,GAAG,YAAY;AAGhD,eAAO,UAAU;AAAA,MACrB;AAAA,MACA,CAAC,UAAU,KAAK,MAAM;AAAA,IAC1B;AAAA,EACJ,CAAC,EACA,MAAM,MAAM;AACrB,CAAC;AAEL,IAAM,cAAc,CAAC,KAAK,KAAK,QAAQ,OAAO,SAAS,UAAU,SAAS,QAAQ;AAElF,IAAM,qBAAqB,WACvB,OAAO,UAAU,YAAY,IAAI,KAAK,KAAK,IAAI,WAAW,KAAK,IAAI,MAAM;AAG7E,IAAM,gBAAgB,YAAU;AAC5B,QAAM,CAAC,MAAM,KAAK,IAAI;AAEtB,QAAM,OAAO,MAAM,SACb,CAAC,IACD,YAAY,OAAO,CAAC,MAAM,SAAS;AAC/B,SAAK,IAAI,IAAI,mBAAmB,MAAM,IAAI,CAAC;AAC3C,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AAEX,SAAO;AAAA,IACH;AAAA,IACA;AAAA,MACI,QAAQ;AAAA,MACR,GAAG;AAAA,MACH,GAAG;AAAA,IACP;AAAA,EACJ;AACJ;AAEA,IAAM,eAAe,UACjB,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC7B,QAAM,eAAe,IAAI,MAAM;AAC/B,eAAa,MAAM,IAAI,gBAAgB,IAAI;AAG3C,QAAM,UAAU,MAAM;AAClB,UAAM,QAAQ,aAAa;AAC3B,UAAM,SAAS,aAAa;AAC5B,UAAM,UAAU,SAAS;AACzB,QAAI,CAAC,QAAS;AAEd,QAAI,gBAAgB,aAAa,GAAG;AACpC,kBAAc,UAAU;AACxB,YAAQ,EAAE,OAAO,OAAO,CAAC;AAAA,EAC7B;AAEA,eAAa,UAAU,SAAO;AAC1B,QAAI,gBAAgB,aAAa,GAAG;AACpC,kBAAc,UAAU;AACxB,WAAO,GAAG;AAAA,EACd;AAEA,QAAM,aAAa,YAAY,SAAS,CAAC;AAEzC,UAAQ;AACZ,CAAC;AAKL,IAAI,OAAO,WAAW,eAAe,OAAO,OAAO,aAAa,aAAa;AACzE,MAAI,CAAC,kBAAkB,UAAU,QAAQ;AACrC,WAAO,eAAe,kBAAkB,WAAW,UAAU;AAAA,MACzD,OAAO,SAAS,IAAI,MAAM,SAAS;AAC/B,cAAM,SAAS;AACf,mBAAW,MAAM;AACb,gBAAM,UAAU,OAAO,UAAU,MAAM,OAAO,EAAE,MAAM,GAAG,EAAE,CAAC;AAC5D,gBAAM,SAAS,KAAK,OAAO;AAC3B,cAAI,QAAQ,OAAO;AACnB,gBAAM,OAAO,IAAI,WAAW,KAAK;AACjC,iBAAO,SAAS;AACZ,iBAAK,KAAK,IAAI,OAAO,WAAW,KAAK;AAAA,UACzC;AACA,aAAG,IAAI,KAAK,CAAC,IAAI,GAAG,EAAE,MAAM,QAAQ,YAAY,CAAC,CAAC;AAAA,QACtD,CAAC;AAAA,MACL;AAAA,IACJ,CAAC;AAAA,EACL;AACJ;AAEA,IAAM,YAAY,OAAO,WAAW,eAAe,OAAO,OAAO,aAAa;AAC9E,IAAM,QAAQ,aAAa,mBAAmB,KAAK,UAAU,SAAS,KAAK,CAAC,OAAO;AAKnF,IAAM,SAAS,CAAC,EAAE,WAAW,MAAM,MAAM;AACrC,QAAM,EAAE,MAAM,OAAO,iBAAiB,OAAO,IAAI;AAOjD,QAAM,iBAAiB,CAAC,QAAQ,UAAU,UAAU,UAAU,QAAQ;AAEtE,QAAM,uBAAuB,oBAAkB,CAAC,WAAW,MAAM,aAC7D,UAAU,MAAM,iBAAiB,eAAe,QAAQ,IAAI,QAAQ;AAExE,QAAM,gBAAgB,UAClB,KAAK,gBAAgB,QACrB,KAAK,aAAa,KAClB,KAAK,SAAS,KACd,KAAK,UACL,KAAK,OAAO,MAAM,OAClB,KAAK,OAAO,MAAM,OAClB,KAAK,QACL,KAAK,KAAK,eAAe,SACzB,KAAK,KAAK,aAAa;AAK3B;AAAA,IACI;AAAA,IACA,CAAC,qBAAqB,EAAE,MAAM,MAC1B,IAAI,QAAQ,aAAW;AAEnB,cAAQ,CAAC,MAAM,UAAU,CAAC;AAAA,IAC9B,CAAC;AAAA,EACT;AAEA,QAAM,sBAAsB,CAAC,OAAO,MAAM,SACtC,IAAI,QAAQ,aAAW;AACnB,QACI,CAAC,MAAM,2BAA2B,KAClC,KAAK,YACL,CAAC,OAAO,IAAI,KACZ,CAAC,QAAQ,IAAI,GACf;AACE,aAAO,QAAQ,KAAK;AAAA,IACxB;AAGA,iBAAa,IAAI,EACZ,KAAK,MAAM;AACR,YAAM,KAAK,MAAM,kCAAkC;AACnD,UAAI,IAAI;AACJ,cAAM,eAAe,GAAG,IAAI;AAC5B,YAAI,gBAAgB,MAAM;AAEtB,iBAAO,aAAa,IAAI;AAAA,QAC5B;AACA,YAAI,OAAO,iBAAiB,WAAW;AACnC,iBAAO,QAAQ,YAAY;AAAA,QAC/B;AACA,YAAI,OAAO,aAAa,SAAS,YAAY;AACzC,iBAAO,aAAa,KAAK,OAAO;AAAA,QACpC;AAAA,MACJ;AAEA,cAAQ,IAAI;AAAA,IAChB,CAAC,EACA,MAAM,SAAO;AACV,cAAQ,KAAK;AAAA,IACjB,CAAC;AAAA,EACT,CAAC;AAEL,YAAU,mBAAmB,CAAC,MAAM,EAAE,OAAO,SAAS,MAAM;AACxD,QAAI,CAAC,MAAM,2BAA2B,EAAG;AAEzC,SAAK;AAAA,MACD;AAAA,MACA,MACI,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC7B;AAAA,UACI;AAAA,UACA;AAAA,YACI,OAAO,KAAK;AAAA,YACZ;AAAA,YACA,SAAS;AAAA,YACT,SAAS;AAAA,UACb;AAAA,UACA;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACT;AAAA,EACJ,CAAC;AAGD;AAAA,IACI;AAAA,IACA,CAAC,MAAM,EAAE,OAAO,KAAK,MACjB,IAAI,QAAQ,aAAW;AACnB,0BAAoB,OAAO,MAAM,IAAI,EAAE,KAAK,qBAAmB;AAE3D,YAAI,CAAC,gBAAiB,QAAO,QAAQ,IAAI;AAGzC,cAAM,WAAW,CAAC;AAGlB,YAAI,MAAM,+CAA+C,GAAG;AACxD,mBAAS;AAAA,YACL,MACI,IAAI,QAAQ,CAAAI,aAAW;AACnB,cAAAA,SAAQ;AAAA,gBACJ,MAAM,MAAM,4CAA4C;AAAA,gBACxD;AAAA,cACJ,CAAC;AAAA,YACL,CAAC;AAAA,UACT;AAAA,QACJ;AAGA,YAAI,MAAM,8CAA8C,GAAG;AACvD,mBAAS;AAAA,YACL,CAACH,YAAWK,OAAM,aACd,IAAI,QAAQ,CAAAF,aAAW;AACnB,cAAAH,WAAUK,OAAM,QAAQ,EAAE;AAAA,gBAAK,CAAAA,UAC3BF,SAAQ;AAAA,kBACJ,MAAM;AAAA,oBACF;AAAA,kBACJ;AAAA,kBACA,MAAAE;AAAA,gBACJ,CAAC;AAAA,cACL;AAAA,YACJ,CAAC;AAAA,UACT;AAAA,QACJ;AAGA,cAAM,qBAAqB,MAAM,8BAA8B,KAAK,CAAC;AACrE,cAAM,oBAAoB,CAAC,KAAK,OAAO;AACnC,gBAAM,gBAAgB,qBAAqB,EAAE;AAC7C,mBAAS;AAAA,YACL,CAACL,YAAWK,OAAM,aACd,IAAI,QAAQ,CAAAF,aAAW;AACnB,4BAAcH,YAAWK,OAAM,QAAQ,EAAE;AAAA,gBAAK,CAAAA,UAC1CF,SAAQ,EAAE,MAAM,KAAK,MAAAE,MAAK,CAAC;AAAA,cAC/B;AAAA,YACJ,CAAC;AAAA,UACT;AAAA,QACJ,CAAC;AAGD,cAAM,sBAAsB,MAAM,oCAAoC;AACtE,cAAM,cAAc,MAAM,yCAAyC;AACnE,cAAM,UAAU,wBAAwB,OAAO,OAAO,sBAAsB;AAC5E,cAAM,OAAO,MAAM,sCAAsC;AACzD,cAAM,mBACF,MAAM,uCAAuC,KAAK;AAGtD,aAAK;AAAA,UACD;AAAA,UACA;AAAA,YACI;AAAA,YACA;AAAA,YACA,QAAQ;AAAA,UACZ;AAAA,UACA;AAAA,QACJ;AAGA,cAAM,YAAY,CAACA,OAAM,aACrB,IAAI,QAAQ,CAACF,UAAS,WAAW;AAC7B,gBAAM,mBAAmB,EAAE,GAAG,SAAS;AAEvC,iBAAO,KAAK,gBAAgB,EACvB,OAAO,iBAAe,gBAAgB,MAAM,EAC5C,QAAQ,iBAAe;AAEpB,gBAAI,iBAAiB,QAAQ,WAAW,MAAM,IAAI;AAC9C,qBAAO,iBAAiB,WAAW;AAAA,YACvC;AAAA,UACJ,CAAC;AAEL,gBAAM,EAAE,QAAQ,MAAM,QAAQ,MAAM,QAAQ,OAAO,IAAI;AAEvD,gBAAM,eAAe;AAAA,YACjB,OAAO;AAAA,cACH,aAAa,OAAO,KAAK,cAAc;AAAA,YAC3C;AAAA,YACA,QACI,WACC,OAAO,QACJ,OAAO,OAAO,YAAY,YAC1B,OAAO,cACL;AAAA,cACI,MAAM,OAAO;AAAA,cACb,SACI,OAAO,OAAO,YAAY,WACpB,OAAO,UAAU,MACjB;AAAA,cACV,YACI,OAAO,cACP;AAAA,gBACI;AAAA,cACJ,KACA;AAAA,YACR,IACA;AAAA,YACV,MACI,WAAW,OAAO,KAAK,SAAS,OAAO,KAAK,UACtC;AAAA,cACI,MAAM,OAAO;AAAA,cACb,SAAS,OAAO;AAAA,cAChB,GAAG,OAAO;AAAA,YACd,IACA;AAAA,YACV,MACI,QAAQ,CAAC,cAAc,IAAI,IACrB;AAAA,cACI,GAAG;AAAA,YACP,IACA;AAAA,YACV,QAAQ,UAAU,OAAO,SAAS,OAAO,IAAI,aAAa,IAAI,CAAC;AAAA,YAC/D;AAAA,UACJ;AAEA,cAAI,aAAa,QAAQ;AAErB,kBAAM,iBAAiB,OAAO;AAAA;AAAA,cAExB,OAAO,SAASE,MAAK;AAAA;AAAA;AAAA,cAErB;AAAA;AAEN,kBAAM,mBAAmB,WAAW,KAAKA,MAAK,IAAI;AAClD,kBAAM,oBACF,OAAO,YAAY;AAAA;AAAA,cAEb,oBAAoB,gBAAgB;AAAA;AAAA;AAAA,cAEpC;AAAA;AAGV,kBAAM,sBAAsB,CAAC,EACzB,aAAa,QACb,aAAa,QACb,aAAa,UACb,kBACA;AAIJ,gBAAI,CAAC,oBAAqB,QAAOF,SAAQE,KAAI;AAAA,UACjD;AAEA,gBAAM,UAAU;AAAA,YACZ,kBAAkB,MAAM,wCAAwC;AAAA,YAChE,iBAAiB,MAAM,uCAAuC;AAAA,YAC9D,mBAAmB,MAAM,yCAAyC;AAAA,YAClE,gBAAgB;AAAA,cACZ;AAAA,YACJ;AAAA,UACJ;AAEA,yBAAeA,OAAM,cAAc,OAAO,EACrC,KAAK,UAAQ;AAEV,kBAAM,MAAM;AAAA,cACR;AAAA;AAAA,cAEA;AAAA,gBACIA,MAAK;AAAA,gBACL,uBAAuB,KAAK,IAAI;AAAA,cACpC;AAAA,YACJ;AAEA,YAAAF,SAAQ,GAAG;AAAA,UACf,CAAC,EACA,MAAM,MAAM;AAAA,QACrB,CAAC;AAGL,cAAM,kBAAkB,SAAS;AAAA,UAAI,YACjC,OAAO,WAAW,MAAM,KAAK,YAAY,CAAC;AAAA,QAC9C;AAGA,gBAAQ,IAAI,eAAe,EAAE,KAAK,WAAS;AAEvC;AAAA,YACI,MAAM,WAAW,KAAK,MAAM,CAAC,EAAE,SAAS;AAAA;AAAA,cAElC,MAAM,CAAC,EAAE;AAAA;AAAA;AAAA,cAET;AAAA;AAAA,UACV;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL,CAAC;AAAA,EACT;AAGA,SAAO;AAAA,IACH,SAAS;AAAA,MACL,qBAAqB,CAAC,MAAM,KAAK,OAAO;AAAA;AAAA,MAGxC,2BAA2B,CAAC,MAAM,KAAK,QAAQ;AAAA;AAAA,MAG/C,8BAA8B,CAAC,MAAM,KAAK,MAAM;AAAA;AAAA,MAGhD,6BAA6B,CAAC,MAAM,KAAK,GAAG;AAAA;AAAA,MAG5C,oCAAoC,CAAC,MAAM,KAAK,OAAO;AAAA;AAAA,MAGvD,gCAAgC,CAAC,MAAM,KAAK,KAAK;AAAA;AAAA,MAGjD,iCAAiC,CAAC,UAAU,KAAK,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,MAMvD,wBAAwB,CAAC,MAAM,KAAK,MAAM;AAAA;AAAA,MAG1C,sCAAsC,CAAC,MAAM,KAAK,OAAO;AAAA;AAAA,MAGzD,mCAAmC,CAAC,MAAM,KAAK,MAAM;AAAA;AAAA,MAGrD,uCAAuC,CAAC,OAAO,KAAK,OAAO;AAAA;AAAA,MAG3D,oCAAoC,CAAC,aAAa,KAAK,MAAM;AAAA;AAAA,MAG7D,gCAAgC,CAAC,MAAM,KAAK,QAAQ;AAAA;AAAA,MAGpD,+BAA+B,CAAC,MAAM,KAAK,QAAQ;AAAA;AAAA,MAGnD,iCAAiC,CAAC,aAAa,QAAQ,OAAO,OAAO,MAAM,KAAK,GAAG;AAAA;AAAA,MAGnF,qCAAqC,CAAC,MAAM,KAAK,MAAM;AAAA,IAC3D;AAAA,EACJ;AACJ;AAGA,IAAI,WAAW;AACX,WAAS,cAAc,IAAI,YAAY,yBAAyB,EAAE,QAAQ,OAAO,CAAC,CAAC;AACvF;AAEA,IAAO,8CAAQ;", "names": ["canvas", "transform", "filter", "cb", "resolve", "options", "file"]}