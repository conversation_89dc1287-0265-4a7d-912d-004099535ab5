# File Size Validation plugin for FilePond

[![npm version](https://badge.fury.io/js/filepond-plugin-file-validate-size.svg)](https://badge.fury.io/js/filepond)

https://pqina.nl/filepond/docs/patterns/plugins/file-validate-size/

The File Size Validation plugin is used to block files that are too large to upload. Set a maximum size for single files and a maximum size for all files.

[Demo](https://pqina.github.io/filepond-plugin-file-validate-size/)