import{u as m,j as e}from"./app-CZ-bNXzO.js";import{u,S as p,M as x,a as h}from"./use-translation-Bbr1NOyb.js";import{a as t}from"./createLucideIcon-DkVbiciO.js";function f({className:n="",...s}){const{appearance:l,updateAppearance:o}=m(),{translate:a}=u(),c=[{value:"light",icon:p,label:a("header.light_mode")},{value:"dark",icon:x,label:a("header.dark_mode")},{value:"system",icon:h,label:a("header.system_mode")}];return e.jsx("div",{className:t("inline-flex gap-1 rounded-lg bg-neutral-100 p-1 dark:bg-neutral-800",n),...s,children:c.map(({value:r,icon:d,label:i})=>e.jsxs("button",{onClick:()=>o(r),className:t("flex items-center rounded-md px-3.5 py-1.5 transition-colors",l===r?"bg-white shadow-xs dark:bg-neutral-700 dark:text-neutral-100":"text-neutral-500 hover:bg-neutral-200/60 hover:text-black dark:text-neutral-400 dark:hover:bg-neutral-700/60"),children:[e.jsx(d,{className:"-ml-1 h-4 w-4"}),e.jsx("span",{className:"ml-1.5 text-sm",children:i})]},r))})}export{f as A};
