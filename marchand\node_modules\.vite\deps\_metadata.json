{"hash": "3232a56f", "configHash": "c4adf34d", "lockfileHash": "95a03669", "browserHash": "697a03a0", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "bbb9f03a", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "c8688c13", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "b8f06b30", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "3fa6d345", "needsInterop": true}, "@headlessui/react": {"src": "../../@headlessui/react/dist/headlessui.esm.js", "file": "@headlessui_react.js", "fileHash": "9eaad583", "needsInterop": false}, "@inertiajs/react": {"src": "../../@inertiajs/react/dist/index.esm.js", "file": "@inertiajs_react.js", "fileHash": "6c960e9a", "needsInterop": false}, "@radix-ui/react-avatar": {"src": "../../@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "59c9709f", "needsInterop": false}, "@radix-ui/react-checkbox": {"src": "../../@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "370cd413", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "2e6daeca", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "ccb434c8", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "0d81e935", "needsInterop": false}, "@radix-ui/react-progress": {"src": "../../@radix-ui/react-progress/dist/index.mjs", "file": "@radix-ui_react-progress.js", "fileHash": "da831def", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "aff70a93", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "dd5b7ece", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "45d218bc", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "ef230fea", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "178bba93", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "fced0ef0", "needsInterop": false}, "filepond-plugin-file-validate-size": {"src": "../../filepond-plugin-file-validate-size/dist/filepond-plugin-file-validate-size.esm.js", "file": "filepond-plugin-file-validate-size.js", "fileHash": "887c4c70", "needsInterop": false}, "filepond-plugin-file-validate-type": {"src": "../../filepond-plugin-file-validate-type/dist/filepond-plugin-file-validate-type.esm.js", "file": "filepond-plugin-file-validate-type.js", "fileHash": "f2d4d75c", "needsInterop": false}, "filepond-plugin-image-preview": {"src": "../../filepond-plugin-image-preview/dist/filepond-plugin-image-preview.esm.js", "file": "filepond-plugin-image-preview.js", "fileHash": "5b766466", "needsInterop": false}, "filepond-plugin-image-resize": {"src": "../../filepond-plugin-image-resize/dist/filepond-plugin-image-resize.esm.js", "file": "filepond-plugin-image-resize.js", "fileHash": "cb8bcdd0", "needsInterop": false}, "filepond-plugin-image-transform": {"src": "../../filepond-plugin-image-transform/dist/filepond-plugin-image-transform.esm.js", "file": "filepond-plugin-image-transform.js", "fileHash": "c410ff97", "needsInterop": false}, "laravel-vite-plugin/inertia-helpers": {"src": "../../laravel-vite-plugin/inertia-helpers/index.js", "file": "laravel-vite-plugin_inertia-helpers.js", "fileHash": "3fb79860", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "1e4ec5f7", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "d88e016a", "needsInterop": true}, "react-filepond": {"src": "../../react-filepond/dist/react-filepond.esm.js", "file": "react-filepond.js", "fileHash": "bce9701a", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "1cba0c2e", "needsInterop": false}}, "chunks": {"chunk-PJ664RS2": {"file": "chunk-PJ664RS2.js"}, "chunk-KHAYJDO5": {"file": "chunk-KHAYJDO5.js"}, "chunk-EJUMEZYM": {"file": "chunk-EJUMEZYM.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-SXB54R6U": {"file": "chunk-SXB54R6U.js"}, "chunk-4Y43LDCH": {"file": "chunk-4Y43LDCH.js"}, "chunk-4JKHPIC7": {"file": "chunk-4JKHPIC7.js"}, "chunk-7ATFNNHF": {"file": "chunk-7ATFNNHF.js"}, "chunk-CN7RQBZE": {"file": "chunk-CN7RQBZE.js"}, "chunk-7YTMGNOY": {"file": "chunk-7YTMGNOY.js"}, "chunk-PBPI5PFA": {"file": "chunk-PBPI5PFA.js"}, "chunk-5QB6WZBA": {"file": "chunk-5QB6WZBA.js"}, "chunk-EZGML3GP": {"file": "chunk-EZGML3GP.js"}, "chunk-35AGI64M": {"file": "chunk-35AGI64M.js"}, "chunk-Q5B56BVT": {"file": "chunk-Q5B56BVT.js"}, "chunk-3M4ZFO5U": {"file": "chunk-3M4ZFO5U.js"}, "chunk-YJYY6GXC": {"file": "chunk-YJYY6GXC.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}