import{j as t,L as e}from"./app-CZ-bNXzO.js";import{A as r}from"./appearance-tabs-DbdmmdZ1.js";import{S as a,H as s}from"./layout-BQ0PIX5a.js";import{A as p}from"./app-layout-Cr6sLaR1.js";import"./use-translation-Bbr1NOyb.js";import"./createLucideIcon-DkVbiciO.js";import"./index-k7GfkN7R.js";import"./sheet-DnOgsUoR.js";import"./index-uB82XUlN.js";import"./dropdown-menu-DUK2pmBw.js";import"./x-ImbT-hFH.js";import"./index-g7rG-vGS.js";import"./app-logo-icon-BCY66tir.js";const i=[{title:"Appearance settings",href:"/settings/appearance"}];function y(){return t.jsxs(p,{breadcrumbs:i,children:[t.jsx(e,{title:"Appearance settings"}),t.jsx(a,{children:t.jsxs("div",{className:"space-y-6",children:[t.jsx(s,{title:"Appearance settings",description:"Update your account's appearance settings"}),t.jsx(r,{})]})})]})}export{y as default};
