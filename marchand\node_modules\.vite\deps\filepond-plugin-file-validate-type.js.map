{"version": 3, "sources": ["../../filepond-plugin-file-validate-type/dist/filepond-plugin-file-validate-type.esm.js"], "sourcesContent": ["/*!\n * FilePondPluginFileValidateType 1.2.9\n * Licensed under MIT, https://opensource.org/licenses/MIT/\n * Please visit https://pqina.nl/filepond/ for details.\n */\n\n/* eslint-disable */\n\nconst plugin = ({ addFilter, utils }) => {\n    // get quick reference to Type utils\n    const {\n        Type,\n        isString,\n        replaceInString,\n        guesstimateMimeType,\n        getExtensionFromFilename,\n        getFilenameFromURL,\n    } = utils;\n\n    const mimeTypeMatchesWildCard = (mimeType, wildcard) => {\n        const mimeTypeGroup = (/^[^/]+/.exec(mimeType) || []).pop(); // image/png -> image\n        const wildcardGroup = wildcard.slice(0, -2); // image/* -> image\n        return mimeTypeGroup === wildcardGroup;\n    };\n\n    const isValidMimeType = (acceptedTypes, userInputType) =>\n        acceptedTypes.some(acceptedType => {\n            // accepted is wildcard mime type\n            if (/\\*$/.test(acceptedType)) {\n                return mimeTypeMatchesWildCard(userInputType, acceptedType);\n            }\n\n            // is normal mime type\n            return acceptedType === userInputType;\n        });\n\n    const getItemType = item => {\n        // if the item is a url we guess the mime type by the extension\n        let type = '';\n        if (isString(item)) {\n            const filename = getFilenameFromURL(item);\n            const extension = getExtensionFromFilename(filename);\n            if (extension) {\n                type = guesstimateMimeType(extension);\n            }\n        } else {\n            type = item.type;\n        }\n\n        return type;\n    };\n\n    const validateFile = (item, acceptedFileTypes, typeDetector) => {\n        // no types defined, everything is allowed \\o/\n        if (acceptedFileTypes.length === 0) {\n            return true;\n        }\n\n        // gets the item type\n        const type = getItemType(item);\n\n        // no type detector, test now\n        if (!typeDetector) {\n            return isValidMimeType(acceptedFileTypes, type);\n        }\n\n        // use type detector\n        return new Promise((resolve, reject) => {\n            typeDetector(item, type)\n                .then(detectedType => {\n                    if (isValidMimeType(acceptedFileTypes, detectedType)) {\n                        resolve();\n                    } else {\n                        reject();\n                    }\n                })\n                .catch(reject);\n        });\n    };\n\n    const applyMimeTypeMap = map => acceptedFileType =>\n        map[acceptedFileType] === null ? false : map[acceptedFileType] || acceptedFileType;\n\n    // setup attribute mapping for accept\n    addFilter('SET_ATTRIBUTE_TO_OPTION_MAP', map =>\n        Object.assign(map, {\n            accept: 'acceptedFileTypes',\n        })\n    );\n\n    // filtering if an item is allowed in hopper\n    addFilter('ALLOW_HOPPER_ITEM', (file, { query }) => {\n        // if we are not doing file type validation exit\n        if (!query('GET_ALLOW_FILE_TYPE_VALIDATION')) {\n            return true;\n        }\n\n        // we validate the file against the accepted file types\n        return validateFile(file, query('GET_ACCEPTED_FILE_TYPES'));\n    });\n\n    // called for each file that is loaded\n    // right before it is set to the item state\n    // should return a promise\n    addFilter(\n        'LOAD_FILE',\n        (file, { query }) =>\n            new Promise((resolve, reject) => {\n                if (!query('GET_ALLOW_FILE_TYPE_VALIDATION')) {\n                    resolve(file);\n                    return;\n                }\n\n                const acceptedFileTypes = query('GET_ACCEPTED_FILE_TYPES');\n\n                // custom type detector method\n                const typeDetector = query('GET_FILE_VALIDATE_TYPE_DETECT_TYPE');\n\n                // if invalid, exit here\n                const validationResult = validateFile(file, acceptedFileTypes, typeDetector);\n\n                const handleRejection = () => {\n                    const acceptedFileTypesMapped = acceptedFileTypes\n                        .map(\n                            applyMimeTypeMap(\n                                query('GET_FILE_VALIDATE_TYPE_LABEL_EXPECTED_TYPES_MAP')\n                            )\n                        )\n                        .filter(label => label !== false);\n\n                    const acceptedFileTypesMappedUnique = acceptedFileTypesMapped.filter(\n                        (item, index) => acceptedFileTypesMapped.indexOf(item) === index\n                    );\n\n                    reject({\n                        status: {\n                            main: query('GET_LABEL_FILE_TYPE_NOT_ALLOWED'),\n                            sub: replaceInString(\n                                query('GET_FILE_VALIDATE_TYPE_LABEL_EXPECTED_TYPES'),\n                                {\n                                    allTypes: acceptedFileTypesMappedUnique.join(', '),\n                                    allButLastType: acceptedFileTypesMappedUnique\n                                        .slice(0, -1)\n                                        .join(', '),\n                                    lastType:\n                                        acceptedFileTypesMappedUnique[\n                                            acceptedFileTypesMappedUnique.length - 1\n                                        ],\n                                }\n                            ),\n                        },\n                    });\n                };\n\n                // has returned new filename immidiately\n                if (typeof validationResult === 'boolean') {\n                    if (!validationResult) {\n                        return handleRejection();\n                    }\n                    return resolve(file);\n                }\n\n                // is promise\n                validationResult\n                    .then(() => {\n                        resolve(file);\n                    })\n                    .catch(handleRejection);\n            })\n    );\n\n    // expose plugin\n    return {\n        // default options\n        options: {\n            // Enable or disable file type validation\n            allowFileTypeValidation: [true, Type.BOOLEAN],\n\n            // What file types to accept\n            acceptedFileTypes: [[], Type.ARRAY],\n            // - must be comma separated\n            // - mime types: image/png, image/jpeg, image/gif\n            // - extensions: .png, .jpg, .jpeg ( not enabled yet )\n            // - wildcards: image/*\n\n            // label to show when a type is not allowed\n            labelFileTypeNotAllowed: ['File is of invalid type', Type.STRING],\n\n            // nicer label\n            fileValidateTypeLabelExpectedTypes: [\n                'Expects {allButLastType} or {lastType}',\n                Type.STRING,\n            ],\n\n            // map mime types to extensions\n            fileValidateTypeLabelExpectedTypesMap: [{}, Type.OBJECT],\n\n            // Custom function to detect type of file\n            fileValidateTypeDetectType: [null, Type.FUNCTION],\n        },\n    };\n};\n\n// fire pluginloaded event if running in browser, this allows registering the plugin when using async script tags\nconst isBrowser = typeof window !== 'undefined' && typeof window.document !== 'undefined';\nif (isBrowser) {\n    document.dispatchEvent(new CustomEvent('FilePond:pluginloaded', { detail: plugin }));\n}\n\nexport default plugin;\n"], "mappings": ";;;AAQA,IAAM,SAAS,CAAC,EAAE,WAAW,MAAM,MAAM;AAErC,QAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,IAAI;AAEJ,QAAM,0BAA0B,CAAC,UAAU,aAAa;AACpD,UAAM,iBAAiB,SAAS,KAAK,QAAQ,KAAK,CAAC,GAAG,IAAI;AAC1D,UAAM,gBAAgB,SAAS,MAAM,GAAG,EAAE;AAC1C,WAAO,kBAAkB;AAAA,EAC7B;AAEA,QAAM,kBAAkB,CAAC,eAAe,kBACpC,cAAc,KAAK,kBAAgB;AAE/B,QAAI,MAAM,KAAK,YAAY,GAAG;AAC1B,aAAO,wBAAwB,eAAe,YAAY;AAAA,IAC9D;AAGA,WAAO,iBAAiB;AAAA,EAC5B,CAAC;AAEL,QAAM,cAAc,UAAQ;AAExB,QAAI,OAAO;AACX,QAAI,SAAS,IAAI,GAAG;AAChB,YAAM,WAAW,mBAAmB,IAAI;AACxC,YAAM,YAAY,yBAAyB,QAAQ;AACnD,UAAI,WAAW;AACX,eAAO,oBAAoB,SAAS;AAAA,MACxC;AAAA,IACJ,OAAO;AACH,aAAO,KAAK;AAAA,IAChB;AAEA,WAAO;AAAA,EACX;AAEA,QAAM,eAAe,CAAC,MAAM,mBAAmB,iBAAiB;AAE5D,QAAI,kBAAkB,WAAW,GAAG;AAChC,aAAO;AAAA,IACX;AAGA,UAAM,OAAO,YAAY,IAAI;AAG7B,QAAI,CAAC,cAAc;AACf,aAAO,gBAAgB,mBAAmB,IAAI;AAAA,IAClD;AAGA,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,mBAAa,MAAM,IAAI,EAClB,KAAK,kBAAgB;AAClB,YAAI,gBAAgB,mBAAmB,YAAY,GAAG;AAClD,kBAAQ;AAAA,QACZ,OAAO;AACH,iBAAO;AAAA,QACX;AAAA,MACJ,CAAC,EACA,MAAM,MAAM;AAAA,IACrB,CAAC;AAAA,EACL;AAEA,QAAM,mBAAmB,SAAO,sBAC5B,IAAI,gBAAgB,MAAM,OAAO,QAAQ,IAAI,gBAAgB,KAAK;AAGtE;AAAA,IAAU;AAAA,IAA+B,SACrC,OAAO,OAAO,KAAK;AAAA,MACf,QAAQ;AAAA,IACZ,CAAC;AAAA,EACL;AAGA,YAAU,qBAAqB,CAAC,MAAM,EAAE,MAAM,MAAM;AAEhD,QAAI,CAAC,MAAM,gCAAgC,GAAG;AAC1C,aAAO;AAAA,IACX;AAGA,WAAO,aAAa,MAAM,MAAM,yBAAyB,CAAC;AAAA,EAC9D,CAAC;AAKD;AAAA,IACI;AAAA,IACA,CAAC,MAAM,EAAE,MAAM,MACX,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC7B,UAAI,CAAC,MAAM,gCAAgC,GAAG;AAC1C,gBAAQ,IAAI;AACZ;AAAA,MACJ;AAEA,YAAM,oBAAoB,MAAM,yBAAyB;AAGzD,YAAM,eAAe,MAAM,oCAAoC;AAG/D,YAAM,mBAAmB,aAAa,MAAM,mBAAmB,YAAY;AAE3E,YAAM,kBAAkB,MAAM;AAC1B,cAAM,0BAA0B,kBAC3B;AAAA,UACG;AAAA,YACI,MAAM,iDAAiD;AAAA,UAC3D;AAAA,QACJ,EACC,OAAO,WAAS,UAAU,KAAK;AAEpC,cAAM,gCAAgC,wBAAwB;AAAA,UAC1D,CAAC,MAAM,UAAU,wBAAwB,QAAQ,IAAI,MAAM;AAAA,QAC/D;AAEA,eAAO;AAAA,UACH,QAAQ;AAAA,YACJ,MAAM,MAAM,iCAAiC;AAAA,YAC7C,KAAK;AAAA,cACD,MAAM,6CAA6C;AAAA,cACnD;AAAA,gBACI,UAAU,8BAA8B,KAAK,IAAI;AAAA,gBACjD,gBAAgB,8BACX,MAAM,GAAG,EAAE,EACX,KAAK,IAAI;AAAA,gBACd,UACI,8BACI,8BAA8B,SAAS,CAC3C;AAAA,cACR;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ,CAAC;AAAA,MACL;AAGA,UAAI,OAAO,qBAAqB,WAAW;AACvC,YAAI,CAAC,kBAAkB;AACnB,iBAAO,gBAAgB;AAAA,QAC3B;AACA,eAAO,QAAQ,IAAI;AAAA,MACvB;AAGA,uBACK,KAAK,MAAM;AACR,gBAAQ,IAAI;AAAA,MAChB,CAAC,EACA,MAAM,eAAe;AAAA,IAC9B,CAAC;AAAA,EACT;AAGA,SAAO;AAAA;AAAA,IAEH,SAAS;AAAA;AAAA,MAEL,yBAAyB,CAAC,MAAM,KAAK,OAAO;AAAA;AAAA,MAG5C,mBAAmB,CAAC,CAAC,GAAG,KAAK,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOlC,yBAAyB,CAAC,2BAA2B,KAAK,MAAM;AAAA;AAAA,MAGhE,oCAAoC;AAAA,QAChC;AAAA,QACA,KAAK;AAAA,MACT;AAAA;AAAA,MAGA,uCAAuC,CAAC,CAAC,GAAG,KAAK,MAAM;AAAA;AAAA,MAGvD,4BAA4B,CAAC,MAAM,KAAK,QAAQ;AAAA,IACpD;AAAA,EACJ;AACJ;AAGA,IAAM,YAAY,OAAO,WAAW,eAAe,OAAO,OAAO,aAAa;AAC9E,IAAI,WAAW;AACX,WAAS,cAAc,IAAI,YAAY,yBAAyB,EAAE,QAAQ,OAAO,CAAC,CAAC;AACvF;AAEA,IAAO,iDAAQ;", "names": []}