// @ts-ignore
import { FilePondOptions } from "filepond";

declare module "filepond" {
  export interface FilePondOptions {
    /** Enable or disable preview mode */
    allowImagePreview?: boolean;

    /** Minimum image preview height */
    imagePreviewMinHeight?: number;

    /** Maximum image preview height */
    imagePreviewMaxHeight?: number;

    /** Fixed image preview height, overrides min and max preview height */
    imagePreviewHeight?: number;

    /** Can be used to prevent loading of large images when createImageBitmap is not supported.
     * By default no maximum file size is defined, expects a string, like 2MB or 500KB. */
    imagePreviewMaxFileSize?: string;

    /** Set to 'grid' to render a transparency grid behind the image, set to a color value (for example '#f00') to set transparent image background color.
     * Please note that this is only for preview purposes, the background color or grid is not embedded in the output image. */
    imagePreviewTransparencyIndicator?: string;

    /** Maximum file size for images to preview immediately,
     * if files are larger and the browser doesn’t support createImageBitmap the preview is queued till FilePond is in rest state. */
    imagePreviewMaxInstantPreviewFileSize?: number;

    /** Set to false to hide image markup in the preview panel */
    imagePreviewMarkupShow?: boolean;

    /** Use to filter markup items, useful to show only certain items and hide others till the image file is generated by the image transform plugin */
    imagePreviewMarkupFilter?: (markupItem: any) => true;
  }
}
