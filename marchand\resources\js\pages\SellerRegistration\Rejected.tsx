import { Head, <PERSON> } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
    XCircle, 
    Mail, 
    Phone, 
    FileText, 
    RefreshCw,
    AlertTriangle,
    MessageCircle,
    Clock
} from 'lucide-react';

export default function Rejected() {
    const commonReasons = [
        {
            title: "Documents illisibles ou incomplets",
            description: "Les documents fournis ne sont pas suffisamment clairs ou il manque des informations requises"
        },
        {
            title: "Informations incohérentes",
            description: "Les informations fournies ne correspondent pas entre les différents documents"
        },
        {
            title: "Documents expirés",
            description: "Certains documents fournis ont dépassé leur date de validité"
        },
        {
            title: "Type d'activité non autorisé",
            description: "L'activité commerciale déclarée n'est pas compatible avec notre plateforme"
        }
    ];

    const nextSteps = [
        {
            icon: MessageCircle,
            title: "Contactez notre support",
            description: "Obtenez des détails sur les raisons du rejet et les corrections nécessaires"
        },
        {
            icon: FileText,
            title: "Préparez les documents corrigés",
            description: "Rassemblez les documents manquants ou corrigez ceux qui posent problème"
        },
        {
            icon: RefreshCw,
            title: "Soumettez une nouvelle demande",
            description: "Recommencez le processus d'inscription avec les documents corrigés"
        }
    ];

    return (
        <>
            <Head title="Inscription rejetée - Lorelei Seller" />
            
            <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50">
                <div className="container mx-auto px-4 py-8 max-w-4xl">
                    {/* Header */}
                    <div className="text-center mb-8">
                        <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
                            <XCircle className="w-8 h-8 text-red-600" />
                        </div>
                        <h1 className="text-3xl font-bold text-gray-900 mb-2">
                            Inscription non validée
                        </h1>
                        <p className="text-xl text-gray-600 mb-4">
                            Votre demande d'inscription marchand n'a pas pu être approuvée
                        </p>
                    </div>

                    {/* Message principal */}
                    <Alert className="mb-8 border-red-200 bg-red-50">
                        <AlertTriangle className="h-4 w-4 text-red-600" />
                        <AlertDescription className="text-red-800">
                            <strong>Ne vous découragez pas !</strong> La plupart des rejets sont dus à des documents 
                            manquants ou illisibles. Vous pouvez corriger ces problèmes et soumettre une nouvelle demande.
                        </AlertDescription>
                    </Alert>

                    {/* Raisons communes de rejet */}
                    <Card className="mb-8">
                        <CardHeader>
                            <CardTitle>Raisons communes de rejet</CardTitle>
                            <CardDescription>
                                Voici les principales raisons pour lesquelles une inscription peut être rejetée
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {commonReasons.map((reason, index) => (
                                    <div key={index} className="border-l-4 border-l-orange-400 pl-4">
                                        <h3 className="font-medium text-gray-900 mb-1">
                                            {reason.title}
                                        </h3>
                                        <p className="text-gray-600 text-sm">
                                            {reason.description}
                                        </p>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Prochaines étapes */}
                    <Card className="mb-8">
                        <CardHeader>
                            <CardTitle>Comment procéder maintenant ?</CardTitle>
                            <CardDescription>
                                Suivez ces étapes pour corriger votre dossier et soumettre une nouvelle demande
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-6">
                                {nextSteps.map((step, index) => (
                                    <div key={index} className="flex items-start space-x-4">
                                        <div className="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                                            <step.icon className="w-4 h-4" />
                                        </div>
                                        <div className="flex-1">
                                            <h3 className="font-medium text-gray-900 mb-1">
                                                {step.title}
                                            </h3>
                                            <p className="text-gray-600 text-sm">
                                                {step.description}
                                            </p>
                                        </div>
                                        <div className="text-2xl font-bold text-gray-300">
                                            {index + 1}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Conseils pour réussir */}
                    <Card className="mb-8">
                        <CardHeader>
                            <CardTitle>Conseils pour réussir votre prochaine demande</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid md:grid-cols-2 gap-6">
                                <div className="space-y-3">
                                    <h4 className="font-medium text-gray-900">📄 Documents</h4>
                                    <ul className="text-sm text-gray-600 space-y-1">
                                        <li>• Assurez-vous que tous les documents sont lisibles</li>
                                        <li>• Vérifiez les dates de validité</li>
                                        <li>• Utilisez des scans haute qualité</li>
                                        <li>• Respectez les formats demandés</li>
                                    </ul>
                                </div>
                                <div className="space-y-3">
                                    <h4 className="font-medium text-gray-900">ℹ️ Informations</h4>
                                    <ul className="text-sm text-gray-600 space-y-1">
                                        <li>• Vérifiez la cohérence entre tous les documents</li>
                                        <li>• Utilisez les mêmes noms et adresses partout</li>
                                        <li>• Fournissez des informations complètes</li>
                                        <li>• Contactez-nous en cas de doute</li>
                                    </ul>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Délai d'attente */}
                    <Alert className="mb-8">
                        <Clock className="h-4 w-4" />
                        <AlertDescription>
                            <strong>Délai d'attente :</strong> Vous pouvez soumettre une nouvelle demande immédiatement. 
                            Nous vous recommandons de contacter notre support avant de recommencer pour éviter un nouveau rejet.
                        </AlertDescription>
                    </Alert>

                    {/* Actions */}
                    <div className="text-center space-y-4">
                        <div className="space-x-4">
                            <Button asChild>
                                <a href="mailto:<EMAIL>">
                                    <Mail className="w-4 h-4 mr-2" />
                                    Contacter le support
                                </a>
                            </Button>
                            <Button variant="outline" asChild>
                                <Link href={route('seller.welcome')}>
                                    <RefreshCw className="w-4 h-4 mr-2" />
                                    Nouvelle demande
                                </Link>
                            </Button>
                        </div>
                        
                        <div className="text-sm text-gray-500 space-y-2">
                            <p>
                                <strong>Support par email :</strong>{' '}
                                <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
                                    <EMAIL>
                                </a>
                            </p>
                            <p>
                                <strong>Support téléphonique :</strong>{' '}
                                <a href="tel:+237123456789" className="text-blue-600 hover:underline">
                                    +237 123 456 789
                                </a>
                            </p>
                            <p className="text-xs">
                                Disponible du lundi au vendredi de 8h à 18h
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}
