/*!
 * FilePondPluginFileValidateSize 2.2.8
 * Licensed under MIT, https://opensource.org/licenses/MIT/
 * Please visit https://pqina.nl/filepond/ for details.
 */

/* eslint-disable */

const e=({addFilter:e,utils:E})=>{const{Type:i,replaceInString:_,toNaturalFileSize:l}=E;return e("ALLOW_HOPPER_ITEM",(e,{query:E})=>{if(!E("GET_ALLOW_FILE_SIZE_VALIDATION"))return!0;const i=E("GET_MAX_FILE_SIZE");if(null!==i&&e.size>i)return!1;const _=E("GET_MIN_FILE_SIZE");return!(null!==_&&e.size<_)}),e("LOAD_FILE",(e,{query:E})=>new Promise((i,I)=>{if(!E("GET_ALLOW_FILE_SIZE_VALIDATION"))return i(e);const L=E("GET_FILE_VALIDATE_SIZE_FILTER");if(L&&!L(e))return i(e);const t=E("GET_MAX_FILE_SIZE");if(null!==t&&e.size>t)return void I({status:{main:E("GET_LABEL_MAX_FILE_SIZE_EXCEEDED"),sub:_(E("GET_LABEL_MAX_FILE_SIZE"),{filesize:l(t,".",E("GET_FILE_SIZE_BASE"),E("GET_FILE_SIZE_LABELS",E))})}});const n=E("GET_MIN_FILE_SIZE");if(null!==n&&e.size<n)return void I({status:{main:E("GET_LABEL_MIN_FILE_SIZE_EXCEEDED"),sub:_(E("GET_LABEL_MIN_FILE_SIZE"),{filesize:l(n,".",E("GET_FILE_SIZE_BASE"),E("GET_FILE_SIZE_LABELS",E))})}});const T=E("GET_MAX_TOTAL_FILE_SIZE");if(null!==T){if(E("GET_ACTIVE_ITEMS").reduce((e,E)=>e+E.fileSize,0)>T)return void I({status:{main:E("GET_LABEL_MAX_TOTAL_FILE_SIZE_EXCEEDED"),sub:_(E("GET_LABEL_MAX_TOTAL_FILE_SIZE"),{filesize:l(T,".",E("GET_FILE_SIZE_BASE"),E("GET_FILE_SIZE_LABELS",E))})}})}i(e)})),{options:{allowFileSizeValidation:[!0,i.BOOLEAN],maxFileSize:[null,i.INT],minFileSize:[null,i.INT],maxTotalFileSize:[null,i.INT],fileValidateSizeFilter:[null,i.FUNCTION],labelMinFileSizeExceeded:["File is too small",i.STRING],labelMinFileSize:["Minimum file size is {filesize}",i.STRING],labelMaxFileSizeExceeded:["File is too large",i.STRING],labelMaxFileSize:["Maximum file size is {filesize}",i.STRING],labelMaxTotalFileSizeExceeded:["Maximum total size exceeded",i.STRING],labelMaxTotalFileSize:["Maximum total file size is {filesize}",i.STRING]}}};"undefined"!=typeof window&&void 0!==window.document&&document.dispatchEvent(new CustomEvent("FilePond:pluginloaded",{detail:e}));export default e;
