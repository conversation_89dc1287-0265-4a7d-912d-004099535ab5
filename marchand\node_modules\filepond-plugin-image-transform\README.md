# Image Transform plugin for FilePond

[![npm version](https://badge.fury.io/js/filepond-plugin-image-transform.svg)](https://badge.fury.io/js/filepond)

https://pqina.nl/filepond/docs/api/plugins/image-transform/

The Image transform plugin applies the image modifications supplied by the [Image crop](https://github.com/pqina/filepond-plugin-image-crop) and [Image resize](https://github.com/pqina/filepond-plugin-image-resize) plugins before the image is uploaded. It can also draw markup on the image and change the file format to either JPEG or PNG, plus compress the output image.

[Demo](https://pqina.github.io/filepond-plugin-image-transform/)
