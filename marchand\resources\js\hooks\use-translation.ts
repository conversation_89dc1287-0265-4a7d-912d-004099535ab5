import { useState, useEffect } from 'react';

// Importation des fichiers de traduction
import frSeller from '/lang/seller/fr.json';
import enSeller from '/lang/seller/en.json';

// Type pour les langues supportées
type Locale = 'fr' | 'en';

/**
 * Hook personnalisé pour la gestion des traductions
 *
 * @returns Objet contenant la langue actuelle, la fonction de changement de langue et la fonction de traduction
 */
export function useTranslation() {
  // État pour la langue actuelle
  const [currentLocale, setCurrentLocale] = useState<Locale>(() => {
    // Récupérer la langue depuis le localStorage ou utiliser 'fr' par défaut
    if (typeof window !== 'undefined') {
      const savedLocale = localStorage.getItem('locale') as Locale;
      return savedLocale && ['fr', 'en'].includes(savedLocale) ? savedLocale : 'fr';
    }
    return 'fr';
  });

  /**
   * Change la langue de l'application
   *
   * @param locale - Code de la langue
   */
  const setLocale = (locale: Locale) => {
    setCurrentLocale(locale);
    if (typeof window !== 'undefined') {
      localStorage.setItem('locale', locale);
    }
  };

  /**
   * Fonction utilitaire pour naviguer dans un objet avec une clé en notation pointée
   *
   * @param obj - Objet dans lequel chercher
   * @param path - Chemin en notation pointée (ex: 'header.menu')
   * @returns La valeur trouvée ou null si non trouvée
   */
  const getTranslation = (obj: Record<string, unknown>, path: string[]): string | string[] | null => {
    let current: unknown = obj;

    for (const key of path) {
      if (current && typeof current === 'object' && key in current) {
        current = (current as Record<string, unknown>)[key];
      } else {
        return null;
      }
    }

    return typeof current === 'string' || Array.isArray(current) ? current : null;
  };

  /**
   * Traduit une clé donnée dans la langue actuelle
   *
   * @param key - Clé de traduction en notation pointée (ex: 'header.menu')
   * @param params - Paramètres à remplacer dans la traduction (ex: {name: 'John'})
   * @returns Traduction ou la clé si non trouvée
   */
  const translate = (key: string, params: Record<string, string | number> = {}): string | string[] => {
    // Si la clé est vide, retourner une chaîne vide
    if (!key) {
      console.warn('Translation key is empty');
      return '';
    }

    // Fusionner les traductions des fichiers JSON importés
    const translations: Record<Locale, Record<string, unknown>> = {
      fr: frSeller,
      en: enSeller
    };

    // Diviser la clé en parties (ex: 'header.menu' -> ['header', 'menu'])
    const keyParts = key.split('.');

    // Obtenir les traductions pour la langue actuelle
    const localeTranslations = translations[currentLocale];

    if (!localeTranslations) {
      console.warn(`No translations found for locale: ${currentLocale}`);
      return key;
    }

    // Essayer de trouver la traduction dans la langue actuelle
    let translation = getTranslation(localeTranslations, keyParts);

    // Si la traduction n'existe pas dans la langue actuelle, essayer avec le français comme fallback
    if (translation === null && currentLocale !== 'fr') {
      translation = getTranslation(translations['fr'], keyParts);

      // Si toujours pas de traduction, essayer avec l'anglais comme second fallback
      if (translation === null) {
        translation = getTranslation(translations['en'], keyParts);
      }
    }

    // Si aucune traduction n'est trouvée, utiliser la clé comme fallback et logger un avertissement
    if (translation === null) {
      console.warn(`Translation missing for key: ${key}`);
      return key;
    }

    // Si c'est un tableau, le retourner directement
    if (Array.isArray(translation)) {
      return translation;
    }

    // Remplacer les paramètres dans la traduction (seulement pour les chaînes)
    let result = translation;

    // Parcourir tous les paramètres et les remplacer dans la chaîne
    for (const [paramKey, paramValue] of Object.entries(params)) {
      const regex = new RegExp(`{${paramKey}}`, 'g');
      result = result.replace(regex, String(paramValue));
    }

    return result;
  };

  /**
   * Vérifie si une clé de traduction existe
   *
   * @param key - Clé de traduction à vérifier
   * @returns true si la clé existe, false sinon
   */
  const hasTranslation = (key: string): boolean => {
    if (!key) return false;

    const translations: Record<Locale, Record<string, unknown>> = {
      fr: frSeller,
      en: enSeller
    };

    const keyParts = key.split('.');
    const localeTranslations = translations[currentLocale];

    if (!localeTranslations) return false;

    return getTranslation(localeTranslations, keyParts) !== null;
  };

  return {
    currentLocale,
    setLocale,
    translate,
    hasTranslation,
  };
}

// Export du type Locale pour utilisation dans d'autres composants
export type { Locale };
