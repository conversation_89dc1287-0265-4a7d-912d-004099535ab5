# Image Preview plugin for FilePond

[![npm version](https://badge.fury.io/js/filepond-plugin-image-preview.svg)](https://badge.fury.io/js/filepond)

https://pqina.nl/filepond/docs/patterns/plugins/image-preview/

The Image Preview plugin renders a downscaled preview for dropped images.

Combined with the [Image EXIF Orientation plugin](https://github.com/pqina/filepond-plugin-image-exif-orientation) it automatically corrects any mobile rotation information to ensure the image is alway shown correctly. Previews crop information supplied by the [Image Crop plugin](https://github.com/pqina/filepond-plugin-image-crop) Can also preview image markup, uses the [Image Resize plugin](https://github.com/pqina/filepond-plugin-image-resize) information to correctly scale the markup.

[Demo](https://pqina.github.io/filepond-plugin-image-preview/)