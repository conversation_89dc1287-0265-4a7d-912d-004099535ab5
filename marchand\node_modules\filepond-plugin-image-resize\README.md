# Image Resize plugin for FilePond

[![npm version](https://badge.fury.io/js/filepond-plugin-image-resize.svg)](https://badge.fury.io/js/filepond)

https://pqina.nl/filepond/docs/patterns/plugins/image-resize/

The Image resize plugin automatically calculates and adds resize information.

The [Image preview plugin](https://github.com/pqina/filepond-plugin-image-preview) uses this information to show the correct preview. The [Image transform plugin](https://github.com/pqina/filepond-plugin-image-transform) uses this information to transform the image before uploading it to the server.

[Demo](https://pqina.github.io/filepond-plugin-image-resize/)