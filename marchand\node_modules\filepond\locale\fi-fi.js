export default {
    labelIdle: 'Ved<PERSON> ja pudota tiedostoja tai <span class="filepond--label-action"> <PERSON><PERSON><PERSON> </span>',
    labelInvalidField: '<PERSON><PERSON><PERSON><PERSON> on virheellisiä tiedostoja',
    labelFileWaitingForSize: 'Odotetaan kokoa',
    labelFileSizeNotAvailable: 'Kokoa ei saatavilla',
    labelFileLoading: 'La<PERSON><PERSON>',
    labelFileLoadError: 'Virhe latauksessa',
    labelFileProcessing: 'Lähetetään',
    labelFileProcessingComplete: 'Lähetys valmis',
    labelFileProcessingAborted: 'Lähe<PERSON>s peruttu',
    labelFileProcessingError: 'Virhe lähetyksessä',
    labelFileProcessingRevertError: 'Virhe palautuksessa',
    labelFileRemoveError: 'Virhe poistamisessa',
    labelTapToCancel: 'peruuta napauttamalla',
    labelTapToRetry: 'yritä uudelleen napauttamalla',
    labelTapToUndo: 'kumoa napauttamalla',
    labelButtonRemoveItem: 'Poista',
    labelButtonAbortItemLoad: 'Keskeytä',
    labelButtonRetryItemLoad: 'Yritä uudelleen',
    labelButtonAbortItemProcessing: 'Peruuta',
    labelButtonUndoItemProcessing: 'Kumoa',
    labelButtonRetryItemProcessing: 'Yritä uudelleen',
    labelButtonProcessItem: 'Lähetä',
    labelMaxFileSizeExceeded: 'Tiedoston koko on liian suuri',
    labelMaxFileSize: 'Tiedoston maksimikoko on {filesize}',
    labelMaxTotalFileSizeExceeded: 'Tiedostojen yhdistetty maksimikoko ylitetty',
    labelMaxTotalFileSize: 'Tiedostojen yhdistetty maksimikoko on {filesize}',
    labelFileTypeNotAllowed: 'Tiedostotyyppiä ei sallita',
    fileValidateTypeLabelExpectedTypes: 'Sallitaan {allButLastType} tai {lastType}',
    imageValidateSizeLabelFormatError: 'Kuvatyyppiä ei tueta',
    imageValidateSizeLabelImageSizeTooSmall: 'Kuva on liian pieni',
    imageValidateSizeLabelImageSizeTooBig: 'Kuva on liian suuri',
    imageValidateSizeLabelExpectedMinSize: 'Minimikoko on {minWidth} × {minHeight}',
    imageValidateSizeLabelExpectedMaxSize: 'Maksimikoko on {maxWidth} × {maxHeight}',
    imageValidateSizeLabelImageResolutionTooLow: 'Resoluutio on liian pieni',
    imageValidateSizeLabelImageResolutionTooHigh: 'Resoluutio on liian suuri',
    imageValidateSizeLabelExpectedMinResolution: 'Minimiresoluutio on {minResolution}',
    imageValidateSizeLabelExpectedMaxResolution: 'Maksimiresoluutio on {maxResolution}'
};
