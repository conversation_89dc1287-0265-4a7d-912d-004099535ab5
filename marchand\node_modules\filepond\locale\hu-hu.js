export default {
    labelIdle: '<PERSON><PERSON><PERSON><PERSON> ide a fájlt a feltöltéshez, vagy <span class="filepond--label-action"> tallózás </span>',
    labelInvalidField: 'A mező érvénytelen fájlokat tartalmaz',
    labelFileWaitingForSize: 'Fálj<PERSON>ret kisz<PERSON>l<PERSON>',
    labelFileSizeNotAvailable: 'A fájlméret nem elérhető',
    labelFileLoading: 'Töltés',
    labelFileLoadError: 'Hiba a betöltés során',
    labelFileProcessing: 'Feltöltés',
    labelFileProcessingComplete: 'Sikeres feltöltés',
    labelFileProcessingAborted: 'A feltöltés megszakítva',
    labelFileProcessingError: 'Hiba történt a feltöltés során',
    labelFileProcessingRevertError: 'Hiba a visszaállítás során',
    labelFileRemoveError: 'Hiba történt az eltávolítás során',
    labelTapToCancel: 'koppints a törléshez',
    labelTapToRetry: 'koppints az újrakezdéshez',
    labelTapToUndo: 'koppints a visszavonáshoz',
    labelButtonRemoveItem: 'Eltávolítás',
    labelButtonAbortItemLoad: 'Megszakítás',
    labelButtonRetryItemLoad: 'Újrapróbálkozás',
    labelButtonAbortItemProcessing: 'Megszakítás',
    labelButtonUndoItemProcessing: 'Visszavonás',
    labelButtonRetryItemProcessing: 'Újrapróbálkozás',
    labelButtonProcessItem: 'Feltöltés',
    labelMaxFileSizeExceeded: 'A fájl túllépte a maximális méretet',
    labelMaxFileSize: 'Maximális fájlméret: {filesize}',
    labelMaxTotalFileSizeExceeded: 'Túllépte a maximális teljes méretet',
    labelMaxTotalFileSize: 'A maximáis teljes fájlméret: {filesize}',
    labelFileTypeNotAllowed: 'Érvénytelen típusú fájl',
    fileValidateTypeLabelExpectedTypes: 'Engedélyezett típusok {allButLastType} vagy {lastType}',
    imageValidateSizeLabelFormatError: 'A képtípus nem támogatott',
    imageValidateSizeLabelImageSizeTooSmall: 'A kép túl kicsi',
    imageValidateSizeLabelImageSizeTooBig: 'A kép túl nagy',
    imageValidateSizeLabelExpectedMinSize: 'Minimum méret: {minWidth} × {minHeight}',
    imageValidateSizeLabelExpectedMaxSize: 'Maximum méret: {maxWidth} × {maxHeight}',
    imageValidateSizeLabelImageResolutionTooLow: 'A felbontás túl alacsony',
    imageValidateSizeLabelImageResolutionTooHigh: 'A felbontás túl magas',
    imageValidateSizeLabelExpectedMinResolution: 'Minimáis felbontás: {minResolution}',
    imageValidateSizeLabelExpectedMaxResolution: 'Maximális felbontás: {maxResolution}'
  };