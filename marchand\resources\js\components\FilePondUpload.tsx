import React, { useEffect, useRef } from 'react';
import { FilePond, registerPlugin } from 'react-filepond';
import 'filepond/dist/filepond.min.css';
import 'filepond-plugin-image-preview/dist/filepond-plugin-image-preview.css';

// Import des plugins FilePond
import FilePondPluginImagePreview from 'filepond-plugin-image-preview';
import FilePondPluginFileValidateType from 'filepond-plugin-file-validate-type';
import FilePondPluginFileValidateSize from 'filepond-plugin-file-validate-size';
import FilePondPluginImageResize from 'filepond-plugin-image-resize';
import FilePondPluginImageTransform from 'filepond-plugin-image-transform';

// Enregistrer les plugins
registerPlugin(
    FilePondPluginImagePreview,
    FilePondPluginFileValidateType,
    FilePondPluginFileValidateSize,
    FilePondPluginImageResize,
    FilePondPluginImageTransform
);

interface FilePondUploadProps {
    files: any[];
    onUpdateFiles: (files: any[]) => void;
    acceptedFileTypes?: string[];
    maxFileSize?: string;
    maxFiles?: number;
    allowMultiple?: boolean;
    name?: string;
    labelIdle?: string;
    className?: string;
    disabled?: boolean;
    required?: boolean;
    imageResizeTargetWidth?: number;
    imageResizeTargetHeight?: number;
    onProcessFile?: (error: any, file: any) => void;
    onRemoveFile?: (error: any, file: any) => void;
    server?: any;
    instantUpload?: boolean;
}

export default function FilePondUpload({
    files,
    onUpdateFiles,
    acceptedFileTypes = ['image/*', 'application/pdf', '.doc', '.docx'],
    maxFileSize = '10MB',
    maxFiles = 5,
    allowMultiple = true,
    name = 'filepond',
    labelIdle = 'Glissez-déposez vos fichiers ou <span class="filepond--label-action">Parcourir</span>',
    className = '',
    disabled = false,
    required = false,
    imageResizeTargetWidth = 1920,
    imageResizeTargetHeight = 1080,
    onProcessFile,
    onRemoveFile,
    server,
    instantUpload = false
}: FilePondUploadProps) {
    const pondRef = useRef<FilePond>(null);

    // Configuration du serveur pour l'upload
    const serverConfig = server || {
        process: {
            url: '/api/upload',
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            },
            onload: (response: string) => {
                // Retourner l'ID du fichier pour pouvoir le supprimer plus tard
                return JSON.parse(response).id;
            },
            onerror: (response: string) => {
                console.error('Erreur upload:', response);
            }
        },
        revert: {
            url: '/api/upload',
            method: 'DELETE'
        },
        load: {
            url: '/api/files/'
        }
    };

    useEffect(() => {
        // Appliquer les styles CSS personnalisés pour le thème
        const style = document.createElement('style');
        style.textContent = `
            .filepond--root {
                font-family: inherit;
            }
            
            .filepond--drop-label {
                color: hsl(var(--muted-foreground));
            }
            
            .filepond--label-action {
                color: hsl(var(--primary));
                text-decoration: underline;
            }
            
            .filepond--panel-root {
                background-color: hsl(var(--background));
                border: 2px dashed hsl(var(--border));
                border-radius: calc(var(--radius) - 2px);
            }
            
            .filepond--item {
                border-radius: calc(var(--radius) - 4px);
            }
            
            .filepond--item-panel {
                background-color: hsl(var(--card));
                border: 1px solid hsl(var(--border));
            }
            
            .filepond--file-status-main {
                color: hsl(var(--foreground));
            }
            
            .filepond--file-status-sub {
                color: hsl(var(--muted-foreground));
            }
            
            .filepond--file-info-main {
                color: hsl(var(--foreground));
            }
            
            .filepond--file-info-sub {
                color: hsl(var(--muted-foreground));
            }
            
            /* Styles pour les fichiers validés (vert) */
            .filepond--item[data-filepond-item-state="processing-complete"] .filepond--item-panel {
                background-color: hsl(var(--primary) / 0.1);
                border-color: hsl(var(--primary));
            }
            
            .filepond--item[data-filepond-item-state="processing-complete"] .filepond--file-status-main {
                color: hsl(var(--primary));
            }
            
            /* Styles pour les erreurs */
            .filepond--item[data-filepond-item-state="processing-error"] .filepond--item-panel {
                background-color: hsl(var(--destructive) / 0.1);
                border-color: hsl(var(--destructive));
            }
            
            .filepond--item[data-filepond-item-state="processing-error"] .filepond--file-status-main {
                color: hsl(var(--destructive));
            }
            
            /* Boutons d'action */
            .filepond--action-remove-item {
                background-color: hsl(var(--destructive));
            }
            
            .filepond--action-remove-item:hover {
                background-color: hsl(var(--destructive) / 0.8);
            }
            
            .filepond--action-retry-item-load,
            .filepond--action-retry-item-processing {
                background-color: hsl(var(--primary));
            }
            
            .filepond--action-retry-item-load:hover,
            .filepond--action-retry-item-processing:hover {
                background-color: hsl(var(--primary) / 0.8);
            }
            
            /* Mode sombre */
            .dark .filepond--panel-root {
                background-color: hsl(var(--background));
                border-color: hsl(var(--border));
            }
            
            .dark .filepond--drop-label {
                color: hsl(var(--muted-foreground));
            }
            
            .dark .filepond--label-action {
                color: hsl(var(--primary));
            }
        `;
        document.head.appendChild(style);

        return () => {
            document.head.removeChild(style);
        };
    }, []);

    return (
        <div className={`filepond-wrapper ${className}`}>
            <FilePond
                ref={pondRef}
                files={files}
                onupdatefiles={onUpdateFiles}
                allowMultiple={allowMultiple}
                maxFiles={maxFiles}
                name={name}
                labelIdle={labelIdle}
                acceptedFileTypes={acceptedFileTypes}
                maxFileSize={maxFileSize}
                disabled={disabled}
                required={required}
                imageResizeTargetWidth={imageResizeTargetWidth}
                imageResizeTargetHeight={imageResizeTargetHeight}
                imageResizeMode="contain"
                imageResizeUpscale={false}
                server={instantUpload ? serverConfig : undefined}
                onprocessfile={onProcessFile}
                onremovefile={onRemoveFile}
                credits={false}
                // Labels en français
                labelFileWaitingForSize="En attente de taille"
                labelFileSizeNotAvailable="Taille non disponible"
                labelFileLoading="Chargement"
                labelFileLoadError="Erreur lors du chargement"
                labelFileProcessing="Upload en cours"
                labelFileProcessingComplete="Upload terminé"
                labelFileProcessingAborted="Upload annulé"
                labelFileProcessingError="Erreur lors de l'upload"
                labelFileProcessingRevertError="Erreur lors de l'annulation"
                labelFileRemoveError="Erreur lors de la suppression"
                labelTapToCancel="Appuyer pour annuler"
                labelTapToRetry="Appuyer pour réessayer"
                labelTapToUndo="Appuyer pour annuler"
                labelButtonRemoveItem="Supprimer"
                labelButtonAbortItemLoad="Annuler"
                labelButtonRetryItemLoad="Réessayer"
                labelButtonAbortItemProcessing="Annuler"
                labelButtonUndoItemProcessing="Annuler"
                labelButtonRetryItemProcessing="Réessayer"
                labelButtonProcessItem="Upload"
                // Messages d'erreur
                labelMaxFileSizeExceeded="Le fichier est trop volumineux"
                labelMaxFileSize="La taille maximale est de {filesize}"
                labelMaxTotalFileSizeExceeded="Taille totale maximale dépassée"
                labelMaxTotalFileSize="La taille totale maximale est de {filesize}"
                labelFileTypeNotAllowed="Type de fichier non autorisé"
                labelFileCountSingular="fichier dans la liste"
                labelFileCountPlural="fichiers dans la liste"
                labelDecimalSeparator=","
                labelThousandsSeparator=" "
            />
        </div>
    );
}
