{"version": 3, "sources": ["../../filepond-plugin-image-resize/dist/filepond-plugin-image-resize.esm.js"], "sourcesContent": ["/*!\n * FilePondPluginImageResize 2.0.10\n * Licensed under MIT, https://opensource.org/licenses/MIT/\n * Please visit https://pqina.nl/filepond/ for details.\n */\n\n/* eslint-disable */\n\n// test if file is of type image\nconst isImage = file => /^image/.test(file.type);\n\nconst getImageSize = (url, cb) => {\n    let image = new Image();\n    image.onload = () => {\n        const width = image.naturalWidth;\n        const height = image.naturalHeight;\n        image = null;\n        cb({ width, height });\n    };\n    image.onerror = () => cb(null);\n    image.src = url;\n};\n\n/**\n * Image Auto Resize Plugin\n */\nconst plugin = ({ addFilter, utils }) => {\n    const { Type } = utils;\n\n    // subscribe to file load and append required transformations\n    addFilter(\n        'DID_LOAD_ITEM',\n        (item, { query }) =>\n            new Promise((resolve, reject) => {\n                // get file reference\n                const file = item.file;\n\n                // if this is not an image we do not have any business cropping it\n                if (!isImage(file) || !query('GET_ALLOW_IMAGE_RESIZE')) {\n                    // continue with the unaltered dataset\n                    return resolve(item);\n                }\n\n                const mode = query('GET_IMAGE_RESIZE_MODE');\n                const width = query('GET_IMAGE_RESIZE_TARGET_WIDTH');\n                const height = query('GET_IMAGE_RESIZE_TARGET_HEIGHT');\n                const upscale = query('GET_IMAGE_RESIZE_UPSCALE');\n\n                // no resizing to be done\n                if (width === null && height === null) return resolve(item);\n\n                const targetWidth = width === null ? height : width;\n                const targetHeight = height === null ? targetWidth : height;\n\n                // if should not upscale, we need to determine the size of the file\n                const fileURL = URL.createObjectURL(file);\n                getImageSize(fileURL, size => {\n                    URL.revokeObjectURL(fileURL);\n\n                    // something went wrong\n                    if (!size) return resolve(item);\n\n                    let { width: imageWidth, height: imageHeight } = size;\n\n                    // get exif orientation\n                    const orientation = (item.getMetadata('exif') || {}).orientation || -1;\n\n                    // swap width and height if orientation needs correcting\n                    if (orientation >= 5 && orientation <= 8) {\n                        [imageWidth, imageHeight] = [imageHeight, imageWidth];\n                    }\n\n                    // image is already perfect size, no transformations required\n                    if (imageWidth === targetWidth && imageHeight === targetHeight)\n                        return resolve(item);\n\n                    // already contained?\n                    // can't upscale image, so if already at correct scale, exit\n                    if (!upscale) {\n                        // covering target size\n                        if (mode === 'cover') {\n                            // if one of edges is smaller than target size, exit\n                            if (imageWidth <= targetWidth || imageHeight <= targetHeight)\n                                return resolve(item);\n                        }\n\n                        // not covering target size, if image is contained in target size, exit\n                        else if (imageWidth <= targetWidth && imageHeight <= targetWidth) {\n                            return resolve(item);\n                        }\n                    }\n\n                    // the image needs to be resized\n                    item.setMetadata('resize', {\n                        mode,\n                        upscale,\n                        size: {\n                            width: targetWidth,\n                            height: targetHeight,\n                        },\n                    });\n\n                    resolve(item);\n                });\n            })\n    );\n\n    // Expose plugin options\n    return {\n        options: {\n            // Enable or disable image resizing\n            allowImageResize: [true, Type.BOOLEAN],\n\n            // the method of rescaling\n            // - force => force set size\n            // - cover => pick biggest dimension\n            // - contain => pick smaller dimension\n            imageResizeMode: ['cover', Type.STRING],\n\n            // set to false to disable upscaling of image smaller than the target width / height\n            imageResizeUpscale: [true, Type.BOOLEAN],\n\n            // target width\n            imageResizeTargetWidth: [null, Type.INT],\n\n            // target height\n            imageResizeTargetHeight: [null, Type.INT],\n        },\n    };\n};\n\n// fire pluginloaded event if running in browser, this allows registering the plugin when using async script tags\nconst isBrowser = typeof window !== 'undefined' && typeof window.document !== 'undefined';\nif (isBrowser) {\n    document.dispatchEvent(new CustomEvent('FilePond:pluginloaded', { detail: plugin }));\n}\n\nexport default plugin;\n"], "mappings": ";;;AASA,IAAM,UAAU,UAAQ,SAAS,KAAK,KAAK,IAAI;AAE/C,IAAM,eAAe,CAAC,KAAK,OAAO;AAC9B,MAAI,QAAQ,IAAI,MAAM;AACtB,QAAM,SAAS,MAAM;AACjB,UAAM,QAAQ,MAAM;AACpB,UAAM,SAAS,MAAM;AACrB,YAAQ;AACR,OAAG,EAAE,OAAO,OAAO,CAAC;AAAA,EACxB;AACA,QAAM,UAAU,MAAM,GAAG,IAAI;AAC7B,QAAM,MAAM;AAChB;AAKA,IAAM,SAAS,CAAC,EAAE,WAAW,MAAM,MAAM;AACrC,QAAM,EAAE,KAAK,IAAI;AAGjB;AAAA,IACI;AAAA,IACA,CAAC,MAAM,EAAE,MAAM,MACX,IAAI,QAAQ,CAAC,SAAS,WAAW;AAE7B,YAAM,OAAO,KAAK;AAGlB,UAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,MAAM,wBAAwB,GAAG;AAEpD,eAAO,QAAQ,IAAI;AAAA,MACvB;AAEA,YAAM,OAAO,MAAM,uBAAuB;AAC1C,YAAM,QAAQ,MAAM,+BAA+B;AACnD,YAAM,SAAS,MAAM,gCAAgC;AACrD,YAAM,UAAU,MAAM,0BAA0B;AAGhD,UAAI,UAAU,QAAQ,WAAW,KAAM,QAAO,QAAQ,IAAI;AAE1D,YAAM,cAAc,UAAU,OAAO,SAAS;AAC9C,YAAM,eAAe,WAAW,OAAO,cAAc;AAGrD,YAAM,UAAU,IAAI,gBAAgB,IAAI;AACxC,mBAAa,SAAS,UAAQ;AAC1B,YAAI,gBAAgB,OAAO;AAG3B,YAAI,CAAC,KAAM,QAAO,QAAQ,IAAI;AAE9B,YAAI,EAAE,OAAO,YAAY,QAAQ,YAAY,IAAI;AAGjD,cAAM,eAAe,KAAK,YAAY,MAAM,KAAK,CAAC,GAAG,eAAe;AAGpE,YAAI,eAAe,KAAK,eAAe,GAAG;AACtC,WAAC,YAAY,WAAW,IAAI,CAAC,aAAa,UAAU;AAAA,QACxD;AAGA,YAAI,eAAe,eAAe,gBAAgB;AAC9C,iBAAO,QAAQ,IAAI;AAIvB,YAAI,CAAC,SAAS;AAEV,cAAI,SAAS,SAAS;AAElB,gBAAI,cAAc,eAAe,eAAe;AAC5C,qBAAO,QAAQ,IAAI;AAAA,UAC3B,WAGS,cAAc,eAAe,eAAe,aAAa;AAC9D,mBAAO,QAAQ,IAAI;AAAA,UACvB;AAAA,QACJ;AAGA,aAAK,YAAY,UAAU;AAAA,UACvB;AAAA,UACA;AAAA,UACA,MAAM;AAAA,YACF,OAAO;AAAA,YACP,QAAQ;AAAA,UACZ;AAAA,QACJ,CAAC;AAED,gBAAQ,IAAI;AAAA,MAChB,CAAC;AAAA,IACL,CAAC;AAAA,EACT;AAGA,SAAO;AAAA,IACH,SAAS;AAAA;AAAA,MAEL,kBAAkB,CAAC,MAAM,KAAK,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,MAMrC,iBAAiB,CAAC,SAAS,KAAK,MAAM;AAAA;AAAA,MAGtC,oBAAoB,CAAC,MAAM,KAAK,OAAO;AAAA;AAAA,MAGvC,wBAAwB,CAAC,MAAM,KAAK,GAAG;AAAA;AAAA,MAGvC,yBAAyB,CAAC,MAAM,KAAK,GAAG;AAAA,IAC5C;AAAA,EACJ;AACJ;AAGA,IAAM,YAAY,OAAO,WAAW,eAAe,OAAO,OAAO,aAAa;AAC9E,IAAI,WAAW;AACX,WAAS,cAAc,IAAI,YAAY,yBAAyB,EAAE,QAAQ,OAAO,CAAC,CAAC;AACvF;AAEA,IAAO,2CAAQ;", "names": []}