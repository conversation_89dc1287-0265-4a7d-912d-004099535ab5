export default {
    labelIdle: 'ファイルをドラッグ&ドロップ又は<span class="filepond--label-action">ファイル選択</span>',
    labelInvalidField: "アップロードできないファイルが含まれています",
    labelFileWaitingForSize: "ファイルサイズを待っています",
    labelFileSizeNotAvailable: "ファイルサイズがみつかりません",
    labelFileLoading: "読込中...",
    labelFileLoadError: "読込中にエラーが発生",
    labelFileProcessing: "読込中...",
    labelFileProcessingComplete: "アップロード完了",                                                                                          
    labelFileProcessingAborted: "アップロードがキャンセルされました",                                                                           
    labelFileProcessingError: "アップロード中にエラーが発生",                                                                                 
    labelFileProcessingRevertError: "ロールバック中にエラーが発生",                                                                           
    labelFileRemoveError: "削除中にエラーが発生",                                                                                             
    labelTapToCancel: "クリックしてキャンセル",                                                                                               
    labelTapToRetry: "クリックしてもう一度お試し下さい",                                                                                      
    labelTapToUndo: "元に戻すにはタップします",                                                                                               
    labelButtonRemoveItem: "削除",                                                                                                            
    labelButtonAbortItemLoad: "中断",                                                                                                         
    labelButtonRetryItemLoad: "もう一度実行",                                                                                                 
    labelButtonAbortItemProcessing: "キャンセル",                                                                                             
    labelButtonUndoItemProcessing: "元に戻す",                                                                                                
    labelButtonRetryItemProcessing: "もう一度実行",                                                                                           
    labelButtonProcessItem: "アップロード",                                                                                                   
    labelMaxFileSizeExceeded: "ファイルサイズが大きすぎます",                                                                                 
    labelMaxFileSize: "最大ファイルサイズは {filesize} です",                                                                                 
    labelMaxTotalFileSizeExceeded: "最大合計サイズを超えました",                                                                              
    labelMaxTotalFileSize: "最大合計ファイルサイズは {filesize} です",                                                                        
    labelFileTypeNotAllowed: "無効なファイルです",                                                                                            
    fileValidateTypeLabelExpectedTypes: "サポートしているファイルは {allButLastType} 又は {lastType} です",                                   
    imageValidateSizeLabelFormatError: "サポートしていない画像です",                                                                          
    imageValidateSizeLabelImageSizeTooSmall: "画像が小さすぎます",                                                                            
    imageValidateSizeLabelImageSizeTooBig: "画像が大きすぎます",                                                                              
    imageValidateSizeLabelExpectedMinSize: "画像の最小サイズは{minWidth}×{minHeight}です",                                                   
    imageValidateSizeLabelExpectedMaxSize: "画像の最大サイズは{maxWidth} × {maxHeight}です",                                                 
    imageValidateSizeLabelImageResolutionTooLow: "画像の解像度が低すぎます",                                                                  
    imageValidateSizeLabelImageResolutionTooHigh: "画像の解像度が高すぎます",                                                                 
    imageValidateSizeLabelExpectedMinResolution: "画像の最小解像度は{minResolution}です",                                                     
    imageValidateSizeLabelExpectedMaxResolution: "画像の最大解像度は{maxResolution}です",                                                     
};
