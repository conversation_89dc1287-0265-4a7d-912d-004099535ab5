export default {
    labelIdle: 'Drag och släpp dina filer eller <span class="filepond--label-action"> Bl<PERSON>ddra </span>',
    labelInvalidField: 'Fältet innehåller felaktiga filer',
    labelFileWaitingForSize: 'V<PERSON><PERSON><PERSON> på storlek',
    labelFileSizeNotAvailable: 'Storleken finns inte tillgänglig',
    labelFileLoading: 'Laddar',
    labelFileLoadError: 'Fel under laddning',
    labelFileProcessing: 'Laddar upp',
    labelFileProcessingComplete: 'Uppladdning klar',
    labelFileProcessingAborted: 'Uppladdning avbruten',
    labelFileProcessingError: 'Fel under uppladdning',
    labelFileProcessingRevertError: 'Fel under återställning',
    labelFileRemoveError: 'Fel under borttagning',
    labelTapToCancel: 'tryck för att avbryta',
    labelTapToRetry: 'tryck för att försöka igen',
    labelTapToUndo: 'tryck för att ångra',
    labelButtonRemoveItem: 'Tabort',
    labelButtonAbortItemLoad: 'Avbryt',
    labelButtonRetryItemLoad: 'Försök igen',
    labelButtonAbortItemProcessing: 'Avbryt',
    labelButtonUndoItemProcessing: 'Ångra',
    labelButtonRetryItemProcessing: 'Försök igen',
    labelButtonProcessItem: 'Ladda upp',
    labelMaxFileSizeExceeded: 'Filen är för stor',
    labelMaxFileSize: 'Största tillåtna filstorlek är {filesize}',
    labelMaxTotalFileSizeExceeded: 'Maximal uppladdningsstorlek uppnåd',
    labelMaxTotalFileSize: 'Maximal uppladdningsstorlek är {filesize}',
    labelFileTypeNotAllowed: 'Felaktig filtyp',
    fileValidateTypeLabelExpectedTypes: 'Godkända filtyper {allButLastType} eller {lastType}',
    imageValidateSizeLabelFormatError: 'Bildtypen saknar stöd',
    imageValidateSizeLabelImageSizeTooSmall: 'Bilden är för liten',
    imageValidateSizeLabelImageSizeTooBig: 'Bilden är för stor',
    imageValidateSizeLabelExpectedMinSize: 'Minimal storlek är {minWidth} × {minHeight}',
    imageValidateSizeLabelExpectedMaxSize: 'Maximal storlek är {maxWidth} × {maxHeight}',
    imageValidateSizeLabelImageResolutionTooLow: 'Upplösningen är för låg',
    imageValidateSizeLabelImageResolutionTooHigh: 'Upplösningen är för hög',
    imageValidateSizeLabelExpectedMinResolution: 'Minsta tillåtna upplösning är {minResolution}',
    imageValidateSizeLabelExpectedMaxResolution: 'Högsta tillåtna upplösning är {maxResolution}'
};
