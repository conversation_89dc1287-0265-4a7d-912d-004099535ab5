export default {
    labelIdle: '파일을 드래그 하거나 <span class="filepond--label-action"> 찾아보기 </span>',
    labelInvalidField: '필드에 유효하지 않은 파일이 있습니다.',
    labelFileWaitingForSize: '용량 확인중',
    labelFileSizeNotAvailable: '사용할 수 없는 용량',
    labelFileLoading: '불러오는 중',
    labelFileLoadError: '파일 불러오기 실패',
    labelFileProcessing: '업로드 중',
    labelFileProcessingComplete: '업로드 성공',
    labelFileProcessingAborted: '업로드 취소됨',
    labelFileProcessingError: '파일 업로드 실패',
    labelFileProcessingRevertError: '되돌리기 실패',
    labelFileRemoveError: '제거 실패',
    labelTapToCancel: '탭하여 취소',
    labelTapToRetry: '탭하여 재시작',
    labelTapToUndo: '탭하여 실행 취소',
    labelButtonRemoveItem: '제거',
    labelButtonAbortItemLoad: '중단',
    labelButtonRetryItemLoad: '재시작',
    labelButtonAbortItemProcessing: '취소',
    labelButtonUndoItemProcessing: '실행 취소',
    labelButtonRetryItemProcessing: '재시작',
    labelButtonProcessItem: '업로드',
    labelMaxFileSizeExceeded: '파일이 너무 큽니다.',
    labelMaxFileSize: '최대 파일 용량은 {filesize} 입니다.',
    labelMaxTotalFileSizeExceeded: '최대 전체 파일 용량 초과하였습니다.',
    labelMaxTotalFileSize: '최대 전체 파일 용량은 {filesize} 입니다.',
    labelFileTypeNotAllowed: '잘못된 형식의 파일',
    fileValidateTypeLabelExpectedTypes: '{allButLastType} 또는 {lastType}',
    imageValidateSizeLabelFormatError: '지원되지 않는 이미지 유형',
    imageValidateSizeLabelImageSizeTooSmall: '이미지가 너무 작습니다.',
    imageValidateSizeLabelImageSizeTooBig: '이미지가 너무 큽니다.',
    imageValidateSizeLabelExpectedMinSize: '이미지 최소 크기는 {minWidth} × {minHeight} 입니다',
    imageValidateSizeLabelExpectedMaxSize: '이미지 최대 크기는 {maxWidth} × {maxHeight} 입니다',
    imageValidateSizeLabelImageResolutionTooLow: '해상도가 너무 낮습니다.',
    imageValidateSizeLabelImageResolutionTooHigh: '해상도가 너무 높습니다.',
    imageValidateSizeLabelExpectedMinResolution: '최소 해상도는 {minResolution} 입니다.',
    imageValidateSizeLabelExpectedMaxResolution: '최대 해상도는 {maxResolution} 입니다.'
};
