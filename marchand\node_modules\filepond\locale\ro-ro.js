export default {
    labelIdle: 'Trage și plasează fișiere sau <span class="filepond--label-action"> Caută-le </span>',
    labelInvalidField: 'Câmpul conține fișiere care nu sunt valide',
    labelFileWaitingForSize: 'În așteptarea dimensiunii',
    labelFileSizeNotAvailable: 'Dimensiunea nu este diponibilă',
    labelFileLoading: 'Se încarcă',
    labelFileLoadError: 'Eroare la încărcare',
    labelFileProcessing: 'Se încarcă',
    labelFileProcessingComplete: 'Încărcare finalizată',
    labelFileProcessingAborted: 'Încărcare anulată',
    labelFileProcessingError: 'Eroare la încărcare',
    labelFileProcessingRevertError: 'Eroare la anulare',
    labelFileRemoveError: 'Eroare la ştergere',
    labelTapToCancel: 'apasă pentru a anula',
    labelTapToRetry: 'apasă pentru a reîncerca',
    labelTapToUndo: 'apasă pentru a anula',
    labelButtonRemoveItem: 'Şterge',
    labelButtonAbortItemLoad: 'Anulează',
    labelButtonRetryItemLoad: 'Reîncearcă',
    labelButtonAbortItemProcessing: 'Anulează',
    labelButtonUndoItemProcessing: 'Anulează',
    labelButtonRetryItemProcessing: 'Reîncearcă',
    labelButtonProcessItem: 'Încarcă',
    labelMaxFileSizeExceeded: 'Fișierul este prea mare',
    labelMaxFileSize: 'Dimensiunea maximă a unui fișier este de {filesize}',
    labelMaxTotalFileSizeExceeded: 'Dimensiunea totală maximă a fost depășită',
    labelMaxTotalFileSize: 'Dimensiunea totală maximă a fișierelor este de {filesize}',
    labelFileTypeNotAllowed: 'Tipul fișierului nu este valid',
    fileValidateTypeLabelExpectedTypes: 'Se așteaptă {allButLastType} sau {lastType}',
    imageValidateSizeLabelFormatError: 'Formatul imaginii nu este acceptat',
    imageValidateSizeLabelImageSizeTooSmall: 'Imaginea este prea mică',
    imageValidateSizeLabelImageSizeTooBig: 'Imaginea este prea mare',
    imageValidateSizeLabelExpectedMinSize: 'Mărimea minimă este de {maxWidth} x {maxHeight}',
    imageValidateSizeLabelExpectedMaxSize: 'Mărimea maximă este de {maxWidth} x {maxHeight}',
    imageValidateSizeLabelImageResolutionTooLow: 'Rezoluția este prea mică',
    imageValidateSizeLabelImageResolutionTooHigh: 'Rezoluția este prea mare',
    imageValidateSizeLabelExpectedMinResolution: 'Rezoluția minimă este de {minResolution}',
    imageValidateSizeLabelExpectedMaxResolution: 'Rezoluția maximă este de {maxResolution}'
};
