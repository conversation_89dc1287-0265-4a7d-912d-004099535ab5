export default {
    labelIdle: 'Įdėkite failus čia arba <span class="filepond--label-action"> Ieškokite </span>',
    labelInvalidField: 'Laukelis talpina netinkamus failus',
    labelFileWaitingForSize: 'Laukiama dydžio',
    labelFileSizeNotAvailable: '<PERSON><PERSON><PERSON>',
    labelFileLoading: '<PERSON>ra<PERSON><PERSON>',
    labelFileLoadError: 'Klaida įkeliant',
    labelFileProcessing: 'Įkeliama',
    labelFileProcessingComplete: 'Įkėlimas sėkmingas',
    labelFileProcessingAborted: 'Įkėlimas atšauktas',
    labelFileProcessingError: 'Įkeliant įvyko klaida',
    labelFileProcessingRevertError: 'Atšaukiant įvyko klaida',
    labelFileRemoveError: 'Ištrinant įvyko klaida',
    labelTapToCancel: 'Palieskite norėdami atšaukti',
    labelTapToRetry: 'Palieskite norėdami pakartoti',
    labelTapToUndo: 'Palieskite norėdami at<PERSON><PERSON>ti',
    labelButtonRemoveItem: 'I<PERSON><PERSON><PERSON>',
    labelButtonAbortItemLoad: 'Sustab<PERSON><PERSON>',
    labelButtonRetryItemLoad: 'Pakartoti',
    labelButtonAbortItemProcessing: 'Atšaukti',
    labelButtonUndoItemProcessing: 'Atšaukti',
    labelButtonRetryItemProcessing: 'Pakartoti',
    labelButtonProcessItem: 'Įkelti',
    labelMaxFileSizeExceeded: 'Failas per didelis',
    labelMaxFileSize: 'Maksimalus failo dydis yra {filesize}',
    labelMaxTotalFileSizeExceeded: 'Viršijote maksimalų leistiną dydį',
    labelMaxTotalFileSize: 'Maksimalus leistinas dydis yra {filesize}',
    labelFileTypeNotAllowed: 'Netinkamas failas',
    fileValidateTypeLabelExpectedTypes: 'Tikisi {allButLastType} arba {lastType}',
    imageValidateSizeLabelFormatError: 'Nuotraukos formatas nepalaikomas',
    imageValidateSizeLabelImageSizeTooSmall: 'Nuotrauka per maža',
    imageValidateSizeLabelImageSizeTooBig: 'Nuotrauka per didelė',
    imageValidateSizeLabelExpectedMinSize: 'Minimalus dydis yra {minWidth} × {minHeight}',
    imageValidateSizeLabelExpectedMaxSize: 'Maksimalus dydis yra {maxWidth} × {maxHeight}',
    imageValidateSizeLabelImageResolutionTooLow: 'Rezoliucija per maža',
    imageValidateSizeLabelImageResolutionTooHigh: 'Rezoliucija per didelė',
    imageValidateSizeLabelExpectedMinResolution: 'Minimali rezoliucija yra {minResolution}',
    imageValidateSizeLabelExpectedMaxResolution: 'Maksimali rezoliucija yra {maxResolution}'
};
