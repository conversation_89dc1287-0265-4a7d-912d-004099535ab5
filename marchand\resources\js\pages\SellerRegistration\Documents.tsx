import { Head, useForm } from '@inertiajs/react';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
    Upload, 
    FileText, 
    CheckCircle, 
    AlertCircle, 
    X, 
    Eye,
    Download
} from 'lucide-react';

interface Document {
    id: number;
    type_document: string;
    nom_original: string;
    statut_validation: string;
    date_upload: string;
    taille_fichier: number;
}

interface Props {
    marchand: {
        id: number;
        nomEntreprise: string;
        type_business: string;
        etape_inscription: string;
    };
    documentsRequis: string[];
    documentsExistants: Record<string, Document>;
    typesDocuments: Record<string, string>;
    extensionsAutorisees: string[];
    tailleMaximale: number;
}

export default function Documents({ 
    marchand, 
    documentsRequis, 
    documentsExistants, 
    typesDocuments,
    extensionsAutorisees,
    tailleMaximale 
}: Props) {
    const [uploadingFiles, setUploadingFiles] = useState<Record<string, boolean>>({});
    const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});
    const [errors, setErrors] = useState<Record<string, string>>({});

    const { post, processing } = useForm();

    const handleFileUpload = async (typeDocument: string, file: File) => {
        // Validation côté client
        const maxSize = tailleMaximale;
        const allowedExtensions = extensionsAutorisees;
        const fileExtension = file.name.split('.').pop()?.toLowerCase();

        if (file.size > maxSize) {
            setErrors(prev => ({
                ...prev,
                [typeDocument]: `Le fichier est trop volumineux. Taille maximale: ${(maxSize / 1024 / 1024).toFixed(1)}MB`
            }));
            return;
        }

        if (!fileExtension || !allowedExtensions.includes(fileExtension)) {
            setErrors(prev => ({
                ...prev,
                [typeDocument]: `Format non autorisé. Formats acceptés: ${allowedExtensions.join(', ')}`
            }));
            return;
        }

        setUploadingFiles(prev => ({ ...prev, [typeDocument]: true }));
        setUploadProgress(prev => ({ ...prev, [typeDocument]: 0 }));
        setErrors(prev => ({ ...prev, [typeDocument]: '' }));

        const formData = new FormData();
        formData.append('fichier', file);
        formData.append('type_document', typeDocument);

        try {
            // Simulation du progrès d'upload
            const progressInterval = setInterval(() => {
                setUploadProgress(prev => {
                    const current = prev[typeDocument] || 0;
                    if (current < 90) {
                        return { ...prev, [typeDocument]: current + 10 };
                    }
                    return prev;
                });
            }, 200);

            const response = await fetch(route('seller.documents.upload'), {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
            });

            clearInterval(progressInterval);
            setUploadProgress(prev => ({ ...prev, [typeDocument]: 100 }));

            if (response.ok) {
                const result = await response.json();
                // Recharger la page pour afficher le nouveau document
                window.location.reload();
            } else {
                const errorData = await response.json();
                setErrors(prev => ({
                    ...prev,
                    [typeDocument]: errorData.error || 'Erreur lors de l\'upload'
                }));
            }
        } catch (error) {
            setErrors(prev => ({
                ...prev,
                [typeDocument]: 'Erreur de connexion'
            }));
        } finally {
            setUploadingFiles(prev => ({ ...prev, [typeDocument]: false }));
            setTimeout(() => {
                setUploadProgress(prev => ({ ...prev, [typeDocument]: 0 }));
            }, 2000);
        }
    };

    const getDocumentStatus = (typeDocument: string) => {
        const doc = documentsExistants[typeDocument];
        if (!doc) return 'missing';
        return doc.statut_validation;
    };

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'valide':
                return <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Validé</Badge>;
            case 'en_attente':
                return <Badge className="bg-yellow-100 text-yellow-800"><AlertCircle className="w-3 h-3 mr-1" />En attente</Badge>;
            case 'rejete':
                return <Badge className="bg-red-100 text-red-800"><X className="w-3 h-3 mr-1" />Rejeté</Badge>;
            default:
                return <Badge variant="outline">Non fourni</Badge>;
        }
    };

    const formatFileSize = (bytes: number) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    const allDocumentsUploaded = documentsRequis.every(type => 
        documentsExistants[type] && documentsExistants[type].statut_validation !== 'rejete'
    );

    const handleFinalize = () => {
        post(route('seller.finalize'));
    };

    return (
        <>
            <Head title="Documents - Inscription Marchand" />
            
            <div className="min-h-screen bg-gray-50 py-8">
                <div className="container mx-auto px-4 max-w-4xl">
                    {/* Progress Bar */}
                    <div className="mb-8">
                        <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium text-gray-600">Étape 2 sur 3</span>
                            <span className="text-sm text-gray-500">Documents</span>
                        </div>
                        <Progress value={66} className="h-2" />
                    </div>

                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center space-x-2">
                                <FileText className="w-5 h-5" />
                                <span>Documents requis</span>
                            </CardTitle>
                            <CardDescription>
                                Uploadez les documents nécessaires pour valider votre inscription
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            {/* Instructions */}
                            <Alert>
                                <AlertCircle className="h-4 w-4" />
                                <AlertDescription>
                                    <strong>Formats acceptés :</strong> {extensionsAutorisees.join(', ')} • 
                                    <strong> Taille max :</strong> {(tailleMaximale / 1024 / 1024).toFixed(1)}MB
                                </AlertDescription>
                            </Alert>

                            {/* Liste des documents */}
                            <div className="space-y-4">
                                {documentsRequis.map((typeDocument) => {
                                    const document = documentsExistants[typeDocument];
                                    const status = getDocumentStatus(typeDocument);
                                    const isUploading = uploadingFiles[typeDocument];
                                    const progress = uploadProgress[typeDocument] || 0;
                                    const error = errors[typeDocument];

                                    return (
                                        <Card key={typeDocument} className="border-l-4 border-l-blue-500">
                                            <CardContent className="p-4">
                                                <div className="flex items-center justify-between">
                                                    <div className="flex-1">
                                                        <h3 className="font-medium text-gray-900">
                                                            {typesDocuments[typeDocument] || typeDocument}
                                                        </h3>
                                                        {document && (
                                                            <div className="mt-1 text-sm text-gray-500">
                                                                {document.nom_original} • {formatFileSize(document.taille_fichier)}
                                                            </div>
                                                        )}
                                                        {error && (
                                                            <div className="mt-1 text-sm text-red-600">{error}</div>
                                                        )}
                                                    </div>
                                                    
                                                    <div className="flex items-center space-x-3">
                                                        {getStatusBadge(status)}
                                                        
                                                        {document && (
                                                            <div className="flex space-x-2">
                                                                <Button size="sm" variant="outline">
                                                                    <Eye className="w-4 h-4" />
                                                                </Button>
                                                                <Button size="sm" variant="outline">
                                                                    <Download className="w-4 h-4" />
                                                                </Button>
                                                            </div>
                                                        )}
                                                        
                                                        <div>
                                                            <input
                                                                type="file"
                                                                id={`file-${typeDocument}`}
                                                                className="hidden"
                                                                accept={extensionsAutorisees.map(ext => `.${ext}`).join(',')}
                                                                onChange={(e) => {
                                                                    const file = e.target.files?.[0];
                                                                    if (file) {
                                                                        handleFileUpload(typeDocument, file);
                                                                    }
                                                                }}
                                                                disabled={isUploading}
                                                            />
                                                            <Button
                                                                size="sm"
                                                                onClick={() => document.getElementById(`file-${typeDocument}`)?.click()}
                                                                disabled={isUploading}
                                                            >
                                                                <Upload className="w-4 h-4 mr-2" />
                                                                {document ? 'Remplacer' : 'Upload'}
                                                            </Button>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                {isUploading && (
                                                    <div className="mt-3">
                                                        <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
                                                            <span>Upload en cours...</span>
                                                            <span>{progress}%</span>
                                                        </div>
                                                        <Progress value={progress} className="h-2" />
                                                    </div>
                                                )}
                                            </CardContent>
                                        </Card>
                                    );
                                })}
                            </div>

                            {/* Actions */}
                            <div className="flex justify-between pt-6">
                                <Button type="button" variant="outline" asChild>
                                    <a href={route('seller.business-info')}>Retour</a>
                                </Button>
                                
                                <Button 
                                    onClick={handleFinalize}
                                    disabled={!allDocumentsUploaded || processing}
                                >
                                    {processing ? 'Finalisation...' : 'Finaliser l\'inscription'}
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </>
    );
}
