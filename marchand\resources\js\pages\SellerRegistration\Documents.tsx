import { Head, useForm } from '@inertiajs/react';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
    FileText,
    CheckCircle,
    AlertCircle,
    X,
    Eye,
    Download
} from 'lucide-react';
import { useTranslation } from '@/hooks/use-translation';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import AppearanceToggleDropdown from '@/components/appearance-dropdown';
import FilePondUpload from '@/components/FilePondUpload';

interface Document {
    id: number;
    type_document: string;
    nom_original: string;
    statut_validation: string;
    date_upload: string;
    taille_fichier: number;
}

interface Props {
    marchand: {
        id: number;
        nomEntreprise: string;
        type_business: string;
        etape_inscription: string;
    };
    documentsRequis: string[];
    documentsExistants: Record<string, Document>;
    typesDocuments: Record<string, string>;
    extensionsAutorisees: string[];
    tailleMaximale: number;
}

export default function Documents({
    marchand,
    documentsRequis,
    documentsExistants,
    typesDocuments,
    extensionsAutorisees,
    tailleMaximale
}: Props) {
    const { translate } = useTranslation();
    const [files, setFiles] = useState<Record<string, any[]>>({});
    const [errors, setErrors] = useState<Record<string, string>>({});

    const { post, processing } = useForm();

    const handleFilesUpdate = (typeDocument: string, fileItems: any[]) => {
        setFiles(prev => ({
            ...prev,
            [typeDocument]: fileItems
        }));

        // Effacer les erreurs quand un fichier est ajouté
        if (fileItems.length > 0) {
            setErrors(prev => ({
                ...prev,
                [typeDocument]: ''
            }));
        }
    };

    const handleProcessFile = (typeDocument: string) => (error: any, file: any) => {
        if (error) {
            setErrors(prev => ({
                ...prev,
                [typeDocument]: translate('seller.documents.upload_error')
            }));
            return;
        }

        // Fichier uploadé avec succès
        console.log('Fichier uploadé:', file);
        // Ici vous pouvez mettre à jour l'état ou recharger les données
    };

    const getServerConfig = (typeDocument: string) => ({
        process: {
            url: route('seller.documents.upload'),
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            },
            ondata: (formData: FormData) => {
                formData.append('type_document', typeDocument);
                return formData;
            },
            onload: (response: string) => {
                try {
                    const result = JSON.parse(response);
                    return result.id;
                } catch (e) {
                    return response;
                }
            },
            onerror: (response: string) => {
                setErrors(prev => ({
                    ...prev,
                    [typeDocument]: translate('seller.documents.upload_error')
                }));
            }
        },
        revert: {
            url: route('seller.documents.upload'),
            method: 'DELETE'
        }
    });

    const getDocumentStatus = (typeDocument: string) => {
        const doc = documentsExistants[typeDocument];
        if (!doc) return 'missing';
        return doc.statut_validation;
    };

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'valide':
                return (
                    <Badge className="bg-green-500/10 text-green-700 border-green-500/20 dark:text-green-400">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        {translate('seller.documents.status.valid')}
                    </Badge>
                );
            case 'en_attente':
                return (
                    <Badge className="bg-yellow-500/10 text-yellow-700 border-yellow-500/20 dark:text-yellow-400">
                        <AlertCircle className="w-3 h-3 mr-1" />
                        {translate('seller.documents.status.pending')}
                    </Badge>
                );
            case 'rejete':
                return (
                    <Badge className="bg-red-500/10 text-red-700 border-red-500/20 dark:text-red-400">
                        <X className="w-3 h-3 mr-1" />
                        {translate('seller.documents.status.rejected')}
                    </Badge>
                );
            default:
                return (
                    <Badge variant="outline" className="text-muted-foreground">
                        {translate('seller.documents.status.missing')}
                    </Badge>
                );
        }
    };

    const formatFileSize = (bytes: number) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    const allDocumentsUploaded = documentsRequis.every(type =>
        documentsExistants[type] && documentsExistants[type].statut_validation !== 'rejete'
    );

    const handleFinalize = () => {
        post(route('seller.finalize'));
    };

    return (
        <>
            <Head title={translate('seller.documents.title')} />

            <div className="min-h-screen bg-background text-foreground">
                {/* Header avec contrôles */}
                <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                    <div className="container mx-auto px-4">
                        <nav className="flex h-16 items-center justify-between">
                            <div className="flex items-center space-x-2">
                                <span className="text-2xl font-bold text-primary">Lorelei</span>
                                <span className="text-xl font-medium text-muted-foreground">Marchand</span>
                            </div>
                            <div className="flex items-center space-x-2">
                                <LanguageSwitcher />
                                <AppearanceToggleDropdown />
                            </div>
                        </nav>
                    </div>
                </header>

                <div className="container mx-auto px-4 py-8 max-w-4xl">
                    {/* Progress Bar */}
                    <div className="mb-8">
                        <div className="flex items-center justify-center mb-6">
                            <div className="flex items-center space-x-4">
                                {/* Étape 1 - Complétée */}
                                <div className="flex flex-col items-center">
                                    <div className="w-10 h-10 bg-primary text-primary-foreground rounded-full flex items-center justify-center">
                                        ✓
                                    </div>
                                    <span className="text-sm font-medium text-foreground mt-2">
                                        {translate('seller.steps.information')}
                                    </span>
                                </div>
                                <div className="w-16 h-0.5 bg-primary"></div>

                                {/* Étape 2 - Complétée */}
                                <div className="flex flex-col items-center">
                                    <div className="w-10 h-10 bg-primary text-primary-foreground rounded-full flex items-center justify-center">
                                        ✓
                                    </div>
                                    <span className="text-sm font-medium text-foreground mt-2">
                                        {translate('seller.steps.billing')}
                                    </span>
                                </div>
                                <div className="w-16 h-0.5 bg-primary"></div>

                                {/* Étape 3 - Complétée */}
                                <div className="flex flex-col items-center">
                                    <div className="w-10 h-10 bg-primary text-primary-foreground rounded-full flex items-center justify-center">
                                        ✓
                                    </div>
                                    <span className="text-sm font-medium text-foreground mt-2">
                                        {translate('seller.steps.store')}
                                    </span>
                                </div>
                                <div className="w-16 h-0.5 bg-primary"></div>

                                {/* Étape 4 - Active */}
                                <div className="flex flex-col items-center">
                                    <div className="w-10 h-10 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-semibold">
                                        4
                                    </div>
                                    <span className="text-sm font-medium text-foreground mt-2">
                                        {translate('seller.steps.verification')}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <Progress value={100} className="h-2" />
                    </div>

                    <Card className="border-border bg-card">
                        <CardHeader>
                            <CardTitle className="flex items-center space-x-2 text-card-foreground">
                                <FileText className="w-5 h-5 text-primary" />
                                <span>{translate('seller.documents.required_title')}</span>
                            </CardTitle>
                            <CardDescription className="text-muted-foreground">
                                {translate('seller.documents.required_description')}
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            {/* Instructions */}
                            <Alert className="border-primary/20 bg-primary/5">
                                <AlertCircle className="h-4 w-4 text-primary" />
                                <AlertDescription className="text-foreground">
                                    <strong>{translate('seller.documents.accepted_formats')}:</strong> {extensionsAutorisees.join(', ')} •
                                    <strong> {translate('seller.documents.max_size')}:</strong> {(tailleMaximale / 1024 / 1024).toFixed(1)}MB
                                </AlertDescription>
                            </Alert>

                            {/* Liste des documents */}
                            <div className="space-y-6">
                                {documentsRequis.map((typeDocument) => {
                                    const document = documentsExistants[typeDocument];
                                    const status = getDocumentStatus(typeDocument);
                                    const error = errors[typeDocument];
                                    const currentFiles = files[typeDocument] || [];

                                    return (
                                        <Card key={typeDocument} className={`border-l-4 transition-all duration-300 ${
                                            status === 'valide'
                                                ? 'border-l-green-500 bg-green-50/50 dark:bg-green-950/20'
                                                : status === 'rejete'
                                                ? 'border-l-red-500 bg-red-50/50 dark:bg-red-950/20'
                                                : 'border-l-primary bg-card'
                                        }`}>
                                            <CardContent className="p-6">
                                                <div className="space-y-4">
                                                    {/* En-tête du document */}
                                                    <div className="flex items-center justify-between">
                                                        <div className="flex-1">
                                                            <h3 className="font-medium text-card-foreground text-lg">
                                                                {typesDocuments[typeDocument] || typeDocument}
                                                            </h3>
                                                            {document && (
                                                                <div className="mt-1 text-sm text-muted-foreground">
                                                                    {document.nom_original} • {formatFileSize(document.taille_fichier)}
                                                                </div>
                                                            )}
                                                        </div>

                                                        <div className="flex items-center space-x-3">
                                                            {getStatusBadge(status)}

                                                            {document && (
                                                                <div className="flex space-x-2">
                                                                    <Button size="sm" variant="outline">
                                                                        <Eye className="w-4 h-4" />
                                                                    </Button>
                                                                    <Button size="sm" variant="outline">
                                                                        <Download className="w-4 h-4" />
                                                                    </Button>
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>

                                                    {/* Zone d'upload FilePond */}
                                                    <div className="space-y-2">
                                                        <FilePondUpload
                                                            files={currentFiles}
                                                            onUpdateFiles={(fileItems) => handleFilesUpdate(typeDocument, fileItems)}
                                                            acceptedFileTypes={extensionsAutorisees.map(ext => `.${ext}`)}
                                                            maxFileSize={`${Math.round(tailleMaximale / 1024 / 1024)}MB`}
                                                            maxFiles={1}
                                                            allowMultiple={false}
                                                            name={`document-${typeDocument}`}
                                                            labelIdle={`${translate('seller.documents.drag_drop')} <span class="filepond--label-action">${translate('seller.documents.browse')}</span>`}
                                                            server={getServerConfig(typeDocument)}
                                                            instantUpload={true}
                                                            onProcessFile={handleProcessFile(typeDocument)}
                                                            className="filepond-document"
                                                        />

                                                        {error && (
                                                            <div className="text-sm text-red-600 dark:text-red-400 mt-2">
                                                                {error}
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>
                                            </CardContent>
                                        </Card>
                                    );
                                })}
                            </div>

                            {/* Actions */}
                            <div className="flex justify-between pt-6 border-t border-border">
                                <Button type="button" variant="outline" asChild>
                                    <a href={route('seller.store')}>
                                        {translate('seller.documents.back')}
                                    </a>
                                </Button>

                                <Button
                                    onClick={handleFinalize}
                                    disabled={!allDocumentsUploaded || processing}
                                    className="min-w-[200px]"
                                >
                                    {processing
                                        ? translate('seller.documents.finalizing')
                                        : translate('seller.documents.finalize')
                                    }
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </>
    );
}
