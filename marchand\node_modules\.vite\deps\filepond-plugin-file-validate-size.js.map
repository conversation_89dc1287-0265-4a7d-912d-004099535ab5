{"version": 3, "sources": ["../../filepond-plugin-file-validate-size/dist/filepond-plugin-file-validate-size.esm.js"], "sourcesContent": ["/*!\n * FilePondPluginFileValidateSize 2.2.8\n * Licensed under MIT, https://opensource.org/licenses/MIT/\n * Please visit https://pqina.nl/filepond/ for details.\n */\n\n/* eslint-disable */\n\nconst plugin = ({ addFilter, utils }) => {\n    // get quick reference to Type utils\n    const { Type, replaceInString, toNaturalFileSize } = utils;\n\n    // filtering if an item is allowed in hopper\n    addFilter('ALLOW_HOPPER_ITEM', (file, { query }) => {\n        if (!query('GET_ALLOW_FILE_SIZE_VALIDATION')) {\n            return true;\n        }\n\n        const sizeMax = query('GET_MAX_FILE_SIZE');\n        if (sizeMax !== null && file.size > sizeMax) {\n            return false;\n        }\n\n        const sizeMin = query('GET_MIN_FILE_SIZE');\n        if (sizeMin !== null && file.size < sizeMin) {\n            return false;\n        }\n\n        return true;\n    });\n\n    // called for each file that is loaded\n    // right before it is set to the item state\n    // should return a promise\n    addFilter(\n        'LOAD_FILE',\n        (file, { query }) =>\n            new Promise((resolve, reject) => {\n                // if not allowed, all fine, exit\n                if (!query('GET_ALLOW_FILE_SIZE_VALIDATION')) {\n                    return resolve(file);\n                }\n\n                // check if file should be filtered\n                const fileFilter = query('GET_FILE_VALIDATE_SIZE_FILTER');\n                if (fileFilter && !fileFilter(file)) {\n                    return resolve(file);\n                }\n\n                // reject or resolve based on file size\n                const sizeMax = query('GET_MAX_FILE_SIZE');\n                if (sizeMax !== null && file.size > sizeMax) {\n                    reject({\n                        status: {\n                            main: query('GET_LABEL_MAX_FILE_SIZE_EXCEEDED'),\n                            sub: replaceInString(query('GET_LABEL_MAX_FILE_SIZE'), {\n                                filesize: toNaturalFileSize(\n                                    sizeMax,\n                                    '.',\n                                    query('GET_FILE_SIZE_BASE'),\n                                    query('GET_FILE_SIZE_LABELS', query)\n                                ),\n                            }),\n                        },\n                    });\n                    return;\n                }\n\n                // reject or resolve based on file size\n                const sizeMin = query('GET_MIN_FILE_SIZE');\n                if (sizeMin !== null && file.size < sizeMin) {\n                    reject({\n                        status: {\n                            main: query('GET_LABEL_MIN_FILE_SIZE_EXCEEDED'),\n                            sub: replaceInString(query('GET_LABEL_MIN_FILE_SIZE'), {\n                                filesize: toNaturalFileSize(\n                                    sizeMin,\n                                    '.',\n                                    query('GET_FILE_SIZE_BASE'),\n                                    query('GET_FILE_SIZE_LABELS', query)\n                                ),\n                            }),\n                        },\n                    });\n                    return;\n                }\n\n                // returns the current option value\n                const totalSizeMax = query('GET_MAX_TOTAL_FILE_SIZE');\n                if (totalSizeMax !== null) {\n                    // get the current total file size\n                    const currentTotalSize = query('GET_ACTIVE_ITEMS').reduce((total, item) => {\n                        return total + item.fileSize;\n                    }, 0);\n\n                    // get the size of the new file\n                    if (currentTotalSize > totalSizeMax) {\n                        reject({\n                            status: {\n                                main: query('GET_LABEL_MAX_TOTAL_FILE_SIZE_EXCEEDED'),\n                                sub: replaceInString(query('GET_LABEL_MAX_TOTAL_FILE_SIZE'), {\n                                    filesize: toNaturalFileSize(\n                                        totalSizeMax,\n                                        '.',\n                                        query('GET_FILE_SIZE_BASE'),\n                                        query('GET_FILE_SIZE_LABELS', query)\n                                    ),\n                                }),\n                            },\n                        });\n                        return;\n                    }\n                }\n\n                // file is fine, let's pass it back\n                resolve(file);\n            })\n    );\n\n    return {\n        options: {\n            // Enable or disable file type validation\n            allowFileSizeValidation: [true, Type.BOOLEAN],\n\n            // Max individual file size in bytes\n            maxFileSize: [null, Type.INT],\n\n            // Min individual file size in bytes\n            minFileSize: [null, Type.INT],\n\n            // Max total file size in bytes\n            maxTotalFileSize: [null, Type.INT],\n\n            // Filter the files that need to be validated for size\n            fileValidateSizeFilter: [null, Type.FUNCTION],\n\n            // error labels\n            labelMinFileSizeExceeded: ['File is too small', Type.STRING],\n            labelMinFileSize: ['Minimum file size is {filesize}', Type.STRING],\n\n            labelMaxFileSizeExceeded: ['File is too large', Type.STRING],\n            labelMaxFileSize: ['Maximum file size is {filesize}', Type.STRING],\n\n            labelMaxTotalFileSizeExceeded: ['Maximum total size exceeded', Type.STRING],\n            labelMaxTotalFileSize: ['Maximum total file size is {filesize}', Type.STRING],\n        },\n    };\n};\n\n// fire pluginloaded event if running in browser, this allows registering the plugin when using async script tags\nconst isBrowser = typeof window !== 'undefined' && typeof window.document !== 'undefined';\nif (isBrowser) {\n    document.dispatchEvent(new CustomEvent('FilePond:pluginloaded', { detail: plugin }));\n}\n\nexport default plugin;\n"], "mappings": ";;;AAQA,IAAM,SAAS,CAAC,EAAE,WAAW,MAAM,MAAM;AAErC,QAAM,EAAE,MAAM,iBAAiB,kBAAkB,IAAI;AAGrD,YAAU,qBAAqB,CAAC,MAAM,EAAE,MAAM,MAAM;AAChD,QAAI,CAAC,MAAM,gCAAgC,GAAG;AAC1C,aAAO;AAAA,IACX;AAEA,UAAM,UAAU,MAAM,mBAAmB;AACzC,QAAI,YAAY,QAAQ,KAAK,OAAO,SAAS;AACzC,aAAO;AAAA,IACX;AAEA,UAAM,UAAU,MAAM,mBAAmB;AACzC,QAAI,YAAY,QAAQ,KAAK,OAAO,SAAS;AACzC,aAAO;AAAA,IACX;AAEA,WAAO;AAAA,EACX,CAAC;AAKD;AAAA,IACI;AAAA,IACA,CAAC,MAAM,EAAE,MAAM,MACX,IAAI,QAAQ,CAAC,SAAS,WAAW;AAE7B,UAAI,CAAC,MAAM,gCAAgC,GAAG;AAC1C,eAAO,QAAQ,IAAI;AAAA,MACvB;AAGA,YAAM,aAAa,MAAM,+BAA+B;AACxD,UAAI,cAAc,CAAC,WAAW,IAAI,GAAG;AACjC,eAAO,QAAQ,IAAI;AAAA,MACvB;AAGA,YAAM,UAAU,MAAM,mBAAmB;AACzC,UAAI,YAAY,QAAQ,KAAK,OAAO,SAAS;AACzC,eAAO;AAAA,UACH,QAAQ;AAAA,YACJ,MAAM,MAAM,kCAAkC;AAAA,YAC9C,KAAK,gBAAgB,MAAM,yBAAyB,GAAG;AAAA,cACnD,UAAU;AAAA,gBACN;AAAA,gBACA;AAAA,gBACA,MAAM,oBAAoB;AAAA,gBAC1B,MAAM,wBAAwB,KAAK;AAAA,cACvC;AAAA,YACJ,CAAC;AAAA,UACL;AAAA,QACJ,CAAC;AACD;AAAA,MACJ;AAGA,YAAM,UAAU,MAAM,mBAAmB;AACzC,UAAI,YAAY,QAAQ,KAAK,OAAO,SAAS;AACzC,eAAO;AAAA,UACH,QAAQ;AAAA,YACJ,MAAM,MAAM,kCAAkC;AAAA,YAC9C,KAAK,gBAAgB,MAAM,yBAAyB,GAAG;AAAA,cACnD,UAAU;AAAA,gBACN;AAAA,gBACA;AAAA,gBACA,MAAM,oBAAoB;AAAA,gBAC1B,MAAM,wBAAwB,KAAK;AAAA,cACvC;AAAA,YACJ,CAAC;AAAA,UACL;AAAA,QACJ,CAAC;AACD;AAAA,MACJ;AAGA,YAAM,eAAe,MAAM,yBAAyB;AACpD,UAAI,iBAAiB,MAAM;AAEvB,cAAM,mBAAmB,MAAM,kBAAkB,EAAE,OAAO,CAAC,OAAO,SAAS;AACvE,iBAAO,QAAQ,KAAK;AAAA,QACxB,GAAG,CAAC;AAGJ,YAAI,mBAAmB,cAAc;AACjC,iBAAO;AAAA,YACH,QAAQ;AAAA,cACJ,MAAM,MAAM,wCAAwC;AAAA,cACpD,KAAK,gBAAgB,MAAM,+BAA+B,GAAG;AAAA,gBACzD,UAAU;AAAA,kBACN;AAAA,kBACA;AAAA,kBACA,MAAM,oBAAoB;AAAA,kBAC1B,MAAM,wBAAwB,KAAK;AAAA,gBACvC;AAAA,cACJ,CAAC;AAAA,YACL;AAAA,UACJ,CAAC;AACD;AAAA,QACJ;AAAA,MACJ;AAGA,cAAQ,IAAI;AAAA,IAChB,CAAC;AAAA,EACT;AAEA,SAAO;AAAA,IACH,SAAS;AAAA;AAAA,MAEL,yBAAyB,CAAC,MAAM,KAAK,OAAO;AAAA;AAAA,MAG5C,aAAa,CAAC,MAAM,KAAK,GAAG;AAAA;AAAA,MAG5B,aAAa,CAAC,MAAM,KAAK,GAAG;AAAA;AAAA,MAG5B,kBAAkB,CAAC,MAAM,KAAK,GAAG;AAAA;AAAA,MAGjC,wBAAwB,CAAC,MAAM,KAAK,QAAQ;AAAA;AAAA,MAG5C,0BAA0B,CAAC,qBAAqB,KAAK,MAAM;AAAA,MAC3D,kBAAkB,CAAC,mCAAmC,KAAK,MAAM;AAAA,MAEjE,0BAA0B,CAAC,qBAAqB,KAAK,MAAM;AAAA,MAC3D,kBAAkB,CAAC,mCAAmC,KAAK,MAAM;AAAA,MAEjE,+BAA+B,CAAC,+BAA+B,KAAK,MAAM;AAAA,MAC1E,uBAAuB,CAAC,yCAAyC,KAAK,MAAM;AAAA,IAChF;AAAA,EACJ;AACJ;AAGA,IAAM,YAAY,OAAO,WAAW,eAAe,OAAO,OAAO,aAAa;AAC9E,IAAI,WAAW;AACX,WAAS,cAAc,IAAI,YAAY,yBAAyB,EAAE,QAAQ,OAAO,CAAC,CAAC;AACvF;AAEA,IAAO,iDAAQ;", "names": []}