import{r as s,j as t,K as $,$ as _,S as we}from"./app-CZ-bNXzO.js";import{c as y,u as X,d as ye,a as h,S as F,b as Ce,B as Ne}from"./createLucideIcon-DkVbiciO.js";import{S as Te,c as Se,d as ke,e as _e,f as Ae}from"./sheet-DnOgsUoR.js";import{c as Z,u as Ee,a as C,P as J,d as Le,b as Q}from"./index-uB82XUlN.js";import{c as ee,a as Me,R as Pe,A as Ie,P as Re,f as De,D as Oe,C as Be,g as ze,i as W,j as He,k as Y,l as $e,m as Fe,n as Ge}from"./dropdown-menu-DUK2pmBw.js";import{P as E}from"./index-k7GfkN7R.js";import{R as Ve}from"./index-g7rG-vGS.js";import{A as Ke}from"./app-logo-icon-BCY66tir.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ue=[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]],qe=y("BookOpen",Ue);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const We=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],Ye=y("ChevronRight",We);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xe=[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]],Ze=y("ChevronsUpDown",Xe);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Je=[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]],Qe=y("Folder",Je);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const et=[["rect",{width:"7",height:"7",x:"3",y:"3",rx:"1",key:"1g98yp"}],["rect",{width:"7",height:"7",x:"14",y:"3",rx:"1",key:"6d4xhi"}],["rect",{width:"7",height:"7",x:"14",y:"14",rx:"1",key:"nxv5o0"}],["rect",{width:"7",height:"7",x:"3",y:"14",rx:"1",key:"1bb6yr"}]],tt=y("LayoutGrid",et);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const at=[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]],rt=y("LogOut",at);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nt=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]],ot=y("PanelLeft",nt);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const st=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],it=y("Settings",st),B=768;function te(){const[e,a]=s.useState();return s.useEffect(()=>{const r=window.matchMedia(`(max-width: ${B-1}px)`),n=()=>{a(window.innerWidth<B)};return r.addEventListener("change",n),a(window.innerWidth<B),()=>r.removeEventListener("change",n)},[]),!!e}var[L,Ma]=Z("Tooltip",[ee]),M=ee(),ae="TooltipProvider",lt=700,z="tooltip.open",[ct,G]=L(ae),re=e=>{const{__scopeTooltip:a,delayDuration:r=lt,skipDelayDuration:n=300,disableHoverableContent:o=!1,children:i}=e,[c,d]=s.useState(!0),l=s.useRef(!1),p=s.useRef(0);return s.useEffect(()=>{const u=p.current;return()=>window.clearTimeout(u)},[]),t.jsx(ct,{scope:a,isOpenDelayed:c,delayDuration:r,onOpen:s.useCallback(()=>{window.clearTimeout(p.current),d(!1)},[]),onClose:s.useCallback(()=>{window.clearTimeout(p.current),p.current=window.setTimeout(()=>d(!0),n)},[n]),isPointerInTransitRef:l,onPointerInTransitChange:s.useCallback(u=>{l.current=u},[]),disableHoverableContent:o,children:i})};re.displayName=ae;var P="Tooltip",[dt,A]=L(P),ne=e=>{const{__scopeTooltip:a,children:r,open:n,defaultOpen:o=!1,onOpenChange:i,disableHoverableContent:c,delayDuration:d}=e,l=G(P,e.__scopeTooltip),p=M(a),[u,b]=s.useState(null),g=Me(),f=s.useRef(0),v=c??l.disableHoverableContent,j=d??l.delayDuration,w=s.useRef(!1),[x=!1,m]=Ee({prop:n,defaultProp:o,onChange:q=>{q?(l.onOpen(),document.dispatchEvent(new CustomEvent(z))):l.onClose(),i==null||i(q)}}),T=s.useMemo(()=>x?w.current?"delayed-open":"instant-open":"closed",[x]),S=s.useCallback(()=>{window.clearTimeout(f.current),f.current=0,w.current=!1,m(!0)},[m]),k=s.useCallback(()=>{window.clearTimeout(f.current),f.current=0,m(!1)},[m]),U=s.useCallback(()=>{window.clearTimeout(f.current),f.current=window.setTimeout(()=>{w.current=!0,m(!0),f.current=0},j)},[j,m]);return s.useEffect(()=>()=>{f.current&&(window.clearTimeout(f.current),f.current=0)},[]),t.jsx(Pe,{...p,children:t.jsx(dt,{scope:a,contentId:g,open:x,stateAttribute:T,trigger:u,onTriggerChange:b,onTriggerEnter:s.useCallback(()=>{l.isOpenDelayed?U():S()},[l.isOpenDelayed,U,S]),onTriggerLeave:s.useCallback(()=>{v?k():(window.clearTimeout(f.current),f.current=0)},[k,v]),onOpen:S,onClose:k,disableHoverableContent:v,children:r})})};ne.displayName=P;var H="TooltipTrigger",oe=s.forwardRef((e,a)=>{const{__scopeTooltip:r,...n}=e,o=A(H,r),i=G(H,r),c=M(r),d=s.useRef(null),l=X(a,d,o.onTriggerChange),p=s.useRef(!1),u=s.useRef(!1),b=s.useCallback(()=>p.current=!1,[]);return s.useEffect(()=>()=>document.removeEventListener("pointerup",b),[b]),t.jsx(Ie,{asChild:!0,...c,children:t.jsx(E.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...n,ref:l,onPointerMove:C(e.onPointerMove,g=>{g.pointerType!=="touch"&&!u.current&&!i.isPointerInTransitRef.current&&(o.onTriggerEnter(),u.current=!0)}),onPointerLeave:C(e.onPointerLeave,()=>{o.onTriggerLeave(),u.current=!1}),onPointerDown:C(e.onPointerDown,()=>{p.current=!0,document.addEventListener("pointerup",b,{once:!0})}),onFocus:C(e.onFocus,()=>{p.current||o.onOpen()}),onBlur:C(e.onBlur,o.onClose),onClick:C(e.onClick,o.onClose)})})});oe.displayName=H;var V="TooltipPortal",[ut,pt]=L(V,{forceMount:void 0}),se=e=>{const{__scopeTooltip:a,forceMount:r,children:n,container:o}=e,i=A(V,a);return t.jsx(ut,{scope:a,forceMount:r,children:t.jsx(J,{present:r||i.open,children:t.jsx(Re,{asChild:!0,container:o,children:n})})})};se.displayName=V;var N="TooltipContent",ie=s.forwardRef((e,a)=>{const r=pt(N,e.__scopeTooltip),{forceMount:n=r.forceMount,side:o="top",...i}=e,c=A(N,e.__scopeTooltip);return t.jsx(J,{present:n||c.open,children:c.disableHoverableContent?t.jsx(le,{side:o,...i,ref:a}):t.jsx(ft,{side:o,...i,ref:a})})}),ft=s.forwardRef((e,a)=>{const r=A(N,e.__scopeTooltip),n=G(N,e.__scopeTooltip),o=s.useRef(null),i=X(a,o),[c,d]=s.useState(null),{trigger:l,onClose:p}=r,u=o.current,{onPointerInTransitChange:b}=n,g=s.useCallback(()=>{d(null),b(!1)},[b]),f=s.useCallback((v,j)=>{const w=v.currentTarget,x={x:v.clientX,y:v.clientY},m=mt(x,w.getBoundingClientRect()),T=bt(x,m),S=gt(j.getBoundingClientRect()),k=jt([...T,...S]);d(k),b(!0)},[b]);return s.useEffect(()=>()=>g(),[g]),s.useEffect(()=>{if(l&&u){const v=w=>f(w,u),j=w=>f(w,l);return l.addEventListener("pointerleave",v),u.addEventListener("pointerleave",j),()=>{l.removeEventListener("pointerleave",v),u.removeEventListener("pointerleave",j)}}},[l,u,f,g]),s.useEffect(()=>{if(c){const v=j=>{const w=j.target,x={x:j.clientX,y:j.clientY},m=(l==null?void 0:l.contains(w))||(u==null?void 0:u.contains(w)),T=!vt(x,c);m?g():T&&(g(),p())};return document.addEventListener("pointermove",v),()=>document.removeEventListener("pointermove",v)}},[l,u,c,p,g]),t.jsx(le,{...e,ref:i})}),[ht,xt]=L(P,{isInside:!1}),le=s.forwardRef((e,a)=>{const{__scopeTooltip:r,children:n,"aria-label":o,onEscapeKeyDown:i,onPointerDownOutside:c,...d}=e,l=A(N,r),p=M(r),{onClose:u}=l;return s.useEffect(()=>(document.addEventListener(z,u),()=>document.removeEventListener(z,u)),[u]),s.useEffect(()=>{if(l.trigger){const b=g=>{const f=g.target;f!=null&&f.contains(l.trigger)&&u()};return window.addEventListener("scroll",b,{capture:!0}),()=>window.removeEventListener("scroll",b,{capture:!0})}},[l.trigger,u]),t.jsx(Oe,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:c,onFocusOutside:b=>b.preventDefault(),onDismiss:u,children:t.jsxs(Be,{"data-state":l.stateAttribute,...p,...d,ref:a,style:{...d.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[t.jsx(ye,{children:n}),t.jsx(ht,{scope:r,isInside:!0,children:t.jsx(Ve,{id:l.contentId,role:"tooltip",children:o||n})})]})})});ie.displayName=N;var ce="TooltipArrow",de=s.forwardRef((e,a)=>{const{__scopeTooltip:r,...n}=e,o=M(r);return xt(ce,r).isInside?null:t.jsx(De,{...o,...n,ref:a})});de.displayName=ce;function mt(e,a){const r=Math.abs(a.top-e.y),n=Math.abs(a.bottom-e.y),o=Math.abs(a.right-e.x),i=Math.abs(a.left-e.x);switch(Math.min(r,n,o,i)){case i:return"left";case o:return"right";case r:return"top";case n:return"bottom";default:throw new Error("unreachable")}}function bt(e,a,r=5){const n=[];switch(a){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r});break}return n}function gt(e){const{top:a,right:r,bottom:n,left:o}=e;return[{x:o,y:a},{x:r,y:a},{x:r,y:n},{x:o,y:n}]}function vt(e,a){const{x:r,y:n}=e;let o=!1;for(let i=0,c=a.length-1;i<a.length;c=i++){const d=a[i].x,l=a[i].y,p=a[c].x,u=a[c].y;l>n!=u>n&&r<(p-d)*(n-l)/(u-l)+d&&(o=!o)}return o}function jt(e){const a=e.slice();return a.sort((r,n)=>r.x<n.x?-1:r.x>n.x?1:r.y<n.y?-1:r.y>n.y?1:0),wt(a)}function wt(e){if(e.length<=1)return e.slice();const a=[];for(let n=0;n<e.length;n++){const o=e[n];for(;a.length>=2;){const i=a[a.length-1],c=a[a.length-2];if((i.x-c.x)*(o.y-c.y)>=(i.y-c.y)*(o.x-c.x))a.pop();else break}a.push(o)}a.pop();const r=[];for(let n=e.length-1;n>=0;n--){const o=e[n];for(;r.length>=2;){const i=r[r.length-1],c=r[r.length-2];if((i.x-c.x)*(o.y-c.y)>=(i.y-c.y)*(o.x-c.x))r.pop();else break}r.push(o)}return r.pop(),a.length===1&&r.length===1&&a[0].x===r[0].x&&a[0].y===r[0].y?a:a.concat(r)}var yt=re,Ct=ne,Nt=oe,Tt=se,St=ie,kt=de;function ue({delayDuration:e=0,...a}){return t.jsx(yt,{"data-slot":"tooltip-provider",delayDuration:e,...a})}function _t({...e}){return t.jsx(ue,{children:t.jsx(Ct,{"data-slot":"tooltip",...e})})}function At({...e}){return t.jsx(Nt,{"data-slot":"tooltip-trigger",...e})}function Et({className:e,sideOffset:a=4,children:r,...n}){return t.jsx(Tt,{children:t.jsxs(St,{"data-slot":"tooltip-content",sideOffset:a,className:h("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-w-sm rounded-md px-3 py-1.5 text-xs",e),...n,children:[r,t.jsx(kt,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}const Lt="sidebar_state",Mt=60*60*24*7,Pt="16rem",It="18rem",Rt="3rem",Dt="b",pe=s.createContext(null);function I(){const e=s.useContext(pe);if(!e)throw new Error("useSidebar must be used within a SidebarProvider.");return e}function Ot({defaultOpen:e=!0,open:a,onOpenChange:r,className:n,style:o,children:i,...c}){const d=te(),[l,p]=s.useState(!1),[u,b]=s.useState(e),g=a??u,f=s.useCallback(x=>{const m=typeof x=="function"?x(g):x;r?r(m):b(m),document.cookie=`${Lt}=${m}; path=/; max-age=${Mt}`},[r,g]),v=s.useCallback(()=>d?p(x=>!x):f(x=>!x),[d,f,p]);s.useEffect(()=>{const x=m=>{m.key===Dt&&(m.metaKey||m.ctrlKey)&&(m.preventDefault(),v())};return window.addEventListener("keydown",x),()=>window.removeEventListener("keydown",x)},[v]);const j=g?"expanded":"collapsed",w=s.useMemo(()=>({state:j,open:g,setOpen:f,isMobile:d,openMobile:l,setOpenMobile:p,toggleSidebar:v}),[j,g,f,d,l,p,v]);return t.jsx(pe.Provider,{value:w,children:t.jsx(ue,{delayDuration:0,children:t.jsx("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":Pt,"--sidebar-width-icon":Rt,...o},className:h("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",n),...c,children:i})})})}function Bt({side:e="left",variant:a="sidebar",collapsible:r="offcanvas",className:n,children:o,...i}){const{isMobile:c,state:d,openMobile:l,setOpenMobile:p}=I();return r==="none"?t.jsx("div",{"data-slot":"sidebar",className:h("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",n),...i,children:o}):c?t.jsxs(Te,{open:l,onOpenChange:p,...i,children:[t.jsxs(Se,{className:"sr-only",children:[t.jsx(ke,{children:"Sidebar"}),t.jsx(_e,{children:"Displays the mobile sidebar."})]}),t.jsx(Ae,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":It},side:e,children:t.jsx("div",{className:"flex h-full w-full flex-col",children:o})})]}):t.jsxs("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":d,"data-collapsible":d==="collapsed"?r:"","data-variant":a,"data-side":e,"data-slot":"sidebar",children:[t.jsx("div",{className:h("relative h-svh w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180",a==="floating"||a==="inset"?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),t.jsx("div",{className:h("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex",e==="left"?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]",a==="floating"||a==="inset"?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",n),...i,children:t.jsx("div",{"data-sidebar":"sidebar",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:o})})]})}function zt({className:e,onClick:a,...r}){const{toggleSidebar:n}=I();return t.jsxs(Ne,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:h("h-7 w-7",e),onClick:o=>{a==null||a(o),n()},...r,children:[t.jsx(ot,{}),t.jsx("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function Ht({className:e,...a}){return t.jsx("main",{"data-slot":"sidebar-inset",className:h("bg-background relative flex max-w-full min-h-svh flex-1 flex-col","peer-data-[variant=inset]:min-h-[calc(100svh-(--spacing(4)))] md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-0",e),...a})}function $t({className:e,...a}){return t.jsx("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:h("flex flex-col gap-2 p-2",e),...a})}function Ft({className:e,...a}){return t.jsx("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:h("flex flex-col gap-2 p-2",e),...a})}function Gt({className:e,...a}){return t.jsx("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:h("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...a})}function fe({className:e,...a}){return t.jsx("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:h("relative flex w-full min-w-0 flex-col p-2",e),...a})}function Vt({className:e,asChild:a=!1,...r}){const n=a?F:"div";return t.jsx(n,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:h("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",e),...r})}function Kt({className:e,...a}){return t.jsx("div",{"data-slot":"sidebar-group-content","data-sidebar":"group-content",className:h("w-full text-sm",e),...a})}function R({className:e,...a}){return t.jsx("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:h("flex w-full min-w-0 flex-col gap-1",e),...a})}function D({className:e,...a}){return t.jsx("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:h("group/menu-item relative",e),...a})}const Ut=Ce("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function O({asChild:e=!1,isActive:a=!1,variant:r="default",size:n="default",tooltip:o,className:i,...c}){const d=e?F:"button",{isMobile:l,state:p}=I(),u=t.jsx(d,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":n,"data-active":a,className:h(Ut({variant:r,size:n}),i),...c});return o?(typeof o=="string"&&(o={children:o}),t.jsxs(_t,{children:[t.jsx(At,{asChild:!0,children:u}),t.jsx(Et,{side:"right",align:"center",hidden:p!=="collapsed"||l,...o})]})):u}function qt({variant:e="header",children:a,...r}){return e==="sidebar"?t.jsx(Ht,{...r,children:a}):t.jsx("main",{className:"mx-auto flex h-full w-full max-w-7xl flex-1 flex-col gap-4 rounded-xl",...r,children:a})}function Wt({children:e,variant:a="header"}){const r=$().props.sidebarOpen;return a==="header"?t.jsx("div",{className:"flex min-h-screen w-full flex-col",children:e}):t.jsx(Ot,{defaultOpen:r,children:e})}function Yt({iconNode:e,className:a,...r}){return t.jsx(e,{className:h("h-4 w-4",a),...r})}function Xt({items:e,className:a,...r}){return t.jsx(fe,{...r,className:`group-data-[collapsible=icon]:p-0 ${a||""}`,children:t.jsx(Kt,{children:t.jsx(R,{children:e.map(n=>t.jsx(D,{children:t.jsx(O,{asChild:!0,className:"text-neutral-600 hover:text-neutral-800 dark:text-neutral-300 dark:hover:text-neutral-100",children:t.jsxs("a",{href:n.href,target:"_blank",rel:"noopener noreferrer",children:[n.icon&&t.jsx(Yt,{iconNode:n.icon,className:"h-5 w-5"}),t.jsx("span",{children:n.title})]})})},n.title))})})})}function Zt({items:e=[]}){const a=$();return t.jsxs(fe,{className:"px-2 py-0",children:[t.jsx(Vt,{children:"Platform"}),t.jsx(R,{children:e.map(r=>t.jsx(D,{children:t.jsx(O,{asChild:!0,isActive:r.href===a.url,tooltip:{children:r.title},children:t.jsxs(_,{href:r.href,prefetch:!0,children:[r.icon&&t.jsx(r.icon,{}),t.jsx("span",{children:r.title})]})})},r.title))})]})}var K="Avatar",[Jt,Pa]=Z(K),[Qt,he]=Jt(K),xe=s.forwardRef((e,a)=>{const{__scopeAvatar:r,...n}=e,[o,i]=s.useState("idle");return t.jsx(Qt,{scope:r,imageLoadingStatus:o,onImageLoadingStatusChange:i,children:t.jsx(E.span,{...n,ref:a})})});xe.displayName=K;var me="AvatarImage",be=s.forwardRef((e,a)=>{const{__scopeAvatar:r,src:n,onLoadingStatusChange:o=()=>{},...i}=e,c=he(me,r),d=ea(n,i.referrerPolicy),l=Le(p=>{o(p),c.onImageLoadingStatusChange(p)});return Q(()=>{d!=="idle"&&l(d)},[d,l]),d==="loaded"?t.jsx(E.img,{...i,ref:a,src:n}):null});be.displayName=me;var ge="AvatarFallback",ve=s.forwardRef((e,a)=>{const{__scopeAvatar:r,delayMs:n,...o}=e,i=he(ge,r),[c,d]=s.useState(n===void 0);return s.useEffect(()=>{if(n!==void 0){const l=window.setTimeout(()=>d(!0),n);return()=>window.clearTimeout(l)}},[n]),c&&i.imageLoadingStatus!=="loaded"?t.jsx(E.span,{...o,ref:a}):null});ve.displayName=ge;function ea(e,a){const[r,n]=s.useState("idle");return Q(()=>{if(!e){n("error");return}let o=!0;const i=new window.Image,c=d=>()=>{o&&n(d)};return n("loading"),i.onload=c("loaded"),i.onerror=c("error"),i.src=e,a&&(i.referrerPolicy=a),()=>{o=!1}},[e,a]),r}var ta=xe,aa=be,ra=ve;function na({className:e,...a}){return t.jsx(ta,{"data-slot":"avatar",className:h("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...a})}function oa({className:e,...a}){return t.jsx(aa,{"data-slot":"avatar-image",className:h("aspect-square size-full",e),...a})}function sa({className:e,...a}){return t.jsx(ra,{"data-slot":"avatar-fallback",className:h("bg-muted flex size-full items-center justify-center rounded-full",e),...a})}function ia(){return s.useCallback(e=>{const a=e.trim().split(" ");if(a.length===0)return"";if(a.length===1)return a[0].charAt(0).toUpperCase();const r=a[0].charAt(0),n=a[a.length-1].charAt(0);return`${r}${n}`.toUpperCase()},[])}function je({user:e,showEmail:a=!1}){const r=ia();return t.jsxs(t.Fragment,{children:[t.jsxs(na,{className:"h-8 w-8 overflow-hidden rounded-full",children:[t.jsx(oa,{src:e.avatar,alt:e.name}),t.jsx(sa,{className:"rounded-lg bg-neutral-200 text-black dark:bg-neutral-700 dark:text-white",children:r(e.name)})]}),t.jsxs("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[t.jsx("span",{className:"truncate font-medium",children:e.name}),a&&t.jsx("span",{className:"text-muted-foreground truncate text-xs",children:e.email})]})]})}function la(){return s.useCallback(()=>{document.body.style.removeProperty("pointer-events")},[])}function ca({user:e}){const a=la(),r=()=>{a(),we.flushAll()};return t.jsxs(t.Fragment,{children:[t.jsx(ze,{className:"p-0 font-normal",children:t.jsx("div",{className:"flex items-center gap-2 px-1 py-1.5 text-left text-sm",children:t.jsx(je,{user:e,showEmail:!0})})}),t.jsx(W,{}),t.jsx(He,{children:t.jsx(Y,{asChild:!0,children:t.jsxs(_,{className:"block w-full",href:route("profile.edit"),as:"button",prefetch:!0,onClick:a,children:[t.jsx(it,{className:"mr-2"}),"Settings"]})})}),t.jsx(W,{}),t.jsx(Y,{asChild:!0,children:t.jsxs(_,{className:"block w-full",method:"post",href:route("logout"),as:"button",onClick:r,children:[t.jsx(rt,{className:"mr-2"}),"Log out"]})})]})}function da(){const{auth:e}=$().props,{state:a}=I(),r=te();return t.jsx(R,{children:t.jsx(D,{children:t.jsxs($e,{children:[t.jsx(Fe,{asChild:!0,children:t.jsxs(O,{size:"lg",className:"text-sidebar-accent-foreground data-[state=open]:bg-sidebar-accent group",children:[t.jsx(je,{user:e.user}),t.jsx(Ze,{className:"ml-auto size-4"})]})}),t.jsx(Ge,{className:"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg",align:"end",side:r?"bottom":a==="collapsed"?"left":"bottom",children:t.jsx(ca,{user:e.user})})]})})})}function ua(){return t.jsxs(t.Fragment,{children:[t.jsx("div",{className:"bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-md",children:t.jsx(Ke,{className:"size-5 fill-current text-white dark:text-black"})}),t.jsx("div",{className:"ml-1 grid flex-1 text-left text-sm",children:t.jsx("span",{className:"mb-0.5 truncate leading-none font-semibold",children:"Laravel Starter Kit"})})]})}const pa=[{title:"Dashboard",href:"/dashboard",icon:tt}],fa=[{title:"Repository",href:"https://github.com/laravel/react-starter-kit",icon:Qe},{title:"Documentation",href:"https://laravel.com/docs/starter-kits#react",icon:qe}];function ha(){return t.jsxs(Bt,{collapsible:"icon",variant:"inset",children:[t.jsx($t,{children:t.jsx(R,{children:t.jsx(D,{children:t.jsx(O,{size:"lg",asChild:!0,children:t.jsx(_,{href:"/dashboard",prefetch:!0,children:t.jsx(ua,{})})})})})}),t.jsx(Gt,{children:t.jsx(Zt,{items:pa})}),t.jsxs(Ft,{children:[t.jsx(Xt,{items:fa,className:"mt-auto"}),t.jsx(da,{})]})]})}function xa({...e}){return t.jsx("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",...e})}function ma({className:e,...a}){return t.jsx("ol",{"data-slot":"breadcrumb-list",className:h("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",e),...a})}function ba({className:e,...a}){return t.jsx("li",{"data-slot":"breadcrumb-item",className:h("inline-flex items-center gap-1.5",e),...a})}function ga({asChild:e,className:a,...r}){const n=e?F:"a";return t.jsx(n,{"data-slot":"breadcrumb-link",className:h("hover:text-foreground transition-colors",a),...r})}function va({className:e,...a}){return t.jsx("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",className:h("text-foreground font-normal",e),...a})}function ja({children:e,className:a,...r}){return t.jsx("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",className:h("[&>svg]:size-3.5",a),...r,children:e??t.jsx(Ye,{})})}function wa({breadcrumbs:e}){return t.jsx(t.Fragment,{children:e.length>0&&t.jsx(xa,{children:t.jsx(ma,{children:e.map((a,r)=>{const n=r===e.length-1;return t.jsxs(s.Fragment,{children:[t.jsx(ba,{children:n?t.jsx(va,{children:a.title}):t.jsx(ga,{asChild:!0,children:t.jsx(_,{href:a.href,children:a.title})})}),!n&&t.jsx(ja,{})]},r)})})})})}function ya({breadcrumbs:e=[]}){return t.jsx("header",{className:"border-sidebar-border/50 flex h-16 shrink-0 items-center gap-2 border-b px-6 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 md:px-4",children:t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx(zt,{className:"-ml-1"}),t.jsx(wa,{breadcrumbs:e})]})})}function Ca({children:e,breadcrumbs:a=[]}){return t.jsxs(Wt,{variant:"sidebar",children:[t.jsx(ha,{}),t.jsxs(qt,{variant:"sidebar",children:[t.jsx(ya,{breadcrumbs:a}),e]})]})}const Ia=({children:e,breadcrumbs:a,...r})=>t.jsx(Ca,{breadcrumbs:a,...r,children:e});export{Ia as A};
