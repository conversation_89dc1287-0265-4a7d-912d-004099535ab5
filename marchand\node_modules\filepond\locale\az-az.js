export default {
    labelIdle: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ürüşd<PERSON>rün & <PERSON>uraxın ya da <span class="filepond--label-action"> Seçin </span>',
    labelInvalidField: 'Sahədə etibarsız fayllar var',
    labelFileWaitingForSize: 'Ölçü hesablanır',
    labelFileSizeNotAvailable: 'Ölçü mövcud deyil',
    labelFileLoading: 'Yüklənir',
    labelFileLoadError: 'Yükləmə əsnasında xəta baş verdi',
    labelFileProcessing: 'Yüklənir',
    labelFileProcessingComplete: 'Yükləmə tamamlandı',
    labelFileProcessingAborted: 'Yükləmə ləğv edildi',
    labelFileProcessingError: 'Yükəyərkən xəta baş verdi',
    labelFileProcessingRevertError: '<PERSON><PERSON> çəkərkən xəta baş verdi',
    labelFileRemoveError: 'Çı<PERSON>rarkən xəta baş verdi',
    labelTapToCancel: 'İmtina etmək üçün klikləyin',
    labelTapToRetry: 'Təkrar yoxlamaq üçün klikləyin',
    labelTapToUndo: 'Geri almaq üçün klikləyin',
    labelButtonRemoveItem: 'Çıxar',
    labelButtonAbortItemLoad: 'İmtina Et',
    labelButtonRetryItemLoad: 'Təkrar yoxla',
    labelButtonAbortItemProcessing: 'İmtina et',
    labelButtonUndoItemProcessing: 'Geri Al',
    labelButtonRetryItemProcessing: 'Təkrar yoxla',
    labelButtonProcessItem: 'Yüklə',
    labelMaxFileSizeExceeded: 'Fayl çox böyükdür',
    labelMaxFileSize: 'Ən böyük fayl ölçüsü: {filesize}',
    labelMaxTotalFileSizeExceeded: 'Maksimum ölçü keçildi',
    labelMaxTotalFileSize: 'Maksimum fayl ölçüsü :{filesize}',
    labelFileTypeNotAllowed: 'Etibarsız fayl tipi',
    fileValidateTypeLabelExpectedTypes: 'Bu {allButLastType} ya da bu fayl olması lazımdır: {lastType}',
    imageValidateSizeLabelFormatError: 'Şəkil tipi dəstəklənmir',
    imageValidateSizeLabelImageSizeTooSmall: 'Şəkil çox kiçik',
    imageValidateSizeLabelImageSizeTooBig: 'Şəkil çox böyük',
    imageValidateSizeLabelExpectedMinSize: 'Minimum ölçü {minWidth} × {minHeight}',
    imageValidateSizeLabelExpectedMaxSize: 'Maksimum ölçü {maxWidth} × {maxHeight}',
    imageValidateSizeLabelImageResolutionTooLow: 'Görüntü imkanı çox aşağı',
    imageValidateSizeLabelImageResolutionTooHigh: 'Görüntü imkanı çox yüksək',
    imageValidateSizeLabelExpectedMinResolution: 'Minimum görüntü imkanı {minResolution}',
    imageValidateSizeLabelExpectedMaxResolution: 'Maximum görüntü imkanı {maxResolution}'
};
