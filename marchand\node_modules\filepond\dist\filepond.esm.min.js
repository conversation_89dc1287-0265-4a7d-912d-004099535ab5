/*!
 * FilePond 4.32.7
 * Licensed under MIT, https://opensource.org/licenses/MIT/
 * Please visit https://pqina.nl/filepond/ for details.
 */

/* eslint-disable */

const e=(e,t)=>{for(const r in e)e.hasOwnProperty(r)&&t(r,e[r])},t=t=>{const r={};return e(t,e=>{((e,t,r)=>{"function"!=typeof r?Object.defineProperty(e,t,{...r}):e[t]=r})(r,e,t[e])}),r},r=(e,t,r=null)=>{if(null===r)return e.getAttribute(t)||e.hasAttribute(t);e.setAttribute(t,r)},o=["svg","path"],n=e=>o.includes(e),i=(t,o,i={})=>{"object"==typeof o&&(i=o,o=null);const s=n(t)?document.createElementNS("http://www.w3.org/2000/svg",t):document.createElement(t);return o&&(n(t)?r(s,"class",o):s.className=o),e(i,(e,t)=>{r(s,e,t)}),s},s=e=>(t,r)=>{void 0!==r&&e.children[r]?e.insertBefore(t,e.children[r]):e.appendChild(t)},a=(e,t)=>(e,r)=>(void 0!==r?t.splice(r,0,e):t.push(e),e),l=(e,t)=>r=>(t.splice(t.indexOf(r),1),r.element.parentNode&&e.removeChild(r.element),r),c=(()=>"undefined"!=typeof window&&void 0!==window.document)(),d=()=>c,p="children"in(d()?i("svg"):{})?e=>e.children.length:e=>e.childNodes.length,E=(e,t,r,o)=>{const n=r[0]||e.left,i=r[1]||e.top,s=n+e.width,a=i+e.height*(o[1]||1),l={element:{...e},inner:{left:e.left,top:e.top,right:e.right,bottom:e.bottom},outer:{left:n,top:i,right:s,bottom:a}};return t.filter(e=>!e.isRectIgnored()).map(e=>e.rect).forEach(e=>{u(l.inner,{...e.inner}),u(l.outer,{...e.outer})}),_(l.inner),l.outer.bottom+=l.element.marginBottom,l.outer.right+=l.element.marginRight,_(l.outer),l},u=(e,t)=>{t.top+=e.top,t.right+=e.left,t.bottom+=e.top,t.left+=e.left,t.bottom>e.bottom&&(e.bottom=t.bottom),t.right>e.right&&(e.right=t.right)},_=e=>{e.width=e.right-e.left,e.height=e.bottom-e.top},T=e=>"number"==typeof e,I=e=>e<.5?2*e*e:(4-2*e)*e-1,f={spring:({stiffness:e=.5,damping:r=.75,mass:o=10}={})=>{let n=null,i=null,s=0,a=!1;const l=t({interpolate:(t,c)=>{if(!a)return T(n)&&T(i)?void(((e,t,r,o=.001)=>Math.abs(e-t)<o&&Math.abs(r)<o)(i+=s+=-(i-n)*e/o,n,s*=r)||c?(i=n,s=0,a=!0,l.onupdate(i),l.oncomplete(i)):l.onupdate(i)):(a=!0,void(s=0))},target:{set:e=>{if(T(e)&&!T(i)&&(i=e),null===n&&(n=e,i=e),i===(n=e)||void 0===n)return a=!0,s=0,l.onupdate(i),void l.oncomplete(i);a=!1},get:()=>n},resting:{get:()=>a},onupdate:e=>{},oncomplete:e=>{}});return l},tween:({duration:e=500,easing:r=I,delay:o=0}={})=>{let n,i,s=null,a=!0,l=!1,c=null;const d=t({interpolate:(t,p)=>{a||null===c||(null===s&&(s=t),t-s<o||((n=t-s-o)>=e||p?(n=1,i=l?0:1,d.onupdate(i*c),d.oncomplete(i*c),a=!0):(i=n/e,d.onupdate((n>=0?r(l?1-i:i):0)*c))))},target:{get:()=>l?0:c,set:e=>{if(null===c)return c=e,d.onupdate(e),void d.oncomplete(e);e<c?(c=1,l=!0):(l=!1,c=e),a=!1,s=null}},resting:{get:()=>a},onupdate:e=>{},oncomplete:e=>{}});return d}},m=(e,t,r)=>{const o=e[t]&&"object"==typeof e[t][r]?e[t][r]:e[t]||e,n="string"==typeof o?o:o.type,i="object"==typeof o?{...o}:{};return f[n]?f[n](i):null},h=(e,t,r,o=!1)=>{(t=Array.isArray(t)?t:[t]).forEach(t=>{e.forEach(e=>{let n=e,i=()=>r[e],s=t=>r[e]=t;"object"==typeof e&&(n=e.key,i=e.getter||i,s=e.setter||s),t[n]&&!o||(t[n]={get:i,set:s})})})},R=e=>null!=e,g={opacity:1,scaleX:1,scaleY:1,translateX:0,translateY:0,rotateX:0,rotateY:0,rotateZ:0,originX:0,originY:0},O=(e,t)=>{if(Object.keys(e).length!==Object.keys(t).length)return!0;for(const r in t)if(t[r]!==e[r])return!0;return!1},D=(e,{opacity:t,perspective:r,translateX:o,translateY:n,scaleX:i,scaleY:s,rotateX:a,rotateY:l,rotateZ:c,originX:d,originY:p,width:E,height:u})=>{let _="",T="";(R(d)||R(p))&&(T+=`transform-origin: ${d||0}px ${p||0}px;`),R(r)&&(_+=`perspective(${r}px) `),(R(o)||R(n))&&(_+=`translate3d(${o||0}px, ${n||0}px, 0) `),(R(i)||R(s))&&(_+=`scale3d(${R(i)?i:1}, ${R(s)?s:1}, 1) `),R(c)&&(_+=`rotateZ(${c}rad) `),R(a)&&(_+=`rotateX(${a}rad) `),R(l)&&(_+=`rotateY(${l}rad) `),_.length&&(T+=`transform:${_};`),R(t)&&(T+=`opacity:${t};`,0===t&&(T+="visibility:hidden;"),t<1&&(T+="pointer-events:none;")),R(u)&&(T+=`height:${u}px;`),R(E)&&(T+=`width:${E}px;`);const I=e.elementCurrentStyle||"";T.length===I.length&&T===I||(e.style.cssText=T,e.elementCurrentStyle=T)},S={styles:({mixinConfig:e,viewProps:t,viewInternalAPI:r,viewExternalAPI:o,view:n})=>{const i={...t},s={};h(e,[r,o],t);const a=()=>n.rect?E(n.rect,n.childViews,(()=>[t.translateX||0,t.translateY||0])(),(()=>[t.scaleX||0,t.scaleY||0])()):null;return r.rect={get:a},o.rect={get:a},e.forEach(e=>{t[e]=void 0===i[e]?g[e]:i[e]}),{write:()=>{if(O(s,t))return D(n.element,t),Object.assign(s,{...t}),!0},destroy:()=>{}}},listeners:({mixinConfig:e,viewProps:t,viewInternalAPI:r,viewExternalAPI:o,viewState:n,view:i})=>{const s=[],a=(e=>(t,r)=>{e.addEventListener(t,r)})(i.element),l=(e=>(t,r)=>{e.removeEventListener(t,r)})(i.element);return o.on=((e,t)=>{s.push({type:e,fn:t}),a(e,t)}),o.off=((e,t)=>{s.splice(s.findIndex(r=>r.type===e&&r.fn===t),1),l(e,t)}),{write:()=>!0,destroy:()=>{s.forEach(e=>{l(e.type,e.fn)})}}},animations:({mixinConfig:t,viewProps:r,viewInternalAPI:o,viewExternalAPI:n})=>{const i={...r},s=[];return e(t,(e,t)=>{const a=m(t);a&&(a.onupdate=(t=>{r[e]=t}),a.target=i[e],h([{key:e,setter:e=>{a.target!==e&&(a.target=e)},getter:()=>r[e]}],[o,n],r,!0),s.push(a))}),{write:e=>{let t=document.hidden,r=!0;return s.forEach(o=>{o.resting||(r=!1),o.interpolate(e,t)}),r},destroy:()=>{}}},apis:({mixinConfig:e,viewProps:t,viewExternalAPI:r})=>{h(e,r,t)}},y=(e={},t={},r={})=>(t.layoutCalculated||(e.paddingTop=parseInt(r.paddingTop,10)||0,e.marginTop=parseInt(r.marginTop,10)||0,e.marginRight=parseInt(r.marginRight,10)||0,e.marginBottom=parseInt(r.marginBottom,10)||0,e.marginLeft=parseInt(r.marginLeft,10)||0,t.layoutCalculated=!0),e.left=t.offsetLeft||0,e.top=t.offsetTop||0,e.width=t.offsetWidth||0,e.height=t.offsetHeight||0,e.right=e.left+e.width,e.bottom=e.top+e.height,e.scrollTop=t.scrollTop,e.hidden=null===t.offsetParent,e),A=({tag:e="div",name:r=null,attributes:o={},read:n=(()=>{}),write:c=(()=>{}),create:d=(()=>{}),destroy:u=(()=>{}),filterFrameActionsForChild:_=((e,t)=>t),didCreateView:T=(()=>{}),didWriteView:I=(()=>{}),ignoreRect:f=!1,ignoreRectUpdate:m=!1,mixins:h=[]}={})=>(R,g={})=>{const O=i(e,`filepond--${r}`,o),D=window.getComputedStyle(O,null),A=y();let L=null,P=!1;const v=[],M=[],b={},C={},N=[c],w=[n],G=[u],U=()=>O,B=()=>v.concat(),F=()=>L||(L=E(A,v,[0,0],[1,1])),q={element:{get:U},style:{get:()=>D},childViews:{get:B}},V={...q,rect:{get:F},ref:{get:()=>b},is:e=>r===e,appendChild:s(O),createChildView:(e=>(t,r)=>t(e,r))(R),linkView:e=>(v.push(e),e),unlinkView:e=>{v.splice(v.indexOf(e),1)},appendChildView:a(0,v),removeChildView:l(O,v),registerWriter:e=>N.push(e),registerReader:e=>w.push(e),registerDestroyer:e=>G.push(e),invalidateLayout:()=>O.layoutCalculated=!1,dispatch:R.dispatch,query:R.query},x={element:{get:U},childViews:{get:B},rect:{get:F},resting:{get:()=>P},isRectIgnored:()=>f,_read:()=>{L=null,v.forEach(e=>e._read()),!(m&&A.width&&A.height)&&y(A,O,D);const e={root:H,props:g,rect:A};w.forEach(t=>t(e))},_write:(e,t,r)=>{let o=0===t.length;return N.forEach(n=>{!1===n({props:g,root:H,actions:t,timestamp:e,shouldOptimize:r})&&(o=!1)}),M.forEach(t=>{!1===t.write(e)&&(o=!1)}),v.filter(e=>!!e.element.parentNode).forEach(n=>{n._write(e,_(n,t),r)||(o=!1)}),v.forEach((n,i)=>{n.element.parentNode||(H.appendChild(n.element,i),n._read(),n._write(e,_(n,t),r),o=!1)}),P=o,I({props:g,root:H,actions:t,timestamp:e}),o},_destroy:()=>{M.forEach(e=>e.destroy()),G.forEach(e=>{e({root:H,props:g})}),v.forEach(e=>e._destroy())}},Y={...q,rect:{get:()=>A}};Object.keys(h).sort((e,t)=>"styles"===e?1:"styles"===t?-1:0).forEach(e=>{const r=S[e]({mixinConfig:h[e],viewProps:g,viewState:C,viewInternalAPI:V,viewExternalAPI:x,view:t(Y)});r&&M.push(r)});const H=t(V);d({root:H,props:g});const k=p(O);return v.forEach((e,t)=>{H.appendChild(e.element,k+t)}),T(H),t(x)},L=(e,t)=>({root:r,props:o,actions:n=[],timestamp:i,shouldOptimize:s})=>{n.filter(t=>e[t.type]).forEach(t=>e[t.type]({root:r,props:o,action:t.data,timestamp:i,shouldOptimize:s})),t&&t({root:r,props:o,actions:n,timestamp:i,shouldOptimize:s})},P=(e,t)=>t.parentNode.insertBefore(e,t),v=(e,t)=>t.parentNode.insertBefore(e,t.nextSibling),M=e=>Array.isArray(e),b=e=>null==e,C=e=>e.trim(),N=e=>""+e,w=e=>"boolean"==typeof e,G=e=>w(e)?e:"true"===e,U=e=>"string"==typeof e,B=e=>T(e)?e:U(e)?N(e).replace(/[a-z]+/gi,""):0,F=e=>parseInt(B(e),10),q=e=>parseFloat(B(e)),V=e=>T(e)&&isFinite(e)&&Math.floor(e)===e,x=(e,t=1e3)=>{if(V(e))return e;let r=N(e).trim();return/MB$/i.test(r)?(r=r.replace(/MB$i/,"").trim(),F(r)*t*t):/KB/i.test(r)?(r=r.replace(/KB$i/,"").trim(),F(r)*t):F(r)},Y=e=>"function"==typeof e,H={process:"POST",patch:"PATCH",revert:"DELETE",fetch:"GET",restore:"GET",load:"GET"},k=(e,t,r,o,n)=>{if(null===t)return null;if("function"==typeof t)return t;const i={url:"GET"===r||"PATCH"===r?`?${e}=`:"",method:r,headers:n,withCredentials:!1,timeout:o,onload:null,ondata:null,onerror:null};if(U(t))return i.url=t,i;if(Object.assign(i,t),U(i.headers)){const e=i.headers.split(/:(.+)/);i.headers={header:e[0],value:e[1]}}return i.withCredentials=G(i.withCredentials),i},X=e=>"object"==typeof e&&null!==e,$=e=>M(e)?"array":(e=>null===e)(e)?"null":V(e)?"int":/^[0-9]+ ?(?:GB|MB|KB)$/gi.test(e)?"bytes":(e=>X(e)&&U(e.url)&&X(e.process)&&X(e.revert)&&X(e.restore)&&X(e.fetch))(e)?"api":typeof e,W={array:(e,t=",")=>b(e)?[]:M(e)?e:N(e).split(t).map(C).filter(e=>e.length),boolean:G,int:e=>"bytes"===$(e)?x(e):F(e),number:q,float:q,bytes:x,string:e=>Y(e)?e:N(e),function:e=>(e=>{let t=self,r=e.split("."),o=null;for(;o=r.shift();)if(!(t=t[o]))return null;return t})(e),serverapi:t=>(t=>{const r={};return r.url=U(t)?t:t.url||"",r.timeout=t.timeout?parseInt(t.timeout,10):0,r.headers=t.headers?t.headers:{},e(H,e=>{r[e]=k(e,t[e],H[e],r.timeout,r.headers)}),r.process=t.process||U(t)||t.url?r.process:null,r.remove=t.remove||null,delete r.headers,r})(t),object:e=>{try{return JSON.parse((e=>e.replace(/{\s*'/g,'{"').replace(/'\s*}/g,'"}').replace(/'\s*:/g,'":').replace(/:\s*'/g,':"').replace(/,\s*'/g,',"').replace(/'\s*,/g,'",'))(e))}catch(e){return null}}},z=(e,t,r)=>{if(e===t)return e;let o=$(e);if(o!==r){const t=((e,t)=>W[t](e))(e,r);if(o=$(t),null===t)throw`Trying to assign value with incorrect type to "${option}", allowed type: "${r}"`;e=t}return e},j=r=>{const o={};return e(r,e=>{const t=r[e];o[e]=((e,t)=>{let r=e;return{enumerable:!0,get:()=>r,set:o=>{r=z(o,e,t)}}})(t[0],t[1])}),t(o)},Q=(e,t="-")=>e.split(/(?=[A-Z])/).map(e=>e.toLowerCase()).join(t),Z=(t,r)=>{const o={};return e(r,e=>{o[e]={get:()=>t.getState().options[e],set:r=>{t.dispatch(`SET_${Q(e,"_").toUpperCase()}`,{value:r})}}}),o},K=t=>(r,o,n)=>{const i={};return e(t,e=>{const t=Q(e,"_").toUpperCase();i[`SET_${t}`]=(o=>{try{n.options[e]=o.value}catch(e){}r(`DID_SET_${t}`,{value:n.options[e]})})}),i},J=t=>r=>{const o={};return e(t,e=>{o[`GET_${Q(e,"_").toUpperCase()}`]=(t=>r.options[e])}),o},ee=1,te=2,re=3,oe=4,ne=5,ie=()=>Math.random().toString(36).substring(2,11),se=(e,t)=>e.splice(t,1),ae=()=>{const e=[],t=(t,r)=>{se(e,e.findIndex(e=>e.event===t&&(e.cb===r||!r)))},r=(t,r,o)=>{e.filter(e=>e.event===t).map(e=>e.cb).forEach(e=>((e,t)=>{t?e():document.hidden?Promise.resolve(1).then(e):setTimeout(e,0)})(()=>e(...r),o))};return{fireSync:(e,...t)=>{r(e,t,!0)},fire:(e,...t)=>{r(e,t,!1)},on:(t,r)=>{e.push({event:t,cb:r})},onOnce:(r,o)=>{e.push({event:r,cb:(...e)=>{t(r,o),o(...e)}})},off:t}},le=(e,t,r)=>{Object.getOwnPropertyNames(e).filter(e=>!r.includes(e)).forEach(r=>Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(e,r)))},ce=["fire","process","revert","load","on","off","onOnce","retryLoad","extend","archive","archived","release","released","requestProcessing","freeze"],de=e=>{const t={};return le(e,t,ce),t},pe={INIT:1,IDLE:2,PROCESSING_QUEUED:9,PROCESSING:3,PROCESSING_COMPLETE:5,PROCESSING_ERROR:6,PROCESSING_REVERT_ERROR:10,LOADING:7,LOAD_ERROR:8},Ee={INPUT:1,LIMBO:2,LOCAL:3},ue=e=>/[^0-9]+/.exec(e),_e=()=>ue(1.1.toLocaleString())[0],Te={BOOLEAN:"boolean",INT:"int",NUMBER:"number",STRING:"string",ARRAY:"array",OBJECT:"object",FUNCTION:"function",ACTION:"action",SERVER_API:"serverapi",REGEX:"regex"},Ie=[],fe=(e,t,r)=>new Promise((o,n)=>{const i=Ie.filter(t=>t.key===e).map(e=>e.cb);if(0===i.length)return void o(t);const s=i.shift();i.reduce((e,t)=>e.then(e=>t(e,r)),s(t,r)).then(e=>o(e)).catch(e=>n(e))}),me=(e,t,r)=>Ie.filter(t=>t.key===e).map(e=>e.cb(t,r)),he=(e,t)=>Ie.push({key:e,cb:t}),Re=()=>({...ge}),ge={id:[null,Te.STRING],name:["filepond",Te.STRING],disabled:[!1,Te.BOOLEAN],className:[null,Te.STRING],required:[!1,Te.BOOLEAN],captureMethod:[null,Te.STRING],allowSyncAcceptAttribute:[!0,Te.BOOLEAN],allowDrop:[!0,Te.BOOLEAN],allowBrowse:[!0,Te.BOOLEAN],allowPaste:[!0,Te.BOOLEAN],allowMultiple:[!1,Te.BOOLEAN],allowReplace:[!0,Te.BOOLEAN],allowRevert:[!0,Te.BOOLEAN],allowRemove:[!0,Te.BOOLEAN],allowProcess:[!0,Te.BOOLEAN],allowReorder:[!1,Te.BOOLEAN],allowDirectoriesOnly:[!1,Te.BOOLEAN],storeAsFile:[!1,Te.BOOLEAN],forceRevert:[!1,Te.BOOLEAN],maxFiles:[null,Te.INT],checkValidity:[!1,Te.BOOLEAN],itemInsertLocationFreedom:[!0,Te.BOOLEAN],itemInsertLocation:["before",Te.STRING],itemInsertInterval:[75,Te.INT],dropOnPage:[!1,Te.BOOLEAN],dropOnElement:[!0,Te.BOOLEAN],dropValidation:[!1,Te.BOOLEAN],ignoredFiles:[[".ds_store","thumbs.db","desktop.ini"],Te.ARRAY],instantUpload:[!0,Te.BOOLEAN],maxParallelUploads:[2,Te.INT],allowMinimumUploadDuration:[!0,Te.BOOLEAN],chunkUploads:[!1,Te.BOOLEAN],chunkForce:[!1,Te.BOOLEAN],chunkSize:[5e6,Te.INT],chunkRetryDelays:[[500,1e3,3e3],Te.ARRAY],server:[null,Te.SERVER_API],fileSizeBase:[1e3,Te.INT],labelFileSizeBytes:["bytes",Te.STRING],labelFileSizeKilobytes:["KB",Te.STRING],labelFileSizeMegabytes:["MB",Te.STRING],labelFileSizeGigabytes:["GB",Te.STRING],labelDecimalSeparator:[_e(),Te.STRING],labelThousandsSeparator:[(()=>{const e=_e(),t=1e3.toLocaleString();return t!==1e3.toString()?ue(t)[0]:"."===e?",":"."})(),Te.STRING],labelIdle:['Drag & Drop your files or <span class="filepond--label-action">Browse</span>',Te.STRING],labelInvalidField:["Field contains invalid files",Te.STRING],labelFileWaitingForSize:["Waiting for size",Te.STRING],labelFileSizeNotAvailable:["Size not available",Te.STRING],labelFileCountSingular:["file in list",Te.STRING],labelFileCountPlural:["files in list",Te.STRING],labelFileLoading:["Loading",Te.STRING],labelFileAdded:["Added",Te.STRING],labelFileLoadError:["Error during load",Te.STRING],labelFileRemoved:["Removed",Te.STRING],labelFileRemoveError:["Error during remove",Te.STRING],labelFileProcessing:["Uploading",Te.STRING],labelFileProcessingComplete:["Upload complete",Te.STRING],labelFileProcessingAborted:["Upload cancelled",Te.STRING],labelFileProcessingError:["Error during upload",Te.STRING],labelFileProcessingRevertError:["Error during revert",Te.STRING],labelTapToCancel:["tap to cancel",Te.STRING],labelTapToRetry:["tap to retry",Te.STRING],labelTapToUndo:["tap to undo",Te.STRING],labelButtonRemoveItem:["Remove",Te.STRING],labelButtonAbortItemLoad:["Abort",Te.STRING],labelButtonRetryItemLoad:["Retry",Te.STRING],labelButtonAbortItemProcessing:["Cancel",Te.STRING],labelButtonUndoItemProcessing:["Undo",Te.STRING],labelButtonRetryItemProcessing:["Retry",Te.STRING],labelButtonProcessItem:["Upload",Te.STRING],iconRemove:['<svg width="26" height="26" viewBox="0 0 26 26" xmlns="http://www.w3.org/2000/svg"><path d="M11.586 13l-2.293 2.293a1 1 0 0 0 1.414 1.414L13 14.414l2.293 2.293a1 1 0 0 0 1.414-1.414L14.414 13l2.293-2.293a1 1 0 0 0-1.414-1.414L13 11.586l-2.293-2.293a1 1 0 0 0-1.414 1.414L11.586 13z" fill="currentColor" fill-rule="nonzero"/></svg>',Te.STRING],iconProcess:['<svg width="26" height="26" viewBox="0 0 26 26" xmlns="http://www.w3.org/2000/svg"><path d="M14 10.414v3.585a1 1 0 0 1-2 0v-3.585l-1.293 1.293a1 1 0 0 1-1.414-1.415l3-3a1 1 0 0 1 1.414 0l3 3a1 1 0 0 1-1.414 1.415L14 10.414zM9 18a1 1 0 0 1 0-2h8a1 1 0 0 1 0 2H9z" fill="currentColor" fill-rule="evenodd"/></svg>',Te.STRING],iconRetry:['<svg width="26" height="26" viewBox="0 0 26 26" xmlns="http://www.w3.org/2000/svg"><path d="M10.81 9.185l-.038.02A4.997 4.997 0 0 0 8 13.683a5 5 0 0 0 5 5 5 5 0 0 0 5-5 1 1 0 0 1 2 0A7 7 0 1 1 9.722 7.496l-.842-.21a.999.999 0 1 1 .484-1.94l3.23.806c.535.133.86.675.73 1.21l-.804 3.233a.997.997 0 0 1-1.21.73.997.997 0 0 1-.73-1.21l.23-.928v-.002z" fill="currentColor" fill-rule="nonzero"/></svg>',Te.STRING],iconUndo:['<svg width="26" height="26" viewBox="0 0 26 26" xmlns="http://www.w3.org/2000/svg"><path d="M9.185 10.81l.02-.038A4.997 4.997 0 0 1 13.683 8a5 5 0 0 1 5 5 5 5 0 0 1-5 5 1 1 0 0 0 0 2A7 7 0 1 0 7.496 9.722l-.21-.842a.999.999 0 1 0-1.94.484l.806 3.23c.133.535.675.86 1.21.73l3.233-.803a.997.997 0 0 0 .73-1.21.997.997 0 0 0-1.21-.73l-.928.23-.002-.001z" fill="currentColor" fill-rule="nonzero"/></svg>',Te.STRING],iconDone:['<svg width="26" height="26" viewBox="0 0 26 26" xmlns="http://www.w3.org/2000/svg"><path d="M18.293 9.293a1 1 0 0 1 1.414 1.414l-7.002 7a1 1 0 0 1-1.414 0l-3.998-4a1 1 0 1 1 1.414-1.414L12 15.586l6.294-6.293z" fill="currentColor" fill-rule="nonzero"/></svg>',Te.STRING],oninit:[null,Te.FUNCTION],onwarning:[null,Te.FUNCTION],onerror:[null,Te.FUNCTION],onactivatefile:[null,Te.FUNCTION],oninitfile:[null,Te.FUNCTION],onaddfilestart:[null,Te.FUNCTION],onaddfileprogress:[null,Te.FUNCTION],onaddfile:[null,Te.FUNCTION],onprocessfilestart:[null,Te.FUNCTION],onprocessfileprogress:[null,Te.FUNCTION],onprocessfileabort:[null,Te.FUNCTION],onprocessfilerevert:[null,Te.FUNCTION],onprocessfile:[null,Te.FUNCTION],onprocessfiles:[null,Te.FUNCTION],onremovefile:[null,Te.FUNCTION],onpreparefile:[null,Te.FUNCTION],onupdatefiles:[null,Te.FUNCTION],onreorderfiles:[null,Te.FUNCTION],beforeDropFile:[null,Te.FUNCTION],beforeAddFile:[null,Te.FUNCTION],beforeRemoveFile:[null,Te.FUNCTION],beforePrepareFile:[null,Te.FUNCTION],stylePanelLayout:[null,Te.STRING],stylePanelAspectRatio:[null,Te.STRING],styleItemPanelAspectRatio:[null,Te.STRING],styleButtonRemoveItemPosition:["left",Te.STRING],styleButtonProcessItemPosition:["right",Te.STRING],styleLoadIndicatorPosition:["right",Te.STRING],styleProgressIndicatorPosition:["right",Te.STRING],styleButtonRemoveItemAlign:[!1,Te.BOOLEAN],files:[[],Te.ARRAY],credits:[["https://pqina.nl/","Powered by PQINA"],Te.ARRAY]},Oe=(e,t)=>b(t)?e[0]||null:V(t)?e[t]||null:("object"==typeof t&&(t=t.id),e.find(e=>e.id===t)||null),De=e=>{if(b(e))return e;if(/:/.test(e)){const t=e.split(":");return t[1]/t[0]}return parseFloat(e)},Se=e=>e.filter(e=>!e.archived),ye={EMPTY:0,IDLE:1,ERROR:2,BUSY:3,READY:4};let Ae=null;const Le=[pe.LOAD_ERROR,pe.PROCESSING_ERROR,pe.PROCESSING_REVERT_ERROR],Pe=[pe.LOADING,pe.PROCESSING,pe.PROCESSING_QUEUED,pe.INIT],ve=[pe.PROCESSING_COMPLETE],Me=e=>Le.includes(e.status),be=e=>Pe.includes(e.status),Ce=e=>ve.includes(e.status),Ne=e=>X(e.options.server)&&(X(e.options.server.process)||Y(e.options.server.process)),we=e=>({GET_STATUS:()=>{const t=Se(e.items),{EMPTY:r,ERROR:o,BUSY:n,IDLE:i,READY:s}=ye;return 0===t.length?r:t.some(Me)?o:t.some(be)?n:t.some(Ce)?s:i},GET_ITEM:t=>Oe(e.items,t),GET_ACTIVE_ITEM:t=>Oe(Se(e.items),t),GET_ACTIVE_ITEMS:()=>Se(e.items),GET_ITEMS:()=>e.items,GET_ITEM_NAME:t=>{const r=Oe(e.items,t);return r?r.filename:null},GET_ITEM_SIZE:t=>{const r=Oe(e.items,t);return r?r.fileSize:null},GET_STYLES:()=>Object.keys(e.options).filter(e=>/^style/.test(e)).map(t=>({name:t,value:e.options[t]})),GET_PANEL_ASPECT_RATIO:()=>{return/circle/.test(e.options.stylePanelLayout)?1:De(e.options.stylePanelAspectRatio)},GET_ITEM_PANEL_ASPECT_RATIO:()=>e.options.styleItemPanelAspectRatio,GET_ITEMS_BY_STATUS:t=>Se(e.items).filter(e=>e.status===t),GET_TOTAL_ITEMS:()=>Se(e.items).length,SHOULD_UPDATE_FILE_INPUT:()=>e.options.storeAsFile&&(()=>{if(null===Ae)try{const e=new DataTransfer;e.items.add(new File(["hello world"],"This_Works.txt"));const t=document.createElement("input");t.setAttribute("type","file"),t.files=e.files,Ae=1===t.files.length}catch(e){Ae=!1}return Ae})()&&!Ne(e),IS_ASYNC:()=>Ne(e),GET_FILE_SIZE_LABELS:e=>({labelBytes:e("GET_LABEL_FILE_SIZE_BYTES")||void 0,labelKilobytes:e("GET_LABEL_FILE_SIZE_KILOBYTES")||void 0,labelMegabytes:e("GET_LABEL_FILE_SIZE_MEGABYTES")||void 0,labelGigabytes:e("GET_LABEL_FILE_SIZE_GIGABYTES")||void 0})}),Ge=(e,t,r)=>Math.max(Math.min(r,e),t),Ue=e=>/^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*)\s*$/i.test(e),Be=e=>`${e}`.split("/").pop().split("?").shift(),Fe=e=>e.split(".").pop(),qe=(e,t="")=>(t+e).slice(-t.length),Ve=(e=new Date)=>`${e.getFullYear()}-${qe(e.getMonth()+1,"00")}-${qe(e.getDate(),"00")}_${qe(e.getHours(),"00")}-${qe(e.getMinutes(),"00")}-${qe(e.getSeconds(),"00")}`,xe=(e,t,r=null,o=null)=>{const n="string"==typeof r?e.slice(0,e.size,r):e.slice(0,e.size,e.type);return n.lastModifiedDate=new Date,e._relativePath&&(n._relativePath=e._relativePath),U(t)||(t=Ve()),t&&null===o&&Fe(t)?n.name=t:(o=o||(e=>{if("string"!=typeof e)return"";const t=e.split("/").pop();return/svg/.test(t)?"svg":/zip|compressed/.test(t)?"zip":/plain/.test(t)?"txt":/msword/.test(t)?"doc":/[a-z]+/.test(t)?"jpeg"===t?"jpg":t:""})(n.type),n.name=t+(o?"."+o:"")),n},Ye=(e,t)=>{const r=(()=>window.BlobBuilder=window.BlobBuilder||window.WebKitBlobBuilder||window.MozBlobBuilder||window.MSBlobBuilder)();if(r){const o=new r;return o.append(e),o.getBlob(t)}return new Blob([e],{type:t})},He=e=>(/^data:(.+);/.exec(e)||[])[1]||null,ke=e=>{const t=He(e);return((e,t)=>{const r=new ArrayBuffer(e.length),o=new Uint8Array(r);for(let t=0;t<e.length;t++)o[t]=e.charCodeAt(t);return Ye(r,t)})((e=>atob((e=>e.split(",")[1].replace(/\s/g,""))(e)))(e),t)},Xe=e=>{if(!/^content-disposition:/i.test(e))return null;const t=e.split(/filename=|filename\*=.+''/).splice(1).map(e=>e.trim().replace(/^["']|[;"']{0,2}$/g,"")).filter(e=>e.length);return t.length?decodeURI(t[t.length-1]):null},$e=e=>{if(/content-length:/i.test(e)){const t=e.match(/[0-9]+/)[0];return t?parseInt(t,10):null}return null},We=e=>{if(/x-content-transfer-id:/i.test(e)){return(e.split(":")[1]||"").trim()||null}return null},ze=e=>{const t={source:null,name:null,size:null},r=e.split("\n");for(let e of r){const r=Xe(e);if(r){t.name=r;continue}const o=$e(e);if(o){t.size=o;continue}const n=We(e);n&&(t.source=n)}return t},je=e=>{const t={source:null,complete:!1,progress:0,size:null,timestamp:null,duration:0,request:null},r=r=>{e?(t.timestamp=Date.now(),t.request=e(r,e=>{t.duration=Date.now()-t.timestamp,t.complete=!0,e instanceof Blob&&(e=xe(e,e.name||Be(r))),o.fire("load",e instanceof Blob?e:e?e.body:null)},e=>{o.fire("error","string"==typeof e?{type:"error",code:0,body:e}:e)},(e,r,n)=>{n&&(t.size=n),t.duration=Date.now()-t.timestamp,e?(t.progress=r/n,o.fire("progress",t.progress)):t.progress=null},()=>{o.fire("abort")},e=>{const r=ze("string"==typeof e?e:e.headers);o.fire("meta",{size:t.size||r.size,filename:r.name,source:r.source})})):o.fire("error",{type:"error",body:"Can't load URL",code:400})},o={...ae(),setSource:e=>t.source=e,getProgress:()=>t.progress,abort:()=>{t.request&&t.request.abort&&t.request.abort()},load:()=>{const e=t.source;o.fire("init",e),e instanceof File?o.fire("load",e):e instanceof Blob?o.fire("load",xe(e,e.name)):Ue(e)?o.fire("load",((e,t,r)=>xe(ke(e),t,null,r))(e)):r(e)}};return o},Qe=e=>/GET|HEAD/.test(e),Ze=(e,t,r)=>{const o={onheaders:()=>{},onprogress:()=>{},onload:()=>{},ontimeout:()=>{},onerror:()=>{},onabort:()=>{},abort:()=>{n=!0,s.abort()}};let n=!1,i=!1;r={method:"POST",headers:{},withCredentials:!1,...r},t=encodeURI(t),Qe(r.method)&&e&&(t=`${t}${encodeURIComponent("string"==typeof e?e:JSON.stringify(e))}`);const s=new XMLHttpRequest;return(Qe(r.method)?s:s.upload).onprogress=(e=>{n||o.onprogress(e.lengthComputable,e.loaded,e.total)}),s.onreadystatechange=(()=>{s.readyState<2||4===s.readyState&&0===s.status||i||(i=!0,o.onheaders(s))}),s.onload=(()=>{s.status>=200&&s.status<300?o.onload(s):o.onerror(s)}),s.onerror=(()=>o.onerror(s)),s.onabort=(()=>{n=!0,o.onabort()}),s.ontimeout=(()=>o.ontimeout(s)),s.open(r.method,t,!0),V(r.timeout)&&(s.timeout=r.timeout),Object.keys(r.headers).forEach(e=>{const t=unescape(encodeURIComponent(r.headers[e]));s.setRequestHeader(e,t)}),r.responseType&&(s.responseType=r.responseType),r.withCredentials&&(s.withCredentials=!0),s.send(e),o},Ke=(e,t,r,o)=>({type:e,code:t,body:r,headers:o}),Je=e=>t=>{e(Ke("error",0,"Timeout",t.getAllResponseHeaders()))},et=e=>/\?/.test(e),tt=(...e)=>{let t="";return e.forEach(e=>{t+=et(t)&&et(e)?e.replace(/\?/,"&"):e}),t},rt=(e="",t)=>{if("function"==typeof t)return t;if(!t||!U(t.url))return null;const r=t.onload||(e=>e),o=t.onerror||(e=>null);return(n,i,s,a,l,c)=>{const d=Ze(n,tt(e,t.url),{...t,responseType:"blob"});return d.onload=(e=>{const o=e.getAllResponseHeaders(),s=ze(o).name||Be(n);i(Ke("load",e.status,"HEAD"===t.method?null:xe(r(e.response),s),o))}),d.onerror=(e=>{s(Ke("error",e.status,o(e.response)||e.statusText,e.getAllResponseHeaders()))}),d.onheaders=(e=>{c(Ke("headers",e.status,null,e.getAllResponseHeaders()))}),d.ontimeout=Je(s),d.onprogress=a,d.onabort=l,d}},ot=0,nt=1,it=2,st=3,at=4,lt=(e,t,r,o,n,i,s,a,l,c,d)=>{const p=[],{chunkTransferId:E,chunkServer:u,chunkSize:_,chunkRetryDelays:T}=d,I={serverId:E,aborted:!1},f=t.ondata||(e=>e),m=t.onload||((e,t)=>"HEAD"===t?e.getResponseHeader("Upload-Offset"):e.response),h=t.onerror||(e=>null),R=Math.floor(o.size/_);for(let e=0;e<=R;e++){const t=e*_,r=o.slice(t,t+_,"application/offset+octet-stream");p[e]={index:e,size:r.size,offset:t,data:r,file:o,progress:0,retries:[...T],status:ot,error:null,request:null,timeout:null}}const g=e=>e.status===ot||e.status===st,O=t=>{if(I.aborted)return;if(!(t=t||p.find(g)))return void(p.every(e=>e.status===nt)&&(()=>i(I.serverId))());t.status=it,t.progress=null;const r=u.ondata||(e=>e),n=u.onerror||(e=>null),a=u.onload||(()=>{}),c=tt(e,u.url,I.serverId),d="function"==typeof u.headers?u.headers(t):{...u.headers,"Content-Type":"application/offset+octet-stream","Upload-Offset":t.offset,"Upload-Length":o.size,"Upload-Name":o.name},E=t.request=Ze(r(t.data),c,{...u,headers:d});E.onload=(e=>{a(e,t.index,p.length),t.status=nt,t.request=null,y()}),E.onprogress=((e,r,o)=>{t.progress=e?r:null,S()}),E.onerror=(e=>{t.status=st,t.request=null,t.error=n(e.response)||e.statusText,D(t)||s(Ke("error",e.status,n(e.response)||e.statusText,e.getAllResponseHeaders()))}),E.ontimeout=(e=>{t.status=st,t.request=null,D(t)||Je(s)(e)}),E.onabort=(()=>{t.status=ot,t.request=null,l()})},D=e=>0!==e.retries.length&&(e.status=at,clearTimeout(e.timeout),e.timeout=setTimeout(()=>{O(e)},e.retries.shift()),!0),S=()=>{const e=p.reduce((e,t)=>null===e||null===t.progress?null:e+t.progress,0);if(null===e)return a(!1,0,0);const t=p.reduce((e,t)=>e+t.size,0);a(!0,e,t)},y=()=>{p.filter(e=>e.status===it).length>=1||O()};return I.serverId?(r=>{const o=tt(e,u.url,I.serverId),n={headers:"function"==typeof t.headers?t.headers(I.serverId):{...t.headers},method:"HEAD"},i=Ze(null,o,n);i.onload=(e=>r(m(e,n.method))),i.onerror=(e=>s(Ke("error",e.status,h(e.response)||e.statusText,e.getAllResponseHeaders()))),i.ontimeout=Je(s)})(e=>{I.aborted||(p.filter(t=>t.offset<e).forEach(e=>{e.status=nt,e.progress=e.size}),y())}):(i=>{const a=new FormData;X(n)&&a.append(r,JSON.stringify(n));const l="function"==typeof t.headers?t.headers(o,n):{...t.headers,"Upload-Length":o.size},c={...t,headers:l},d=Ze(f(a),tt(e,t.url),c);d.onload=(e=>i(m(e,c.method))),d.onerror=(e=>s(Ke("error",e.status,h(e.response)||e.statusText,e.getAllResponseHeaders()))),d.ontimeout=Je(s)})(e=>{I.aborted||(c(e),I.serverId=e,y())}),{abort:()=>{I.aborted=!0,p.forEach(e=>{clearTimeout(e.timeout),e.request&&e.request.abort()})}}},ct=(e="",t,r,o)=>"function"==typeof t?(...e)=>t(r,...e,o):t&&U(t.url)?((e,t,r,o)=>(n,i,s,a,l,c,d)=>{if(!n)return;const p=o.chunkUploads,E=p&&n.size>o.chunkSize,u=p&&(E||o.chunkForce);if(n instanceof Blob&&u)return lt(e,t,r,n,i,s,a,l,c,d,o);const _=t.ondata||(e=>e),T=t.onload||(e=>e),I=t.onerror||(e=>null),f="function"==typeof t.headers?t.headers(n,i)||{}:{...t.headers},m={...t,headers:f};var h=new FormData;X(i)&&h.append(r,JSON.stringify(i)),(n instanceof Blob?[{name:null,file:n}]:n).forEach(e=>{h.append(r,e.file,null===e.name?e.file.name:`${e.name}${e.file.name}`)});const R=Ze(_(h),tt(e,t.url),m);return R.onload=(e=>{s(Ke("load",e.status,T(e.response),e.getAllResponseHeaders()))}),R.onerror=(e=>{a(Ke("error",e.status,I(e.response)||e.statusText,e.getAllResponseHeaders()))}),R.ontimeout=Je(a),R.onprogress=l,R.onabort=c,R})(e,t,r,o):null,dt=(e="",t)=>{if("function"==typeof t)return t;if(!t||!U(t.url))return(e,t)=>t();const r=t.onload||(e=>e),o=t.onerror||(e=>null);return(n,i,s)=>{const a=Ze(n,e+t.url,t);return a.onload=(e=>{i(Ke("load",e.status,r(e.response),e.getAllResponseHeaders()))}),a.onerror=(e=>{s(Ke("error",e.status,o(e.response)||e.statusText,e.getAllResponseHeaders()))}),a.ontimeout=Je(s),a}},pt=(e=0,t=1)=>e+Math.random()*(t-e),Et=(e,t)=>{const r={complete:!1,perceivedProgress:0,perceivedPerformanceUpdater:null,progress:null,timestamp:null,perceivedDuration:0,duration:0,request:null,response:null},{allowMinimumUploadDuration:o}=t,n=()=>{r.request&&(r.perceivedPerformanceUpdater.clear(),r.request.abort&&r.request.abort(),r.complete=!0)},i=o?()=>r.progress?Math.min(r.progress,r.perceivedProgress):null:()=>r.progress||null,s=o?()=>Math.min(r.duration,r.perceivedDuration):()=>r.duration,a={...ae(),process:(t,n)=>{const i=()=>{0!==r.duration&&null!==r.progress&&a.fire("progress",a.getProgress())},s=()=>{r.complete=!0,a.fire("load-perceived",r.response.body)};a.fire("start"),r.timestamp=Date.now(),r.perceivedPerformanceUpdater=((e,t=1e3,r=0,o=25,n=250)=>{let i=null;const s=Date.now(),a=()=>{let r=Date.now()-s,l=pt(o,n);r+l>t&&(l=r+l-t);let c=r/t;c>=1||document.hidden?e(1):(e(c),i=setTimeout(a,l))};return t>0&&a(),{clear:()=>{clearTimeout(i)}}})(e=>{r.perceivedProgress=e,r.perceivedDuration=Date.now()-r.timestamp,i(),r.response&&1===r.perceivedProgress&&!r.complete&&s()},o?pt(750,1500):0),r.request=e(t,n,e=>{r.response=X(e)?e:{type:"load",code:200,body:`${e}`,headers:{}},r.duration=Date.now()-r.timestamp,r.progress=1,a.fire("load",r.response.body),(!o||o&&1===r.perceivedProgress)&&s()},e=>{r.perceivedPerformanceUpdater.clear(),a.fire("error",X(e)?e:{type:"error",code:0,body:`${e}`})},(e,t,o)=>{r.duration=Date.now()-r.timestamp,r.progress=e?t/o:null,i()},()=>{r.perceivedPerformanceUpdater.clear(),a.fire("abort",r.response?r.response.body:null)},e=>{a.fire("transfer",e)})},abort:n,getProgress:i,getDuration:s,reset:()=>{n(),r.complete=!1,r.perceivedProgress=0,r.progress=0,r.timestamp=null,r.perceivedDuration=0,r.duration=0,r.request=null,r.response=null}};return a},ut=e=>e.substring(0,e.lastIndexOf("."))||e,_t=e=>!!(e instanceof File||e instanceof Blob&&e.name),Tt=e=>{if(!X(e))return e;const t=M(e)?[]:{};for(const r in e){if(!e.hasOwnProperty(r))continue;const o=e[r];t[r]=o&&X(o)?Tt(o):o}return t},It=(e=null,r=null,o=null)=>{const n=ie(),i={archived:!1,frozen:!1,released:!1,source:null,file:o,serverFileReference:r,transferId:null,processingAborted:!1,status:r?pe.PROCESSING_COMPLETE:pe.INIT,activeLoader:null,activeProcessor:null};let s=null;const a={},l=e=>i.status=e,c=(e,...t)=>{i.released||i.frozen||E.fire(e,...t)},d=(e,t)=>{if(i.processingAborted)return void(i.processingAborted=!1);if(l(pe.PROCESSING),s=null,!(i.file instanceof Blob))return void E.on("load",()=>{d(e,t)});e.on("load",e=>{i.transferId=null,i.serverFileReference=e}),e.on("transfer",e=>{i.transferId=e}),e.on("load-perceived",e=>{i.activeProcessor=null,i.transferId=null,i.serverFileReference=e,l(pe.PROCESSING_COMPLETE),c("process-complete",e)}),e.on("start",()=>{c("process-start")}),e.on("error",e=>{i.activeProcessor=null,l(pe.PROCESSING_ERROR),c("process-error",e)}),e.on("abort",e=>{i.activeProcessor=null,i.serverFileReference=e,l(pe.IDLE),c("process-abort"),s&&s()}),e.on("progress",e=>{c("process-progress",e)});const r=console.error;t(i.file,t=>{i.archived||e.process(t,{...a})},r),i.activeProcessor=e},p=(e,t,r)=>{const o=e.split("."),n=o[0],i=o.pop();let s=a;o.forEach(e=>s=s[e]),JSON.stringify(s[i])!==JSON.stringify(t)&&(s[i]=t,c("metadata-update",{key:n,value:a[n],silent:r}))},E={id:{get:()=>n},origin:{get:()=>e,set:t=>e=t},serverId:{get:()=>i.serverFileReference},transferId:{get:()=>i.transferId},status:{get:()=>i.status},filename:{get:()=>i.file.name},filenameWithoutExtension:{get:()=>ut(i.file.name)},fileExtension:{get:()=>Fe(i.file.name)},fileType:{get:()=>i.file.type},fileSize:{get:()=>i.file.size},file:{get:()=>i.file},relativePath:{get:()=>i.file._relativePath},source:{get:()=>i.source},getMetadata:e=>Tt(e?a[e]:a),setMetadata:(e,t,r)=>{if(X(e)){const r=e;return Object.keys(r).forEach(e=>{p(e,r[e],t)}),e}return p(e,t,r),t},extend:(e,t)=>u[e]=t,abortLoad:()=>{i.activeLoader?i.activeLoader.abort():(l(pe.INIT),c("load-abort"))},retryLoad:()=>{i.activeLoader&&i.activeLoader.load()},requestProcessing:()=>{i.processingAborted=!1,l(pe.PROCESSING_QUEUED)},abortProcessing:()=>new Promise(e=>{if(!i.activeProcessor)return i.processingAborted=!0,l(pe.IDLE),c("process-abort"),void e();s=(()=>{e()}),i.activeProcessor.abort()}),load:(t,r,o)=>{i.source=t,E.fireSync("init"),i.file?E.fireSync("load-skip"):(i.file=(e=>{let t=[e.name,e.size,e.type];return e instanceof Blob||Ue(e)?t[0]=e.name||Ve():Ue(e)?(t[1]=e.length,t[2]=He(e)):U(e)&&(t[0]=Be(e),t[1]=0,t[2]="application/octet-stream"),{name:t[0],size:t[1],type:t[2]}})(t),r.on("init",()=>{c("load-init")}),r.on("meta",t=>{i.file.size=t.size,i.file.filename=t.filename,t.source&&(e=Ee.LIMBO,i.serverFileReference=t.source,i.status=pe.PROCESSING_COMPLETE),c("load-meta")}),r.on("progress",e=>{l(pe.LOADING),c("load-progress",e)}),r.on("error",e=>{l(pe.LOAD_ERROR),c("load-request-error",e)}),r.on("abort",()=>{l(pe.INIT),c("load-abort")}),r.on("load",t=>{i.activeLoader=null;const r=t=>{i.file=_t(t)?t:i.file,e===Ee.LIMBO&&i.serverFileReference?l(pe.PROCESSING_COMPLETE):l(pe.IDLE),c("load")};i.serverFileReference?r(t):o(t,r,e=>{i.file=t,c("load-meta"),l(pe.LOAD_ERROR),c("load-file-error",e)})}),r.setSource(t),i.activeLoader=r,r.load())},process:d,revert:(e,t)=>new Promise((r,o)=>{const n=null!==i.serverFileReference?i.serverFileReference:i.transferId;null!==n?(e(n,()=>{i.serverFileReference=null,i.transferId=null,r()},e=>{t?(l(pe.PROCESSING_REVERT_ERROR),c("process-revert-error"),o(e)):r()}),l(pe.IDLE),c("process-revert")):r()}),...ae(),freeze:()=>i.frozen=!0,release:()=>i.released=!0,released:{get:()=>i.released},archive:()=>i.archived=!0,archived:{get:()=>i.archived},setFile:e=>i.file=e},u=t(E);return u},ft=(e,t)=>{const r=((e,t)=>b(t)?0:U(t)?e.findIndex(e=>e.id===t):-1)(e,t);if(!(r<0))return e[r]||null},mt=(e,t,r,o,n,i)=>{const s=Ze(null,e,{method:"GET",responseType:"blob"});return s.onload=(r=>{const o=r.getAllResponseHeaders(),n=ze(o).name||Be(e);t(Ke("load",r.status,xe(r.response,n),o))}),s.onerror=(e=>{r(Ke("error",e.status,e.statusText,e.getAllResponseHeaders()))}),s.onheaders=(e=>{i(Ke("headers",e.status,null,e.getAllResponseHeaders()))}),s.ontimeout=Je(r),s.onprogress=o,s.onabort=n,s},ht=e=>(0===e.indexOf("//")&&(e=location.protocol+e),e.toLowerCase().replace("blob:","").replace(/([a-z])?:\/\//,"$1").split("/")[0]),Rt=e=>(...t)=>Y(e)?e(...t):e,gt=(e,t)=>{clearTimeout(t.listUpdateTimeout),t.listUpdateTimeout=setTimeout(()=>{e("DID_UPDATE_ITEMS",{items:Se(t.items)})},0)},Ot=(e,...t)=>new Promise(r=>{if(!e)return r(!0);const o=e(...t);return null==o?r(!0):"boolean"==typeof o?r(o):void("function"==typeof o.then&&o.then(r))}),Dt=(e,t)=>{e.items.sort((e,r)=>t(de(e),de(r)))},St=(e,t)=>({query:r,success:o=(()=>{}),failure:n=(()=>{}),...i}={})=>{const s=Oe(e.items,r);s?t(s,o,n,i||{}):n({error:Ke("error",0,"Item not found"),file:null})},yt=(e,t,r)=>({ABORT_ALL:()=>{Se(r.items).forEach(e=>{e.freeze(),e.abortLoad(),e.abortProcessing()})},DID_SET_FILES:({value:t=[]})=>{const o=t.map(e=>({source:e.source?e.source:e,options:e.options}));let n=Se(r.items);n.forEach(t=>{o.find(e=>e.source===t.source||e.source===t.file)||e("REMOVE_ITEM",{query:t,remove:!1})}),n=Se(r.items),o.forEach((t,r)=>{n.find(e=>e.source===t.source||e.file===t.source)||e("ADD_ITEM",{...t,interactionMethod:ne,index:r})})},DID_UPDATE_ITEM_METADATA:({id:o,action:n,change:i})=>{i.silent||(clearTimeout(r.itemUpdateTimeout),r.itemUpdateTimeout=setTimeout(()=>{const s=ft(r.items,o);if(!t("IS_ASYNC"))return void fe("SHOULD_PREPARE_OUTPUT",!1,{item:s,query:t,action:n,change:i}).then(r=>{const n=t("GET_BEFORE_PREPARE_FILE");n&&(r=n(s,r)),r&&e("REQUEST_PREPARE_OUTPUT",{query:o,item:s,success:t=>{e("DID_PREPARE_OUTPUT",{id:o,file:t})}},!0)});s.origin===Ee.LOCAL&&e("DID_LOAD_ITEM",{id:s.id,error:null,serverFileReference:s.source});const a=()=>{setTimeout(()=>{e("REQUEST_ITEM_PROCESSING",{query:o})},32)};return s.status===pe.PROCESSING_COMPLETE?(e=>{s.revert(dt(r.options.server.url,r.options.server.revert),t("GET_FORCE_REVERT")).then(e?a:()=>{}).catch(()=>{})})(r.options.instantUpload):s.status===pe.PROCESSING?(e=>{s.abortProcessing().then(e?a:()=>{})})(r.options.instantUpload):void(r.options.instantUpload&&a())},0))},MOVE_ITEM:({query:e,index:t})=>{const o=Oe(r.items,e);if(!o)return;const n=r.items.indexOf(o);n!==(t=Ge(t,0,r.items.length-1))&&r.items.splice(t,0,r.items.splice(n,1)[0])},SORT:({compare:o})=>{Dt(r,o),e("DID_SORT_ITEMS",{items:t("GET_ACTIVE_ITEMS")})},ADD_ITEMS:({items:r,index:o,interactionMethod:n,success:i=(()=>{}),failure:s=(()=>{})})=>{let a=o;if(-1===o||void 0===o){const e=t("GET_ITEM_INSERT_LOCATION"),r=t("GET_TOTAL_ITEMS");a="before"===e?0:r}const l=t("GET_IGNORED_FILES"),c=r.filter(e=>_t(e)?!l.includes(e.name.toLowerCase()):!b(e)).map(t=>new Promise((r,o)=>{e("ADD_ITEM",{interactionMethod:n,source:t.source||t,success:r,failure:o,index:a++,options:t.options||{}})}));Promise.all(c).then(i).catch(s)},ADD_ITEM:({source:o,index:n=-1,interactionMethod:i,success:s=(()=>{}),failure:a=(()=>{}),options:l={}})=>{if(b(o))return void a({error:Ke("error",0,"No source"),file:null});if(_t(o)&&r.options.ignoredFiles.includes(o.name.toLowerCase()))return;if(!(e=>{const t=Se(e.items).length;if(!e.options.allowMultiple)return 0===t;const r=e.options.maxFiles;return null===r||t<r})(r)){if(r.options.allowMultiple||!r.options.allowMultiple&&!r.options.allowReplace){const t=Ke("warning",0,"Max files");return e("DID_THROW_MAX_FILES",{source:o,error:t}),void a({error:t,file:null})}const c=Se(r.items)[0];if(c.status===pe.PROCESSING_COMPLETE||c.status===pe.PROCESSING_REVERT_ERROR){const d=t("GET_FORCE_REVERT");if(c.revert(dt(r.options.server.url,r.options.server.revert),d).then(()=>{d&&e("ADD_ITEM",{source:o,index:n,interactionMethod:i,success:s,failure:a,options:l})}).catch(()=>{}),d)return}e("REMOVE_ITEM",{query:c.id})}const c="local"===l.type?Ee.LOCAL:"limbo"===l.type?Ee.LIMBO:Ee.INPUT,d=It(c,c===Ee.INPUT?null:o,l.file);Object.keys(l.metadata||{}).forEach(e=>{d.setMetadata(e,l.metadata[e])}),me("DID_CREATE_ITEM",d,{query:t,dispatch:e});const p=t("GET_ITEM_INSERT_LOCATION");r.options.itemInsertLocationFreedom||(n="before"===p?-1:r.items.length),((e,t,r)=>b(t)?null:void 0===r?(e.push(t),t):(((e,t,r)=>e.splice(t,0,r))(e,r=Ge(r,0,e.length),t),t))(r.items,d,n),Y(p)&&o&&Dt(r,p);const E=d.id;d.on("init",()=>{e("DID_INIT_ITEM",{id:E})}),d.on("load-init",()=>{e("DID_START_ITEM_LOAD",{id:E})}),d.on("load-meta",()=>{e("DID_UPDATE_ITEM_META",{id:E})}),d.on("load-progress",t=>{e("DID_UPDATE_ITEM_LOAD_PROGRESS",{id:E,progress:t})}),d.on("load-request-error",t=>{const o=Rt(r.options.labelFileLoadError)(t);if(t.code>=400&&t.code<500)return e("DID_THROW_ITEM_INVALID",{id:E,error:t,status:{main:o,sub:`${t.code} (${t.body})`}}),void a({error:t,file:de(d)});e("DID_THROW_ITEM_LOAD_ERROR",{id:E,error:t,status:{main:o,sub:r.options.labelTapToRetry}})}),d.on("load-file-error",t=>{e("DID_THROW_ITEM_INVALID",{id:E,error:t.status,status:t.status}),a({error:t.status,file:de(d)})}),d.on("load-abort",()=>{e("REMOVE_ITEM",{query:E})}),d.on("load-skip",()=>{d.on("metadata-update",t=>{_t(d.file)&&e("DID_UPDATE_ITEM_METADATA",{id:E,change:t})}),e("COMPLETE_LOAD_ITEM",{query:E,item:d,data:{source:o,success:s}})}),d.on("load",()=>{const n=n=>{n?(d.on("metadata-update",t=>{e("DID_UPDATE_ITEM_METADATA",{id:E,change:t})}),fe("SHOULD_PREPARE_OUTPUT",!1,{item:d,query:t}).then(n=>{const i=t("GET_BEFORE_PREPARE_FILE");i&&(n=i(d,n));const a=()=>{e("COMPLETE_LOAD_ITEM",{query:E,item:d,data:{source:o,success:s}}),gt(e,r)};n?e("REQUEST_PREPARE_OUTPUT",{query:E,item:d,success:t=>{e("DID_PREPARE_OUTPUT",{id:E,file:t}),a()}},!0):a()})):e("REMOVE_ITEM",{query:E})};fe("DID_LOAD_ITEM",d,{query:t,dispatch:e}).then(()=>{Ot(t("GET_BEFORE_ADD_FILE"),de(d)).then(n)}).catch(t=>{if(!t||!t.error||!t.status)return n(!1);e("DID_THROW_ITEM_INVALID",{id:E,error:t.error,status:t.status})})}),d.on("process-start",()=>{e("DID_START_ITEM_PROCESSING",{id:E})}),d.on("process-progress",t=>{e("DID_UPDATE_ITEM_PROCESS_PROGRESS",{id:E,progress:t})}),d.on("process-error",t=>{e("DID_THROW_ITEM_PROCESSING_ERROR",{id:E,error:t,status:{main:Rt(r.options.labelFileProcessingError)(t),sub:r.options.labelTapToRetry}})}),d.on("process-revert-error",t=>{e("DID_THROW_ITEM_PROCESSING_REVERT_ERROR",{id:E,error:t,status:{main:Rt(r.options.labelFileProcessingRevertError)(t),sub:r.options.labelTapToRetry}})}),d.on("process-complete",t=>{e("DID_COMPLETE_ITEM_PROCESSING",{id:E,error:null,serverFileReference:t}),e("DID_DEFINE_VALUE",{id:E,value:t})}),d.on("process-abort",()=>{e("DID_ABORT_ITEM_PROCESSING",{id:E})}),d.on("process-revert",()=>{e("DID_REVERT_ITEM_PROCESSING",{id:E}),e("DID_DEFINE_VALUE",{id:E,value:null})}),e("DID_ADD_ITEM",{id:E,index:n,interactionMethod:i}),gt(e,r);const{url:u,load:_,restore:T,fetch:I}=r.options.server||{};d.load(o,je(c===Ee.INPUT?U(o)&&(e=>(e.indexOf(":")>-1||e.indexOf("//")>-1)&&ht(location.href)!==ht(e))(o)&&I?rt(u,I):mt:rt(u,c===Ee.LIMBO?T:_)),(e,r,o)=>{fe("LOAD_FILE",e,{query:t}).then(r).catch(o)})},REQUEST_PREPARE_OUTPUT:({item:e,success:r,failure:o=(()=>{})})=>{const n={error:Ke("error",0,"Item not found"),file:null};if(e.archived)return o(n);fe("PREPARE_OUTPUT",e.file,{query:t,item:e}).then(i=>{fe("COMPLETE_PREPARE_OUTPUT",i,{query:t,item:e}).then(t=>{if(e.archived)return o(n);r(t)})})},COMPLETE_LOAD_ITEM:({item:o,data:n})=>{const{success:i,source:s}=n,a=t("GET_ITEM_INSERT_LOCATION");if(Y(a)&&s&&Dt(r,a),e("DID_LOAD_ITEM",{id:o.id,error:null,serverFileReference:o.origin===Ee.INPUT?null:s}),i(de(o)),o.origin!==Ee.LOCAL)return o.origin===Ee.LIMBO?(e("DID_COMPLETE_ITEM_PROCESSING",{id:o.id,error:null,serverFileReference:s}),void e("DID_DEFINE_VALUE",{id:o.id,value:o.serverId||s})):void(t("IS_ASYNC")&&r.options.instantUpload&&e("REQUEST_ITEM_PROCESSING",{query:o.id}));e("DID_LOAD_LOCAL_ITEM",{id:o.id})},RETRY_ITEM_LOAD:St(r,e=>{e.retryLoad()}),REQUEST_ITEM_PREPARE:St(r,(t,r,o)=>{e("REQUEST_PREPARE_OUTPUT",{query:t.id,item:t,success:o=>{e("DID_PREPARE_OUTPUT",{id:t.id,file:o}),r({file:t,output:o})},failure:o},!0)}),REQUEST_ITEM_PROCESSING:St(r,(o,n,i)=>{if(o.status===pe.IDLE||o.status===pe.PROCESSING_ERROR)o.status!==pe.PROCESSING_QUEUED&&(o.requestProcessing(),e("DID_REQUEST_ITEM_PROCESSING",{id:o.id}),e("PROCESS_ITEM",{query:o,success:n,failure:i},!0));else{const s=()=>e("REQUEST_ITEM_PROCESSING",{query:o,success:n,failure:i}),a=()=>document.hidden?s():setTimeout(s,32);o.status===pe.PROCESSING_COMPLETE||o.status===pe.PROCESSING_REVERT_ERROR?o.revert(dt(r.options.server.url,r.options.server.revert),t("GET_FORCE_REVERT")).then(a).catch(()=>{}):o.status===pe.PROCESSING&&o.abortProcessing().then(a)}}),PROCESS_ITEM:St(r,(o,n,i)=>{const s=t("GET_MAX_PARALLEL_UPLOADS");if(t("GET_ITEMS_BY_STATUS",pe.PROCESSING).length===s)return void r.processingQueue.push({id:o.id,success:n,failure:i});if(o.status===pe.PROCESSING)return;const a=()=>{const t=r.processingQueue.shift();if(!t)return;const{id:o,success:n,failure:i}=t,s=Oe(r.items,o);s&&!s.archived?e("PROCESS_ITEM",{query:o,success:n,failure:i},!0):a()};o.onOnce("process-complete",()=>{n(de(o)),a();const i=r.options.server;if(r.options.instantUpload&&o.origin===Ee.LOCAL&&Y(i.remove)){const e=()=>{};o.origin=Ee.LIMBO,r.options.server.remove(o.source,e,e)}t("GET_ITEMS_BY_STATUS",pe.PROCESSING_COMPLETE).length===r.items.length&&e("DID_COMPLETE_ITEM_PROCESSING_ALL")}),o.onOnce("process-error",e=>{i({error:e,file:de(o)}),a()});const l=r.options;o.process(Et(ct(l.server.url,l.server.process,l.name,{chunkTransferId:o.transferId,chunkServer:l.server.patch,chunkUploads:l.chunkUploads,chunkForce:l.chunkForce,chunkSize:l.chunkSize,chunkRetryDelays:l.chunkRetryDelays}),{allowMinimumUploadDuration:t("GET_ALLOW_MINIMUM_UPLOAD_DURATION")}),(r,n,i)=>{fe("PREPARE_OUTPUT",r,{query:t,item:o}).then(t=>{e("DID_PREPARE_OUTPUT",{id:o.id,file:t}),n(t)}).catch(i)})}),RETRY_ITEM_PROCESSING:St(r,t=>{e("REQUEST_ITEM_PROCESSING",{query:t})}),REQUEST_REMOVE_ITEM:St(r,r=>{Ot(t("GET_BEFORE_REMOVE_FILE"),de(r)).then(t=>{t&&e("REMOVE_ITEM",{query:r})})}),RELEASE_ITEM:St(r,e=>{e.release()}),REMOVE_ITEM:St(r,(o,n,i,s)=>{const a=()=>{const t=o.id;ft(r.items,t).archive(),e("DID_REMOVE_ITEM",{error:null,id:t,item:o}),gt(e,r),n(de(o))},l=r.options.server;o.origin===Ee.LOCAL&&l&&Y(l.remove)&&!1!==s.remove?(e("DID_START_ITEM_REMOVE",{id:o.id}),l.remove(o.source,()=>a(),t=>{e("DID_THROW_ITEM_REMOVE_ERROR",{id:o.id,error:Ke("error",0,t,null),status:{main:Rt(r.options.labelFileRemoveError)(t),sub:r.options.labelTapToRetry}})})):((s.revert&&o.origin!==Ee.LOCAL&&null!==o.serverId||r.options.chunkUploads&&o.file.size>r.options.chunkSize||r.options.chunkUploads&&r.options.chunkForce)&&o.revert(dt(r.options.server.url,r.options.server.revert),t("GET_FORCE_REVERT")),a())}),ABORT_ITEM_LOAD:St(r,e=>{e.abortLoad()}),ABORT_ITEM_PROCESSING:St(r,t=>{t.serverId?e("REVERT_ITEM_PROCESSING",{id:t.id}):t.abortProcessing().then(()=>{r.options.instantUpload&&e("REMOVE_ITEM",{query:t.id})})}),REQUEST_REVERT_ITEM_PROCESSING:St(r,o=>{if(!r.options.instantUpload)return void e("REVERT_ITEM_PROCESSING",{query:o});const n=t=>{t&&e("REVERT_ITEM_PROCESSING",{query:o})},i=t("GET_BEFORE_REMOVE_FILE");if(!i)return n(!0);const s=i(de(o));return null==s?n(!0):"boolean"==typeof s?n(s):void("function"==typeof s.then&&s.then(n))}),REVERT_ITEM_PROCESSING:St(r,o=>{o.revert(dt(r.options.server.url,r.options.server.revert),t("GET_FORCE_REVERT")).then(()=>{(r.options.instantUpload||(e=>!_t(e.file))(o))&&e("REMOVE_ITEM",{query:o.id})}).catch(()=>{})}),SET_OPTIONS:({options:t})=>{const r=Object.keys(t),o=At.filter(e=>r.includes(e));[...o,...Object.keys(t).filter(e=>!o.includes(e))].forEach(r=>{e(`SET_${Q(r,"_").toUpperCase()}`,{value:t[r]})})}}),At=["server"],Lt=e=>e,Pt=e=>document.createElement(e),vt=(e,t)=>{let r=e.childNodes[0];r?t!==r.nodeValue&&(r.nodeValue=t):(r=document.createTextNode(t),e.appendChild(r))},Mt=(e,t,r,o)=>{const n=(o%360-90)*Math.PI/180;return{x:e+r*Math.cos(n),y:t+r*Math.sin(n)}},bt=(e,t,r,o,n)=>{let i=1;return n>o&&n-o<=.5&&(i=0),o>n&&o-n>=.5&&(i=0),((e,t,r,o,n,i)=>{const s=Mt(e,t,r,n),a=Mt(e,t,r,o);return["M",s.x,s.y,"A",r,r,0,i,0,a.x,a.y].join(" ")})(e,t,r,360*Math.min(.9999,o),360*Math.min(.9999,n),i)},Ct=A({tag:"div",name:"progress-indicator",ignoreRectUpdate:!0,ignoreRect:!0,create:({root:e,props:t})=>{t.spin=!1,t.progress=0,t.opacity=0;const r=i("svg");e.ref.path=i("path",{"stroke-width":2,"stroke-linecap":"round"}),r.appendChild(e.ref.path),e.ref.svg=r,e.appendChild(r)},write:({root:e,props:t})=>{if(0===t.opacity)return;t.align&&(e.element.dataset.align=t.align);const o=parseInt(r(e.ref.path,"stroke-width"),10),n=.5*e.rect.element.width;let i=0,s=0;t.spin?(i=0,s=.5):(i=0,s=t.progress);const a=bt(n,n,n-o,i,s);r(e.ref.path,"d",a),r(e.ref.path,"stroke-opacity",t.spin||t.progress>0?1:0)},mixins:{apis:["progress","spin","align"],styles:["opacity"],animations:{opacity:{type:"tween",duration:500},progress:{type:"spring",stiffness:.95,damping:.65,mass:10}}}}),Nt=A({tag:"button",attributes:{type:"button"},ignoreRect:!0,ignoreRectUpdate:!0,name:"file-action-button",mixins:{apis:["label"],styles:["translateX","translateY","scaleX","scaleY","opacity"],animations:{scaleX:"spring",scaleY:"spring",translateX:"spring",translateY:"spring",opacity:{type:"tween",duration:250}},listeners:!0},create:({root:e,props:t})=>{e.element.innerHTML=(t.icon||"")+`<span>${t.label}</span>`,t.isDisabled=!1},write:({root:e,props:t})=>{const{isDisabled:o}=t,n=e.query("GET_DISABLED")||0===t.opacity;n&&!o?(t.isDisabled=!0,r(e.element,"disabled","disabled")):!n&&o&&(t.isDisabled=!1,e.element.removeAttribute("disabled"))}}),wt=(e,t=".",r=1e3,o={})=>{const{labelBytes:n="bytes",labelKilobytes:i="KB",labelMegabytes:s="MB",labelGigabytes:a="GB"}=o,l=r,c=r*r,d=r*r*r;return(e=Math.round(Math.abs(e)))<l?`${e} ${n}`:e<c?`${Math.floor(e/l)} ${i}`:e<d?`${Gt(e/c,1,t)} ${s}`:`${Gt(e/d,2,t)} ${a}`},Gt=(e,t,r)=>e.toFixed(t).split(".").filter(e=>"0"!==e).join(r),Ut=({root:e,props:t})=>{vt(e.ref.fileSize,wt(e.query("GET_ITEM_SIZE",t.id),".",e.query("GET_FILE_SIZE_BASE"),e.query("GET_FILE_SIZE_LABELS",e.query))),vt(e.ref.fileName,Lt(e.query("GET_ITEM_NAME",t.id)))},Bt=({root:e,props:t})=>{V(e.query("GET_ITEM_SIZE",t.id))?Ut({root:e,props:t}):vt(e.ref.fileSize,e.query("GET_LABEL_FILE_SIZE_NOT_AVAILABLE"))},Ft=A({name:"file-info",ignoreRect:!0,ignoreRectUpdate:!0,write:L({DID_LOAD_ITEM:Ut,DID_UPDATE_ITEM_META:Ut,DID_THROW_ITEM_LOAD_ERROR:Bt,DID_THROW_ITEM_INVALID:Bt}),didCreateView:e=>{me("CREATE_VIEW",{...e,view:e})},create:({root:e,props:t})=>{const o=Pt("span");o.className="filepond--file-info-main",r(o,"aria-hidden","true"),e.appendChild(o),e.ref.fileName=o;const n=Pt("span");n.className="filepond--file-info-sub",e.appendChild(n),e.ref.fileSize=n,vt(n,e.query("GET_LABEL_FILE_WAITING_FOR_SIZE")),vt(o,Lt(e.query("GET_ITEM_NAME",t.id)))},mixins:{styles:["translateX","translateY"],animations:{translateX:"spring",translateY:"spring"}}}),qt=e=>Math.round(100*e),Vt=({root:e,action:t})=>{const r=null===t.progress?e.query("GET_LABEL_FILE_LOADING"):`${e.query("GET_LABEL_FILE_LOADING")} ${qt(t.progress)}%`;vt(e.ref.main,r),vt(e.ref.sub,e.query("GET_LABEL_TAP_TO_CANCEL"))},xt=({root:e})=>{vt(e.ref.main,""),vt(e.ref.sub,"")},Yt=({root:e,action:t})=>{vt(e.ref.main,t.status.main),vt(e.ref.sub,t.status.sub)},Ht=A({name:"file-status",ignoreRect:!0,ignoreRectUpdate:!0,write:L({DID_LOAD_ITEM:xt,DID_REVERT_ITEM_PROCESSING:xt,DID_REQUEST_ITEM_PROCESSING:({root:e})=>{vt(e.ref.main,e.query("GET_LABEL_FILE_PROCESSING")),vt(e.ref.sub,e.query("GET_LABEL_TAP_TO_CANCEL"))},DID_ABORT_ITEM_PROCESSING:({root:e})=>{vt(e.ref.main,e.query("GET_LABEL_FILE_PROCESSING_ABORTED")),vt(e.ref.sub,e.query("GET_LABEL_TAP_TO_RETRY"))},DID_COMPLETE_ITEM_PROCESSING:({root:e})=>{vt(e.ref.main,e.query("GET_LABEL_FILE_PROCESSING_COMPLETE")),vt(e.ref.sub,e.query("GET_LABEL_TAP_TO_UNDO"))},DID_UPDATE_ITEM_PROCESS_PROGRESS:({root:e,action:t})=>{const r=null===t.progress?e.query("GET_LABEL_FILE_PROCESSING"):`${e.query("GET_LABEL_FILE_PROCESSING")} ${qt(t.progress)}%`;vt(e.ref.main,r),vt(e.ref.sub,e.query("GET_LABEL_TAP_TO_CANCEL"))},DID_UPDATE_ITEM_LOAD_PROGRESS:Vt,DID_THROW_ITEM_LOAD_ERROR:Yt,DID_THROW_ITEM_INVALID:Yt,DID_THROW_ITEM_PROCESSING_ERROR:Yt,DID_THROW_ITEM_PROCESSING_REVERT_ERROR:Yt,DID_THROW_ITEM_REMOVE_ERROR:Yt}),didCreateView:e=>{me("CREATE_VIEW",{...e,view:e})},create:({root:e})=>{const t=Pt("span");t.className="filepond--file-status-main",e.appendChild(t),e.ref.main=t;const r=Pt("span");r.className="filepond--file-status-sub",e.appendChild(r),e.ref.sub=r,Vt({root:e,action:{progress:null}})},mixins:{styles:["translateX","translateY","opacity"],animations:{opacity:{type:"tween",duration:250},translateX:"spring",translateY:"spring"}}}),kt={AbortItemLoad:{label:"GET_LABEL_BUTTON_ABORT_ITEM_LOAD",action:"ABORT_ITEM_LOAD",className:"filepond--action-abort-item-load",align:"LOAD_INDICATOR_POSITION"},RetryItemLoad:{label:"GET_LABEL_BUTTON_RETRY_ITEM_LOAD",action:"RETRY_ITEM_LOAD",icon:"GET_ICON_RETRY",className:"filepond--action-retry-item-load",align:"BUTTON_PROCESS_ITEM_POSITION"},RemoveItem:{label:"GET_LABEL_BUTTON_REMOVE_ITEM",action:"REQUEST_REMOVE_ITEM",icon:"GET_ICON_REMOVE",className:"filepond--action-remove-item",align:"BUTTON_REMOVE_ITEM_POSITION"},ProcessItem:{label:"GET_LABEL_BUTTON_PROCESS_ITEM",action:"REQUEST_ITEM_PROCESSING",icon:"GET_ICON_PROCESS",className:"filepond--action-process-item",align:"BUTTON_PROCESS_ITEM_POSITION"},AbortItemProcessing:{label:"GET_LABEL_BUTTON_ABORT_ITEM_PROCESSING",action:"ABORT_ITEM_PROCESSING",className:"filepond--action-abort-item-processing",align:"BUTTON_PROCESS_ITEM_POSITION"},RetryItemProcessing:{label:"GET_LABEL_BUTTON_RETRY_ITEM_PROCESSING",action:"RETRY_ITEM_PROCESSING",icon:"GET_ICON_RETRY",className:"filepond--action-retry-item-processing",align:"BUTTON_PROCESS_ITEM_POSITION"},RevertItemProcessing:{label:"GET_LABEL_BUTTON_UNDO_ITEM_PROCESSING",action:"REQUEST_REVERT_ITEM_PROCESSING",icon:"GET_ICON_UNDO",className:"filepond--action-revert-item-processing",align:"BUTTON_PROCESS_ITEM_POSITION"}},Xt=[];e(kt,e=>{Xt.push(e)});const $t=e=>{if("right"===Qt(e))return 0;const t=e.ref.buttonRemoveItem.rect.element;return t.hidden?null:t.width+t.left},Wt=e=>{return e.ref.buttonAbortItemLoad.rect.element.width},zt=e=>Math.floor(e.ref.buttonRemoveItem.rect.element.height/4),jt=e=>Math.floor(e.ref.buttonRemoveItem.rect.element.left/2),Qt=e=>e.query("GET_STYLE_BUTTON_REMOVE_ITEM_POSITION"),Zt={buttonAbortItemLoad:{opacity:0},buttonRetryItemLoad:{opacity:0},buttonRemoveItem:{opacity:0},buttonProcessItem:{opacity:0},buttonAbortItemProcessing:{opacity:0},buttonRetryItemProcessing:{opacity:0},buttonRevertItemProcessing:{opacity:0},loadProgressIndicator:{opacity:0,align:e=>e.query("GET_STYLE_LOAD_INDICATOR_POSITION")},processProgressIndicator:{opacity:0,align:e=>e.query("GET_STYLE_PROGRESS_INDICATOR_POSITION")},processingCompleteIndicator:{opacity:0,scaleX:.75,scaleY:.75},info:{translateX:0,translateY:0,opacity:0},status:{translateX:0,translateY:0,opacity:0}},Kt={buttonRemoveItem:{opacity:1},buttonProcessItem:{opacity:1},info:{translateX:$t},status:{translateX:$t}},Jt={buttonAbortItemProcessing:{opacity:1},processProgressIndicator:{opacity:1},status:{opacity:1}},er={DID_THROW_ITEM_INVALID:{buttonRemoveItem:{opacity:1},info:{translateX:$t},status:{translateX:$t,opacity:1}},DID_START_ITEM_LOAD:{buttonAbortItemLoad:{opacity:1},loadProgressIndicator:{opacity:1},status:{opacity:1}},DID_THROW_ITEM_LOAD_ERROR:{buttonRetryItemLoad:{opacity:1},buttonRemoveItem:{opacity:1},info:{translateX:$t},status:{opacity:1}},DID_START_ITEM_REMOVE:{processProgressIndicator:{opacity:1,align:Qt},info:{translateX:$t},status:{opacity:0}},DID_THROW_ITEM_REMOVE_ERROR:{processProgressIndicator:{opacity:0,align:Qt},buttonRemoveItem:{opacity:1},info:{translateX:$t},status:{opacity:1,translateX:$t}},DID_LOAD_ITEM:Kt,DID_LOAD_LOCAL_ITEM:{buttonRemoveItem:{opacity:1},info:{translateX:$t},status:{translateX:$t}},DID_START_ITEM_PROCESSING:Jt,DID_REQUEST_ITEM_PROCESSING:Jt,DID_UPDATE_ITEM_PROCESS_PROGRESS:Jt,DID_COMPLETE_ITEM_PROCESSING:{buttonRevertItemProcessing:{opacity:1},info:{opacity:1},status:{opacity:1}},DID_THROW_ITEM_PROCESSING_ERROR:{buttonRemoveItem:{opacity:1},buttonRetryItemProcessing:{opacity:1},status:{opacity:1},info:{translateX:$t}},DID_THROW_ITEM_PROCESSING_REVERT_ERROR:{buttonRevertItemProcessing:{opacity:1},status:{opacity:1},info:{opacity:1}},DID_ABORT_ITEM_PROCESSING:{buttonRemoveItem:{opacity:1},buttonProcessItem:{opacity:1},info:{translateX:$t},status:{opacity:1}},DID_REVERT_ITEM_PROCESSING:Kt},tr=A({create:({root:e})=>{e.element.innerHTML=e.query("GET_ICON_DONE")},name:"processing-complete-indicator",ignoreRect:!0,mixins:{styles:["scaleX","scaleY","opacity"],animations:{scaleX:"spring",scaleY:"spring",opacity:{type:"tween",duration:250}}}}),rr=L({DID_SET_LABEL_BUTTON_ABORT_ITEM_PROCESSING:({root:e,action:t})=>{e.ref.buttonAbortItemProcessing.label=t.value},DID_SET_LABEL_BUTTON_ABORT_ITEM_LOAD:({root:e,action:t})=>{e.ref.buttonAbortItemLoad.label=t.value},DID_SET_LABEL_BUTTON_ABORT_ITEM_REMOVAL:({root:e,action:t})=>{e.ref.buttonAbortItemRemoval.label=t.value},DID_REQUEST_ITEM_PROCESSING:({root:e})=>{e.ref.processProgressIndicator.spin=!0,e.ref.processProgressIndicator.progress=0},DID_START_ITEM_LOAD:({root:e})=>{e.ref.loadProgressIndicator.spin=!0,e.ref.loadProgressIndicator.progress=0},DID_START_ITEM_REMOVE:({root:e})=>{e.ref.processProgressIndicator.spin=!0,e.ref.processProgressIndicator.progress=0},DID_UPDATE_ITEM_LOAD_PROGRESS:({root:e,action:t})=>{e.ref.loadProgressIndicator.spin=!1,e.ref.loadProgressIndicator.progress=t.progress},DID_UPDATE_ITEM_PROCESS_PROGRESS:({root:e,action:t})=>{e.ref.processProgressIndicator.spin=!1,e.ref.processProgressIndicator.progress=t.progress}}),or=A({create:({root:t,props:r})=>{const o=Object.keys(kt).reduce((e,t)=>(e[t]={...kt[t]},e),{}),{id:n}=r,i=t.query("GET_ALLOW_REVERT"),s=t.query("GET_ALLOW_REMOVE"),a=t.query("GET_ALLOW_PROCESS"),l=t.query("GET_INSTANT_UPLOAD"),c=t.query("IS_ASYNC"),d=t.query("GET_STYLE_BUTTON_REMOVE_ITEM_ALIGN");let p;c?a&&!i?p=(e=>!/RevertItemProcessing/.test(e)):!a&&i?p=(e=>!/ProcessItem|RetryItemProcessing|AbortItemProcessing/.test(e)):a||i||(p=(e=>!/Process/.test(e))):p=(e=>!/Process/.test(e));const E=p?Xt.filter(p):Xt.concat();if(l&&i&&(o.RevertItemProcessing.label="GET_LABEL_BUTTON_REMOVE_ITEM",o.RevertItemProcessing.icon="GET_ICON_REMOVE"),c&&!i){const e=er.DID_COMPLETE_ITEM_PROCESSING;e.info.translateX=jt,e.info.translateY=zt,e.status.translateY=zt,e.processingCompleteIndicator={opacity:1,scaleX:1,scaleY:1}}if(c&&!a&&(["DID_START_ITEM_PROCESSING","DID_REQUEST_ITEM_PROCESSING","DID_UPDATE_ITEM_PROCESS_PROGRESS","DID_THROW_ITEM_PROCESSING_ERROR"].forEach(e=>{er[e].status.translateY=zt}),er.DID_THROW_ITEM_PROCESSING_ERROR.status.translateX=Wt),d&&i){o.RevertItemProcessing.align="BUTTON_REMOVE_ITEM_POSITION";const e=er.DID_COMPLETE_ITEM_PROCESSING;e.info.translateX=$t,e.status.translateY=zt,e.processingCompleteIndicator={opacity:1,scaleX:1,scaleY:1}}s||(o.RemoveItem.disabled=!0),e(o,(e,r)=>{const o=t.createChildView(Nt,{label:t.query(r.label),icon:t.query(r.icon),opacity:0});E.includes(e)&&t.appendChildView(o),r.disabled&&(o.element.setAttribute("disabled","disabled"),o.element.setAttribute("hidden","hidden")),o.element.dataset.align=t.query(`GET_STYLE_${r.align}`),o.element.classList.add(r.className),o.on("click",e=>{e.stopPropagation(),r.disabled||t.dispatch(r.action,{query:n})}),t.ref[`button${e}`]=o}),t.ref.processingCompleteIndicator=t.appendChildView(t.createChildView(tr)),t.ref.processingCompleteIndicator.element.dataset.align=t.query("GET_STYLE_BUTTON_PROCESS_ITEM_POSITION"),t.ref.info=t.appendChildView(t.createChildView(Ft,{id:n})),t.ref.status=t.appendChildView(t.createChildView(Ht,{id:n}));const u=t.appendChildView(t.createChildView(Ct,{opacity:0,align:t.query("GET_STYLE_LOAD_INDICATOR_POSITION")}));u.element.classList.add("filepond--load-indicator"),t.ref.loadProgressIndicator=u;const _=t.appendChildView(t.createChildView(Ct,{opacity:0,align:t.query("GET_STYLE_PROGRESS_INDICATOR_POSITION")}));_.element.classList.add("filepond--process-indicator"),t.ref.processProgressIndicator=_,t.ref.activeStyles=[]},write:({root:t,actions:r,props:o})=>{rr({root:t,actions:r,props:o});let n=r.concat().filter(e=>/^DID_/.test(e.type)).reverse().find(e=>er[e.type]);if(n){t.ref.activeStyles=[];const r=er[n.type];e(Zt,(o,n)=>{const i=t.ref[o];e(n,(e,n)=>{const s=r[o]&&void 0!==r[o][e]?r[o][e]:n;t.ref.activeStyles.push({control:i,key:e,value:s})})})}t.ref.activeStyles.forEach(({control:e,key:r,value:o})=>{e[r]="function"==typeof o?o(t):o})},didCreateView:e=>{me("CREATE_VIEW",{...e,view:e})},name:"file"}),nr=A({create:({root:e,props:t})=>{e.ref.fileName=Pt("legend"),e.appendChild(e.ref.fileName),e.ref.file=e.appendChildView(e.createChildView(or,{id:t.id})),e.ref.data=!1},ignoreRect:!0,write:L({DID_LOAD_ITEM:({root:e,props:t})=>{vt(e.ref.fileName,Lt(e.query("GET_ITEM_NAME",t.id)))}}),didCreateView:e=>{me("CREATE_VIEW",{...e,view:e})},tag:"fieldset",name:"file-wrapper"}),ir={type:"spring",damping:.6,mass:7},sr=(e,t,r)=>{const o=A({name:`panel-${t.name} filepond--${r}`,mixins:t.mixins,ignoreRectUpdate:!0}),n=e.createChildView(o,t.props);e.ref[t.name]=e.appendChildView(n)},ar=A({name:"panel",read:({root:e,props:t})=>t.heightCurrent=e.ref.bottom.translateY,write:({root:e,props:t})=>{if(null!==e.ref.scalable&&t.scalable===e.ref.scalable||(e.ref.scalable=!w(t.scalable)||t.scalable,e.element.dataset.scalable=e.ref.scalable),!t.height)return;const r=e.ref.top.rect.element,o=e.ref.bottom.rect.element,n=Math.max(r.height+o.height,t.height);e.ref.center.translateY=r.height,e.ref.center.scaleY=(n-r.height-o.height)/100,e.ref.bottom.translateY=n-o.height},create:({root:e,props:t})=>{[{name:"top"},{name:"center",props:{translateY:null,scaleY:null},mixins:{animations:{scaleY:ir},styles:["translateY","scaleY"]}},{name:"bottom",props:{translateY:null},mixins:{animations:{translateY:ir},styles:["translateY"]}}].forEach(r=>{sr(e,r,t.name)}),e.element.classList.add(`filepond--${t.name}`),e.ref.scalable=null},ignoreRect:!0,mixins:{apis:["height","heightCurrent","scalable"]}}),lr={type:"spring",stiffness:.75,damping:.45,mass:10},cr={DID_START_ITEM_LOAD:"busy",DID_UPDATE_ITEM_LOAD_PROGRESS:"loading",DID_THROW_ITEM_INVALID:"load-invalid",DID_THROW_ITEM_LOAD_ERROR:"load-error",DID_LOAD_ITEM:"idle",DID_THROW_ITEM_REMOVE_ERROR:"remove-error",DID_START_ITEM_REMOVE:"busy",DID_START_ITEM_PROCESSING:"busy processing",DID_REQUEST_ITEM_PROCESSING:"busy processing",DID_UPDATE_ITEM_PROCESS_PROGRESS:"processing",DID_COMPLETE_ITEM_PROCESSING:"processing-complete",DID_THROW_ITEM_PROCESSING_ERROR:"processing-error",DID_THROW_ITEM_PROCESSING_REVERT_ERROR:"processing-revert-error",DID_ABORT_ITEM_PROCESSING:"cancelled",DID_REVERT_ITEM_PROCESSING:"idle"},dr=L({DID_UPDATE_PANEL_HEIGHT:({root:e,action:t})=>{e.height=t.height}}),pr=L({DID_GRAB_ITEM:({root:e,props:t})=>{t.dragOrigin={x:e.translateX,y:e.translateY}},DID_DRAG_ITEM:({root:e})=>{e.element.dataset.dragState="drag"},DID_DROP_ITEM:({root:e,props:t})=>{t.dragOffset=null,t.dragOrigin=null,e.element.dataset.dragState="drop"}},({root:e,actions:t,props:r,shouldOptimize:o})=>{"drop"===e.element.dataset.dragState&&e.scaleX<=1&&(e.element.dataset.dragState="idle");let n=t.concat().filter(e=>/^DID_/.test(e.type)).reverse().find(e=>cr[e.type]);n&&n.type!==r.currentState&&(r.currentState=n.type,e.element.dataset.filepondItemState=cr[r.currentState]||"");const i=e.query("GET_ITEM_PANEL_ASPECT_RATIO")||e.query("GET_PANEL_ASPECT_RATIO");i?o||(e.height=e.rect.element.width*i):(dr({root:e,actions:t,props:r}),!e.height&&e.ref.container.rect.element.height>0&&(e.height=e.ref.container.rect.element.height)),o&&(e.ref.panel.height=null),e.ref.panel.height=e.height}),Er=A({create:({root:e,props:t})=>{if(e.ref.handleClick=(r=>e.dispatch("DID_ACTIVATE_ITEM",{id:t.id})),e.element.id=`filepond--item-${t.id}`,e.element.addEventListener("click",e.ref.handleClick),e.ref.container=e.appendChildView(e.createChildView(nr,{id:t.id})),e.ref.panel=e.appendChildView(e.createChildView(ar,{name:"item-panel"})),e.ref.panel.height=null,t.markedForRemoval=!1,!e.query("GET_ALLOW_REORDER"))return;e.element.dataset.dragState="idle";e.element.addEventListener("pointerdown",r=>{if(!r.isPrimary)return;let o=!1;const n=r.pageX,i=r.pageY;t.dragOrigin={x:e.translateX,y:e.translateY},t.dragCenter={x:r.offsetX,y:r.offsetY};const s=(e=>{const t=e.map(e=>e.id);let r=void 0;return{setIndex:e=>{r=e},getIndex:()=>r,getItemIndex:e=>t.indexOf(e.id)}})(e.query("GET_ACTIVE_ITEMS"));e.dispatch("DID_GRAB_ITEM",{id:t.id,dragState:s});const a=r=>{r.isPrimary&&(r.stopPropagation(),r.preventDefault(),t.dragOffset={x:r.pageX-n,y:r.pageY-i},t.dragOffset.x*t.dragOffset.x+t.dragOffset.y*t.dragOffset.y>16&&!o&&(o=!0,e.element.removeEventListener("click",e.ref.handleClick)),e.dispatch("DID_DRAG_ITEM",{id:t.id,dragState:s}))},l=e=>{e.isPrimary&&(t.dragOffset={x:e.pageX-n,y:e.pageY-i},d())},c=()=>{d()},d=()=>{document.removeEventListener("pointercancel",c),document.removeEventListener("pointermove",a),document.removeEventListener("pointerup",l),e.dispatch("DID_DROP_ITEM",{id:t.id,dragState:s}),o&&setTimeout(()=>e.element.addEventListener("click",e.ref.handleClick),0)};document.addEventListener("pointercancel",c),document.addEventListener("pointermove",a),document.addEventListener("pointerup",l)})},write:pr,destroy:({root:e,props:t})=>{e.element.removeEventListener("click",e.ref.handleClick),e.dispatch("RELEASE_ITEM",{query:t.id})},tag:"li",name:"item",mixins:{apis:["id","interactionMethod","markedForRemoval","spawnDate","dragCenter","dragOrigin","dragOffset"],styles:["translateX","translateY","scaleX","scaleY","opacity","height"],animations:{scaleX:"spring",scaleY:"spring",translateX:lr,translateY:lr,opacity:{type:"tween",duration:150}}}});var ur=(e,t)=>Math.max(1,Math.floor((e+1)/t));const _r=(e,t,r)=>{if(!r)return;const o=e.rect.element.width,n=t.length;let i=null;if(0===n||r.top<t[0].rect.element.top)return-1;const s=t[0].rect.element,a=s.marginLeft+s.marginRight,l=s.width+a,c=ur(o,l);if(1===c){for(let e=0;e<n;e++){const o=t[e],n=o.rect.outer.top+.5*o.rect.element.height;if(r.top<n)return e}return n}const d=s.marginTop+s.marginBottom,p=s.height+d;for(let e=0;e<n;e++){const t=e%c*l,o=Math.floor(e/c)*p,a=o-s.marginTop,d=t+l,E=o+p+s.marginBottom;if(r.top<E&&r.top>a){if(r.left<d)return e;i=e!==n-1?e:null}}return null!==i?i:n},Tr={height:0,width:0,get getHeight(){return this.height},set setHeight(e){0!==this.height&&0!==e||(this.height=e)},get getWidth(){return this.width},set setWidth(e){0!==this.width&&0!==e||(this.width=e)},setDimensions:function(e,t){0!==this.height&&0!==e||(this.height=e),0!==this.width&&0!==t||(this.width=t)}},Ir=(e,t,r,o=0,n=1)=>{e.dragOffset?(e.translateX=null,e.translateY=null,e.translateX=e.dragOrigin.x+e.dragOffset.x,e.translateY=e.dragOrigin.y+e.dragOffset.y,e.scaleX=1.025,e.scaleY=1.025):(e.translateX=t,e.translateY=r,Date.now()>e.spawnDate&&(0===e.opacity&&fr(e,t,r,o,n),e.scaleX=1,e.scaleY=1,e.opacity=1))},fr=(e,t,r,o,n)=>{e.interactionMethod===ne?(e.translateX=null,e.translateX=t,e.translateY=null,e.translateY=r):e.interactionMethod===te?(e.translateX=null,e.translateX=t-20*o,e.translateY=null,e.translateY=r-10*n,e.scaleX=.8,e.scaleY=.8):e.interactionMethod===re?(e.translateY=null,e.translateY=r-30):e.interactionMethod===ee&&(e.translateX=null,e.translateX=t-30,e.translateY=null)},mr=e=>e.rect.element.height+.5*e.rect.element.marginBottom+.5*e.rect.element.marginTop,hr=L({DID_ADD_ITEM:({root:e,action:t})=>{const{id:r,index:o,interactionMethod:n}=t;e.ref.addIndex=o;const i=Date.now();let s=i,a=1;if(n!==ne){a=0;const t=e.query("GET_ITEM_INSERT_INTERVAL"),r=i-e.ref.lastItemSpanwDate;s=r<t?i+(t-r):i}e.ref.lastItemSpanwDate=s,e.appendChildView(e.createChildView(Er,{spawnDate:s,id:r,opacity:a,interactionMethod:n}),o)},DID_REMOVE_ITEM:({root:e,action:t})=>{const{id:r}=t,o=e.childViews.find(e=>e.id===r);o&&(o.scaleX=.9,o.scaleY=.9,o.opacity=0,o.markedForRemoval=!0)},DID_DRAG_ITEM:({root:e,action:t})=>{const{id:r,dragState:o}=t,n=e.query("GET_ITEM",{id:r}),i=e.childViews.find(e=>e.id===r),s=e.childViews.length,a=o.getItemIndex(n);if(!i)return;const l=i.dragOrigin.x+i.dragOffset.x+i.dragCenter.x,c=i.dragOrigin.y+i.dragOffset.y+i.dragCenter.y,d=mr(i),p=(e=>e.rect.element.width+.5*e.rect.element.marginLeft+.5*e.rect.element.marginRight)(i);let E=Math.floor(e.rect.outer.width/p);E>s&&(E=s);const u=Math.floor(s/E+1);Tr.setHeight=d*u,Tr.setWidth=p*E;var _={y:Math.floor(c/d),x:Math.floor(l/p),getGridIndex:function(){return c>Tr.getHeight||c<0||l>Tr.getWidth||l<0?a:this.y*E+this.x},getColIndex:function(){const t=e.query("GET_ACTIVE_ITEMS"),r=e.childViews.filter(e=>e.rect.element.height),o=t.map(e=>r.find(t=>t.id===e.id)),n=o.findIndex(e=>e===i),s=mr(i),a=o.length;let l=a,d=0,p=0,E=0;for(let e=0;e<a;e++)if(d=mr(o[e]),c<(p=(E=p)+d)){if(n>e){if(c<E+s){l=e;break}continue}l=e;break}return l}};const T=E>1?_.getGridIndex():_.getColIndex();e.dispatch("MOVE_ITEM",{query:i,index:T});const I=o.getIndex();if(void 0===I||I!==T){if(o.setIndex(T),void 0===I)return;e.dispatch("DID_REORDER_ITEMS",{items:e.query("GET_ACTIVE_ITEMS"),origin:a,target:T})}}}),Rr=A({create:({root:e})=>{r(e.element,"role","list"),e.ref.lastItemSpanwDate=Date.now()},write:({root:e,props:t,actions:r,shouldOptimize:o})=>{hr({root:e,props:t,actions:r});const{dragCoordinates:n}=t,i=e.rect.element.width,s=e.childViews.filter(e=>e.rect.element.height),a=e.query("GET_ACTIVE_ITEMS").map(e=>s.find(t=>t.id===e.id)).filter(e=>e),l=n?_r(e,a,n):null,c=e.ref.addIndex||null;e.ref.addIndex=null;let d=0,p=0,E=0;if(0===a.length)return;const u=a[0].rect.element,_=u.marginTop+u.marginBottom,T=u.marginLeft+u.marginRight,I=u.width+T,f=u.height+_,m=ur(i,I);if(1===m){let e=0,t=0;a.forEach((r,n)=>{if(l){let e=n-l;t=-2===e?.25*-_:-1===e?.75*-_:0===e?.75*_:1===e?.25*_:0}o&&(r.translateX=null,r.translateY=null),r.markedForRemoval||Ir(r,0,e+t);let i=(r.rect.element.height+_)*(r.markedForRemoval?r.opacity:1);e+=i})}else{let e=0,t=0;a.forEach((r,n)=>{n===l&&(d=1),n===c&&(E+=1),r.markedForRemoval&&r.opacity<.5&&(p-=1);const i=n+E+d+p,s=i%m,a=Math.floor(i/m),u=s*I,_=a*f,T=Math.sign(u-e),h=Math.sign(_-t);e=u,t=_,r.markedForRemoval||(o&&(r.translateX=null,r.translateY=null),Ir(r,u,_,T,h))})}},tag:"ul",name:"list",didWriteView:({root:e})=>{e.childViews.filter(e=>e.markedForRemoval&&0===e.opacity&&e.resting).forEach(t=>{t._destroy(),e.removeChildView(t)})},filterFrameActionsForChild:(e,t)=>t.filter(t=>!t.data||!t.data.id||e.id===t.data.id),mixins:{apis:["dragCoordinates"]}}),gr=L({DID_DRAG:({root:e,props:t,action:r})=>{e.query("GET_ITEM_INSERT_LOCATION_FREEDOM")&&(t.dragCoordinates={left:r.position.scopeLeft-e.ref.list.rect.element.left,top:r.position.scopeTop-(e.rect.outer.top+e.rect.element.marginTop+e.rect.element.scrollTop)})},DID_END_DRAG:({props:e})=>{e.dragCoordinates=null}}),Or=A({create:({root:e,props:t})=>{e.ref.list=e.appendChildView(e.createChildView(Rr)),t.dragCoordinates=null,t.overflowing=!1},write:({root:e,props:t,actions:r})=>{if(gr({root:e,props:t,actions:r}),e.ref.list.dragCoordinates=t.dragCoordinates,t.overflowing&&!t.overflow&&(t.overflowing=!1,e.element.dataset.state="",e.height=null),t.overflow){const r=Math.round(t.overflow);r!==e.height&&(t.overflowing=!0,e.element.dataset.state="overflow",e.height=r)}},name:"list-scroller",mixins:{apis:["overflow","dragCoordinates"],styles:["height","translateY"],animations:{translateY:"spring"}}}),Dr=(e,t,o,n="")=>{o?r(e,t,n):e.removeAttribute(t)},Sr=({root:e,action:t})=>{e.query("GET_ALLOW_SYNC_ACCEPT_ATTRIBUTE")&&Dr(e.element,"accept",!!t.value,t.value?t.value.join(","):"")},yr=({root:e,action:t})=>{Dr(e.element,"multiple",t.value)},Ar=({root:e,action:t})=>{Dr(e.element,"webkitdirectory",t.value)},Lr=({root:e})=>{const t=e.query("GET_DISABLED"),r=e.query("GET_ALLOW_BROWSE"),o=t||!r;Dr(e.element,"disabled",o)},Pr=({root:e,action:t})=>{t.value?0===e.query("GET_TOTAL_ITEMS")&&Dr(e.element,"required",!0):Dr(e.element,"required",!1)},vr=({root:e,action:t})=>{Dr(e.element,"capture",!!t.value,!0===t.value?"":t.value)},Mr=({root:e})=>{const{element:t}=e;if(e.query("GET_TOTAL_ITEMS")>0){Dr(t,"required",!1),Dr(t,"name",!1);const r=e.query("GET_ACTIVE_ITEMS");let o=!1;for(let e=0;e<r.length;e++)r[e].status===pe.LOAD_ERROR&&(o=!0);e.element.setCustomValidity(o?e.query("GET_LABEL_INVALID_FIELD"):"")}else{Dr(t,"name",!0,e.query("GET_NAME")),e.query("GET_CHECK_VALIDITY")&&t.setCustomValidity(""),e.query("GET_REQUIRED")&&Dr(t,"required",!0)}},br=A({tag:"input",name:"browser",ignoreRect:!0,ignoreRectUpdate:!0,attributes:{type:"file"},create:({root:e,props:t})=>{e.element.id=`filepond--browser-${t.id}`,r(e.element,"name",e.query("GET_NAME")),r(e.element,"aria-controls",`filepond--assistant-${t.id}`),r(e.element,"aria-labelledby",`filepond--drop-label-${t.id}`),Sr({root:e,action:{value:e.query("GET_ACCEPTED_FILE_TYPES")}}),yr({root:e,action:{value:e.query("GET_ALLOW_MULTIPLE")}}),Ar({root:e,action:{value:e.query("GET_ALLOW_DIRECTORIES_ONLY")}}),Lr({root:e}),Pr({root:e,action:{value:e.query("GET_REQUIRED")}}),vr({root:e,action:{value:e.query("GET_CAPTURE_METHOD")}}),e.ref.handleChange=(r=>{if(!e.element.value)return;const o=Array.from(e.element.files).map(e=>(e._relativePath=e.webkitRelativePath,e));setTimeout(()=>{t.onload(o),(e=>{if(e&&""!==e.value){try{e.value=""}catch(e){}if(e.value){const t=Pt("form"),r=e.parentNode,o=e.nextSibling;t.appendChild(e),t.reset(),o?r.insertBefore(e,o):r.appendChild(e)}}})(e.element)},250)}),e.element.addEventListener("change",e.ref.handleChange)},destroy:({root:e})=>{e.element.removeEventListener("change",e.ref.handleChange)},write:L({DID_LOAD_ITEM:Mr,DID_REMOVE_ITEM:Mr,DID_THROW_ITEM_INVALID:({root:e})=>{e.query("GET_CHECK_VALIDITY")&&e.element.setCustomValidity(e.query("GET_LABEL_INVALID_FIELD"))},DID_SET_DISABLED:Lr,DID_SET_ALLOW_BROWSE:Lr,DID_SET_ALLOW_DIRECTORIES_ONLY:Ar,DID_SET_ALLOW_MULTIPLE:yr,DID_SET_ACCEPTED_FILE_TYPES:Sr,DID_SET_CAPTURE_METHOD:vr,DID_SET_REQUIRED:Pr})}),Cr=13,Nr=32,wr=(e,t)=>{e.innerHTML=t;const o=e.querySelector(".filepond--label-action");return o&&r(o,"tabindex","0"),t},Gr=A({name:"drop-label",ignoreRect:!0,create:({root:e,props:t})=>{const o=Pt("label");r(o,"for",`filepond--browser-${t.id}`),r(o,"id",`filepond--drop-label-${t.id}`),e.ref.handleKeyDown=(t=>{(t.keyCode===Cr||t.keyCode===Nr)&&(t.preventDefault(),e.ref.label.click())}),e.ref.handleClick=(t=>{t.target===o||o.contains(t.target)||e.ref.label.click()}),o.addEventListener("keydown",e.ref.handleKeyDown),e.element.addEventListener("click",e.ref.handleClick),wr(o,t.caption),e.appendChild(o),e.ref.label=o},destroy:({root:e})=>{e.ref.label.addEventListener("keydown",e.ref.handleKeyDown),e.element.removeEventListener("click",e.ref.handleClick)},write:L({DID_SET_LABEL_IDLE:({root:e,action:t})=>{wr(e.ref.label,t.value)}}),mixins:{styles:["opacity","translateX","translateY"],animations:{opacity:{type:"tween",duration:150},translateX:"spring",translateY:"spring"}}}),Ur=A({name:"drip-blob",ignoreRect:!0,mixins:{styles:["translateX","translateY","scaleX","scaleY","opacity"],animations:{scaleX:"spring",scaleY:"spring",translateX:"spring",translateY:"spring",opacity:{type:"tween",duration:250}}}}),Br=L({DID_DRAG:({root:e,action:t})=>{e.ref.blob?(e.ref.blob.translateX=t.position.scopeLeft,e.ref.blob.translateY=t.position.scopeTop,e.ref.blob.scaleX=1,e.ref.blob.scaleY=1,e.ref.blob.opacity=1):(({root:e})=>{const t=.5*e.rect.element.width,r=.5*e.rect.element.height;e.ref.blob=e.appendChildView(e.createChildView(Ur,{opacity:0,scaleX:2.5,scaleY:2.5,translateX:t,translateY:r}))})({root:e})},DID_DROP:({root:e})=>{e.ref.blob&&(e.ref.blob.scaleX=2.5,e.ref.blob.scaleY=2.5,e.ref.blob.opacity=0)},DID_END_DRAG:({root:e})=>{e.ref.blob&&(e.ref.blob.opacity=0)}}),Fr=A({ignoreRect:!0,ignoreRectUpdate:!0,name:"drip",write:({root:e,props:t,actions:r})=>{Br({root:e,props:t,actions:r});const{blob:o}=e.ref;0===r.length&&o&&0===o.opacity&&(e.removeChildView(o),e.ref.blob=null)}}),qr=(e,t)=>{try{const r=new DataTransfer;t.forEach(e=>{e instanceof File?r.items.add(e):r.items.add(new File([e],e.name,{type:e.type}))}),e.files=r.files}catch(e){return!1}return!0},Vr=(e,t)=>e.ref.fields[t],xr=e=>{e.query("GET_ACTIVE_ITEMS").forEach(t=>{e.ref.fields[t.id]&&e.element.appendChild(e.ref.fields[t.id])})},Yr=({root:e})=>xr(e),Hr=L({DID_SET_DISABLED:({root:e})=>{e.element.disabled=e.query("GET_DISABLED")},DID_ADD_ITEM:({root:e,action:t})=>{const r=!(e.query("GET_ITEM",t.id).origin===Ee.LOCAL)&&e.query("SHOULD_UPDATE_FILE_INPUT"),o=Pt("input");o.type=r?"file":"hidden",o.name=e.query("GET_NAME"),e.ref.fields[t.id]=o,xr(e)},DID_LOAD_ITEM:({root:e,action:t})=>{const r=Vr(e,t.id);if(!r)return;if(null!==t.serverFileReference&&(r.value=t.serverFileReference),!e.query("SHOULD_UPDATE_FILE_INPUT"))return;const o=e.query("GET_ITEM",t.id);qr(r,[o.file])},DID_REMOVE_ITEM:({root:e,action:t})=>{const r=Vr(e,t.id);r&&(r.parentNode&&r.parentNode.removeChild(r),delete e.ref.fields[t.id])},DID_DEFINE_VALUE:({root:e,action:t})=>{const r=Vr(e,t.id);r&&(null===t.value?r.removeAttribute("value"):"file"!=r.type&&(r.value=t.value),xr(e))},DID_PREPARE_OUTPUT:({root:e,action:t})=>{e.query("SHOULD_UPDATE_FILE_INPUT")&&setTimeout(()=>{const r=Vr(e,t.id);r&&qr(r,[t.file])},0)},DID_REORDER_ITEMS:Yr,DID_SORT_ITEMS:Yr}),kr=A({tag:"fieldset",name:"data",create:({root:e})=>{e.ref.fields={};const t=document.createElement("legend");t.textContent="Files",e.element.appendChild(t)},write:Hr,ignoreRect:!0}),Xr=["jpg","jpeg","png","gif","bmp","webp","svg","tiff"],$r=["css","csv","html","txt"],Wr={zip:"zip|compressed",epub:"application/epub+zip"},zr=(e="")=>(e=e.toLowerCase(),Xr.includes(e)?"image/"+("jpg"===e?"jpeg":"svg"===e?"svg+xml":e):$r.includes(e)?"text/"+e:Wr[e]||""),jr=e=>new Promise((t,r)=>{const o=io(e);if(o.length&&!Qr(e))return t(o);Zr(e).then(t)}),Qr=e=>!!e.files&&e.files.length>0,Zr=e=>new Promise((t,r)=>{const o=(e.items?Array.from(e.items):[]).filter(e=>Kr(e)).map(e=>Jr(e));o.length?Promise.all(o).then(e=>{const r=[];e.forEach(e=>{r.push.apply(r,e)}),t(r.filter(e=>e).map(e=>(e._relativePath||(e._relativePath=e.webkitRelativePath),e)))}).catch(console.error):t(e.files?Array.from(e.files):[])}),Kr=e=>{if(oo(e)){const t=no(e);if(t)return t.isFile||t.isDirectory}return"file"===e.kind},Jr=e=>new Promise((t,r)=>{ro(e)?eo(no(e)).then(t).catch(r):t([e.getAsFile()])}),eo=e=>new Promise((t,r)=>{const o=[];let n=0,i=0;const s=()=>{0===i&&0===n&&t(o)},a=e=>{n++;const t=e.createReader(),l=()=>{t.readEntries(e=>{if(0===e.length)return n--,void s();e.forEach(e=>{e.isDirectory?a(e):(i++,e.file(t=>{const r=to(t);e.fullPath&&(r._relativePath=e.fullPath),o.push(r),i--,s()}))}),l()},r)};l()};a(e)}),to=e=>{if(e.type.length)return e;const t=e.lastModifiedDate,r=e.name,o=zr(Fe(e.name));return o.length?((e=e.slice(0,e.size,o)).name=r,e.lastModifiedDate=t,e):e},ro=e=>oo(e)&&(no(e)||{}).isDirectory,oo=e=>"webkitGetAsEntry"in e,no=e=>e.webkitGetAsEntry(),io=e=>{let t=[];try{if((t=ao(e)).length)return t;t=so(e)}catch(e){}return t},so=e=>{let t=e.getData("url");return"string"==typeof t&&t.length?[t]:[]},ao=e=>{let t=e.getData("text/html");if("string"==typeof t&&t.length){const e=t.match(/src\s*=\s*"(.+?)"/);if(e)return[e[1]]}return[]},lo=[],co=e=>({pageLeft:e.pageX,pageTop:e.pageY,scopeLeft:e.offsetX||e.layerX,scopeTop:e.offsetY||e.layerY}),po=e=>{const t=lo.find(t=>t.element===e);if(t)return t;const r=Eo(e);return lo.push(r),r},Eo=t=>{const r=[],o={dragenter:Io,dragover:fo,dragleave:ho,drop:mo},n={};e(o,(e,o)=>{n[e]=o(t,r),t.addEventListener(e,n[e],!1)});const i={element:t,addListener:s=>(r.push(s),()=>{r.splice(r.indexOf(s),1),0===r.length&&(lo.splice(lo.indexOf(i),1),e(o,e=>{t.removeEventListener(e,n[e],!1)}))})};return i},uo=(e,t)=>{const r=((e,t)=>("elementFromPoint"in e||(e=document),e.elementFromPoint(t.x,t.y)))((e=>"getRootNode"in e?e.getRootNode():document)(t),{x:e.pageX-window.pageXOffset,y:e.pageY-window.pageYOffset});return r===t||t.contains(r)};let _o=null;const To=(e,t)=>{try{e.dropEffect=t}catch(e){}},Io=(e,t)=>e=>{e.preventDefault(),_o=e.target,t.forEach(t=>{const{element:r,onenter:o}=t;uo(e,r)&&(t.state="enter",o(co(e)))})},fo=(e,t)=>e=>{e.preventDefault();const r=e.dataTransfer;jr(r).then(o=>{let n=!1;t.some(t=>{const{filterElement:i,element:s,onenter:a,onexit:l,ondrag:c,allowdrop:d}=t;To(r,"copy");const p=d(o);if(p)if(uo(e,s)){if(n=!0,null===t.state)return t.state="enter",void a(co(e));if(t.state="over",i&&!p)return void To(r,"none");c(co(e))}else i&&!n&&To(r,"none"),t.state&&(t.state=null,l(co(e)));else To(r,"none")})})},mo=(e,t)=>e=>{e.preventDefault();const r=e.dataTransfer;jr(r).then(r=>{t.forEach(t=>{const{filterElement:o,element:n,ondrop:i,onexit:s,allowdrop:a}=t;if(t.state=null,!o||uo(e,n))return a(r)?void i(co(e),r):s(co(e))})})},ho=(e,t)=>e=>{_o===e.target&&t.forEach(t=>{const{onexit:r}=t;t.state=null,r(co(e))})},Ro=(e,t,r)=>{e.classList.add("filepond--hopper");const{catchesDropsOnPage:o,requiresDropOnElement:n,filterItems:i=(e=>e)}=r,s=((e,t,r)=>{const o=po(t),n={element:e,filterElement:r,state:null,ondrop:()=>{},onenter:()=>{},ondrag:()=>{},onexit:()=>{},onload:()=>{},allowdrop:()=>{}};return n.destroy=o.addListener(n),n})(e,o?document.documentElement:e,n);let a="",l="";s.allowdrop=(e=>t(i(e))),s.ondrop=((e,r)=>{const o=i(r);t(o)?(l="drag-drop",c.onload(o,e)):c.ondragend(e)}),s.ondrag=(e=>{c.ondrag(e)}),s.onenter=(e=>{l="drag-over",c.ondragstart(e)}),s.onexit=(e=>{l="drag-exit",c.ondragend(e)});const c={updateHopperState:()=>{a!==l&&(e.dataset.hopperState=l,a=l)},onload:()=>{},ondragstart:()=>{},ondrag:()=>{},ondragend:()=>{},destroy:()=>{s.destroy()}};return c};let go=!1;const Oo=[],Do=e=>{const t=document.activeElement;if(t&&(/textarea|input/i.test(t.nodeName)||"true"===t.getAttribute("contenteditable"))){let e=!1,r=t;for(;r!==document.body;){if(r.classList.contains("filepond--root")){e=!0;break}r=r.parentNode}if(!e)return}jr(e.clipboardData).then(e=>{e.length&&Oo.forEach(t=>t(e))})},So=()=>{const e=e=>{t.onload(e)},t={destroy:()=>{(e=>{se(Oo,Oo.indexOf(e)),0===Oo.length&&(document.removeEventListener("paste",Do),go=!1)})(e)},onload:()=>{}};return(e=>{Oo.includes(e)||(Oo.push(e),go||(go=!0,document.addEventListener("paste",Do)))})(e),t};let yo=null,Ao=null;const Lo=[],Po=(e,t)=>{e.element.textContent=t},vo=(e,t,r)=>{const o=e.query("GET_TOTAL_ITEMS");Po(e,`${r} ${t}, ${o} ${1===o?e.query("GET_LABEL_FILE_COUNT_SINGULAR"):e.query("GET_LABEL_FILE_COUNT_PLURAL")}`),clearTimeout(Ao),Ao=setTimeout(()=>{(e=>{e.element.textContent=""})(e)},1500)},Mo=e=>e.element.parentNode.contains(document.activeElement),bo=({root:e,action:t})=>{const r=e.query("GET_ITEM",t.id).filename,o=e.query("GET_LABEL_FILE_PROCESSING_ABORTED");Po(e,`${r} ${o}`)},Co=({root:e,action:t})=>{const r=e.query("GET_ITEM",t.id).filename;Po(e,`${t.status.main} ${r} ${t.status.sub}`)},No=A({create:({root:e,props:t})=>{e.element.id=`filepond--assistant-${t.id}`,r(e.element,"role","alert"),r(e.element,"aria-live","polite"),r(e.element,"aria-relevant","additions")},ignoreRect:!0,ignoreRectUpdate:!0,write:L({DID_LOAD_ITEM:({root:e,action:t})=>{if(!Mo(e))return;e.element.textContent="";const r=e.query("GET_ITEM",t.id);Lo.push(r.filename),clearTimeout(yo),yo=setTimeout(()=>{vo(e,Lo.join(", "),e.query("GET_LABEL_FILE_ADDED")),Lo.length=0},750)},DID_REMOVE_ITEM:({root:e,action:t})=>{if(!Mo(e))return;const r=t.item;vo(e,r.filename,e.query("GET_LABEL_FILE_REMOVED"))},DID_COMPLETE_ITEM_PROCESSING:({root:e,action:t})=>{const r=e.query("GET_ITEM",t.id).filename,o=e.query("GET_LABEL_FILE_PROCESSING_COMPLETE");Po(e,`${r} ${o}`)},DID_ABORT_ITEM_PROCESSING:bo,DID_REVERT_ITEM_PROCESSING:bo,DID_THROW_ITEM_REMOVE_ERROR:Co,DID_THROW_ITEM_LOAD_ERROR:Co,DID_THROW_ITEM_INVALID:Co,DID_THROW_ITEM_PROCESSING_ERROR:Co}),tag:"span",name:"assistant"}),wo=(e,t="-")=>e.replace(new RegExp(`${t}.`,"g"),e=>e.charAt(1).toUpperCase()),Go=(e,t=16,r=!0)=>{let o=Date.now(),n=null;return(...i)=>{clearTimeout(n);const s=Date.now()-o,a=()=>{o=Date.now(),e(...i)};s<t?r||(n=setTimeout(a,t-s)):a()}},Uo=e=>e.preventDefault(),Bo=e=>{const t=e.ref.list.childViews[0].childViews[0];return t?{top:t.rect.element.marginTop,bottom:t.rect.element.marginBottom}:{top:0,bottom:0}},Fo=e=>{let t=0,r=0;const o=e.ref.list,n=o.childViews[0],i=n.childViews.filter(e=>e.rect.element.height),s=e.query("GET_ACTIVE_ITEMS").map(e=>i.find(t=>t.id===e.id)).filter(e=>e);if(0===s.length)return{visual:t,bounds:r};const a=n.rect.element.width,l=_r(n,s,o.dragCoordinates),c=s[0].rect.element,d=c.marginTop+c.marginBottom,p=c.marginLeft+c.marginRight,E=c.width+p,u=c.height+d,_=void 0!==l&&l>=0?1:0,T=s.find(e=>e.markedForRemoval&&e.opacity<.45)?-1:0,I=s.length+_+T,f=ur(a,E);return 1===f?s.forEach(e=>{const o=e.rect.element.height+d;r+=o,t+=o*e.opacity}):(r=Math.ceil(I/f)*u,t=r),{visual:t,bounds:r}},qo=e=>{const t=e.ref.measureHeight||null;return{cappedHeight:parseInt(e.style.maxHeight,10)||null,fixedHeight:0===t?null:t}},Vo=(e,t)=>{const r=e.query("GET_ALLOW_REPLACE"),o=e.query("GET_ALLOW_MULTIPLE"),n=e.query("GET_TOTAL_ITEMS");let i=e.query("GET_MAX_FILES");const s=t.length;return!o&&s>1?(e.dispatch("DID_THROW_MAX_FILES",{source:t,error:Ke("warning",0,"Max files")}),!0):!(!o&&r)&&(!!(V(i=o?i:1)&&n+s>i)&&(e.dispatch("DID_THROW_MAX_FILES",{source:t,error:Ke("warning",0,"Max files")}),!0))},xo=(e,t,r)=>{const o=e.childViews[0];return _r(o,t,{left:r.scopeLeft-o.rect.element.left,top:r.scopeTop-(e.rect.outer.top+e.rect.element.marginTop+e.rect.element.scrollTop)})},Yo=e=>{const t=e.query("GET_ALLOW_DROP"),r=e.query("GET_DISABLED"),o=t&&!r;if(o&&!e.ref.hopper){const t=Ro(e.element,t=>{const r=e.query("GET_BEFORE_DROP_FILE")||(()=>!0);return!e.query("GET_DROP_VALIDATION")||t.every(t=>me("ALLOW_HOPPER_ITEM",t,{query:e.query}).every(e=>!0===e)&&r(t))},{filterItems:t=>{const r=e.query("GET_IGNORED_FILES");return t.filter(e=>!_t(e)||!r.includes(e.name.toLowerCase()))},catchesDropsOnPage:e.query("GET_DROP_ON_PAGE"),requiresDropOnElement:e.query("GET_DROP_ON_ELEMENT")});t.onload=((t,r)=>{const o=e.ref.list.childViews[0].childViews.filter(e=>e.rect.element.height),n=e.query("GET_ACTIVE_ITEMS").map(e=>o.find(t=>t.id===e.id)).filter(e=>e);fe("ADD_ITEMS",t,{dispatch:e.dispatch}).then(t=>{if(Vo(e,t))return!1;e.dispatch("ADD_ITEMS",{items:t,index:xo(e.ref.list,n,r),interactionMethod:te})}),e.dispatch("DID_DROP",{position:r}),e.dispatch("DID_END_DRAG",{position:r})}),t.ondragstart=(t=>{e.dispatch("DID_START_DRAG",{position:t})}),t.ondrag=Go(t=>{e.dispatch("DID_DRAG",{position:t})}),t.ondragend=(t=>{e.dispatch("DID_END_DRAG",{position:t})}),e.ref.hopper=t,e.ref.drip=e.appendChildView(e.createChildView(Fr))}else!o&&e.ref.hopper&&(e.ref.hopper.destroy(),e.ref.hopper=null,e.removeChildView(e.ref.drip))},Ho=(e,t)=>{const r=e.query("GET_ALLOW_BROWSE"),o=e.query("GET_DISABLED"),n=r&&!o;n&&!e.ref.browser?e.ref.browser=e.appendChildView(e.createChildView(br,{...t,onload:t=>{fe("ADD_ITEMS",t,{dispatch:e.dispatch}).then(t=>{if(Vo(e,t))return!1;e.dispatch("ADD_ITEMS",{items:t,index:-1,interactionMethod:re})})}}),0):!n&&e.ref.browser&&(e.removeChildView(e.ref.browser),e.ref.browser=null)},ko=e=>{const t=e.query("GET_ALLOW_PASTE"),r=e.query("GET_DISABLED"),o=t&&!r;o&&!e.ref.paster?(e.ref.paster=So(),e.ref.paster.onload=(t=>{fe("ADD_ITEMS",t,{dispatch:e.dispatch}).then(t=>{if(Vo(e,t))return!1;e.dispatch("ADD_ITEMS",{items:t,index:-1,interactionMethod:oe})})})):!o&&e.ref.paster&&(e.ref.paster.destroy(),e.ref.paster=null)},Xo=L({DID_SET_ALLOW_BROWSE:({root:e,props:t})=>{Ho(e,t)},DID_SET_ALLOW_DROP:({root:e})=>{Yo(e)},DID_SET_ALLOW_PASTE:({root:e})=>{ko(e)},DID_SET_DISABLED:({root:e,props:t})=>{Yo(e),ko(e),Ho(e,t),e.query("GET_DISABLED")?e.element.dataset.disabled="disabled":e.element.removeAttribute("data-disabled")}}),$o=A({name:"root",read:({root:e})=>{e.ref.measure&&(e.ref.measureHeight=e.ref.measure.offsetHeight)},create:({root:e,props:t})=>{const r=e.query("GET_ID");r&&(e.element.id=r);const o=e.query("GET_CLASS_NAME");o&&o.split(" ").filter(e=>e.length).forEach(t=>{e.element.classList.add(t)}),e.ref.label=e.appendChildView(e.createChildView(Gr,{...t,translateY:null,caption:e.query("GET_LABEL_IDLE")})),e.ref.list=e.appendChildView(e.createChildView(Or,{translateY:null})),e.ref.panel=e.appendChildView(e.createChildView(ar,{name:"panel-root"})),e.ref.assistant=e.appendChildView(e.createChildView(No,{...t})),e.ref.data=e.appendChildView(e.createChildView(kr,{...t})),e.ref.measure=Pt("div"),e.ref.measure.style.height="100%",e.element.appendChild(e.ref.measure),e.ref.bounds=null,e.query("GET_STYLES").filter(e=>!b(e.value)).map(({name:t,value:r})=>{e.element.dataset[t]=r}),e.ref.widthPrevious=null,e.ref.widthUpdated=Go(()=>{e.ref.updateHistory=[],e.dispatch("DID_RESIZE_ROOT")},250),e.ref.previousAspectRatio=null,e.ref.updateHistory=[];const n=window.matchMedia("(pointer: fine) and (hover: hover)").matches,i="PointerEvent"in window;e.query("GET_ALLOW_REORDER")&&i&&!n&&(e.element.addEventListener("touchmove",Uo,{passive:!1}),e.element.addEventListener("gesturestart",Uo));const s=e.query("GET_CREDITS");if(2===s.length){const t=document.createElement("a");t.className="filepond--credits",t.href=s[0],t.tabIndex=-1,t.target="_blank",t.rel="noopener noreferrer nofollow",t.textContent=s[1],e.element.appendChild(t),e.ref.credits=t}},write:({root:e,props:t,actions:r})=>{if(Xo({root:e,props:t,actions:r}),r.filter(e=>/^DID_SET_STYLE_/.test(e.type)).filter(e=>!b(e.data.value)).map(({type:t,data:r})=>{const o=wo(t.substring(8).toLowerCase(),"_");e.element.dataset[o]=r.value,e.invalidateLayout()}),e.rect.element.hidden)return;e.rect.element.width!==e.ref.widthPrevious&&(e.ref.widthPrevious=e.rect.element.width,e.ref.widthUpdated());let o=e.ref.bounds;o||(o=e.ref.bounds=qo(e),e.element.removeChild(e.ref.measure),e.ref.measure=null);const{hopper:n,label:i,list:s,panel:a}=e.ref;n&&n.updateHopperState();const l=e.query("GET_PANEL_ASPECT_RATIO"),c=e.query("GET_ALLOW_MULTIPLE"),d=e.query("GET_TOTAL_ITEMS"),p=d===(c?e.query("GET_MAX_FILES")||1e6:1),E=r.find(e=>"DID_ADD_ITEM"===e.type);if(p&&E){const e=E.data.interactionMethod;i.opacity=0,c?i.translateY=-40:e===ee?i.translateX=40:i.translateY=e===re?40:30}else p||(i.opacity=1,i.translateX=0,i.translateY=0);const u=Bo(e),_=Fo(e),T=i.rect.element.height,I=!c||p?0:T,f=p?s.rect.element.marginTop:0,m=0===d?0:s.rect.element.marginBottom,h=I+f+_.visual+m,R=I+f+_.bounds+m;if(s.translateY=Math.max(0,I-s.rect.element.marginTop)-u.top,l){const t=e.rect.element.width,r=t*l;l!==e.ref.previousAspectRatio&&(e.ref.previousAspectRatio=l,e.ref.updateHistory=[]);const o=e.ref.updateHistory;o.push(t);const n=2;if(o.length>2*n){const e=o.length,t=e-10;let r=0;for(let i=e;i>=t;i--)if(o[i]===o[i-2]&&r++,r>=n)return}a.scalable=!1,a.height=r;const i=r-I-(m-u.bottom)-(p?f:0);_.visual>i?s.overflow=i:s.overflow=null,e.height=r}else if(o.fixedHeight){a.scalable=!1;const e=o.fixedHeight-I-(m-u.bottom)-(p?f:0);_.visual>e?s.overflow=e:s.overflow=null}else if(o.cappedHeight){const t=h>=o.cappedHeight,r=Math.min(o.cappedHeight,h);a.scalable=!0,a.height=t?r:r-u.top-u.bottom;const n=r-I-(m-u.bottom)-(p?f:0);h>o.cappedHeight&&_.visual>n?s.overflow=n:s.overflow=null,e.height=Math.min(o.cappedHeight,R-u.top-u.bottom)}else{const t=d>0?u.top+u.bottom:0;a.scalable=!0,a.height=Math.max(T,h-t),e.height=Math.max(T,R-t)}e.ref.credits&&a.heightCurrent&&(e.ref.credits.style.transform=`translateY(${a.heightCurrent}px)`)},destroy:({root:e})=>{e.ref.paster&&e.ref.paster.destroy(),e.ref.hopper&&e.ref.hopper.destroy(),e.element.removeEventListener("touchmove",Uo),e.element.removeEventListener("gesturestart",Uo)},mixins:{styles:["height"]}}),Wo=(e={})=>{let r=null;const o=Re(),n=((e,t=[],r=[])=>{const o={...e},n=[],i=[],s=(e,t,r)=>{!r||document.hidden?(d[e]&&d[e](t),n.push({type:e,data:t})):i.push({type:e,data:t})},a=(e,...t)=>c[e]?c[e](...t):null,l={getState:()=>({...o}),processActionQueue:()=>{const e=[...n];return n.length=0,e},processDispatchQueue:()=>{const e=[...i];i.length=0,e.forEach(({type:e,data:t})=>{s(e,t)})},dispatch:s,query:a};let c={};t.forEach(e=>{c={...e(o),...c}});let d={};return r.forEach(e=>{d={...e(s,a,o),...d}}),l})((e=>({items:[],listUpdateTimeout:null,itemUpdateTimeout:null,processingQueue:[],options:j(e)}))(o),[we,J(o)],[yt,K(o)]);n.dispatch("SET_OPTIONS",{options:e});const i=()=>{document.hidden||n.dispatch("KICK")};document.addEventListener("visibilitychange",i);let s=null,a=!1,l=!1,c=null,d=null;const p=()=>{a||(a=!0),clearTimeout(s),s=setTimeout(()=>{a=!1,c=null,d=null,l&&(l=!1,n.dispatch("DID_STOP_RESIZE"))},500)};window.addEventListener("resize",p);const E=$o(n,{id:ie()});let u=!1,_=!1;const I={_read:()=>{a&&(d=window.innerWidth,c||(c=d),l||d===c||(n.dispatch("DID_START_RESIZE"),l=!0)),_&&u&&(u=null===E.element.offsetParent),u||(E._read(),_=E.rect.element.hidden)},_write:e=>{const t=n.processActionQueue().filter(e=>!/^SET_/.test(e.type));u&&!t.length||(R(t),u=E._write(e,t,l),(e=>{e.forEach((t,r)=>{t.released&&se(e,r)})})(n.query("GET_ITEMS")),u&&n.processDispatchQueue())}},f=e=>t=>{const r={type:e};if(!t)return r;if(t.hasOwnProperty("error")&&(r.error=t.error?{...t.error}:null),t.status&&(r.status={...t.status}),t.file&&(r.output=t.file),t.source)r.file=t.source;else if(t.item||t.id){const e=t.item?t.item:n.query("GET_ITEM",t.id);r.file=e?de(e):null}return t.items&&(r.items=t.items.map(de)),/progress/.test(e)&&(r.progress=t.progress),t.hasOwnProperty("origin")&&t.hasOwnProperty("target")&&(r.origin=t.origin,r.target=t.target),r},m={DID_DESTROY:f("destroy"),DID_INIT:f("init"),DID_THROW_MAX_FILES:f("warning"),DID_INIT_ITEM:f("initfile"),DID_START_ITEM_LOAD:f("addfilestart"),DID_UPDATE_ITEM_LOAD_PROGRESS:f("addfileprogress"),DID_LOAD_ITEM:f("addfile"),DID_THROW_ITEM_INVALID:[f("error"),f("addfile")],DID_THROW_ITEM_LOAD_ERROR:[f("error"),f("addfile")],DID_THROW_ITEM_REMOVE_ERROR:[f("error"),f("removefile")],DID_PREPARE_OUTPUT:f("preparefile"),DID_START_ITEM_PROCESSING:f("processfilestart"),DID_UPDATE_ITEM_PROCESS_PROGRESS:f("processfileprogress"),DID_ABORT_ITEM_PROCESSING:f("processfileabort"),DID_COMPLETE_ITEM_PROCESSING:f("processfile"),DID_COMPLETE_ITEM_PROCESSING_ALL:f("processfiles"),DID_REVERT_ITEM_PROCESSING:f("processfilerevert"),DID_THROW_ITEM_PROCESSING_ERROR:[f("error"),f("processfile")],DID_REMOVE_ITEM:f("removefile"),DID_UPDATE_ITEMS:f("updatefiles"),DID_ACTIVATE_ITEM:f("activatefile"),DID_REORDER_ITEMS:f("reorderfiles")},h=e=>{const t={pond:A,...e};delete t.type,E.element.dispatchEvent(new CustomEvent(`FilePond:${e.type}`,{detail:t,bubbles:!0,cancelable:!0,composed:!0}));const r=[];e.hasOwnProperty("error")&&r.push(e.error),e.hasOwnProperty("file")&&r.push(e.file);const o=["type","error","file"];Object.keys(e).filter(e=>!o.includes(e)).forEach(t=>r.push(e[t])),A.fire(e.type,...r);const i=n.query(`GET_ON${e.type.toUpperCase()}`);i&&i(...r)},R=e=>{e.length&&e.filter(e=>m[e.type]).forEach(e=>{const t=m[e.type];(Array.isArray(t)?t:[t]).forEach(t=>{"DID_INIT_ITEM"===e.type?h(t(e.data)):setTimeout(()=>{h(t(e.data))},0)})})},g=e=>new Promise((t,r)=>{n.dispatch("REQUEST_ITEM_PREPARE",{query:e,success:e=>{t(e)},failure:e=>{r(e)}})}),O=(e,t)=>("object"!=typeof e||(e=>e.file&&e.id)(e)||t||(t=e,e=void 0),n.dispatch("REMOVE_ITEM",{...t,query:e}),null===n.query("GET_ACTIVE_ITEM",e)),D=(...e)=>new Promise((t,r)=>{const o=[],i={};if(M(e[0]))o.push.apply(o,e[0]),Object.assign(i,e[1]||{});else{const t=e[e.length-1];"object"!=typeof t||t instanceof Blob||Object.assign(i,e.pop()),o.push(...e)}n.dispatch("ADD_ITEMS",{items:o,index:i.index,interactionMethod:ee,success:t,failure:r})}),S=()=>n.query("GET_ACTIVE_ITEMS"),y=e=>new Promise((t,r)=>{n.dispatch("REQUEST_ITEM_PROCESSING",{query:e,success:e=>{t(e)},failure:e=>{r(e)}})}),A={...ae(),...I,...Z(n,o),setOptions:e=>n.dispatch("SET_OPTIONS",{options:e}),addFile:(e,t={})=>new Promise((r,o)=>{D([{source:e,options:t}],{index:t.index}).then(e=>r(e&&e[0])).catch(o)}),addFiles:D,getFile:e=>n.query("GET_ACTIVE_ITEM",e),processFile:y,prepareFile:g,removeFile:O,moveFile:(e,t)=>n.dispatch("MOVE_ITEM",{query:e,index:t}),getFiles:S,processFiles:(...e)=>{const t=Array.isArray(e[0])?e[0]:e;if(!t.length){const e=S().filter(e=>!(e.status===pe.IDLE&&e.origin===Ee.LOCAL)&&e.status!==pe.PROCESSING&&e.status!==pe.PROCESSING_COMPLETE&&e.status!==pe.PROCESSING_REVERT_ERROR);return Promise.all(e.map(y))}return Promise.all(t.map(y))},removeFiles:(...e)=>{const t=Array.isArray(e[0])?e[0]:e;let r;"object"==typeof t[t.length-1]?r=t.pop():Array.isArray(e[0])&&(r=e[1]);const o=S();return t.length?t.map(e=>T(e)?o[e]?o[e].id:null:e).filter(e=>e).map(e=>O(e,r)):Promise.all(o.map(e=>O(e,r)))},prepareFiles:(...e)=>{const t=Array.isArray(e[0])?e[0]:e,r=t.length?t:S();return Promise.all(r.map(g))},sort:e=>n.dispatch("SORT",{compare:e}),browse:()=>{var e=E.element.querySelector("input[type=file]");e&&e.click()},destroy:()=>{A.fire("destroy",E.element),n.dispatch("ABORT_ALL"),E._destroy(),window.removeEventListener("resize",p),document.removeEventListener("visibilitychange",i),n.dispatch("DID_DESTROY")},insertBefore:e=>P(E.element,e),insertAfter:e=>v(E.element,e),appendTo:e=>e.appendChild(E.element),replaceElement:e=>{P(E.element,e),e.parentNode.removeChild(e),r=e},restoreElement:()=>{r&&(v(r,E.element),E.element.parentNode.removeChild(E.element),r=null)},isAttachedTo:e=>E.element===e||r===e,element:{get:()=>E.element},status:{get:()=>n.query("GET_STATUS")}};return n.dispatch("DID_INIT"),t(A)},zo=(t={})=>{const r={};return e(Re(),(e,t)=>{r[e]=t[0]}),Wo({...r,...t})},jo=(t,r)=>{e(r,(r,o)=>{e(t,(e,n)=>{const i=new RegExp(r);if(!i.test(e))return;if(delete t[e],!1===o)return;if(U(o))return void(t[o]=n);const s=o.group;X(o)&&!t[s]&&(t[s]={}),t[s][(e=>e.charAt(0).toLowerCase()+e.slice(1))(e.replace(i,""))]=n}),o.mapping&&jo(t[o.group],o.mapping)})},Qo=(t,o={})=>{const n=[];e(t.attributes,e=>{n.push(t.attributes[e])});const i=n.filter(e=>e.name).reduce((e,o)=>{const n=r(t,o.name);return e[(e=>wo(e.replace(/^data-/,"")))(o.name)]=n===o.name||n,e},{});return jo(i,o),i},Zo=(...e)=>(e=>e instanceof HTMLElement)(e[0])?((e,t={})=>{const r={"^class$":"className","^multiple$":"allowMultiple","^capture$":"captureMethod","^webkitdirectory$":"allowDirectoriesOnly","^server":{group:"server",mapping:{"^process":{group:"process"},"^revert":{group:"revert"},"^fetch":{group:"fetch"},"^restore":{group:"restore"},"^load":{group:"load"}}},"^type$":!1,"^files$":!1};me("SET_ATTRIBUTE_TO_OPTION_MAP",r);const o={...t},n=Qo("FIELDSET"===e.nodeName?e.querySelector("input[type=file]"):e,r);Object.keys(n).forEach(e=>{X(n[e])?(X(o[e])||(o[e]={}),Object.assign(o[e],n[e])):o[e]=n[e]}),o.files=(t.files||[]).concat(Array.from(e.querySelectorAll("input:not([type=file])")).map(e=>({source:e.value,options:{type:e.dataset.type}})));const i=zo(o);return e.files&&Array.from(e.files).forEach(e=>{i.addFile(e)}),i.replaceElement(e),i})(...e):zo(...e),Ko=["fire","_read","_write"],Jo=e=>{const t={};return le(e,t,Ko),t},en=(e,t)=>e.replace(/(?:{([a-zA-Z]+)})/g,(e,r)=>t[r]),tn=e=>{const t=new Blob(["(",e.toString(),")()"],{type:"application/javascript"}),r=URL.createObjectURL(t),o=new Worker(r);return{transfer:(e,t)=>{},post:(e,t,r)=>{const n=ie();o.onmessage=(e=>{e.data.id===n&&t(e.data.message)}),o.postMessage({id:n,message:e},r)},terminate:()=>{o.terminate(),URL.revokeObjectURL(r)}}},rn=e=>new Promise((t,r)=>{const o=new Image;o.onload=(()=>{t(o)}),o.onerror=(e=>{r(e)}),o.src=e}),on=(e,t)=>{const r=e.slice(0,e.size,e.type);return r.lastModifiedDate=e.lastModifiedDate,r.name=t,r},nn=e=>on(e,e.name),sn=[],an=t=>{if(sn.includes(t))return;sn.push(t),(e=>Object.assign(ge,e))(t({addFilter:he,utils:{Type:Te,forin:e,isString:U,isFile:_t,toNaturalFileSize:wt,replaceInString:en,getExtensionFromFilename:Fe,getFilenameWithoutExtension:ut,guesstimateMimeType:zr,getFileFromBlob:xe,getFilenameFromURL:Be,createRoute:L,createWorker:tn,createView:A,createItemAPI:de,loadImage:rn,copyFile:nn,renameFile:on,createBlob:Ye,applyFilterChain:fe,text:vt,getNumericAspectRatioFromString:De},views:{fileActionButton:Nt}}).options)},ln=(()=>{const e=d()&&!(()=>"[object OperaMini]"===Object.prototype.toString.call(window.operamini))()&&(()=>"visibilityState"in document)()&&(()=>"Promise"in window)()&&(()=>"slice"in Blob.prototype)()&&(()=>"URL"in window&&"createObjectURL"in window.URL)()&&(()=>"performance"in window)()&&((()=>"supports"in(window.CSS||{}))()||(()=>/MSIE|Trident/.test(window.navigator.userAgent))());return()=>e})(),cn={apps:[]},dn=()=>{};let pn={},En={},un={},_n={},Tn=dn,In=dn,fn=dn,mn=dn,hn=dn,Rn=dn,gn=dn;if(ln()){((e,t,r=60)=>{const o="__framePainter";if(window[o])return window[o].readers.push(e),void window[o].writers.push(t);window[o]={readers:[e],writers:[t]};const n=window[o],i=1e3/r;let s=null,a=null,l=null,c=null;const d=()=>{document.hidden?(l=(()=>window.setTimeout(()=>p(performance.now()),i)),c=(()=>window.clearTimeout(a))):(l=(()=>window.requestAnimationFrame(p)),c=(()=>window.cancelAnimationFrame(a)))};document.addEventListener("visibilitychange",()=>{c&&c(),d(),p(performance.now())});const p=e=>{a=l(p),s||(s=e);const t=e-s;t<=i||(s=e-t%i,n.readers.forEach(e=>e()),n.writers.forEach(t=>t(e)))};d(),p(performance.now())})(()=>{cn.apps.forEach(e=>e._read())},e=>{cn.apps.forEach(t=>t._write(e))});const t=()=>{document.dispatchEvent(new CustomEvent("FilePond:loaded",{detail:{supported:ln,create:Tn,destroy:In,parse:fn,find:mn,registerPlugin:hn,setOptions:gn}})),document.removeEventListener("DOMContentLoaded",t)};"loading"!==document.readyState?setTimeout(()=>t(),0):document.addEventListener("DOMContentLoaded",t);const r=()=>e(Re(),(e,t)=>{_n[e]=t[1]});pn={...ye},un={...Ee},En={...pe},_n={},r(),Tn=((...e)=>{const t=Zo(...e);return t.on("destroy",In),cn.apps.push(t),Jo(t)}),In=(e=>{const t=cn.apps.findIndex(t=>t.isAttachedTo(e));if(t>=0){return cn.apps.splice(t,1)[0].restoreElement(),!0}return!1}),fn=(e=>{return Array.from(e.querySelectorAll(".filepond")).filter(e=>!cn.apps.find(t=>t.isAttachedTo(e))).map(e=>Tn(e))}),mn=(e=>{const t=cn.apps.find(t=>t.isAttachedTo(e));return t?Jo(t):null}),hn=((...e)=>{e.forEach(an),r()}),Rn=(()=>{const t={};return e(Re(),(e,r)=>{t[e]=r[0]}),t}),gn=(t=>(X(t)&&(cn.apps.forEach(e=>{e.setOptions(t)}),(t=>{e(t,(e,t)=>{ge[e]&&(ge[e][0]=z(t,ge[e][0],ge[e][1]))})})(t)),Rn()))}export{un as FileOrigin,En as FileStatus,_n as OptionTypes,pn as Status,Tn as create,In as destroy,mn as find,Rn as getOptions,fn as parse,hn as registerPlugin,gn as setOptions,ln as supported};
