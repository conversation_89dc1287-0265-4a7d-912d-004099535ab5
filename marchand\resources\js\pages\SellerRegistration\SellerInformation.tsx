import { Head, useForm } from '@inertiajs/react';
import { FormEventHandler } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { User, MapPin, Phone, Calendar, CreditCard } from 'lucide-react';
import { useTranslation } from '@/hooks/use-translation';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import AppearanceToggleDropdown from '@/components/appearance-dropdown';

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
    };
    marchand?: {
        prenom?: string;
        nom?: string;
        nom_complet?: string;
        pays_citoyennete?: string;
        pays_naissance?: string;
        date_naissance?: string;
        type_piece_identite?: string;
        numero_piece_identite?: string;
        pays_emission_piece?: string;
        adresse_ligne1?: string;
        adresse_ligne2?: string;
        ville?: string;
        region?: string;
        code_postal?: string;
        pays_adresse?: string;
        telephone_verification?: string;
    };
    countries: Record<string, string>;
}

export default function SellerInformation({ user, marchand, countries }: Props) {
    const { translate } = useTranslation();
    
    const { data, setData, post, processing, errors } = useForm({
        prenom: marchand?.prenom || '',
        nom: marchand?.nom || '',
        nom_complet: marchand?.nom_complet || '',
        pays_citoyennete: marchand?.pays_citoyennete || '',
        pays_naissance: marchand?.pays_naissance || '',
        date_naissance: marchand?.date_naissance || '',
        type_piece_identite: marchand?.type_piece_identite || 'passport',
        numero_piece_identite: marchand?.numero_piece_identite || '',
        pays_emission_piece: marchand?.pays_emission_piece || '',
        adresse_ligne1: marchand?.adresse_ligne1 || '',
        adresse_ligne2: marchand?.adresse_ligne2 || '',
        ville: marchand?.ville || '',
        region: marchand?.region || '',
        code_postal: marchand?.code_postal || '',
        pays_adresse: marchand?.pays_adresse || '',
        telephone_verification: marchand?.telephone_verification || '',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('seller.information.store'));
    };

    const identityTypes = [
        { value: 'passport', label: translate('seller.information.identity.passport') },
        { value: 'national_id', label: translate('seller.information.identity.national_id') },
        { value: 'driving_license', label: translate('seller.information.identity.driving_license') },
    ];

    return (
        <>
            <Head title={translate('seller.information.title')} />
            
            <div className="min-h-screen bg-background text-foreground">
                {/* Header avec contrôles */}
                <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                    <div className="container mx-auto px-4">
                        <nav className="flex h-16 items-center justify-between">
                            <div className="flex items-center space-x-2">
                                <span className="text-2xl font-bold text-primary">Lorelei</span>
                                <span className="text-xl font-medium text-muted-foreground">Marchand</span>
                            </div>
                            <div className="flex items-center space-x-2">
                                <LanguageSwitcher />
                                <AppearanceToggleDropdown />
                            </div>
                        </nav>
                    </div>
                </header>

                <div className="container mx-auto px-4 py-8 max-w-4xl">
                    {/* Progress Bar */}
                    <div className="mb-8">
                        <div className="flex items-center justify-center mb-6">
                            <div className="flex items-center space-x-4">
                                {/* Étape 1 - Active */}
                                <div className="flex flex-col items-center">
                                    <div className="w-10 h-10 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-semibold">
                                        1
                                    </div>
                                    <span className="text-sm font-medium text-foreground mt-2">
                                        {translate('seller.steps.information')}
                                    </span>
                                </div>
                                <div className="w-16 h-0.5 bg-border"></div>
                                
                                {/* Étape 2 */}
                                <div className="flex flex-col items-center">
                                    <div className="w-10 h-10 bg-muted text-muted-foreground rounded-full flex items-center justify-center font-semibold">
                                        2
                                    </div>
                                    <span className="text-sm text-muted-foreground mt-2">
                                        {translate('seller.steps.billing')}
                                    </span>
                                </div>
                                <div className="w-16 h-0.5 bg-border"></div>
                                
                                {/* Étape 3 */}
                                <div className="flex flex-col items-center">
                                    <div className="w-10 h-10 bg-muted text-muted-foreground rounded-full flex items-center justify-center font-semibold">
                                        3
                                    </div>
                                    <span className="text-sm text-muted-foreground mt-2">
                                        {translate('seller.steps.store')}
                                    </span>
                                </div>
                                <div className="w-16 h-0.5 bg-border"></div>
                                
                                {/* Étape 4 */}
                                <div className="flex flex-col items-center">
                                    <div className="w-10 h-10 bg-muted text-muted-foreground rounded-full flex items-center justify-center font-semibold">
                                        4
                                    </div>
                                    <span className="text-sm text-muted-foreground mt-2">
                                        {translate('seller.steps.verification')}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <Progress value={25} className="h-2" />
                    </div>

                    <Card className="border-border bg-card">
                        <CardHeader>
                            <CardTitle className="flex items-center space-x-2 text-card-foreground">
                                <User className="w-5 h-5 text-primary" />
                                <span>{translate('seller.information.form_title')}</span>
                            </CardTitle>
                            <CardDescription className="text-muted-foreground">
                                {translate('seller.information.form_description')}
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={submit} className="space-y-8">
                                {/* Informations personnelles */}
                                <div className="space-y-6">
                                    <h3 className="text-lg font-semibold text-foreground border-b border-border pb-2">
                                        {translate('seller.information.personal_info')}
                                    </h3>
                                    
                                    <div className="grid md:grid-cols-3 gap-6">
                                        <div className="space-y-2">
                                            <Label htmlFor="prenom">{translate('seller.information.first_name')} *</Label>
                                            <Input
                                                id="prenom"
                                                value={data.prenom}
                                                onChange={(e) => setData('prenom', e.target.value)}
                                                placeholder={translate('seller.information.first_name_placeholder')}
                                                className={errors.prenom ? 'border-destructive' : ''}
                                            />
                                            {errors.prenom && (
                                                <p className="text-sm text-destructive">{errors.prenom}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="nom">{translate('seller.information.last_name')} *</Label>
                                            <Input
                                                id="nom"
                                                value={data.nom}
                                                onChange={(e) => setData('nom', e.target.value)}
                                                placeholder={translate('seller.information.last_name_placeholder')}
                                                className={errors.nom ? 'border-destructive' : ''}
                                            />
                                            {errors.nom && (
                                                <p className="text-sm text-destructive">{errors.nom}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="date_naissance">{translate('seller.information.birth_date')} *</Label>
                                            <Input
                                                id="date_naissance"
                                                type="date"
                                                value={data.date_naissance}
                                                onChange={(e) => setData('date_naissance', e.target.value)}
                                                className={errors.date_naissance ? 'border-destructive' : ''}
                                            />
                                            {errors.date_naissance && (
                                                <p className="text-sm text-destructive">{errors.date_naissance}</p>
                                            )}
                                        </div>
                                    </div>

                                    <div className="grid md:grid-cols-2 gap-6">
                                        <div className="space-y-2">
                                            <Label htmlFor="pays_citoyennete">{translate('seller.information.citizenship_country')} *</Label>
                                            <Select value={data.pays_citoyennete} onValueChange={(value) => setData('pays_citoyennete', value)}>
                                                <SelectTrigger className={errors.pays_citoyennete ? 'border-destructive' : ''}>
                                                    <SelectValue placeholder={translate('seller.information.select_country')} />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {Object.entries(countries).map(([code, name]) => (
                                                        <SelectItem key={code} value={code}>
                                                            {name}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            {errors.pays_citoyennete && (
                                                <p className="text-sm text-destructive">{errors.pays_citoyennete}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="pays_naissance">{translate('seller.information.birth_country')} *</Label>
                                            <Select value={data.pays_naissance} onValueChange={(value) => setData('pays_naissance', value)}>
                                                <SelectTrigger className={errors.pays_naissance ? 'border-destructive' : ''}>
                                                    <SelectValue placeholder={translate('seller.information.select_country')} />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {Object.entries(countries).map(([code, name]) => (
                                                        <SelectItem key={code} value={code}>
                                                            {name}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            {errors.pays_naissance && (
                                                <p className="text-sm text-destructive">{errors.pays_naissance}</p>
                                            )}
                                        </div>
                                    </div>
                                </div>

                                {/* Actions */}
                                <div className="flex justify-between pt-6 border-t border-border">
                                    <Button type="button" variant="outline" asChild>
                                        <a href={route('seller.welcome')}>
                                            {translate('seller.information.back')}
                                        </a>
                                    </Button>
                                    
                                    <Button type="submit" disabled={processing} className="min-w-[150px]">
                                        {processing 
                                            ? translate('seller.information.saving') 
                                            : translate('seller.information.continue')
                                        }
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </>
    );
}
