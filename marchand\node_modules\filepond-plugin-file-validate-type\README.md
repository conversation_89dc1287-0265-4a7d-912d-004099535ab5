# File Type Validation plugin for FilePond

[![npm version](https://badge.fury.io/js/filepond-plugin-file-validate-type.svg)](https://badge.fury.io/js/filepond)

https://pqina.nl/filepond/docs/patterns/plugins/file-validate-type/

The File Type Validation plugin handles blocking of files that are of the wrong type. When creating a FilePond instance based on a input type file, this plugin will automatically interpret the `accept` attribute value.

If you're having trouble setting the correct mime type, use [this codepend demo](https://codepen.io/rikschennink/pen/NzRvbj) to view the mime type detected by different brosers.

[Demo](https://pqina.github.io/filepond-plugin-file-validate-type/)
