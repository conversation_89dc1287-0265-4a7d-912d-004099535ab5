<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('marchands', function (Blueprint $table) {
            // Informations personnelles (Étape 1)
            $table->string('prenom')->nullable()->after('parrain_id');
            $table->string('nom')->nullable()->after('prenom');
            $table->string('nom_complet')->nullable()->after('nom');
            $table->string('pays_citoyennete')->nullable()->after('nom_complet');
            $table->string('pays_naissance')->nullable()->after('pays_citoyennete');
            $table->date('date_naissance')->nullable()->after('pays_naissance');
            $table->enum('type_piece_identite', ['passport', 'national_id', 'driving_license'])->nullable()->after('date_naissance');
            $table->string('numero_piece_identite')->nullable()->after('type_piece_identite');
            $table->string('pays_emission_piece')->nullable()->after('numero_piece_identite');
            $table->string('adresse_ligne1')->nullable()->after('pays_emission_piece');
            $table->string('adresse_ligne2')->nullable()->after('adresse_ligne1');
            $table->string('ville')->nullable()->after('adresse_ligne2');
            $table->string('region')->nullable()->after('ville');
            $table->string('code_postal')->nullable()->after('region');
            $table->string('pays_adresse')->nullable()->after('code_postal');
            $table->string('telephone_verification')->nullable()->after('pays_adresse');

            // Informations de facturation (Étape 2)
            $table->string('numero_carte')->nullable()->after('telephone_verification');
            $table->string('nom_porteur_carte')->nullable()->after('numero_carte');
            $table->string('mois_expiration')->nullable()->after('nom_porteur_carte');
            $table->string('annee_expiration')->nullable()->after('mois_expiration');
            $table->string('adresse_facturation_ligne1')->nullable()->after('annee_expiration');
            $table->string('adresse_facturation_ligne2')->nullable()->after('adresse_facturation_ligne1');
            $table->string('ville_facturation')->nullable()->after('adresse_facturation_ligne2');
            $table->string('region_facturation')->nullable()->after('ville_facturation');
            $table->string('code_postal_facturation')->nullable()->after('region_facturation');
            $table->string('pays_facturation')->nullable()->after('code_postal_facturation');

            // Informations boutique (Étape 3) - Nouveaux champs uniquement
            $table->string('nom_boutique')->nullable()->after('pays_facturation');
            $table->text('description_boutique_nouvelle')->nullable()->after('nom_boutique'); // Éviter conflit avec description_business
            $table->enum('a_codes_upc', ['yes', 'no'])->nullable()->after('description_boutique_nouvelle');
            $table->enum('possede_marque', ['yes', 'no', 'some'])->nullable()->after('a_codes_upc');
            $table->enum('type_vendeur', ['individual', 'business'])->nullable()->after('possede_marque');
            $table->text('categories_produits_nouvelles')->nullable()->after('type_vendeur'); // Éviter conflit avec categories_produits
            $table->string('volume_ventes_mensuel')->nullable()->after('categories_produits_nouvelles');

            // Mettre à jour les étapes d'inscription pour le nouveau processus
            $table->dropColumn('etape_inscription');
        });

        // Ajouter la nouvelle colonne etape_inscription avec les nouvelles valeurs
        Schema::table('marchands', function (Blueprint $table) {
            $table->enum('etape_inscription', ['information', 'billing', 'store', 'documents', 'pret_validation', 'validation', 'complete'])->default('information')->after('type_business');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('marchands', function (Blueprint $table) {
            // Supprimer les colonnes dans l'ordre inverse
            $table->dropColumn([
                'volume_ventes_mensuel',
                'categories_produits',
                'type_vendeur',
                'possede_marque',
                'a_codes_upc',
                'description_boutique',
                'nom_boutique',
                'pays_facturation',
                'code_postal_facturation',
                'region_facturation',
                'ville_facturation',
                'adresse_facturation_ligne2',
                'adresse_facturation_ligne1',
                'annee_expiration',
                'mois_expiration',
                'nom_porteur_carte',
                'numero_carte',
                'telephone_verification',
                'pays_adresse',
                'code_postal',
                'region',
                'ville',
                'adresse_ligne2',
                'adresse_ligne1',
                'pays_emission_piece',
                'numero_piece_identite',
                'type_piece_identite',
                'date_naissance',
                'pays_naissance',
                'pays_citoyennete',
                'nom_complet',
                'nom',
                'prenom'
            ]);
        });
    }
};
