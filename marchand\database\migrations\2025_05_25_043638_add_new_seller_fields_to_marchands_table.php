<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Vérifier si les colonnes existent avant de les ajouter
        Schema::table('marchands', function (Blueprint $table) {
            // Informations personnelles (Étape 1) - Ajouter seulement si elles n'existent pas
            if (!Schema::hasColumn('marchands', 'prenom')) {
                $table->string('prenom')->nullable()->after('user_id');
            }
            if (!Schema::hasColumn('marchands', 'nom')) {
                $table->string('nom')->nullable()->after('prenom');
            }
            if (!Schema::hasColumn('marchands', 'nom_complet')) {
                $table->string('nom_complet')->nullable()->after('nom');
            }
            if (!Schema::hasColumn('marchands', 'pays_citoyennete')) {
                $table->string('pays_citoyennete')->nullable()->after('nom_complet');
            }
            if (!Schema::hasColumn('marchands', 'pays_naissance')) {
                $table->string('pays_naissance')->nullable()->after('pays_citoyennete');
            }
            if (!Schema::hasColumn('marchands', 'date_naissance')) {
                $table->date('date_naissance')->nullable()->after('pays_naissance');
            }
            if (!Schema::hasColumn('marchands', 'type_piece_identite')) {
                $table->enum('type_piece_identite', ['passport', 'national_id', 'driving_license'])->nullable()->after('date_naissance');
            }
            if (!Schema::hasColumn('marchands', 'numero_piece_identite')) {
                $table->string('numero_piece_identite')->nullable()->after('type_piece_identite');
            }
            if (!Schema::hasColumn('marchands', 'pays_emission_piece')) {
                $table->string('pays_emission_piece')->nullable()->after('numero_piece_identite');
            }
            if (!Schema::hasColumn('marchands', 'adresse_ligne1')) {
                $table->string('adresse_ligne1')->nullable()->after('pays_emission_piece');
            }
            if (!Schema::hasColumn('marchands', 'adresse_ligne2')) {
                $table->string('adresse_ligne2')->nullable()->after('adresse_ligne1');
            }
            if (!Schema::hasColumn('marchands', 'ville')) {
                $table->string('ville')->nullable()->after('adresse_ligne2');
            }
            if (!Schema::hasColumn('marchands', 'region')) {
                $table->string('region')->nullable()->after('ville');
            }
            if (!Schema::hasColumn('marchands', 'code_postal')) {
                $table->string('code_postal')->nullable()->after('region');
            }
            if (!Schema::hasColumn('marchands', 'pays_adresse')) {
                $table->string('pays_adresse')->nullable()->after('code_postal');
            }
            if (!Schema::hasColumn('marchands', 'telephone_verification')) {
                $table->string('telephone_verification')->nullable()->after('pays_adresse');
            }

            // Informations de facturation (Étape 2)
            if (!Schema::hasColumn('marchands', 'numero_carte')) {
                $table->string('numero_carte')->nullable()->after('telephone_verification');
            }
            if (!Schema::hasColumn('marchands', 'nom_porteur_carte')) {
                $table->string('nom_porteur_carte')->nullable()->after('numero_carte');
            }
            if (!Schema::hasColumn('marchands', 'mois_expiration')) {
                $table->string('mois_expiration')->nullable()->after('nom_porteur_carte');
            }
            if (!Schema::hasColumn('marchands', 'annee_expiration')) {
                $table->string('annee_expiration')->nullable()->after('mois_expiration');
            }
            if (!Schema::hasColumn('marchands', 'adresse_facturation_ligne1')) {
                $table->string('adresse_facturation_ligne1')->nullable()->after('annee_expiration');
            }
            if (!Schema::hasColumn('marchands', 'adresse_facturation_ligne2')) {
                $table->string('adresse_facturation_ligne2')->nullable()->after('adresse_facturation_ligne1');
            }
            if (!Schema::hasColumn('marchands', 'ville_facturation')) {
                $table->string('ville_facturation')->nullable()->after('adresse_facturation_ligne2');
            }
            if (!Schema::hasColumn('marchands', 'region_facturation')) {
                $table->string('region_facturation')->nullable()->after('ville_facturation');
            }
            if (!Schema::hasColumn('marchands', 'code_postal_facturation')) {
                $table->string('code_postal_facturation')->nullable()->after('region_facturation');
            }
            if (!Schema::hasColumn('marchands', 'pays_facturation')) {
                $table->string('pays_facturation')->nullable()->after('code_postal_facturation');
            }

            // Informations boutique (Étape 3)
            if (!Schema::hasColumn('marchands', 'nom_boutique')) {
                $table->string('nom_boutique')->nullable()->after('pays_facturation');
            }
            if (!Schema::hasColumn('marchands', 'description_boutique')) {
                $table->text('description_boutique')->nullable()->after('nom_boutique');
            }
            if (!Schema::hasColumn('marchands', 'a_codes_upc')) {
                $table->enum('a_codes_upc', ['yes', 'no'])->nullable()->after('description_boutique');
            }
            if (!Schema::hasColumn('marchands', 'possede_marque')) {
                $table->enum('possede_marque', ['yes', 'no', 'some'])->nullable()->after('a_codes_upc');
            }
            if (!Schema::hasColumn('marchands', 'type_vendeur')) {
                $table->enum('type_vendeur', ['individual', 'business'])->nullable()->after('possede_marque');
            }
            if (!Schema::hasColumn('marchands', 'volume_ventes_mensuel')) {
                $table->string('volume_ventes_mensuel')->nullable()->after('type_vendeur');
            }
        });

        // Mettre à jour etape_inscription si elle existe
        if (Schema::hasColumn('marchands', 'etape_inscription')) {
            Schema::table('marchands', function (Blueprint $table) {
                $table->dropColumn('etape_inscription');
            });
        }

        Schema::table('marchands', function (Blueprint $table) {
            $table->enum('etape_inscription', ['information', 'billing', 'store', 'documents', 'pret_validation', 'validation', 'complete'])->default('information')->after('type_business');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remettre l'ancienne colonne etape_inscription
        Schema::table('marchands', function (Blueprint $table) {
            $table->dropColumn('etape_inscription');
        });

        Schema::table('marchands', function (Blueprint $table) {
            $table->enum('etape_inscription', ['business_info', 'documents', 'pret_validation', 'validation', 'complete'])->default('business_info')->after('type_business');
        });

        // Supprimer les nouvelles colonnes
        Schema::table('marchands', function (Blueprint $table) {
            $table->dropColumn([
                'volume_ventes_mensuel',
                'categories_produits_nouvelles',
                'type_vendeur',
                'possede_marque',
                'a_codes_upc',
                'description_boutique_nouvelle',
                'nom_boutique',
                'pays_facturation',
                'code_postal_facturation',
                'region_facturation',
                'ville_facturation',
                'adresse_facturation_ligne2',
                'adresse_facturation_ligne1',
                'annee_expiration',
                'mois_expiration',
                'nom_porteur_carte',
                'numero_carte',
                'telephone_verification',
                'pays_adresse',
                'code_postal',
                'region',
                'ville',
                'adresse_ligne2',
                'adresse_ligne1',
                'pays_emission_piece',
                'numero_piece_identite',
                'type_piece_identite',
                'date_naissance',
                'pays_naissance',
                'pays_citoyennete',
                'nom_complet',
                'nom',
                'prenom'
            ]);
        });
    }
};
