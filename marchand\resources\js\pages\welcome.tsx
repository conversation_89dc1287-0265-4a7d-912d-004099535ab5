import { type SharedData } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
    Store,
    TrendingUp,
    ArrowRight,
    BarChart3,
    Headphones,
    CreditCard,
    CheckCircle,
    Star
} from 'lucide-react';

export default function Welcome() {
    const { auth } = usePage<SharedData>().props;

    const stats = [
        { number: "10,000+", label: "marchands actifs" },
        { number: "500,000+", label: "produits vendus" },
        { number: "98%", label: "satisfaction marchands" }
    ];

    const benefits = [
        {
            icon: TrendingUp,
            title: "Visibilité maximale",
            description: "Accédez à des milliers de clients potentiels"
        },
        {
            icon: BarChart3,
            title: "Outils puissants",
            description: "Tableau de bord complet, analytics avancées"
        },
        {
            icon: Headphones,
            title: "Support dédié",
            description: "Équipe support disponible 24/7"
        },
        {
            icon: CreditCard,
            title: "Paiements sécurisés",
            description: "Orange Money, MTN Money, virements bancaires"
        }
    ];

    const steps = [
        {
            number: "1",
            title: "Inscrivez-vous",
            description: "Créez votre compte en quelques minutes"
        },
        {
            number: "2",
            title: "Ajoutez vos produits",
            description: "Importez ou ajoutez vos produits facilement"
        },
        {
            number: "3",
            title: "Commencez à vendre",
            description: "Recevez vos premières commandes"
        }
    ];

    const testimonials = [
        {
            name: "Marie Kouassi",
            business: "Boutique Mode Abidjan",
            content: "Grâce à Lorelei, j'ai multiplié mes ventes par 3 en 6 mois !",
            rating: 5
        },
        {
            name: "Jean Mbarga",
            business: "Électronique Douala",
            content: "La plateforme est intuitive et le support client exceptionnel.",
            rating: 5
        },
        {
            name: "Fatou Diallo",
            business: "Artisanat Dakar",
            content: "Mes produits artisanaux trouvent enfin leur public.",
            rating: 5
        }
    ];

    const pricingPlans = [
        {
            name: "Gratuit",
            price: "0 FCFA",
            period: "/mois",
            commission: "5-10%",
            features: ["Accès basique", "Support email 48h", "Visibilité standard"]
        },
        {
            name: "Basique",
            price: "32,798 FCFA",
            period: "/mois",
            commission: "4-8%",
            features: ["Visibilité améliorée", "Support prioritaire 24h", "Analytics de base", "Réduction logistique 5%"],
            popular: true
        },
        {
            name: "Premium",
            price: "65,596 FCFA",
            period: "/mois",
            commission: "3-6%",
            features: ["Badge Premium", "Analytics avancées", "Support VIP", "1 campagne/mois", "Réduction logistique 10%"]
        },
        {
            name: "Élite",
            price: "131,191 FCFA",
            period: "/mois",
            commission: "2-4%",
            features: ["Badge Élite", "IA prédictive", "Support exclusif 6h", "3 campagnes/mois", "Réduction logistique 15%"]
        }
    ];

    return (
        <>
            <Head title="Lorelei Seller - La marketplace qui fait grandir votre business">
                <meta name="description" content="Rejoignez des milliers de marchands qui font confiance à Lorelei pour développer leurs ventes. Inscription gratuite." />
            </Head>

            <div className="min-h-screen bg-white">
                {/* Header */}
                <header className="border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60">
                    <div className="container mx-auto px-4">
                        <nav className="flex h-16 items-center justify-between">
                            <div className="flex items-center space-x-8">
                                <Link href="/" className="flex items-center space-x-2">
                                    <Store className="h-8 w-8 text-blue-600" />
                                    <span className="text-xl font-bold">Lorelei Seller</span>
                                </Link>
                                <div className="hidden md:flex items-center space-x-6">
                                    <a href="#pricing" className="text-gray-600 hover:text-gray-900">Pricing</a>
                                    <a href="#services" className="text-gray-600 hover:text-gray-900">Services</a>
                                    <a href="#resources" className="text-gray-600 hover:text-gray-900">Resources</a>
                                </div>
                            </div>
                            <div className="flex items-center space-x-4">
                                {auth.user ? (
                                    <Button asChild>
                                        <Link href={route('dashboard')}>Dashboard</Link>
                                    </Button>
                                ) : (
                                    <>
                                        <Button variant="ghost" asChild>
                                            <Link href={route('login')}>Login</Link>
                                        </Button>
                                        <Button asChild>
                                            <Link href={route('register')}>Register</Link>
                                        </Button>
                                    </>
                                )}
                            </div>
                        </nav>
                    </div>
                </header>

                {/* Hero Section */}
                <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-20">
                    <div className="container mx-auto px-4">
                        <div className="text-center max-w-4xl mx-auto">
                            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
                                Vendez avec Lorelei
                            </h1>
                            <p className="text-xl md:text-2xl text-gray-600 mb-4">
                                La marketplace qui fait grandir votre business
                            </p>
                            <p className="text-lg text-gray-500 mb-8">
                                Rejoignez des milliers de marchands qui font confiance à Lorelei pour développer leurs ventes
                            </p>

                            {/* Stats */}
                            <div className="flex flex-wrap justify-center gap-8 mb-8">
                                {stats.map((stat, index) => (
                                    <div key={index} className="text-center">
                                        <div className="text-2xl md:text-3xl font-bold text-blue-600">{stat.number}</div>
                                        <div className="text-gray-600">{stat.label}</div>
                                    </div>
                                ))}
                            </div>

                            <div className="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
                                <Button size="lg" asChild>
                                    <Link href={auth.user ? route('seller.welcome') : route('register')}>
                                        Commencer à vendre
                                        <ArrowRight className="ml-2 h-4 w-4" />
                                    </Link>
                                </Button>
                                <Button size="lg" variant="outline" asChild>
                                    <a href="#pricing">Voir les tarifs</a>
                                </Button>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Benefits Section */}
                <section id="services" className="py-20 bg-white">
                    <div className="container mx-auto px-4">
                        <div className="text-center mb-16">
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                                Pourquoi choisir Lorelei ?
                            </h2>
                            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                                Découvrez tous les avantages de notre plateforme pour développer votre activité
                            </p>
                        </div>

                        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                            {benefits.map((benefit, index) => (
                                <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                                    <CardHeader>
                                        <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                                            <benefit.icon className="w-6 h-6 text-blue-600" />
                                        </div>
                                        <CardTitle className="text-lg">{benefit.title}</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <CardDescription>{benefit.description}</CardDescription>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    </div>
                </section>

                {/* How it works */}
                <section className="py-20 bg-gray-50">
                    <div className="container mx-auto px-4">
                        <div className="text-center mb-16">
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                                Comment ça marche ?
                            </h2>
                            <p className="text-xl text-gray-600">
                                Suivez ces étapes simples pour commencer à vendre
                            </p>
                        </div>

                        <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
                            {steps.map((step, index) => (
                                <div key={index} className="text-center">
                                    <div className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                                        {step.number}
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 mb-2">{step.title}</h3>
                                    <p className="text-gray-600">{step.description}</p>
                                </div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Testimonials */}
                <section className="py-20 bg-white">
                    <div className="container mx-auto px-4">
                        <div className="text-center mb-16">
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                                Ce que disent nos marchands
                            </h2>
                            <p className="text-xl text-gray-600">
                                Découvrez les témoignages de marchands qui ont réussi avec Lorelei
                            </p>
                        </div>

                        <div className="grid md:grid-cols-3 gap-8">
                            {testimonials.map((testimonial, index) => (
                                <Card key={index} className="hover:shadow-lg transition-shadow">
                                    <CardContent className="p-6">
                                        <div className="flex mb-4">
                                            {[...Array(testimonial.rating)].map((_, i) => (
                                                <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                                            ))}
                                        </div>
                                        <p className="text-gray-600 mb-4 italic">"{testimonial.content}"</p>
                                        <div>
                                            <div className="font-semibold text-gray-900">{testimonial.name}</div>
                                            <div className="text-sm text-gray-500">{testimonial.business}</div>
                                        </div>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Pricing Section */}
                <section id="pricing" className="py-20 bg-gray-50">
                    <div className="container mx-auto px-4">
                        <div className="text-center mb-16">
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                                Choisissez votre plan
                            </h2>
                            <p className="text-xl text-gray-600">
                                Des tarifs transparents adaptés à votre niveau d'activité
                            </p>
                        </div>

                        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
                            {pricingPlans.map((plan, index) => (
                                <Card key={index} className={`relative hover:shadow-lg transition-shadow ${plan.popular ? 'border-blue-500 border-2' : ''}`}>
                                    {plan.popular && (
                                        <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-blue-600">
                                            Populaire
                                        </Badge>
                                    )}
                                    <CardHeader className="text-center">
                                        <CardTitle className="text-xl">{plan.name}</CardTitle>
                                        <div className="mt-4">
                                            <span className="text-3xl font-bold">{plan.price}</span>
                                            <span className="text-gray-500">{plan.period}</span>
                                        </div>
                                        <div className="text-sm text-gray-600">
                                            Commission: {plan.commission}
                                        </div>
                                    </CardHeader>
                                    <CardContent>
                                        <ul className="space-y-2">
                                            {plan.features.map((feature, featureIndex) => (
                                                <li key={featureIndex} className="flex items-center text-sm">
                                                    <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                                                    {feature}
                                                </li>
                                            ))}
                                        </ul>
                                        <Button className="w-full mt-6" variant={plan.popular ? "default" : "outline"}>
                                            Choisir ce plan
                                        </Button>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    </div>
                </section>

                {/* CTA Section */}
                <section className="py-20 bg-blue-600 text-white">
                    <div className="container mx-auto px-4 text-center">
                        <h2 className="text-3xl md:text-4xl font-bold mb-4">
                            Prêt à développer votre business ?
                        </h2>
                        <p className="text-xl mb-8 text-blue-100">
                            Rejoignez des milliers de marchands qui font confiance à Lorelei
                        </p>
                        <div className="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
                            <Button size="lg" variant="secondary" asChild>
                                <Link href={auth.user ? route('seller.welcome') : route('register')}>
                                    Commencer maintenant
                                    <ArrowRight className="ml-2 h-4 w-4" />
                                </Link>
                            </Button>
                            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600" asChild>
                                <a href="mailto:<EMAIL>">Contacter le support</a>
                            </Button>
                        </div>
                    </div>
                </section>

                {/* Footer */}
                <footer className="bg-gray-900 text-white py-12">
                    <div className="container mx-auto px-4">
                        <div className="grid md:grid-cols-4 gap-8">
                            <div>
                                <div className="flex items-center space-x-2 mb-4">
                                    <Store className="h-6 w-6" />
                                    <span className="text-lg font-bold">Lorelei Seller</span>
                                </div>
                                <p className="text-gray-400">
                                    La marketplace qui fait grandir votre business en Afrique de l'Ouest.
                                </p>
                            </div>
                            <div>
                                <h3 className="font-semibold mb-4">Plateforme</h3>
                                <ul className="space-y-2 text-gray-400">
                                    <li><a href="#pricing" className="hover:text-white">Tarifs</a></li>
                                    <li><a href="#services" className="hover:text-white">Services</a></li>
                                    <li><a href="#" className="hover:text-white">Documentation</a></li>
                                </ul>
                            </div>
                            <div>
                                <h3 className="font-semibold mb-4">Support</h3>
                                <ul className="space-y-2 text-gray-400">
                                    <li><a href="mailto:<EMAIL>" className="hover:text-white">Contact</a></li>
                                    <li><a href="#" className="hover:text-white">FAQ</a></li>
                                    <li><a href="#" className="hover:text-white">Guides</a></li>
                                </ul>
                            </div>
                            <div>
                                <h3 className="font-semibold mb-4">Légal</h3>
                                <ul className="space-y-2 text-gray-400">
                                    <li><a href="#" className="hover:text-white">Conditions d'utilisation</a></li>
                                    <li><a href="#" className="hover:text-white">Politique de confidentialité</a></li>
                                </ul>
                            </div>
                        </div>
                        <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                            <p>&copy; 2024 Lorelei. Tous droits réservés.</p>
                        </div>
                    </div>
                </footer>
            </div>
        </>
    );
}
