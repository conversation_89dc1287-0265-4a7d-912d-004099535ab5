export default {
    labelIdle: '拖放檔案，或者 <span class="filepond--label-action"> 瀏覽 </span>',
    labelInvalidField: '不支援此檔案',
    labelFileWaitingForSize: '正在計算檔案大小',
    labelFileSizeNotAvailable: '檔案大小不符',
    labelFileLoading: '讀取中',
    labelFileLoadError: '讀取錯誤',
    labelFileProcessing: '上傳',
    labelFileProcessingComplete: '已上傳',
    labelFileProcessingAborted: '上傳已取消',
    labelFileProcessingError: '上傳發生錯誤',
    labelFileProcessingRevertError: '還原錯誤',
    labelFileRemoveError: '刪除錯誤',
    labelTapToCancel: '點擊取消',
    labelTapToRetry: '點擊重試',
    labelTapToUndo: '點擊還原',
    labelButtonRemoveItem: '刪除',
    labelButtonAbortItemLoad: '停止',
    labelButtonRetryItemLoad: '重試',
    labelButtonAbortItemProcessing: '取消',
    labelButtonUndoItemProcessing: '取消',
    labelButtonRetryItemProcessing: '重試',
    labelButtonProcessItem: '上傳',
    labelMaxFileSizeExceeded: '檔案過大',
    labelMaxFileSize: '最大值：{filesize}',
    labelMaxTotalFileSizeExceeded: '超過最大可上傳大小',
    labelMaxTotalFileSize: '最大可上傳大小：{filesize}',
    labelFileTypeNotAllowed: '不支援此類型檔案',
    fileValidateTypeLabelExpectedTypes: '應為 {allButLastType} 或 {lastType}',
    imageValidateSizeLabelFormatError: '不支持此類圖片類型',
    imageValidateSizeLabelImageSizeTooSmall: '圖片過小',
    imageValidateSizeLabelImageSizeTooBig: '圖片過大',
    imageValidateSizeLabelExpectedMinSize: '最小尺寸：{minWidth} × {minHeight}',
    imageValidateSizeLabelExpectedMaxSize: '最大尺寸：{maxWidth} × {maxHeight}',
    imageValidateSizeLabelImageResolutionTooLow: '解析度過低',
    imageValidateSizeLabelImageResolutionTooHigh: '解析度過高',
    imageValidateSizeLabelExpectedMinResolution: '最低解析度：{minResolution}',
    imageValidateSizeLabelExpectedMaxResolution: '最高解析度：{maxResolution}'
  };
