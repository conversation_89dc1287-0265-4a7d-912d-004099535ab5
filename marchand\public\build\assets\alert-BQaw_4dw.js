import{j as a}from"./app-CZ-bNXzO.js";import{a as e,b as i}from"./createLucideIcon-DkVbiciO.js";const o=i("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-background text-foreground",destructive:"text-destructive-foreground [&>svg]:text-current *:data-[slot=alert-description]:text-destructive-foreground/80"}},defaultVariants:{variant:"default"}});function l({className:t,variant:r,...s}){return a.jsx("div",{"data-slot":"alert",role:"alert",className:e(o({variant:r}),t),...s})}function c({className:t,...r}){return a.jsx("div",{"data-slot":"alert-description",className:e("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...r})}export{l as A,c as a};
