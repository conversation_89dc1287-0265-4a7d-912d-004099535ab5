export default {
    labelIdle: 'Ievelciet savus failus vai <span class="filepond--label-action"> pārlūkojiet šeit </span>',
    labelInvalidField: 'Lauks satur nederīgus failus',
    labelFileWaitingForSize: 'Gaidām faila izmēru',
    labelFileSizeNotAvailable: 'Izmērs nav pieejams',
    labelFileLoading: 'Notiek ielāde',
    labelFileLoadError: 'Notika kļūda ielādes laikā',
    labelFileProcessing: 'Notiek augšupielāde',
    labelFileProcessingComplete: 'Augšupielāde pabeigta',
    labelFileProcessingAborted: 'Augšupielāde atcelta',
    labelFileProcessingError: 'Notika kļūda augšupielādes laikā',
    labelFileProcessingRevertError: 'Notika kļūda atgriešanas laikā',
    labelFileRemoveError: 'Notika kļūda dzēšanas laikā',
    labelTapToCancel: 'pieskarieties, lai atceltu',
    labelTapToRetry: 'pieskarieties, lai mēģinātu vēlreiz',
    labelTapToUndo: 'pieskarieties, lai atsauktu',
    labelButtonRemoveItem: 'Dzēst',
    labelButtonAbortItemLoad: 'Pārtraukt',
    labelButtonRetryItemLoad: 'Mēģināt vēlreiz',
    labelButtonAbortItemProcessing: 'Pārtraucam',
    labelButtonUndoItemProcessing: 'Atsaucam',
    labelButtonRetryItemProcessing: 'Mēģinām vēlreiz',
    labelButtonProcessItem: 'Augšupielādēt',
    labelMaxFileSizeExceeded: 'Fails ir pārāk liels',
    labelMaxFileSize: 'Maksimālais faila izmērs ir {filesize}',
    labelMaxTotalFileSizeExceeded: 'Pārsniegts maksimālais kopējais failu izmērs',
    labelMaxTotalFileSize: 'Maksimālais kopējais failu izmērs ir {filesize}',
    labelFileTypeNotAllowed: 'Nederīgs faila tips',
    fileValidateTypeLabelExpectedTypes: 'Sagaidām {allButLastType} vai {lastType}',
    imageValidateSizeLabelFormatError: 'Neatbilstošs attēla tips',
    imageValidateSizeLabelImageSizeTooSmall: 'Attēls ir pārāk mazs',
    imageValidateSizeLabelImageSizeTooBig: 'Attēls ir pārāk liels',
    imageValidateSizeLabelExpectedMinSize: 'Minimālais izmērs ir {minWidth} × {minHeight}',
    imageValidateSizeLabelExpectedMaxSize: 'Maksimālais izmērs ir {maxWidth} × {maxHeight}',
    imageValidateSizeLabelImageResolutionTooLow: 'Izšķirtspēja ir pārāk zema',
    imageValidateSizeLabelImageResolutionTooHigh: 'Izšķirtspēja ir pārāk augsta',
    imageValidateSizeLabelExpectedMinResolution: 'Minimālā izšķirtspēja ir {minResolution}',
    imageValidateSizeLabelExpectedMaxResolution: 'Maksimālā izšķirtspēja ir {maxResolution}'
};
