{"version": 3, "sources": ["../../react-filepond/dist/react-filepond.esm.js", "../../filepond/dist/filepond.esm.js"], "sourcesContent": ["/*!\n * react-filepond v7.1.3\n * A handy FilePond adapter component for React\n * \n * Copyright (c) 2024 PQINA\n * https://pqina.nl/filepond\n * \n * Licensed under the MIT license.\n */\n\nimport React, { createElement, useCallback } from \"react\";\n\n// Import required methods and styles from the FilePond module, should not need anything else\nimport { create, supported, registerPlugin, FileStatus } from \"filepond\";\n\n// We need to be able to call the registerPlugin method directly so we can add plugins\nexport { registerPlugin, FileStatus };\n\n// Do this once\nconst isSupported = supported();\n\n// filtered methods\nconst filteredMethods = [\n  \"setOptions\",\n  \"on\",\n  \"off\",\n  \"onOnce\",\n  \"appendTo\",\n  \"insertAfter\",\n  \"insertBefore\",\n  \"isAttachedTo\",\n  \"replaceElement\",\n  \"restoreElement\",\n  \"destroy\",\n];\n\n// The React <FilePond/> wrapper\nexport class FilePond extends React.Component {\n  constructor(props) {\n    super(props);\n    this.allowFilesSync = true;\n  }\n\n  // Will setup FilePond instance when mounted\n  componentDidMount() {\n    // clone the input so we can restore it in unmount\n    this._input = this._element.querySelector('input[type=\"file\"]');\n    this._inputClone = this._input.cloneNode();\n\n    // exit here if not supported\n    if (!isSupported) return;\n\n    const options = Object.assign({}, this.props);\n\n    // if onupdate files is defined, make sure setFiles does not cause race condition\n    if (options.onupdatefiles) {\n      const cb = options.onupdatefiles;\n      options.onupdatefiles = (items) => {\n        this.allowFilesSync = false;\n        cb(items);\n      };\n    }\n\n    // Create our pond\n    this._pond = create(this._input, options);\n\n    // Reference pond methods to FilePond component instance\n    Object.keys(this._pond)\n      .filter((key) => !filteredMethods.includes(key))\n      .forEach((key) => {\n        this[key] = this._pond[key];\n      });\n  }\n\n  // Will clean up FilePond instance when unmounted\n  componentWillUnmount() {\n    // exit when no pond defined\n    if (!this._pond) return;\n\n    // This fixed <Strict> errors\n\n    // FilePond destroy is async so we have to move FilePond to a bin element so it can no longer affect current element tree as React unmount / mount is sync\n    const bin = document.createElement(\"div\");\n    bin.append(this._pond.element);\n    bin.id = \"foo\";\n\n    // now we call destroy so FilePond can start it's destroy logic\n    this._pond.destroy();\n    this._pond = undefined;\n\n    // we re-add the original file input element so everything is as it was before\n    this._element.append(this._inputClone);\n  }\n\n  shouldComponentUpdate() {\n    if (!this.allowFilesSync) {\n      this.allowFilesSync = true;\n      return false;\n    }\n    return true;\n  }\n\n  // Something changed\n  componentDidUpdate() {\n    // exit when no pond defined\n    if (!this._pond) return;\n\n    const options = Object.assign({}, this.props);\n\n    // this is only set onces, on didmount\n    delete options.onupdatefiles;\n\n    // update pond options based on new props\n    this._pond.setOptions(options);\n  }\n\n  // Renders basic element hook for FilePond to attach to\n  render() {\n    const {\n      id,\n      name,\n      className,\n      allowMultiple,\n      required,\n      captureMethod,\n      acceptedFileTypes,\n    } = this.props;\n    return createElement(\n      \"div\",\n      {\n        className: \"filepond--wrapper\",\n        ref: (element) => (this._element = element),\n      },\n      createElement(\"input\", {\n        type: \"file\",\n        name,\n        id,\n        accept: acceptedFileTypes,\n        multiple: allowMultiple,\n        required: required,\n        className: className,\n        capture: captureMethod,\n      })\n    );\n  }\n}\n\n", "/*!\n * FilePond 4.32.7\n * Licensed under MIT, https://opensource.org/licenses/MIT/\n * Please visit https://pqina.nl/filepond/ for details.\n */\n\n/* eslint-disable */\n\nconst isNode = value => value instanceof HTMLElement;\n\nconst createStore = (initialState, queries = [], actions = []) => {\n    // internal state\n    const state = {\n        ...initialState,\n    };\n\n    // contains all actions for next frame, is clear when actions are requested\n    const actionQueue = [];\n    const dispatchQueue = [];\n\n    // returns a duplicate of the current state\n    const getState = () => ({ ...state });\n\n    // returns a duplicate of the actions array and clears the actions array\n    const processActionQueue = () => {\n        // create copy of actions queue\n        const queue = [...actionQueue];\n\n        // clear actions queue (we don't want no double actions)\n        actionQueue.length = 0;\n\n        return queue;\n    };\n\n    // processes actions that might block the main UI thread\n    const processDispatchQueue = () => {\n        // create copy of actions queue\n        const queue = [...dispatchQueue];\n\n        // clear actions queue (we don't want no double actions)\n        dispatchQueue.length = 0;\n\n        // now dispatch these actions\n        queue.forEach(({ type, data }) => {\n            dispatch(type, data);\n        });\n    };\n\n    // adds a new action, calls its handler and\n    const dispatch = (type, data, isBlocking) => {\n        // is blocking action (should never block if document is hidden)\n        if (isBlocking && !document.hidden) {\n            dispatchQueue.push({ type, data });\n            return;\n        }\n\n        // if this action has a handler, handle the action\n        if (actionHandlers[type]) {\n            actionHandlers[type](data);\n        }\n\n        // now add action\n        actionQueue.push({\n            type,\n            data,\n        });\n    };\n\n    const query = (str, ...args) => (queryHandles[str] ? queryHandles[str](...args) : null);\n\n    const api = {\n        getState,\n        processActionQueue,\n        processDispatchQueue,\n        dispatch,\n        query,\n    };\n\n    let queryHandles = {};\n    queries.forEach(query => {\n        queryHandles = {\n            ...query(state),\n            ...queryHandles,\n        };\n    });\n\n    let actionHandlers = {};\n    actions.forEach(action => {\n        actionHandlers = {\n            ...action(dispatch, query, state),\n            ...actionHandlers,\n        };\n    });\n\n    return api;\n};\n\nconst defineProperty = (obj, property, definition) => {\n    if (typeof definition === 'function') {\n        obj[property] = definition;\n        return;\n    }\n    Object.defineProperty(obj, property, { ...definition });\n};\n\nconst forin = (obj, cb) => {\n    for (const key in obj) {\n        if (!obj.hasOwnProperty(key)) {\n            continue;\n        }\n\n        cb(key, obj[key]);\n    }\n};\n\nconst createObject = definition => {\n    const obj = {};\n    forin(definition, property => {\n        defineProperty(obj, property, definition[property]);\n    });\n    return obj;\n};\n\nconst attr = (node, name, value = null) => {\n    if (value === null) {\n        return node.getAttribute(name) || node.hasAttribute(name);\n    }\n    node.setAttribute(name, value);\n};\n\nconst ns = 'http://www.w3.org/2000/svg';\nconst svgElements = ['svg', 'path']; // only svg elements used\n\nconst isSVGElement = tag => svgElements.includes(tag);\n\nconst createElement = (tag, className, attributes = {}) => {\n    if (typeof className === 'object') {\n        attributes = className;\n        className = null;\n    }\n    const element = isSVGElement(tag)\n        ? document.createElementNS(ns, tag)\n        : document.createElement(tag);\n    if (className) {\n        if (isSVGElement(tag)) {\n            attr(element, 'class', className);\n        } else {\n            element.className = className;\n        }\n    }\n    forin(attributes, (name, value) => {\n        attr(element, name, value);\n    });\n    return element;\n};\n\nconst appendChild = parent => (child, index) => {\n    if (typeof index !== 'undefined' && parent.children[index]) {\n        parent.insertBefore(child, parent.children[index]);\n    } else {\n        parent.appendChild(child);\n    }\n};\n\nconst appendChildView = (parent, childViews) => (view, index) => {\n    if (typeof index !== 'undefined') {\n        childViews.splice(index, 0, view);\n    } else {\n        childViews.push(view);\n    }\n\n    return view;\n};\n\nconst removeChildView = (parent, childViews) => view => {\n    // remove from child views\n    childViews.splice(childViews.indexOf(view), 1);\n\n    // remove the element\n    if (view.element.parentNode) {\n        parent.removeChild(view.element);\n    }\n\n    return view;\n};\n\nconst IS_BROWSER = (() =>\n    typeof window !== 'undefined' && typeof window.document !== 'undefined')();\nconst isBrowser = () => IS_BROWSER;\n\nconst testElement = isBrowser() ? createElement('svg') : {};\nconst getChildCount =\n    'children' in testElement ? el => el.children.length : el => el.childNodes.length;\n\nconst getViewRect = (elementRect, childViews, offset, scale) => {\n    const left = offset[0] || elementRect.left;\n    const top = offset[1] || elementRect.top;\n    const right = left + elementRect.width;\n    const bottom = top + elementRect.height * (scale[1] || 1);\n\n    const rect = {\n        // the rectangle of the element itself\n        element: {\n            ...elementRect,\n        },\n\n        // the rectangle of the element expanded to contain its children, does not include any margins\n        inner: {\n            left: elementRect.left,\n            top: elementRect.top,\n            right: elementRect.right,\n            bottom: elementRect.bottom,\n        },\n\n        // the rectangle of the element expanded to contain its children including own margin and child margins\n        // margins will be added after we've recalculated the size\n        outer: {\n            left,\n            top,\n            right,\n            bottom,\n        },\n    };\n\n    // expand rect to fit all child rectangles\n    childViews\n        .filter(childView => !childView.isRectIgnored())\n        .map(childView => childView.rect)\n        .forEach(childViewRect => {\n            expandRect(rect.inner, { ...childViewRect.inner });\n            expandRect(rect.outer, { ...childViewRect.outer });\n        });\n\n    // calculate inner width and height\n    calculateRectSize(rect.inner);\n\n    // append additional margin (top and left margins are included in top and left automatically)\n    rect.outer.bottom += rect.element.marginBottom;\n    rect.outer.right += rect.element.marginRight;\n\n    // calculate outer width and height\n    calculateRectSize(rect.outer);\n\n    return rect;\n};\n\nconst expandRect = (parent, child) => {\n    // adjust for parent offset\n    child.top += parent.top;\n    child.right += parent.left;\n    child.bottom += parent.top;\n    child.left += parent.left;\n\n    if (child.bottom > parent.bottom) {\n        parent.bottom = child.bottom;\n    }\n\n    if (child.right > parent.right) {\n        parent.right = child.right;\n    }\n};\n\nconst calculateRectSize = rect => {\n    rect.width = rect.right - rect.left;\n    rect.height = rect.bottom - rect.top;\n};\n\nconst isNumber = value => typeof value === 'number';\n\n/**\n * Determines if position is at destination\n * @param position\n * @param destination\n * @param velocity\n * @param errorMargin\n * @returns {boolean}\n */\nconst thereYet = (position, destination, velocity, errorMargin = 0.001) => {\n    return Math.abs(position - destination) < errorMargin && Math.abs(velocity) < errorMargin;\n};\n\n/**\n * Spring animation\n */\nconst spring =\n    // default options\n    ({ stiffness = 0.5, damping = 0.75, mass = 10 } = {}) =>\n        // method definition\n        {\n            let target = null;\n            let position = null;\n            let velocity = 0;\n            let resting = false;\n\n            // updates spring state\n            const interpolate = (ts, skipToEndState) => {\n                // in rest, don't animate\n                if (resting) return;\n\n                // need at least a target or position to do springy things\n                if (!(isNumber(target) && isNumber(position))) {\n                    resting = true;\n                    velocity = 0;\n                    return;\n                }\n\n                // calculate spring force\n                const f = -(position - target) * stiffness;\n\n                // update velocity by adding force based on mass\n                velocity += f / mass;\n\n                // update position by adding velocity\n                position += velocity;\n\n                // slow down based on amount of damping\n                velocity *= damping;\n\n                // we've arrived if we're near target and our velocity is near zero\n                if (thereYet(position, target, velocity) || skipToEndState) {\n                    position = target;\n                    velocity = 0;\n                    resting = true;\n\n                    // we done\n                    api.onupdate(position);\n                    api.oncomplete(position);\n                } else {\n                    // progress update\n                    api.onupdate(position);\n                }\n            };\n\n            /**\n             * Set new target value\n             * @param value\n             */\n            const setTarget = value => {\n                // if currently has no position, set target and position to this value\n                if (isNumber(value) && !isNumber(position)) {\n                    position = value;\n                }\n\n                // next target value will not be animated to\n                if (target === null) {\n                    target = value;\n                    position = value;\n                }\n\n                // let start moving to target\n                target = value;\n\n                // already at target\n                if (position === target || typeof target === 'undefined') {\n                    // now resting as target is current position, stop moving\n                    resting = true;\n                    velocity = 0;\n\n                    // done!\n                    api.onupdate(position);\n                    api.oncomplete(position);\n\n                    return;\n                }\n\n                resting = false;\n            };\n\n            // need 'api' to call onupdate callback\n            const api = createObject({\n                interpolate,\n                target: {\n                    set: setTarget,\n                    get: () => target,\n                },\n                resting: {\n                    get: () => resting,\n                },\n                onupdate: value => {},\n                oncomplete: value => {},\n            });\n\n            return api;\n        };\n\nconst easeLinear = t => t;\nconst easeInOutQuad = t => (t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t);\n\nconst tween =\n    // default values\n    ({ duration = 500, easing = easeInOutQuad, delay = 0 } = {}) =>\n        // method definition\n        {\n            let start = null;\n            let t;\n            let p;\n            let resting = true;\n            let reverse = false;\n            let target = null;\n\n            const interpolate = (ts, skipToEndState) => {\n                if (resting || target === null) return;\n\n                if (start === null) {\n                    start = ts;\n                }\n\n                if (ts - start < delay) return;\n\n                t = ts - start - delay;\n\n                if (t >= duration || skipToEndState) {\n                    t = 1;\n                    p = reverse ? 0 : 1;\n                    api.onupdate(p * target);\n                    api.oncomplete(p * target);\n                    resting = true;\n                } else {\n                    p = t / duration;\n                    api.onupdate((t >= 0 ? easing(reverse ? 1 - p : p) : 0) * target);\n                }\n            };\n\n            // need 'api' to call onupdate callback\n            const api = createObject({\n                interpolate,\n                target: {\n                    get: () => (reverse ? 0 : target),\n                    set: value => {\n                        // is initial value\n                        if (target === null) {\n                            target = value;\n                            api.onupdate(value);\n                            api.oncomplete(value);\n                            return;\n                        }\n\n                        // want to tween to a smaller value and have a current value\n                        if (value < target) {\n                            target = 1;\n                            reverse = true;\n                        } else {\n                            // not tweening to a smaller value\n                            reverse = false;\n                            target = value;\n                        }\n\n                        // let's go!\n                        resting = false;\n                        start = null;\n                    },\n                },\n                resting: {\n                    get: () => resting,\n                },\n                onupdate: value => {},\n                oncomplete: value => {},\n            });\n\n            return api;\n        };\n\nconst animator = {\n    spring,\n    tween,\n};\n\n/*\n { type: 'spring', stiffness: .5, damping: .75, mass: 10 };\n { translation: { type: 'spring', ... }, ... }\n { translation: { x: { type: 'spring', ... } } }\n*/\nconst createAnimator = (definition, category, property) => {\n    // default is single definition\n    // we check if transform is set, if so, we check if property is set\n    const def =\n        definition[category] && typeof definition[category][property] === 'object'\n            ? definition[category][property]\n            : definition[category] || definition;\n\n    const type = typeof def === 'string' ? def : def.type;\n    const props = typeof def === 'object' ? { ...def } : {};\n\n    return animator[type] ? animator[type](props) : null;\n};\n\nconst addGetSet = (keys, obj, props, overwrite = false) => {\n    obj = Array.isArray(obj) ? obj : [obj];\n    obj.forEach(o => {\n        keys.forEach(key => {\n            let name = key;\n            let getter = () => props[key];\n            let setter = value => (props[key] = value);\n\n            if (typeof key === 'object') {\n                name = key.key;\n                getter = key.getter || getter;\n                setter = key.setter || setter;\n            }\n\n            if (o[name] && !overwrite) {\n                return;\n            }\n\n            o[name] = {\n                get: getter,\n                set: setter,\n            };\n        });\n    });\n};\n\n// add to state,\n// add getters and setters to internal and external api (if not set)\n// setup animators\n\nconst animations = ({ mixinConfig, viewProps, viewInternalAPI, viewExternalAPI }) => {\n    // initial properties\n    const initialProps = { ...viewProps };\n\n    // list of all active animations\n    const animations = [];\n\n    // setup animators\n    forin(mixinConfig, (property, animation) => {\n        const animator = createAnimator(animation);\n        if (!animator) {\n            return;\n        }\n\n        // when the animator updates, update the view state value\n        animator.onupdate = value => {\n            viewProps[property] = value;\n        };\n\n        // set animator target\n        animator.target = initialProps[property];\n\n        // when value is set, set the animator target value\n        const prop = {\n            key: property,\n            setter: value => {\n                // if already at target, we done!\n                if (animator.target === value) {\n                    return;\n                }\n\n                animator.target = value;\n            },\n            getter: () => viewProps[property],\n        };\n\n        // add getters and setters\n        addGetSet([prop], [viewInternalAPI, viewExternalAPI], viewProps, true);\n\n        // add it to the list for easy updating from the _write method\n        animations.push(animator);\n    });\n\n    // expose internal write api\n    return {\n        write: ts => {\n            let skipToEndState = document.hidden;\n            let resting = true;\n            animations.forEach(animation => {\n                if (!animation.resting) resting = false;\n                animation.interpolate(ts, skipToEndState);\n            });\n            return resting;\n        },\n        destroy: () => {},\n    };\n};\n\nconst addEvent = element => (type, fn) => {\n    element.addEventListener(type, fn);\n};\n\nconst removeEvent = element => (type, fn) => {\n    element.removeEventListener(type, fn);\n};\n\n// mixin\nconst listeners = ({\n    mixinConfig,\n    viewProps,\n    viewInternalAPI,\n    viewExternalAPI,\n    viewState,\n    view,\n}) => {\n    const events = [];\n\n    const add = addEvent(view.element);\n    const remove = removeEvent(view.element);\n\n    viewExternalAPI.on = (type, fn) => {\n        events.push({\n            type,\n            fn,\n        });\n        add(type, fn);\n    };\n\n    viewExternalAPI.off = (type, fn) => {\n        events.splice(events.findIndex(event => event.type === type && event.fn === fn), 1);\n        remove(type, fn);\n    };\n\n    return {\n        write: () => {\n            // not busy\n            return true;\n        },\n        destroy: () => {\n            events.forEach(event => {\n                remove(event.type, event.fn);\n            });\n        },\n    };\n};\n\n// add to external api and link to props\n\nconst apis = ({ mixinConfig, viewProps, viewExternalAPI }) => {\n    addGetSet(mixinConfig, viewExternalAPI, viewProps);\n};\n\nconst isDefined = value => value != null;\n\n// add to state,\n// add getters and setters to internal and external api (if not set)\n// set initial state based on props in viewProps\n// apply as transforms each frame\n\nconst defaults = {\n    opacity: 1,\n    scaleX: 1,\n    scaleY: 1,\n    translateX: 0,\n    translateY: 0,\n    rotateX: 0,\n    rotateY: 0,\n    rotateZ: 0,\n    originX: 0,\n    originY: 0,\n};\n\nconst styles = ({ mixinConfig, viewProps, viewInternalAPI, viewExternalAPI, view }) => {\n    // initial props\n    const initialProps = { ...viewProps };\n\n    // current props\n    const currentProps = {};\n\n    // we will add those properties to the external API and link them to the viewState\n    addGetSet(mixinConfig, [viewInternalAPI, viewExternalAPI], viewProps);\n\n    // override rect on internal and external rect getter so it takes in account transforms\n    const getOffset = () => [viewProps['translateX'] || 0, viewProps['translateY'] || 0];\n    const getScale = () => [viewProps['scaleX'] || 0, viewProps['scaleY'] || 0];\n    const getRect = () =>\n        view.rect ? getViewRect(view.rect, view.childViews, getOffset(), getScale()) : null;\n    viewInternalAPI.rect = { get: getRect };\n    viewExternalAPI.rect = { get: getRect };\n\n    // apply view props\n    mixinConfig.forEach(key => {\n        viewProps[key] =\n            typeof initialProps[key] === 'undefined' ? defaults[key] : initialProps[key];\n    });\n\n    // expose api\n    return {\n        write: () => {\n            // see if props have changed\n            if (!propsHaveChanged(currentProps, viewProps)) {\n                return;\n            }\n\n            // moves element to correct position on screen\n            applyStyles(view.element, viewProps);\n\n            // store new transforms\n            Object.assign(currentProps, { ...viewProps });\n\n            // no longer busy\n            return true;\n        },\n        destroy: () => {},\n    };\n};\n\nconst propsHaveChanged = (currentProps, newProps) => {\n    // different amount of keys\n    if (Object.keys(currentProps).length !== Object.keys(newProps).length) {\n        return true;\n    }\n\n    // lets analyze the individual props\n    for (const prop in newProps) {\n        if (newProps[prop] !== currentProps[prop]) {\n            return true;\n        }\n    }\n\n    return false;\n};\n\nconst applyStyles = (\n    element,\n    {\n        opacity,\n        perspective,\n        translateX,\n        translateY,\n        scaleX,\n        scaleY,\n        rotateX,\n        rotateY,\n        rotateZ,\n        originX,\n        originY,\n        width,\n        height,\n    }\n) => {\n    let transforms = '';\n    let styles = '';\n\n    // handle transform origin\n    if (isDefined(originX) || isDefined(originY)) {\n        styles += `transform-origin: ${originX || 0}px ${originY || 0}px;`;\n    }\n\n    // transform order is relevant\n    // 0. perspective\n    if (isDefined(perspective)) {\n        transforms += `perspective(${perspective}px) `;\n    }\n\n    // 1. translate\n    if (isDefined(translateX) || isDefined(translateY)) {\n        transforms += `translate3d(${translateX || 0}px, ${translateY || 0}px, 0) `;\n    }\n\n    // 2. scale\n    if (isDefined(scaleX) || isDefined(scaleY)) {\n        transforms += `scale3d(${isDefined(scaleX) ? scaleX : 1}, ${\n            isDefined(scaleY) ? scaleY : 1\n        }, 1) `;\n    }\n\n    // 3. rotate\n    if (isDefined(rotateZ)) {\n        transforms += `rotateZ(${rotateZ}rad) `;\n    }\n\n    if (isDefined(rotateX)) {\n        transforms += `rotateX(${rotateX}rad) `;\n    }\n\n    if (isDefined(rotateY)) {\n        transforms += `rotateY(${rotateY}rad) `;\n    }\n\n    // add transforms\n    if (transforms.length) {\n        styles += `transform:${transforms};`;\n    }\n\n    // add opacity\n    if (isDefined(opacity)) {\n        styles += `opacity:${opacity};`;\n\n        // if we reach zero, we make the element inaccessible\n        if (opacity === 0) {\n            styles += `visibility:hidden;`;\n        }\n\n        // if we're below 100% opacity this element can't be clicked\n        if (opacity < 1) {\n            styles += `pointer-events:none;`;\n        }\n    }\n\n    // add height\n    if (isDefined(height)) {\n        styles += `height:${height}px;`;\n    }\n\n    // add width\n    if (isDefined(width)) {\n        styles += `width:${width}px;`;\n    }\n\n    // apply styles\n    const elementCurrentStyle = element.elementCurrentStyle || '';\n\n    // if new styles does not match current styles, lets update!\n    if (styles.length !== elementCurrentStyle.length || styles !== elementCurrentStyle) {\n        element.style.cssText = styles;\n        // store current styles so we can compare them to new styles later on\n        // _not_ getting the style value is faster\n        element.elementCurrentStyle = styles;\n    }\n};\n\nconst Mixins = {\n    styles,\n    listeners,\n    animations,\n    apis,\n};\n\nconst updateRect = (rect = {}, element = {}, style = {}) => {\n    if (!element.layoutCalculated) {\n        rect.paddingTop = parseInt(style.paddingTop, 10) || 0;\n        rect.marginTop = parseInt(style.marginTop, 10) || 0;\n        rect.marginRight = parseInt(style.marginRight, 10) || 0;\n        rect.marginBottom = parseInt(style.marginBottom, 10) || 0;\n        rect.marginLeft = parseInt(style.marginLeft, 10) || 0;\n        element.layoutCalculated = true;\n    }\n\n    rect.left = element.offsetLeft || 0;\n    rect.top = element.offsetTop || 0;\n    rect.width = element.offsetWidth || 0;\n    rect.height = element.offsetHeight || 0;\n\n    rect.right = rect.left + rect.width;\n    rect.bottom = rect.top + rect.height;\n\n    rect.scrollTop = element.scrollTop;\n\n    rect.hidden = element.offsetParent === null;\n\n    return rect;\n};\n\nconst createView =\n    // default view definition\n    ({\n        // element definition\n        tag = 'div',\n        name = null,\n        attributes = {},\n\n        // view interaction\n        read = () => {},\n        write = () => {},\n        create = () => {},\n        destroy = () => {},\n\n        // hooks\n        filterFrameActionsForChild = (child, actions) => actions,\n        didCreateView = () => {},\n        didWriteView = () => {},\n\n        // rect related\n        ignoreRect = false,\n        ignoreRectUpdate = false,\n\n        // mixins\n        mixins = [],\n    } = {}) => (\n        // each view requires reference to store\n        store,\n        // specific properties for this view\n        props = {}\n    ) => {\n        // root element should not be changed\n        const element = createElement(tag, `filepond--${name}`, attributes);\n\n        // style reference should also not be changed\n        const style = window.getComputedStyle(element, null);\n\n        // element rectangle\n        const rect = updateRect();\n        let frameRect = null;\n\n        // rest state\n        let isResting = false;\n\n        // pretty self explanatory\n        const childViews = [];\n\n        // loaded mixins\n        const activeMixins = [];\n\n        // references to created children\n        const ref = {};\n\n        // state used for each instance\n        const state = {};\n\n        // list of writers that will be called to update this view\n        const writers = [\n            write, // default writer\n        ];\n\n        const readers = [\n            read, // default reader\n        ];\n\n        const destroyers = [\n            destroy, // default destroy\n        ];\n\n        // core view methods\n        const getElement = () => element;\n        const getChildViews = () => childViews.concat();\n        const getReference = () => ref;\n        const createChildView = store => (view, props) => view(store, props);\n        const getRect = () => {\n            if (frameRect) {\n                return frameRect;\n            }\n            frameRect = getViewRect(rect, childViews, [0, 0], [1, 1]);\n            return frameRect;\n        };\n        const getStyle = () => style;\n\n        /**\n         * Read data from DOM\n         * @private\n         */\n        const _read = () => {\n            frameRect = null;\n\n            // read child views\n            childViews.forEach(child => child._read());\n\n            const shouldUpdate = !(ignoreRectUpdate && rect.width && rect.height);\n            if (shouldUpdate) {\n                updateRect(rect, element, style);\n            }\n\n            // readers\n            const api = { root: internalAPI, props, rect };\n            readers.forEach(reader => reader(api));\n        };\n\n        /**\n         * Write data to DOM\n         * @private\n         */\n        const _write = (ts, frameActions, shouldOptimize) => {\n            // if no actions, we assume that the view is resting\n            let resting = frameActions.length === 0;\n\n            // writers\n            writers.forEach(writer => {\n                const writerResting = writer({\n                    props,\n                    root: internalAPI,\n                    actions: frameActions,\n                    timestamp: ts,\n                    shouldOptimize,\n                });\n                if (writerResting === false) {\n                    resting = false;\n                }\n            });\n\n            // run mixins\n            activeMixins.forEach(mixin => {\n                // if one of the mixins is still busy after write operation, we are not resting\n                const mixinResting = mixin.write(ts);\n                if (mixinResting === false) {\n                    resting = false;\n                }\n            });\n\n            // updates child views that are currently attached to the DOM\n            childViews\n                .filter(child => !!child.element.parentNode)\n                .forEach(child => {\n                    // if a child view is not resting, we are not resting\n                    const childResting = child._write(\n                        ts,\n                        filterFrameActionsForChild(child, frameActions),\n                        shouldOptimize\n                    );\n                    if (!childResting) {\n                        resting = false;\n                    }\n                });\n\n            // append new elements to DOM and update those\n            childViews\n                //.filter(child => !child.element.parentNode)\n                .forEach((child, index) => {\n                    // skip\n                    if (child.element.parentNode) {\n                        return;\n                    }\n\n                    // append to DOM\n                    internalAPI.appendChild(child.element, index);\n\n                    // call read (need to know the size of these elements)\n                    child._read();\n\n                    // re-call write\n                    child._write(\n                        ts,\n                        filterFrameActionsForChild(child, frameActions),\n                        shouldOptimize\n                    );\n\n                    // we just added somthing to the dom, no rest\n                    resting = false;\n                });\n\n            // update resting state\n            isResting = resting;\n\n            didWriteView({\n                props,\n                root: internalAPI,\n                actions: frameActions,\n                timestamp: ts,\n            });\n\n            // let parent know if we are resting\n            return resting;\n        };\n\n        const _destroy = () => {\n            activeMixins.forEach(mixin => mixin.destroy());\n            destroyers.forEach(destroyer => {\n                destroyer({ root: internalAPI, props });\n            });\n            childViews.forEach(child => child._destroy());\n        };\n\n        // sharedAPI\n        const sharedAPIDefinition = {\n            element: {\n                get: getElement,\n            },\n            style: {\n                get: getStyle,\n            },\n            childViews: {\n                get: getChildViews,\n            },\n        };\n\n        // private API definition\n        const internalAPIDefinition = {\n            ...sharedAPIDefinition,\n            rect: {\n                get: getRect,\n            },\n\n            // access to custom children references\n            ref: {\n                get: getReference,\n            },\n\n            // dom modifiers\n            is: needle => name === needle,\n            appendChild: appendChild(element),\n            createChildView: createChildView(store),\n            linkView: view => {\n                childViews.push(view);\n                return view;\n            },\n            unlinkView: view => {\n                childViews.splice(childViews.indexOf(view), 1);\n            },\n            appendChildView: appendChildView(element, childViews),\n            removeChildView: removeChildView(element, childViews),\n            registerWriter: writer => writers.push(writer),\n            registerReader: reader => readers.push(reader),\n            registerDestroyer: destroyer => destroyers.push(destroyer),\n            invalidateLayout: () => (element.layoutCalculated = false),\n\n            // access to data store\n            dispatch: store.dispatch,\n            query: store.query,\n        };\n\n        // public view API methods\n        const externalAPIDefinition = {\n            element: {\n                get: getElement,\n            },\n            childViews: {\n                get: getChildViews,\n            },\n            rect: {\n                get: getRect,\n            },\n            resting: {\n                get: () => isResting,\n            },\n            isRectIgnored: () => ignoreRect,\n            _read,\n            _write,\n            _destroy,\n        };\n\n        // mixin API methods\n        const mixinAPIDefinition = {\n            ...sharedAPIDefinition,\n            rect: {\n                get: () => rect,\n            },\n        };\n\n        // add mixin functionality\n        Object.keys(mixins)\n            .sort((a, b) => {\n                // move styles to the back of the mixin list (so adjustments of other mixins are applied to the props correctly)\n                if (a === 'styles') {\n                    return 1;\n                } else if (b === 'styles') {\n                    return -1;\n                }\n                return 0;\n            })\n            .forEach(key => {\n                const mixinAPI = Mixins[key]({\n                    mixinConfig: mixins[key],\n                    viewProps: props,\n                    viewState: state,\n                    viewInternalAPI: internalAPIDefinition,\n                    viewExternalAPI: externalAPIDefinition,\n                    view: createObject(mixinAPIDefinition),\n                });\n\n                if (mixinAPI) {\n                    activeMixins.push(mixinAPI);\n                }\n            });\n\n        // construct private api\n        const internalAPI = createObject(internalAPIDefinition);\n\n        // create the view\n        create({\n            root: internalAPI,\n            props,\n        });\n\n        // append created child views to root node\n        const childCount = getChildCount(element); // need to know the current child count so appending happens in correct order\n        childViews.forEach((child, index) => {\n            internalAPI.appendChild(child.element, childCount + index);\n        });\n\n        // call did create\n        didCreateView(internalAPI);\n\n        // expose public api\n        return createObject(externalAPIDefinition);\n    };\n\nconst createPainter = (read, write, fps = 60) => {\n    const name = '__framePainter';\n\n    // set global painter\n    if (window[name]) {\n        window[name].readers.push(read);\n        window[name].writers.push(write);\n        return;\n    }\n\n    window[name] = {\n        readers: [read],\n        writers: [write],\n    };\n\n    const painter = window[name];\n\n    const interval = 1000 / fps;\n    let last = null;\n    let id = null;\n    let requestTick = null;\n    let cancelTick = null;\n\n    const setTimerType = () => {\n        if (document.hidden) {\n            requestTick = () => window.setTimeout(() => tick(performance.now()), interval);\n            cancelTick = () => window.clearTimeout(id);\n        } else {\n            requestTick = () => window.requestAnimationFrame(tick);\n            cancelTick = () => window.cancelAnimationFrame(id);\n        }\n    };\n\n    document.addEventListener('visibilitychange', () => {\n        if (cancelTick) cancelTick();\n        setTimerType();\n        tick(performance.now());\n    });\n\n    const tick = ts => {\n        // queue next tick\n        id = requestTick(tick);\n\n        // limit fps\n        if (!last) {\n            last = ts;\n        }\n\n        const delta = ts - last;\n\n        if (delta <= interval) {\n            // skip frame\n            return;\n        }\n\n        // align next frame\n        last = ts - (delta % interval);\n\n        // update view\n        painter.readers.forEach(read => read());\n        painter.writers.forEach(write => write(ts));\n    };\n\n    setTimerType();\n    tick(performance.now());\n\n    return {\n        pause: () => {\n            cancelTick(id);\n        },\n    };\n};\n\nconst createRoute = (routes, fn) => ({ root, props, actions = [], timestamp, shouldOptimize }) => {\n    actions\n        .filter(action => routes[action.type])\n        .forEach(action =>\n            routes[action.type]({ root, props, action: action.data, timestamp, shouldOptimize })\n        );\n    if (fn) {\n        fn({ root, props, actions, timestamp, shouldOptimize });\n    }\n};\n\nconst insertBefore = (newNode, referenceNode) =>\n    referenceNode.parentNode.insertBefore(newNode, referenceNode);\n\nconst insertAfter = (newNode, referenceNode) => {\n    return referenceNode.parentNode.insertBefore(newNode, referenceNode.nextSibling);\n};\n\nconst isArray = value => Array.isArray(value);\n\nconst isEmpty = value => value == null;\n\nconst trim = str => str.trim();\n\nconst toString = value => '' + value;\n\nconst toArray = (value, splitter = ',') => {\n    if (isEmpty(value)) {\n        return [];\n    }\n    if (isArray(value)) {\n        return value;\n    }\n    return toString(value)\n        .split(splitter)\n        .map(trim)\n        .filter(str => str.length);\n};\n\nconst isBoolean = value => typeof value === 'boolean';\n\nconst toBoolean = value => (isBoolean(value) ? value : value === 'true');\n\nconst isString = value => typeof value === 'string';\n\nconst toNumber = value =>\n    isNumber(value) ? value : isString(value) ? toString(value).replace(/[a-z]+/gi, '') : 0;\n\nconst toInt = value => parseInt(toNumber(value), 10);\n\nconst toFloat = value => parseFloat(toNumber(value));\n\nconst isInt = value => isNumber(value) && isFinite(value) && Math.floor(value) === value;\n\nconst toBytes = (value, base = 1000) => {\n    // is in bytes\n    if (isInt(value)) {\n        return value;\n    }\n\n    // is natural file size\n    let naturalFileSize = toString(value).trim();\n\n    // if is value in megabytes\n    if (/MB$/i.test(naturalFileSize)) {\n        naturalFileSize = naturalFileSize.replace(/MB$i/, '').trim();\n        return toInt(naturalFileSize) * base * base;\n    }\n\n    // if is value in kilobytes\n    if (/KB/i.test(naturalFileSize)) {\n        naturalFileSize = naturalFileSize.replace(/KB$i/, '').trim();\n        return toInt(naturalFileSize) * base;\n    }\n\n    return toInt(naturalFileSize);\n};\n\nconst isFunction = value => typeof value === 'function';\n\nconst toFunctionReference = string => {\n    let ref = self;\n    let levels = string.split('.');\n    let level = null;\n    while ((level = levels.shift())) {\n        ref = ref[level];\n        if (!ref) {\n            return null;\n        }\n    }\n    return ref;\n};\n\nconst methods = {\n    process: 'POST',\n    patch: 'PATCH',\n    revert: 'DELETE',\n    fetch: 'GET',\n    restore: 'GET',\n    load: 'GET',\n};\n\nconst createServerAPI = outline => {\n    const api = {};\n\n    api.url = isString(outline) ? outline : outline.url || '';\n    api.timeout = outline.timeout ? parseInt(outline.timeout, 10) : 0;\n    api.headers = outline.headers ? outline.headers : {};\n\n    forin(methods, key => {\n        api[key] = createAction(key, outline[key], methods[key], api.timeout, api.headers);\n    });\n\n    // remove process if no url or process on outline\n    api.process = outline.process || isString(outline) || outline.url ? api.process : null;\n\n    // special treatment for remove\n    api.remove = outline.remove || null;\n\n    // remove generic headers from api object\n    delete api.headers;\n\n    return api;\n};\n\nconst createAction = (name, outline, method, timeout, headers) => {\n    // is explicitely set to null so disable\n    if (outline === null) {\n        return null;\n    }\n\n    // if is custom function, done! Dev handles everything.\n    if (typeof outline === 'function') {\n        return outline;\n    }\n\n    // build action object\n    const action = {\n        url: method === 'GET' || method === 'PATCH' ? `?${name}=` : '',\n        method,\n        headers,\n        withCredentials: false,\n        timeout,\n        onload: null,\n        ondata: null,\n        onerror: null,\n    };\n\n    // is a single url\n    if (isString(outline)) {\n        action.url = outline;\n        return action;\n    }\n\n    // overwrite\n    Object.assign(action, outline);\n\n    // see if should reformat headers;\n    if (isString(action.headers)) {\n        const parts = action.headers.split(/:(.+)/);\n        action.headers = {\n            header: parts[0],\n            value: parts[1],\n        };\n    }\n\n    // if is bool withCredentials\n    action.withCredentials = toBoolean(action.withCredentials);\n\n    return action;\n};\n\nconst toServerAPI = value => createServerAPI(value);\n\nconst isNull = value => value === null;\n\nconst isObject = value => typeof value === 'object' && value !== null;\n\nconst isAPI = value => {\n    return (\n        isObject(value) &&\n        isString(value.url) &&\n        isObject(value.process) &&\n        isObject(value.revert) &&\n        isObject(value.restore) &&\n        isObject(value.fetch)\n    );\n};\n\nconst getType = value => {\n    if (isArray(value)) {\n        return 'array';\n    }\n\n    if (isNull(value)) {\n        return 'null';\n    }\n\n    if (isInt(value)) {\n        return 'int';\n    }\n\n    if (/^[0-9]+ ?(?:GB|MB|KB)$/gi.test(value)) {\n        return 'bytes';\n    }\n\n    if (isAPI(value)) {\n        return 'api';\n    }\n\n    return typeof value;\n};\n\nconst replaceSingleQuotes = str =>\n    str\n        .replace(/{\\s*'/g, '{\"')\n        .replace(/'\\s*}/g, '\"}')\n        .replace(/'\\s*:/g, '\":')\n        .replace(/:\\s*'/g, ':\"')\n        .replace(/,\\s*'/g, ',\"')\n        .replace(/'\\s*,/g, '\",');\n\nconst conversionTable = {\n    array: toArray,\n    boolean: toBoolean,\n    int: value => (getType(value) === 'bytes' ? toBytes(value) : toInt(value)),\n    number: toFloat,\n    float: toFloat,\n    bytes: toBytes,\n    string: value => (isFunction(value) ? value : toString(value)),\n    function: value => toFunctionReference(value),\n    serverapi: toServerAPI,\n    object: value => {\n        try {\n            return JSON.parse(replaceSingleQuotes(value));\n        } catch (e) {\n            return null;\n        }\n    },\n};\n\nconst convertTo = (value, type) => conversionTable[type](value);\n\nconst getValueByType = (newValue, defaultValue, valueType) => {\n    // can always assign default value\n    if (newValue === defaultValue) {\n        return newValue;\n    }\n\n    // get the type of the new value\n    let newValueType = getType(newValue);\n\n    // is valid type?\n    if (newValueType !== valueType) {\n        // is string input, let's attempt to convert\n        const convertedValue = convertTo(newValue, valueType);\n\n        // what is the type now\n        newValueType = getType(convertedValue);\n\n        // no valid conversions found\n        if (convertedValue === null) {\n            throw `Trying to assign value with incorrect type to \"${option}\", allowed type: \"${valueType}\"`;\n        } else {\n            newValue = convertedValue;\n        }\n    }\n\n    // assign new value\n    return newValue;\n};\n\nconst createOption = (defaultValue, valueType) => {\n    let currentValue = defaultValue;\n    return {\n        enumerable: true,\n        get: () => currentValue,\n        set: newValue => {\n            currentValue = getValueByType(newValue, defaultValue, valueType);\n        },\n    };\n};\n\nconst createOptions = options => {\n    const obj = {};\n    forin(options, prop => {\n        const optionDefinition = options[prop];\n        obj[prop] = createOption(optionDefinition[0], optionDefinition[1]);\n    });\n    return createObject(obj);\n};\n\nconst createInitialState = options => ({\n    // model\n    items: [],\n\n    // timeout used for calling update items\n    listUpdateTimeout: null,\n\n    // timeout used for stacking metadata updates\n    itemUpdateTimeout: null,\n\n    // queue of items waiting to be processed\n    processingQueue: [],\n\n    // options\n    options: createOptions(options),\n});\n\nconst fromCamels = (string, separator = '-') =>\n    string\n        .split(/(?=[A-Z])/)\n        .map(part => part.toLowerCase())\n        .join(separator);\n\nconst createOptionAPI = (store, options) => {\n    const obj = {};\n    forin(options, key => {\n        obj[key] = {\n            get: () => store.getState().options[key],\n            set: value => {\n                store.dispatch(`SET_${fromCamels(key, '_').toUpperCase()}`, {\n                    value,\n                });\n            },\n        };\n    });\n    return obj;\n};\n\nconst createOptionActions = options => (dispatch, query, state) => {\n    const obj = {};\n    forin(options, key => {\n        const name = fromCamels(key, '_').toUpperCase();\n\n        obj[`SET_${name}`] = action => {\n            try {\n                state.options[key] = action.value;\n            } catch (e) {\n                // nope, failed\n            }\n\n            // we successfully set the value of this option\n            dispatch(`DID_SET_${name}`, { value: state.options[key] });\n        };\n    });\n    return obj;\n};\n\nconst createOptionQueries = options => state => {\n    const obj = {};\n    forin(options, key => {\n        obj[`GET_${fromCamels(key, '_').toUpperCase()}`] = action => state.options[key];\n    });\n    return obj;\n};\n\nconst InteractionMethod = {\n    API: 1,\n    DROP: 2,\n    BROWSE: 3,\n    PASTE: 4,\n    NONE: 5,\n};\n\nconst getUniqueId = () =>\n    Math.random()\n        .toString(36)\n        .substring(2, 11);\n\nconst arrayRemove = (arr, index) => arr.splice(index, 1);\n\nconst run = (cb, sync) => {\n    if (sync) {\n        cb();\n    } else if (document.hidden) {\n        Promise.resolve(1).then(cb);\n    } else {\n        setTimeout(cb, 0);\n    }\n};\n\nconst on = () => {\n    const listeners = [];\n    const off = (event, cb) => {\n        arrayRemove(\n            listeners,\n            listeners.findIndex(listener => listener.event === event && (listener.cb === cb || !cb))\n        );\n    };\n    const fire = (event, args, sync) => {\n        listeners\n            .filter(listener => listener.event === event)\n            .map(listener => listener.cb)\n            .forEach(cb => run(() => cb(...args), sync));\n    };\n    return {\n        fireSync: (event, ...args) => {\n            fire(event, args, true);\n        },\n        fire: (event, ...args) => {\n            fire(event, args, false);\n        },\n        on: (event, cb) => {\n            listeners.push({ event, cb });\n        },\n        onOnce: (event, cb) => {\n            listeners.push({\n                event,\n                cb: (...args) => {\n                    off(event, cb);\n                    cb(...args);\n                },\n            });\n        },\n        off,\n    };\n};\n\nconst copyObjectPropertiesToObject = (src, target, excluded) => {\n    Object.getOwnPropertyNames(src)\n        .filter(property => !excluded.includes(property))\n        .forEach(key =>\n            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(src, key))\n        );\n};\n\nconst PRIVATE = [\n    'fire',\n    'process',\n    'revert',\n    'load',\n    'on',\n    'off',\n    'onOnce',\n    'retryLoad',\n    'extend',\n    'archive',\n    'archived',\n    'release',\n    'released',\n    'requestProcessing',\n    'freeze',\n];\n\nconst createItemAPI = item => {\n    const api = {};\n    copyObjectPropertiesToObject(item, api, PRIVATE);\n    return api;\n};\n\nconst removeReleasedItems = items => {\n    items.forEach((item, index) => {\n        if (item.released) {\n            arrayRemove(items, index);\n        }\n    });\n};\n\nconst ItemStatus = {\n    INIT: 1,\n    IDLE: 2,\n    PROCESSING_QUEUED: 9,\n    PROCESSING: 3,\n    PROCESSING_COMPLETE: 5,\n    PROCESSING_ERROR: 6,\n    PROCESSING_REVERT_ERROR: 10,\n    LOADING: 7,\n    LOAD_ERROR: 8,\n};\n\nconst FileOrigin = {\n    INPUT: 1,\n    LIMBO: 2,\n    LOCAL: 3,\n};\n\nconst getNonNumeric = str => /[^0-9]+/.exec(str);\n\nconst getDecimalSeparator = () => getNonNumeric((1.1).toLocaleString())[0];\n\nconst getThousandsSeparator = () => {\n    // Added for browsers that do not return the thousands separator (happend on native browser Android 4.4.4)\n    // We check against the normal toString output and if they're the same return a comma when decimal separator is a dot\n    const decimalSeparator = getDecimalSeparator();\n    const thousandsStringWithSeparator = (1000.0).toLocaleString();\n    const thousandsStringWithoutSeparator = (1000.0).toString();\n    if (thousandsStringWithSeparator !== thousandsStringWithoutSeparator) {\n        return getNonNumeric(thousandsStringWithSeparator)[0];\n    }\n    return decimalSeparator === '.' ? ',' : '.';\n};\n\nconst Type = {\n    BOOLEAN: 'boolean',\n    INT: 'int',\n    NUMBER: 'number',\n    STRING: 'string',\n    ARRAY: 'array',\n    OBJECT: 'object',\n    FUNCTION: 'function',\n    ACTION: 'action',\n    SERVER_API: 'serverapi',\n    REGEX: 'regex',\n};\n\n// all registered filters\nconst filters = [];\n\n// loops over matching filters and passes options to each filter, returning the mapped results\nconst applyFilterChain = (key, value, utils) =>\n    new Promise((resolve, reject) => {\n        // find matching filters for this key\n        const matchingFilters = filters.filter(f => f.key === key).map(f => f.cb);\n\n        // resolve now\n        if (matchingFilters.length === 0) {\n            resolve(value);\n            return;\n        }\n\n        // first filter to kick things of\n        const initialFilter = matchingFilters.shift();\n\n        // chain filters\n        matchingFilters\n            .reduce(\n                // loop over promises passing value to next promise\n                (current, next) => current.then(value => next(value, utils)),\n\n                // call initial filter, will return a promise\n                initialFilter(value, utils)\n\n                // all executed\n            )\n            .then(value => resolve(value))\n            .catch(error => reject(error));\n    });\n\nconst applyFilters = (key, value, utils) =>\n    filters.filter(f => f.key === key).map(f => f.cb(value, utils));\n\n// adds a new filter to the list\nconst addFilter = (key, cb) => filters.push({ key, cb });\n\nconst extendDefaultOptions = additionalOptions => Object.assign(defaultOptions, additionalOptions);\n\nconst getOptions = () => ({ ...defaultOptions });\n\nconst setOptions = opts => {\n    forin(opts, (key, value) => {\n        // key does not exist, so this option cannot be set\n        if (!defaultOptions[key]) {\n            return;\n        }\n        defaultOptions[key][0] = getValueByType(\n            value,\n            defaultOptions[key][0],\n            defaultOptions[key][1]\n        );\n    });\n};\n\n// default options on app\nconst defaultOptions = {\n    // the id to add to the root element\n    id: [null, Type.STRING],\n\n    // input field name to use\n    name: ['filepond', Type.STRING],\n\n    // disable the field\n    disabled: [false, Type.BOOLEAN],\n\n    // classname to put on wrapper\n    className: [null, Type.STRING],\n\n    // is the field required\n    required: [false, Type.BOOLEAN],\n\n    // Allow media capture when value is set\n    captureMethod: [null, Type.STRING],\n    // - \"camera\", \"microphone\" or \"camcorder\",\n    // - Does not work with multiple on apple devices\n    // - If set, acceptedFileTypes must be made to match with media wildcard \"image/*\", \"audio/*\" or \"video/*\"\n\n    // sync `acceptedFileTypes` property with `accept` attribute\n    allowSyncAcceptAttribute: [true, Type.BOOLEAN],\n\n    // Feature toggles\n    allowDrop: [true, Type.BOOLEAN], // Allow dropping of files\n    allowBrowse: [true, Type.BOOLEAN], // Allow browsing the file system\n    allowPaste: [true, Type.BOOLEAN], // Allow pasting files\n    allowMultiple: [false, Type.BOOLEAN], // Allow multiple files (disabled by default, as multiple attribute is also required on input to allow multiple)\n    allowReplace: [true, Type.BOOLEAN], // Allow dropping a file on other file to replace it (only works when multiple is set to false)\n    allowRevert: [true, Type.BOOLEAN], // Allows user to revert file upload\n    allowRemove: [true, Type.BOOLEAN], // Allow user to remove a file\n    allowProcess: [true, Type.BOOLEAN], // Allows user to process a file, when set to false, this removes the file upload button\n    allowReorder: [false, Type.BOOLEAN], // Allow reordering of files\n    allowDirectoriesOnly: [false, Type.BOOLEAN], // Allow only selecting directories with browse (no support for filtering dnd at this point)\n\n    // Try store file if `server` not set\n    storeAsFile: [false, Type.BOOLEAN],\n\n    // Revert mode\n    forceRevert: [false, Type.BOOLEAN], // Set to 'force' to require the file to be reverted before removal\n\n    // Input requirements\n    maxFiles: [null, Type.INT], // Max number of files\n    checkValidity: [false, Type.BOOLEAN], // Enables custom validity messages\n\n    // Where to put file\n    itemInsertLocationFreedom: [true, Type.BOOLEAN], // Set to false to always add items to begin or end of list\n    itemInsertLocation: ['before', Type.STRING], // Default index in list to add items that have been dropped at the top of the list\n    itemInsertInterval: [75, Type.INT],\n\n    // Drag 'n Drop related\n    dropOnPage: [false, Type.BOOLEAN], // Allow dropping of files anywhere on page (prevents browser from opening file if dropped outside of Up)\n    dropOnElement: [true, Type.BOOLEAN], // Drop needs to happen on element (set to false to also load drops outside of Up)\n    dropValidation: [false, Type.BOOLEAN], // Enable or disable validating files on drop\n    ignoredFiles: [['.ds_store', 'thumbs.db', 'desktop.ini'], Type.ARRAY],\n\n    // Upload related\n    instantUpload: [true, Type.BOOLEAN], // Should upload files immediately on drop\n    maxParallelUploads: [2, Type.INT], // Maximum files to upload in parallel\n    allowMinimumUploadDuration: [true, Type.BOOLEAN], // if true uploads take at least 750 ms, this ensures the user sees the upload progress giving trust the upload actually happened\n\n    // Chunks\n    chunkUploads: [false, Type.BOOLEAN], // Enable chunked uploads\n    chunkForce: [false, Type.BOOLEAN], // Force use of chunk uploads even for files smaller than chunk size\n    chunkSize: [5000000, Type.INT], // Size of chunks (5MB default)\n    chunkRetryDelays: [[500, 1000, 3000], Type.ARRAY], // Amount of times to retry upload of a chunk when it fails\n\n    // The server api end points to use for uploading (see docs)\n    server: [null, Type.SERVER_API],\n\n    // File size calculations, can set to 1024, this is only used for display, properties use file size base 1000\n    fileSizeBase: [1000, Type.INT],\n\n    // Labels and status messages\n    labelFileSizeBytes: ['bytes', Type.STRING],\n    labelFileSizeKilobytes: ['KB', Type.STRING],\n    labelFileSizeMegabytes: ['MB', Type.STRING],\n    labelFileSizeGigabytes: ['GB', Type.STRING],\n\n    labelDecimalSeparator: [getDecimalSeparator(), Type.STRING], // Default is locale separator\n    labelThousandsSeparator: [getThousandsSeparator(), Type.STRING], // Default is locale separator\n\n    labelIdle: [\n        'Drag & Drop your files or <span class=\"filepond--label-action\">Browse</span>',\n        Type.STRING,\n    ],\n    labelInvalidField: ['Field contains invalid files', Type.STRING],\n    labelFileWaitingForSize: ['Waiting for size', Type.STRING],\n    labelFileSizeNotAvailable: ['Size not available', Type.STRING],\n    labelFileCountSingular: ['file in list', Type.STRING],\n    labelFileCountPlural: ['files in list', Type.STRING],\n    labelFileLoading: ['Loading', Type.STRING],\n    labelFileAdded: ['Added', Type.STRING], // assistive only\n    labelFileLoadError: ['Error during load', Type.STRING],\n    labelFileRemoved: ['Removed', Type.STRING], // assistive only\n    labelFileRemoveError: ['Error during remove', Type.STRING],\n    labelFileProcessing: ['Uploading', Type.STRING],\n    labelFileProcessingComplete: ['Upload complete', Type.STRING],\n    labelFileProcessingAborted: ['Upload cancelled', Type.STRING],\n    labelFileProcessingError: ['Error during upload', Type.STRING],\n    labelFileProcessingRevertError: ['Error during revert', Type.STRING],\n\n    labelTapToCancel: ['tap to cancel', Type.STRING],\n    labelTapToRetry: ['tap to retry', Type.STRING],\n    labelTapToUndo: ['tap to undo', Type.STRING],\n\n    labelButtonRemoveItem: ['Remove', Type.STRING],\n    labelButtonAbortItemLoad: ['Abort', Type.STRING],\n    labelButtonRetryItemLoad: ['Retry', Type.STRING],\n    labelButtonAbortItemProcessing: ['Cancel', Type.STRING],\n    labelButtonUndoItemProcessing: ['Undo', Type.STRING],\n    labelButtonRetryItemProcessing: ['Retry', Type.STRING],\n    labelButtonProcessItem: ['Upload', Type.STRING],\n\n    // make sure width and height plus viewpox are even numbers so icons are nicely centered\n    iconRemove: [\n        '<svg width=\"26\" height=\"26\" viewBox=\"0 0 26 26\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M11.586 13l-2.293 2.293a1 1 0 0 0 1.414 1.414L13 14.414l2.293 2.293a1 1 0 0 0 1.414-1.414L14.414 13l2.293-2.293a1 1 0 0 0-1.414-1.414L13 11.586l-2.293-2.293a1 1 0 0 0-1.414 1.414L11.586 13z\" fill=\"currentColor\" fill-rule=\"nonzero\"/></svg>',\n        Type.STRING,\n    ],\n    iconProcess: [\n        '<svg width=\"26\" height=\"26\" viewBox=\"0 0 26 26\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M14 10.414v3.585a1 1 0 0 1-2 0v-3.585l-1.293 1.293a1 1 0 0 1-1.414-1.415l3-3a1 1 0 0 1 1.414 0l3 3a1 1 0 0 1-1.414 1.415L14 10.414zM9 18a1 1 0 0 1 0-2h8a1 1 0 0 1 0 2H9z\" fill=\"currentColor\" fill-rule=\"evenodd\"/></svg>',\n        Type.STRING,\n    ],\n    iconRetry: [\n        '<svg width=\"26\" height=\"26\" viewBox=\"0 0 26 26\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M10.81 9.185l-.038.02A4.997 4.997 0 0 0 8 13.683a5 5 0 0 0 5 5 5 5 0 0 0 5-5 1 1 0 0 1 2 0A7 7 0 1 1 9.722 7.496l-.842-.21a.999.999 0 1 1 .484-1.94l3.23.806c.535.133.86.675.73 1.21l-.804 3.233a.997.997 0 0 1-1.21.73.997.997 0 0 1-.73-1.21l.23-.928v-.002z\" fill=\"currentColor\" fill-rule=\"nonzero\"/></svg>',\n        Type.STRING,\n    ],\n    iconUndo: [\n        '<svg width=\"26\" height=\"26\" viewBox=\"0 0 26 26\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M9.185 10.81l.02-.038A4.997 4.997 0 0 1 13.683 8a5 5 0 0 1 5 5 5 5 0 0 1-5 5 1 1 0 0 0 0 2A7 7 0 1 0 7.496 9.722l-.21-.842a.999.999 0 1 0-1.94.484l.806 3.23c.133.535.675.86 1.21.73l3.233-.803a.997.997 0 0 0 .73-1.21.997.997 0 0 0-1.21-.73l-.928.23-.002-.001z\" fill=\"currentColor\" fill-rule=\"nonzero\"/></svg>',\n        Type.STRING,\n    ],\n    iconDone: [\n        '<svg width=\"26\" height=\"26\" viewBox=\"0 0 26 26\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M18.293 9.293a1 1 0 0 1 1.414 1.414l-7.002 7a1 1 0 0 1-1.414 0l-3.998-4a1 1 0 1 1 1.414-1.414L12 15.586l6.294-6.293z\" fill=\"currentColor\" fill-rule=\"nonzero\"/></svg>',\n        Type.STRING,\n    ],\n\n    // event handlers\n    oninit: [null, Type.FUNCTION],\n    onwarning: [null, Type.FUNCTION],\n    onerror: [null, Type.FUNCTION],\n    onactivatefile: [null, Type.FUNCTION],\n    oninitfile: [null, Type.FUNCTION],\n    onaddfilestart: [null, Type.FUNCTION],\n    onaddfileprogress: [null, Type.FUNCTION],\n    onaddfile: [null, Type.FUNCTION],\n    onprocessfilestart: [null, Type.FUNCTION],\n    onprocessfileprogress: [null, Type.FUNCTION],\n    onprocessfileabort: [null, Type.FUNCTION],\n    onprocessfilerevert: [null, Type.FUNCTION],\n    onprocessfile: [null, Type.FUNCTION],\n    onprocessfiles: [null, Type.FUNCTION],\n    onremovefile: [null, Type.FUNCTION],\n    onpreparefile: [null, Type.FUNCTION],\n    onupdatefiles: [null, Type.FUNCTION],\n    onreorderfiles: [null, Type.FUNCTION],\n\n    // hooks\n    beforeDropFile: [null, Type.FUNCTION],\n    beforeAddFile: [null, Type.FUNCTION],\n    beforeRemoveFile: [null, Type.FUNCTION],\n    beforePrepareFile: [null, Type.FUNCTION],\n\n    // styles\n    stylePanelLayout: [null, Type.STRING], // null 'integrated', 'compact', 'circle'\n    stylePanelAspectRatio: [null, Type.STRING], // null or '3:2' or 1\n    styleItemPanelAspectRatio: [null, Type.STRING],\n    styleButtonRemoveItemPosition: ['left', Type.STRING],\n    styleButtonProcessItemPosition: ['right', Type.STRING],\n    styleLoadIndicatorPosition: ['right', Type.STRING],\n    styleProgressIndicatorPosition: ['right', Type.STRING],\n    styleButtonRemoveItemAlign: [false, Type.BOOLEAN],\n\n    // custom initial files array\n    files: [[], Type.ARRAY],\n\n    // show support by displaying credits\n    credits: [['https://pqina.nl/', 'Powered by PQINA'], Type.ARRAY],\n};\n\nconst getItemByQuery = (items, query) => {\n    // just return first index\n    if (isEmpty(query)) {\n        return items[0] || null;\n    }\n\n    // query is index\n    if (isInt(query)) {\n        return items[query] || null;\n    }\n\n    // if query is item, get the id\n    if (typeof query === 'object') {\n        query = query.id;\n    }\n\n    // assume query is a string and return item by id\n    return items.find(item => item.id === query) || null;\n};\n\nconst getNumericAspectRatioFromString = aspectRatio => {\n    if (isEmpty(aspectRatio)) {\n        return aspectRatio;\n    }\n    if (/:/.test(aspectRatio)) {\n        const parts = aspectRatio.split(':');\n        return parts[1] / parts[0];\n    }\n    return parseFloat(aspectRatio);\n};\n\nconst getActiveItems = items => items.filter(item => !item.archived);\n\nconst Status = {\n    EMPTY: 0,\n    IDLE: 1, // waiting\n    ERROR: 2, // a file is in error state\n    BUSY: 3, // busy processing or loading\n    READY: 4, // all files uploaded\n};\n\nlet res = null;\nconst canUpdateFileInput = () => {\n    if (res === null) {\n        try {\n            const dataTransfer = new DataTransfer();\n            dataTransfer.items.add(new File(['hello world'], 'This_Works.txt'));\n            const el = document.createElement('input');\n            el.setAttribute('type', 'file');\n            el.files = dataTransfer.files;\n            res = el.files.length === 1;\n        } catch (err) {\n            res = false;\n        }\n    }\n    return res;\n};\n\nconst ITEM_ERROR = [\n    ItemStatus.LOAD_ERROR,\n    ItemStatus.PROCESSING_ERROR,\n    ItemStatus.PROCESSING_REVERT_ERROR,\n];\nconst ITEM_BUSY = [\n    ItemStatus.LOADING,\n    ItemStatus.PROCESSING,\n    ItemStatus.PROCESSING_QUEUED,\n    ItemStatus.INIT,\n];\nconst ITEM_READY = [ItemStatus.PROCESSING_COMPLETE];\n\nconst isItemInErrorState = item => ITEM_ERROR.includes(item.status);\nconst isItemInBusyState = item => ITEM_BUSY.includes(item.status);\nconst isItemInReadyState = item => ITEM_READY.includes(item.status);\n\nconst isAsync = state =>\n    isObject(state.options.server) &&\n    (isObject(state.options.server.process) || isFunction(state.options.server.process));\n\nconst queries = state => ({\n    GET_STATUS: () => {\n        const items = getActiveItems(state.items);\n\n        const { EMPTY, ERROR, BUSY, IDLE, READY } = Status;\n\n        if (items.length === 0) return EMPTY;\n\n        if (items.some(isItemInErrorState)) return ERROR;\n\n        if (items.some(isItemInBusyState)) return BUSY;\n\n        if (items.some(isItemInReadyState)) return READY;\n\n        return IDLE;\n    },\n\n    GET_ITEM: query => getItemByQuery(state.items, query),\n\n    GET_ACTIVE_ITEM: query => getItemByQuery(getActiveItems(state.items), query),\n\n    GET_ACTIVE_ITEMS: () => getActiveItems(state.items),\n\n    GET_ITEMS: () => state.items,\n\n    GET_ITEM_NAME: query => {\n        const item = getItemByQuery(state.items, query);\n        return item ? item.filename : null;\n    },\n\n    GET_ITEM_SIZE: query => {\n        const item = getItemByQuery(state.items, query);\n        return item ? item.fileSize : null;\n    },\n\n    GET_STYLES: () =>\n        Object.keys(state.options)\n            .filter(key => /^style/.test(key))\n            .map(option => ({\n                name: option,\n                value: state.options[option],\n            })),\n\n    GET_PANEL_ASPECT_RATIO: () => {\n        const isShapeCircle = /circle/.test(state.options.stylePanelLayout);\n        const aspectRatio = isShapeCircle\n            ? 1\n            : getNumericAspectRatioFromString(state.options.stylePanelAspectRatio);\n        return aspectRatio;\n    },\n\n    GET_ITEM_PANEL_ASPECT_RATIO: () => state.options.styleItemPanelAspectRatio,\n\n    GET_ITEMS_BY_STATUS: status =>\n        getActiveItems(state.items).filter(item => item.status === status),\n\n    GET_TOTAL_ITEMS: () => getActiveItems(state.items).length,\n\n    SHOULD_UPDATE_FILE_INPUT: () =>\n        state.options.storeAsFile && canUpdateFileInput() && !isAsync(state),\n\n    IS_ASYNC: () => isAsync(state),\n\n    GET_FILE_SIZE_LABELS: query => ({\n        labelBytes: query('GET_LABEL_FILE_SIZE_BYTES') || undefined,\n        labelKilobytes: query('GET_LABEL_FILE_SIZE_KILOBYTES') || undefined,\n        labelMegabytes: query('GET_LABEL_FILE_SIZE_MEGABYTES') || undefined,\n        labelGigabytes: query('GET_LABEL_FILE_SIZE_GIGABYTES') || undefined,\n    }),\n});\n\nconst hasRoomForItem = state => {\n    const count = getActiveItems(state.items).length;\n\n    // if cannot have multiple items, to add one item it should currently not contain items\n    if (!state.options.allowMultiple) {\n        return count === 0;\n    }\n\n    // if allows multiple items, we check if a max item count has been set, if not, there's no limit\n    const maxFileCount = state.options.maxFiles;\n    if (maxFileCount === null) {\n        return true;\n    }\n\n    // we check if the current count is smaller than the max count, if so, another file can still be added\n    if (count < maxFileCount) {\n        return true;\n    }\n\n    // no more room for another file\n    return false;\n};\n\nconst limit = (value, min, max) => Math.max(Math.min(max, value), min);\n\nconst arrayInsert = (arr, index, item) => arr.splice(index, 0, item);\n\nconst insertItem = (items, item, index) => {\n    if (isEmpty(item)) {\n        return null;\n    }\n\n    // if index is undefined, append\n    if (typeof index === 'undefined') {\n        items.push(item);\n        return item;\n    }\n\n    // limit the index to the size of the items array\n    index = limit(index, 0, items.length);\n\n    // add item to array\n    arrayInsert(items, index, item);\n\n    // expose\n    return item;\n};\n\nconst isBase64DataURI = str =>\n    /^\\s*data:([a-z]+\\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\\-._~:@\\/?%\\s]*)\\s*$/i.test(\n        str\n    );\n\nconst getFilenameFromURL = url =>\n    `${url}`\n        .split('/')\n        .pop()\n        .split('?')\n        .shift();\n\nconst getExtensionFromFilename = name => name.split('.').pop();\n\nconst guesstimateExtension = type => {\n    // if no extension supplied, exit here\n    if (typeof type !== 'string') {\n        return '';\n    }\n\n    // get subtype\n    const subtype = type.split('/').pop();\n\n    // is svg subtype\n    if (/svg/.test(subtype)) {\n        return 'svg';\n    }\n\n    if (/zip|compressed/.test(subtype)) {\n        return 'zip';\n    }\n\n    if (/plain/.test(subtype)) {\n        return 'txt';\n    }\n\n    if (/msword/.test(subtype)) {\n        return 'doc';\n    }\n\n    // if is valid subtype\n    if (/[a-z]+/.test(subtype)) {\n        // always use jpg extension\n        if (subtype === 'jpeg') {\n            return 'jpg';\n        }\n\n        // return subtype\n        return subtype;\n    }\n\n    return '';\n};\n\nconst leftPad = (value, padding = '') => (padding + value).slice(-padding.length);\n\nconst getDateString = (date = new Date()) =>\n    `${date.getFullYear()}-${leftPad(date.getMonth() + 1, '00')}-${leftPad(\n        date.getDate(),\n        '00'\n    )}_${leftPad(date.getHours(), '00')}-${leftPad(date.getMinutes(), '00')}-${leftPad(\n        date.getSeconds(),\n        '00'\n    )}`;\n\nconst getFileFromBlob = (blob, filename, type = null, extension = null) => {\n    const file =\n        typeof type === 'string'\n            ? blob.slice(0, blob.size, type)\n            : blob.slice(0, blob.size, blob.type);\n    file.lastModifiedDate = new Date();\n\n    // copy relative path\n    if (blob._relativePath) file._relativePath = blob._relativePath;\n\n    // if blob has name property, use as filename if no filename supplied\n    if (!isString(filename)) {\n        filename = getDateString();\n    }\n\n    // if filename supplied but no extension and filename has extension\n    if (filename && extension === null && getExtensionFromFilename(filename)) {\n        file.name = filename;\n    } else {\n        extension = extension || guesstimateExtension(file.type);\n        file.name = filename + (extension ? '.' + extension : '');\n    }\n\n    return file;\n};\n\nconst getBlobBuilder = () => {\n    return (window.BlobBuilder =\n        window.BlobBuilder ||\n        window.WebKitBlobBuilder ||\n        window.MozBlobBuilder ||\n        window.MSBlobBuilder);\n};\n\nconst createBlob = (arrayBuffer, mimeType) => {\n    const BB = getBlobBuilder();\n\n    if (BB) {\n        const bb = new BB();\n        bb.append(arrayBuffer);\n        return bb.getBlob(mimeType);\n    }\n\n    return new Blob([arrayBuffer], {\n        type: mimeType,\n    });\n};\n\nconst getBlobFromByteStringWithMimeType = (byteString, mimeType) => {\n    const ab = new ArrayBuffer(byteString.length);\n    const ia = new Uint8Array(ab);\n\n    for (let i = 0; i < byteString.length; i++) {\n        ia[i] = byteString.charCodeAt(i);\n    }\n\n    return createBlob(ab, mimeType);\n};\n\nconst getMimeTypeFromBase64DataURI = dataURI => {\n    return (/^data:(.+);/.exec(dataURI) || [])[1] || null;\n};\n\nconst getBase64DataFromBase64DataURI = dataURI => {\n    // get data part of string (remove data:image/jpeg...,)\n    const data = dataURI.split(',')[1];\n\n    // remove any whitespace as that causes InvalidCharacterError in IE\n    return data.replace(/\\s/g, '');\n};\n\nconst getByteStringFromBase64DataURI = dataURI => {\n    return atob(getBase64DataFromBase64DataURI(dataURI));\n};\n\nconst getBlobFromBase64DataURI = dataURI => {\n    const mimeType = getMimeTypeFromBase64DataURI(dataURI);\n    const byteString = getByteStringFromBase64DataURI(dataURI);\n\n    return getBlobFromByteStringWithMimeType(byteString, mimeType);\n};\n\nconst getFileFromBase64DataURI = (dataURI, filename, extension) => {\n    return getFileFromBlob(getBlobFromBase64DataURI(dataURI), filename, null, extension);\n};\n\nconst getFileNameFromHeader = header => {\n    // test if is content disposition header, if not exit\n    if (!/^content-disposition:/i.test(header)) return null;\n\n    // get filename parts\n    const matches = header\n        .split(/filename=|filename\\*=.+''/)\n        .splice(1)\n        .map(name => name.trim().replace(/^[\"']|[;\"']{0,2}$/g, ''))\n        .filter(name => name.length);\n\n    return matches.length ? decodeURI(matches[matches.length - 1]) : null;\n};\n\nconst getFileSizeFromHeader = header => {\n    if (/content-length:/i.test(header)) {\n        const size = header.match(/[0-9]+/)[0];\n        return size ? parseInt(size, 10) : null;\n    }\n    return null;\n};\n\nconst getTranfserIdFromHeader = header => {\n    if (/x-content-transfer-id:/i.test(header)) {\n        const id = (header.split(':')[1] || '').trim();\n        return id || null;\n    }\n    return null;\n};\n\nconst getFileInfoFromHeaders = headers => {\n    const info = {\n        source: null,\n        name: null,\n        size: null,\n    };\n\n    const rows = headers.split('\\n');\n    for (let header of rows) {\n        const name = getFileNameFromHeader(header);\n        if (name) {\n            info.name = name;\n            continue;\n        }\n\n        const size = getFileSizeFromHeader(header);\n        if (size) {\n            info.size = size;\n            continue;\n        }\n\n        const source = getTranfserIdFromHeader(header);\n        if (source) {\n            info.source = source;\n            continue;\n        }\n    }\n\n    return info;\n};\n\nconst createFileLoader = fetchFn => {\n    const state = {\n        source: null,\n        complete: false,\n        progress: 0,\n        size: null,\n        timestamp: null,\n        duration: 0,\n        request: null,\n    };\n\n    const getProgress = () => state.progress;\n    const abort = () => {\n        if (state.request && state.request.abort) {\n            state.request.abort();\n        }\n    };\n\n    // load source\n    const load = () => {\n        // get quick reference\n        const source = state.source;\n\n        api.fire('init', source);\n\n        // Load Files\n        if (source instanceof File) {\n            api.fire('load', source);\n        } else if (source instanceof Blob) {\n            // Load blobs, set default name to current date\n            api.fire('load', getFileFromBlob(source, source.name));\n        } else if (isBase64DataURI(source)) {\n            // Load base 64, set default name to current date\n            api.fire('load', getFileFromBase64DataURI(source));\n        } else {\n            // Deal as if is external URL, let's load it!\n            loadURL(source);\n        }\n    };\n\n    // loads a url\n    const loadURL = url => {\n        // is remote url and no fetch method supplied\n        if (!fetchFn) {\n            api.fire('error', {\n                type: 'error',\n                body: \"Can't load URL\",\n                code: 400,\n            });\n            return;\n        }\n\n        // set request start\n        state.timestamp = Date.now();\n\n        // load file\n        state.request = fetchFn(\n            url,\n            response => {\n                // update duration\n                state.duration = Date.now() - state.timestamp;\n\n                // done!\n                state.complete = true;\n\n                // turn blob response into a file\n                if (response instanceof Blob) {\n                    response = getFileFromBlob(response, response.name || getFilenameFromURL(url));\n                }\n\n                api.fire(\n                    'load',\n                    // if has received blob, we go with blob, if no response, we return null\n                    response instanceof Blob ? response : response ? response.body : null\n                );\n            },\n            error => {\n                api.fire(\n                    'error',\n                    typeof error === 'string'\n                        ? {\n                              type: 'error',\n                              code: 0,\n                              body: error,\n                          }\n                        : error\n                );\n            },\n            (computable, current, total) => {\n                // collected some meta data already\n                if (total) {\n                    state.size = total;\n                }\n\n                // update duration\n                state.duration = Date.now() - state.timestamp;\n\n                // if we can't compute progress, we're not going to fire progress events\n                if (!computable) {\n                    state.progress = null;\n                    return;\n                }\n\n                // update progress percentage\n                state.progress = current / total;\n\n                // expose\n                api.fire('progress', state.progress);\n            },\n            () => {\n                api.fire('abort');\n            },\n            response => {\n                const fileinfo = getFileInfoFromHeaders(\n                    typeof response === 'string' ? response : response.headers\n                );\n                api.fire('meta', {\n                    size: state.size || fileinfo.size,\n                    filename: fileinfo.name,\n                    source: fileinfo.source,\n                });\n            }\n        );\n    };\n\n    const api = {\n        ...on(),\n        setSource: source => (state.source = source),\n        getProgress, // file load progress\n        abort, // abort file load\n        load, // start load\n    };\n\n    return api;\n};\n\nconst isGet = method => /GET|HEAD/.test(method);\n\nconst sendRequest = (data, url, options) => {\n    const api = {\n        onheaders: () => {},\n        onprogress: () => {},\n        onload: () => {},\n        ontimeout: () => {},\n        onerror: () => {},\n        onabort: () => {},\n        abort: () => {\n            aborted = true;\n            xhr.abort();\n        },\n    };\n\n    // timeout identifier, only used when timeout is defined\n    let aborted = false;\n    let headersReceived = false;\n\n    // set default options\n    options = {\n        method: 'POST',\n        headers: {},\n        withCredentials: false,\n        ...options,\n    };\n\n    // encode url\n    url = encodeURI(url);\n\n    // if method is GET, add any received data to url\n\n    if (isGet(options.method) && data) {\n        url = `${url}${encodeURIComponent(typeof data === 'string' ? data : JSON.stringify(data))}`;\n    }\n\n    // create request\n    const xhr = new XMLHttpRequest();\n\n    // progress of load\n    const process = isGet(options.method) ? xhr : xhr.upload;\n    process.onprogress = e => {\n        // no progress event when aborted ( onprogress is called once after abort() )\n        if (aborted) {\n            return;\n        }\n\n        api.onprogress(e.lengthComputable, e.loaded, e.total);\n    };\n\n    // tries to get header info to the app as fast as possible\n    xhr.onreadystatechange = () => {\n        // not interesting in these states ('unsent' and 'openend' as they don't give us any additional info)\n        if (xhr.readyState < 2) {\n            return;\n        }\n\n        // no server response\n        if (xhr.readyState === 4 && xhr.status === 0) {\n            return;\n        }\n\n        if (headersReceived) {\n            return;\n        }\n\n        headersReceived = true;\n\n        // we've probably received some useful data in response headers\n        api.onheaders(xhr);\n    };\n\n    // load successful\n    xhr.onload = () => {\n        // is classified as valid response\n        if (xhr.status >= 200 && xhr.status < 300) {\n            api.onload(xhr);\n        } else {\n            api.onerror(xhr);\n        }\n    };\n\n    // error during load\n    xhr.onerror = () => api.onerror(xhr);\n\n    // request aborted\n    xhr.onabort = () => {\n        aborted = true;\n        api.onabort();\n    };\n\n    // request timeout\n    xhr.ontimeout = () => api.ontimeout(xhr);\n\n    // open up open up!\n    xhr.open(options.method, url, true);\n\n    // set timeout if defined (do it after open so IE11 plays ball)\n    if (isInt(options.timeout)) {\n        xhr.timeout = options.timeout;\n    }\n\n    // add headers\n    Object.keys(options.headers).forEach(key => {\n        const value = unescape(encodeURIComponent(options.headers[key]));\n        xhr.setRequestHeader(key, value);\n    });\n\n    // set type of response\n    if (options.responseType) {\n        xhr.responseType = options.responseType;\n    }\n\n    // set credentials\n    if (options.withCredentials) {\n        xhr.withCredentials = true;\n    }\n\n    // let's send our data\n    xhr.send(data);\n\n    return api;\n};\n\nconst createResponse = (type, code, body, headers) => ({\n    type,\n    code,\n    body,\n    headers,\n});\n\nconst createTimeoutResponse = cb => xhr => {\n    cb(createResponse('error', 0, 'Timeout', xhr.getAllResponseHeaders()));\n};\n\nconst hasQS = str => /\\?/.test(str);\nconst buildURL = (...parts) => {\n    let url = '';\n    parts.forEach(part => {\n        url += hasQS(url) && hasQS(part) ? part.replace(/\\?/, '&') : part;\n    });\n    return url;\n};\n\nconst createFetchFunction = (apiUrl = '', action) => {\n    // custom handler (should also handle file, load, error, progress and abort)\n    if (typeof action === 'function') {\n        return action;\n    }\n\n    // no action supplied\n    if (!action || !isString(action.url)) {\n        return null;\n    }\n\n    // set onload hanlder\n    const onload = action.onload || (res => res);\n    const onerror = action.onerror || (res => null);\n\n    // internal handler\n    return (url, load, error, progress, abort, headers) => {\n        // do local or remote request based on if the url is external\n        const request = sendRequest(url, buildURL(apiUrl, action.url), {\n            ...action,\n            responseType: 'blob',\n        });\n\n        request.onload = xhr => {\n            // get headers\n            const headers = xhr.getAllResponseHeaders();\n\n            // get filename\n            const filename = getFileInfoFromHeaders(headers).name || getFilenameFromURL(url);\n\n            // create response\n            load(\n                createResponse(\n                    'load',\n                    xhr.status,\n                    action.method === 'HEAD'\n                        ? null\n                        : getFileFromBlob(onload(xhr.response), filename),\n                    headers\n                )\n            );\n        };\n\n        request.onerror = xhr => {\n            error(\n                createResponse(\n                    'error',\n                    xhr.status,\n                    onerror(xhr.response) || xhr.statusText,\n                    xhr.getAllResponseHeaders()\n                )\n            );\n        };\n\n        request.onheaders = xhr => {\n            headers(createResponse('headers', xhr.status, null, xhr.getAllResponseHeaders()));\n        };\n\n        request.ontimeout = createTimeoutResponse(error);\n        request.onprogress = progress;\n        request.onabort = abort;\n\n        // should return request\n        return request;\n    };\n};\n\nconst ChunkStatus = {\n    QUEUED: 0,\n    COMPLETE: 1,\n    PROCESSING: 2,\n    ERROR: 3,\n    WAITING: 4,\n};\n\n/*\nfunction signature:\n  (file, metadata, load, error, progress, abort, transfer, options) => {\n    return {\n    abort:() => {}\n  }\n}\n*/\n\n// apiUrl, action, name, file, metadata, load, error, progress, abort, transfer, options\nconst processFileChunked = (\n    apiUrl,\n    action,\n    name,\n    file,\n    metadata,\n    load,\n    error,\n    progress,\n    abort,\n    transfer,\n    options\n) => {\n    // all chunks\n    const chunks = [];\n    const { chunkTransferId, chunkServer, chunkSize, chunkRetryDelays } = options;\n\n    // default state\n    const state = {\n        serverId: chunkTransferId,\n        aborted: false,\n    };\n\n    // set onload handlers\n    const ondata = action.ondata || (fd => fd);\n    const onload =\n        action.onload ||\n        ((xhr, method) =>\n            method === 'HEAD' ? xhr.getResponseHeader('Upload-Offset') : xhr.response);\n    const onerror = action.onerror || (res => null);\n\n    // create server hook\n    const requestTransferId = cb => {\n        const formData = new FormData();\n\n        // add metadata under same name\n        if (isObject(metadata)) formData.append(name, JSON.stringify(metadata));\n\n        const headers =\n            typeof action.headers === 'function'\n                ? action.headers(file, metadata)\n                : {\n                      ...action.headers,\n                      'Upload-Length': file.size,\n                  };\n\n        const requestParams = {\n            ...action,\n            headers,\n        };\n\n        // send request object\n        const request = sendRequest(ondata(formData), buildURL(apiUrl, action.url), requestParams);\n\n        request.onload = xhr => cb(onload(xhr, requestParams.method));\n\n        request.onerror = xhr =>\n            error(\n                createResponse(\n                    'error',\n                    xhr.status,\n                    onerror(xhr.response) || xhr.statusText,\n                    xhr.getAllResponseHeaders()\n                )\n            );\n\n        request.ontimeout = createTimeoutResponse(error);\n    };\n\n    const requestTransferOffset = cb => {\n        const requestUrl = buildURL(apiUrl, chunkServer.url, state.serverId);\n\n        const headers =\n            typeof action.headers === 'function'\n                ? action.headers(state.serverId)\n                : {\n                      ...action.headers,\n                  };\n\n        const requestParams = {\n            headers,\n            method: 'HEAD',\n        };\n\n        const request = sendRequest(null, requestUrl, requestParams);\n\n        request.onload = xhr => cb(onload(xhr, requestParams.method));\n\n        request.onerror = xhr =>\n            error(\n                createResponse(\n                    'error',\n                    xhr.status,\n                    onerror(xhr.response) || xhr.statusText,\n                    xhr.getAllResponseHeaders()\n                )\n            );\n\n        request.ontimeout = createTimeoutResponse(error);\n    };\n\n    // create chunks\n    const lastChunkIndex = Math.floor(file.size / chunkSize);\n    for (let i = 0; i <= lastChunkIndex; i++) {\n        const offset = i * chunkSize;\n        const data = file.slice(offset, offset + chunkSize, 'application/offset+octet-stream');\n        chunks[i] = {\n            index: i,\n            size: data.size,\n            offset,\n            data,\n            file,\n            progress: 0,\n            retries: [...chunkRetryDelays],\n            status: ChunkStatus.QUEUED,\n            error: null,\n            request: null,\n            timeout: null,\n        };\n    }\n\n    const completeProcessingChunks = () => load(state.serverId);\n\n    const canProcessChunk = chunk =>\n        chunk.status === ChunkStatus.QUEUED || chunk.status === ChunkStatus.ERROR;\n\n    const processChunk = chunk => {\n        // processing is paused, wait here\n        if (state.aborted) return;\n\n        // get next chunk to process\n        chunk = chunk || chunks.find(canProcessChunk);\n\n        // no more chunks to process\n        if (!chunk) {\n            // all done?\n            if (chunks.every(chunk => chunk.status === ChunkStatus.COMPLETE)) {\n                completeProcessingChunks();\n            }\n\n            // no chunk to handle\n            return;\n        }\n\n        // now processing this chunk\n        chunk.status = ChunkStatus.PROCESSING;\n        chunk.progress = null;\n\n        // allow parsing of formdata\n        const ondata = chunkServer.ondata || (fd => fd);\n        const onerror = chunkServer.onerror || (res => null);\n        const onload = chunkServer.onload || (() => {});\n\n        // send request object\n        const requestUrl = buildURL(apiUrl, chunkServer.url, state.serverId);\n\n        const headers =\n            typeof chunkServer.headers === 'function'\n                ? chunkServer.headers(chunk)\n                : {\n                      ...chunkServer.headers,\n                      'Content-Type': 'application/offset+octet-stream',\n                      'Upload-Offset': chunk.offset,\n                      'Upload-Length': file.size,\n                      'Upload-Name': file.name,\n                  };\n\n        const request = (chunk.request = sendRequest(ondata(chunk.data), requestUrl, {\n            ...chunkServer,\n            headers,\n        }));\n\n        request.onload = xhr => {\n            // allow hooking into request result\n            onload(xhr, chunk.index, chunks.length);\n\n            // done!\n            chunk.status = ChunkStatus.COMPLETE;\n\n            // remove request reference\n            chunk.request = null;\n\n            // start processing more chunks\n            processChunks();\n        };\n\n        request.onprogress = (lengthComputable, loaded, total) => {\n            chunk.progress = lengthComputable ? loaded : null;\n            updateTotalProgress();\n        };\n\n        request.onerror = xhr => {\n            chunk.status = ChunkStatus.ERROR;\n            chunk.request = null;\n            chunk.error = onerror(xhr.response) || xhr.statusText;\n            if (!retryProcessChunk(chunk)) {\n                error(\n                    createResponse(\n                        'error',\n                        xhr.status,\n                        onerror(xhr.response) || xhr.statusText,\n                        xhr.getAllResponseHeaders()\n                    )\n                );\n            }\n        };\n\n        request.ontimeout = xhr => {\n            chunk.status = ChunkStatus.ERROR;\n            chunk.request = null;\n            if (!retryProcessChunk(chunk)) {\n                createTimeoutResponse(error)(xhr);\n            }\n        };\n\n        request.onabort = () => {\n            chunk.status = ChunkStatus.QUEUED;\n            chunk.request = null;\n            abort();\n        };\n    };\n\n    const retryProcessChunk = chunk => {\n        // no more retries left\n        if (chunk.retries.length === 0) return false;\n\n        // new retry\n        chunk.status = ChunkStatus.WAITING;\n        clearTimeout(chunk.timeout);\n        chunk.timeout = setTimeout(() => {\n            processChunk(chunk);\n        }, chunk.retries.shift());\n\n        // we're going to retry\n        return true;\n    };\n\n    const updateTotalProgress = () => {\n        // calculate total progress fraction\n        const totalBytesTransfered = chunks.reduce((p, chunk) => {\n            if (p === null || chunk.progress === null) return null;\n            return p + chunk.progress;\n        }, 0);\n\n        // can't compute progress\n        if (totalBytesTransfered === null) return progress(false, 0, 0);\n\n        // calculate progress values\n        const totalSize = chunks.reduce((total, chunk) => total + chunk.size, 0);\n\n        // can update progress indicator\n        progress(true, totalBytesTransfered, totalSize);\n    };\n\n    // process new chunks\n    const processChunks = () => {\n        const totalProcessing = chunks.filter(chunk => chunk.status === ChunkStatus.PROCESSING)\n            .length;\n        if (totalProcessing >= 1) return;\n        processChunk();\n    };\n\n    const abortChunks = () => {\n        chunks.forEach(chunk => {\n            clearTimeout(chunk.timeout);\n            if (chunk.request) {\n                chunk.request.abort();\n            }\n        });\n    };\n\n    // let's go!\n    if (!state.serverId) {\n        requestTransferId(serverId => {\n            // stop here if aborted, might have happened in between request and callback\n            if (state.aborted) return;\n\n            // pass back to item so we can use it if something goes wrong\n            transfer(serverId);\n\n            // store internally\n            state.serverId = serverId;\n            processChunks();\n        });\n    } else {\n        requestTransferOffset(offset => {\n            // stop here if aborted, might have happened in between request and callback\n            if (state.aborted) return;\n\n            // mark chunks with lower offset as complete\n            chunks\n                .filter(chunk => chunk.offset < offset)\n                .forEach(chunk => {\n                    chunk.status = ChunkStatus.COMPLETE;\n                    chunk.progress = chunk.size;\n                });\n\n            // continue processing\n            processChunks();\n        });\n    }\n\n    return {\n        abort: () => {\n            state.aborted = true;\n            abortChunks();\n        },\n    };\n};\n\n/*\nfunction signature:\n  (file, metadata, load, error, progress, abort) => {\n    return {\n    abort:() => {}\n  }\n}\n*/\nconst createFileProcessorFunction = (apiUrl, action, name, options) => (\n    file,\n    metadata,\n    load,\n    error,\n    progress,\n    abort,\n    transfer\n) => {\n    // no file received\n    if (!file) return;\n\n    // if was passed a file, and we can chunk it, exit here\n    const canChunkUpload = options.chunkUploads;\n    const shouldChunkUpload = canChunkUpload && file.size > options.chunkSize;\n    const willChunkUpload = canChunkUpload && (shouldChunkUpload || options.chunkForce);\n    if (file instanceof Blob && willChunkUpload)\n        return processFileChunked(\n            apiUrl,\n            action,\n            name,\n            file,\n            metadata,\n            load,\n            error,\n            progress,\n            abort,\n            transfer,\n            options\n        );\n\n    // set handlers\n    const ondata = action.ondata || (fd => fd);\n    const onload = action.onload || (res => res);\n    const onerror = action.onerror || (res => null);\n\n    const headers =\n        typeof action.headers === 'function'\n            ? action.headers(file, metadata) || {}\n            : {\n                  ...action.headers,\n              };\n\n    const requestParams = {\n        ...action,\n        headers,\n    };\n\n    // create formdata object\n    var formData = new FormData();\n\n    // add metadata under same name\n    if (isObject(metadata)) {\n        formData.append(name, JSON.stringify(metadata));\n    }\n\n    // Turn into an array of objects so no matter what the input, we can handle it the same way\n    (file instanceof Blob ? [{ name: null, file }] : file).forEach(item => {\n        formData.append(\n            name,\n            item.file,\n            item.name === null ? item.file.name : `${item.name}${item.file.name}`\n        );\n    });\n\n    // send request object\n    const request = sendRequest(ondata(formData), buildURL(apiUrl, action.url), requestParams);\n    request.onload = xhr => {\n        load(createResponse('load', xhr.status, onload(xhr.response), xhr.getAllResponseHeaders()));\n    };\n\n    request.onerror = xhr => {\n        error(\n            createResponse(\n                'error',\n                xhr.status,\n                onerror(xhr.response) || xhr.statusText,\n                xhr.getAllResponseHeaders()\n            )\n        );\n    };\n\n    request.ontimeout = createTimeoutResponse(error);\n    request.onprogress = progress;\n    request.onabort = abort;\n\n    // should return request\n    return request;\n};\n\nconst createProcessorFunction = (apiUrl = '', action, name, options) => {\n    // custom handler (should also handle file, load, error, progress and abort)\n    if (typeof action === 'function') return (...params) => action(name, ...params, options);\n\n    // no action supplied\n    if (!action || !isString(action.url)) return null;\n\n    // internal handler\n    return createFileProcessorFunction(apiUrl, action, name, options);\n};\n\n/*\n function signature:\n (uniqueFileId, load, error) => { }\n */\nconst createRevertFunction = (apiUrl = '', action) => {\n    // is custom implementation\n    if (typeof action === 'function') {\n        return action;\n    }\n\n    // no action supplied, return stub function, interface will work, but file won't be removed\n    if (!action || !isString(action.url)) {\n        return (uniqueFileId, load) => load();\n    }\n\n    // set onload hanlder\n    const onload = action.onload || (res => res);\n    const onerror = action.onerror || (res => null);\n\n    // internal implementation\n    return (uniqueFileId, load, error) => {\n        const request = sendRequest(\n            uniqueFileId,\n            apiUrl + action.url,\n            action // contains method, headers and withCredentials properties\n        );\n        request.onload = xhr => {\n            load(\n                createResponse(\n                    'load',\n                    xhr.status,\n                    onload(xhr.response),\n                    xhr.getAllResponseHeaders()\n                )\n            );\n        };\n\n        request.onerror = xhr => {\n            error(\n                createResponse(\n                    'error',\n                    xhr.status,\n                    onerror(xhr.response) || xhr.statusText,\n                    xhr.getAllResponseHeaders()\n                )\n            );\n        };\n\n        request.ontimeout = createTimeoutResponse(error);\n\n        return request;\n    };\n};\n\nconst getRandomNumber = (min = 0, max = 1) => min + Math.random() * (max - min);\n\nconst createPerceivedPerformanceUpdater = (\n    cb,\n    duration = 1000,\n    offset = 0,\n    tickMin = 25,\n    tickMax = 250\n) => {\n    let timeout = null;\n    const start = Date.now();\n\n    const tick = () => {\n        let runtime = Date.now() - start;\n        let delay = getRandomNumber(tickMin, tickMax);\n\n        if (runtime + delay > duration) {\n            delay = runtime + delay - duration;\n        }\n\n        let progress = runtime / duration;\n        if (progress >= 1 || document.hidden) {\n            cb(1);\n            return;\n        }\n\n        cb(progress);\n\n        timeout = setTimeout(tick, delay);\n    };\n\n    if (duration > 0) tick();\n\n    return {\n        clear: () => {\n            clearTimeout(timeout);\n        },\n    };\n};\n\nconst createFileProcessor = (processFn, options) => {\n    const state = {\n        complete: false,\n        perceivedProgress: 0,\n        perceivedPerformanceUpdater: null,\n        progress: null,\n        timestamp: null,\n        perceivedDuration: 0,\n        duration: 0,\n        request: null,\n        response: null,\n    };\n\n    const { allowMinimumUploadDuration } = options;\n\n    const process = (file, metadata) => {\n        const progressFn = () => {\n            // we've not yet started the real download, stop here\n            // the request might not go through, for instance, there might be some server trouble\n            // if state.progress is null, the server does not allow computing progress and we show the spinner instead\n            if (state.duration === 0 || state.progress === null) return;\n\n            // as we're now processing, fire the progress event\n            api.fire('progress', api.getProgress());\n        };\n\n        const completeFn = () => {\n            state.complete = true;\n            api.fire('load-perceived', state.response.body);\n        };\n\n        // let's start processing\n        api.fire('start');\n\n        // set request start\n        state.timestamp = Date.now();\n\n        // create perceived performance progress indicator\n        state.perceivedPerformanceUpdater = createPerceivedPerformanceUpdater(\n            progress => {\n                state.perceivedProgress = progress;\n                state.perceivedDuration = Date.now() - state.timestamp;\n\n                progressFn();\n\n                // if fake progress is done, and a response has been received,\n                // and we've not yet called the complete method\n                if (state.response && state.perceivedProgress === 1 && !state.complete) {\n                    // we done!\n                    completeFn();\n                }\n            },\n            // random delay as in a list of files you start noticing\n            // files uploading at the exact same speed\n            allowMinimumUploadDuration ? getRandomNumber(750, 1500) : 0\n        );\n\n        // remember request so we can abort it later\n        state.request = processFn(\n            // the file to process\n            file,\n\n            // the metadata to send along\n            metadata,\n\n            // callbacks (load, error, progress, abort, transfer)\n            // load expects the body to be a server id if\n            // you want to make use of revert\n            response => {\n                // we put the response in state so we can access\n                // it outside of this method\n                state.response = isObject(response)\n                    ? response\n                    : {\n                          type: 'load',\n                          code: 200,\n                          body: `${response}`,\n                          headers: {},\n                      };\n\n                // update duration\n                state.duration = Date.now() - state.timestamp;\n\n                // force progress to 1 as we're now done\n                state.progress = 1;\n\n                // actual load is done let's share results\n                api.fire('load', state.response.body);\n\n                // we are really done\n                // if perceived progress is 1 ( wait for perceived progress to complete )\n                // or if server does not support progress ( null )\n                if (\n                    !allowMinimumUploadDuration ||\n                    (allowMinimumUploadDuration && state.perceivedProgress === 1)\n                ) {\n                    completeFn();\n                }\n            },\n\n            // error is expected to be an object with type, code, body\n            error => {\n                // cancel updater\n                state.perceivedPerformanceUpdater.clear();\n\n                // update others about this error\n                api.fire(\n                    'error',\n                    isObject(error)\n                        ? error\n                        : {\n                              type: 'error',\n                              code: 0,\n                              body: `${error}`,\n                          }\n                );\n            },\n\n            // actual processing progress\n            (computable, current, total) => {\n                // update actual duration\n                state.duration = Date.now() - state.timestamp;\n\n                // update actual progress\n                state.progress = computable ? current / total : null;\n\n                progressFn();\n            },\n\n            // abort does not expect a value\n            () => {\n                // stop updater\n                state.perceivedPerformanceUpdater.clear();\n\n                // fire the abort event so we can switch visuals\n                api.fire('abort', state.response ? state.response.body : null);\n            },\n\n            // register the id for this transfer\n            transferId => {\n                api.fire('transfer', transferId);\n            }\n        );\n    };\n\n    const abort = () => {\n        // no request running, can't abort\n        if (!state.request) return;\n\n        // stop updater\n        state.perceivedPerformanceUpdater.clear();\n\n        // abort actual request\n        if (state.request.abort) state.request.abort();\n\n        // if has response object, we've completed the request\n        state.complete = true;\n    };\n\n    const reset = () => {\n        abort();\n        state.complete = false;\n        state.perceivedProgress = 0;\n        state.progress = 0;\n        state.timestamp = null;\n        state.perceivedDuration = 0;\n        state.duration = 0;\n        state.request = null;\n        state.response = null;\n    };\n\n    const getProgress = allowMinimumUploadDuration\n        ? () => (state.progress ? Math.min(state.progress, state.perceivedProgress) : null)\n        : () => state.progress || null;\n\n    const getDuration = allowMinimumUploadDuration\n        ? () => Math.min(state.duration, state.perceivedDuration)\n        : () => state.duration;\n\n    const api = {\n        ...on(),\n        process, // start processing file\n        abort, // abort active process request\n        getProgress,\n        getDuration,\n        reset,\n    };\n\n    return api;\n};\n\nconst getFilenameWithoutExtension = name => name.substring(0, name.lastIndexOf('.')) || name;\n\nconst createFileStub = source => {\n    let data = [source.name, source.size, source.type];\n\n    // is blob or base64, then we need to set the name\n    if (source instanceof Blob || isBase64DataURI(source)) {\n        data[0] = source.name || getDateString();\n    } else if (isBase64DataURI(source)) {\n        // if is base64 data uri we need to determine the average size and type\n        data[1] = source.length;\n        data[2] = getMimeTypeFromBase64DataURI(source);\n    } else if (isString(source)) {\n        // url\n        data[0] = getFilenameFromURL(source);\n        data[1] = 0;\n        data[2] = 'application/octet-stream';\n    }\n\n    return {\n        name: data[0],\n        size: data[1],\n        type: data[2],\n    };\n};\n\nconst isFile = value => !!(value instanceof File || (value instanceof Blob && value.name));\n\nconst deepCloneObject = src => {\n    if (!isObject(src)) return src;\n    const target = isArray(src) ? [] : {};\n    for (const key in src) {\n        if (!src.hasOwnProperty(key)) continue;\n        const v = src[key];\n        target[key] = v && isObject(v) ? deepCloneObject(v) : v;\n    }\n    return target;\n};\n\nconst createItem = (origin = null, serverFileReference = null, file = null) => {\n    // unique id for this item, is used to identify the item across views\n    const id = getUniqueId();\n\n    /**\n     * Internal item state\n     */\n    const state = {\n        // is archived\n        archived: false,\n\n        // if is frozen, no longer fires events\n        frozen: false,\n\n        // removed from view\n        released: false,\n\n        // original source\n        source: null,\n\n        // file model reference\n        file,\n\n        // id of file on server\n        serverFileReference,\n\n        // id of file transfer on server\n        transferId: null,\n\n        // is aborted\n        processingAborted: false,\n\n        // current item status\n        status: serverFileReference ? ItemStatus.PROCESSING_COMPLETE : ItemStatus.INIT,\n\n        // active processes\n        activeLoader: null,\n        activeProcessor: null,\n    };\n\n    // callback used when abort processing is called to link back to the resolve method\n    let abortProcessingRequestComplete = null;\n\n    /**\n     * Externally added item metadata\n     */\n    const metadata = {};\n\n    // item data\n    const setStatus = status => (state.status = status);\n\n    // fire event unless the item has been archived\n    const fire = (event, ...params) => {\n        if (state.released || state.frozen) return;\n        api.fire(event, ...params);\n    };\n\n    // file data\n    const getFileExtension = () => getExtensionFromFilename(state.file.name);\n    const getFileType = () => state.file.type;\n    const getFileSize = () => state.file.size;\n    const getFile = () => state.file;\n\n    //\n    // logic to load a file\n    //\n    const load = (source, loader, onload) => {\n        // remember the original item source\n        state.source = source;\n\n        // source is known\n        api.fireSync('init');\n\n        // file stub is already there\n        if (state.file) {\n            api.fireSync('load-skip');\n            return;\n        }\n\n        // set a stub file object while loading the actual data\n        state.file = createFileStub(source);\n\n        // starts loading\n        loader.on('init', () => {\n            fire('load-init');\n        });\n\n        // we'eve received a size indication, let's update the stub\n        loader.on('meta', meta => {\n            // set size of file stub\n            state.file.size = meta.size;\n\n            // set name of file stub\n            state.file.filename = meta.filename;\n\n            // if has received source, we done\n            if (meta.source) {\n                origin = FileOrigin.LIMBO;\n                state.serverFileReference = meta.source;\n                state.status = ItemStatus.PROCESSING_COMPLETE;\n            }\n\n            // size has been updated\n            fire('load-meta');\n        });\n\n        // the file is now loading we need to update the progress indicators\n        loader.on('progress', progress => {\n            setStatus(ItemStatus.LOADING);\n\n            fire('load-progress', progress);\n        });\n\n        // an error was thrown while loading the file, we need to switch to error state\n        loader.on('error', error => {\n            setStatus(ItemStatus.LOAD_ERROR);\n\n            fire('load-request-error', error);\n        });\n\n        // user or another process aborted the file load (cannot retry)\n        loader.on('abort', () => {\n            setStatus(ItemStatus.INIT);\n            fire('load-abort');\n        });\n\n        // done loading\n        loader.on('load', file => {\n            // as we've now loaded the file the loader is no longer required\n            state.activeLoader = null;\n\n            // called when file has loaded succesfully\n            const success = result => {\n                // set (possibly) transformed file\n                state.file = isFile(result) ? result : state.file;\n\n                // file received\n                if (origin === FileOrigin.LIMBO && state.serverFileReference) {\n                    setStatus(ItemStatus.PROCESSING_COMPLETE);\n                } else {\n                    setStatus(ItemStatus.IDLE);\n                }\n\n                fire('load');\n            };\n\n            const error = result => {\n                // set original file\n                state.file = file;\n                fire('load-meta');\n\n                setStatus(ItemStatus.LOAD_ERROR);\n                fire('load-file-error', result);\n            };\n\n            // if we already have a server file reference, we don't need to call the onload method\n            if (state.serverFileReference) {\n                success(file);\n                return;\n            }\n\n            // no server id, let's give this file the full treatment\n            onload(file, success, error);\n        });\n\n        // set loader source data\n        loader.setSource(source);\n\n        // set as active loader\n        state.activeLoader = loader;\n\n        // load the source data\n        loader.load();\n    };\n\n    const retryLoad = () => {\n        if (!state.activeLoader) {\n            return;\n        }\n        state.activeLoader.load();\n    };\n\n    const abortLoad = () => {\n        if (state.activeLoader) {\n            state.activeLoader.abort();\n            return;\n        }\n        setStatus(ItemStatus.INIT);\n        fire('load-abort');\n    };\n\n    //\n    // logic to process a file\n    //\n    const process = (processor, onprocess) => {\n        // processing was aborted\n        if (state.processingAborted) {\n            state.processingAborted = false;\n            return;\n        }\n\n        // now processing\n        setStatus(ItemStatus.PROCESSING);\n\n        // reset abort callback\n        abortProcessingRequestComplete = null;\n\n        // if no file loaded we'll wait for the load event\n        if (!(state.file instanceof Blob)) {\n            api.on('load', () => {\n                process(processor, onprocess);\n            });\n            return;\n        }\n\n        // setup processor\n        processor.on('load', serverFileReference => {\n            // need this id to be able to revert the upload\n            state.transferId = null;\n            state.serverFileReference = serverFileReference;\n        });\n\n        // register transfer id\n        processor.on('transfer', transferId => {\n            // need this id to be able to revert the upload\n            state.transferId = transferId;\n        });\n\n        processor.on('load-perceived', serverFileReference => {\n            // no longer required\n            state.activeProcessor = null;\n\n            // need this id to be able to rever the upload\n            state.transferId = null;\n            state.serverFileReference = serverFileReference;\n\n            setStatus(ItemStatus.PROCESSING_COMPLETE);\n            fire('process-complete', serverFileReference);\n        });\n\n        processor.on('start', () => {\n            fire('process-start');\n        });\n\n        processor.on('error', error => {\n            state.activeProcessor = null;\n            setStatus(ItemStatus.PROCESSING_ERROR);\n            fire('process-error', error);\n        });\n\n        processor.on('abort', serverFileReference => {\n            state.activeProcessor = null;\n\n            // if file was uploaded but processing was cancelled during perceived processor time store file reference\n            state.serverFileReference = serverFileReference;\n\n            setStatus(ItemStatus.IDLE);\n            fire('process-abort');\n\n            // has timeout so doesn't interfere with remove action\n            if (abortProcessingRequestComplete) {\n                abortProcessingRequestComplete();\n            }\n        });\n\n        processor.on('progress', progress => {\n            fire('process-progress', progress);\n        });\n\n        // when successfully transformed\n        const success = file => {\n            // if was archived in the mean time, don't process\n            if (state.archived) return;\n\n            // process file!\n            processor.process(file, { ...metadata });\n        };\n\n        // something went wrong during transform phase\n        const error = console.error;\n\n        // start processing the file\n        onprocess(state.file, success, error);\n\n        // set as active processor\n        state.activeProcessor = processor;\n    };\n\n    const requestProcessing = () => {\n        state.processingAborted = false;\n        setStatus(ItemStatus.PROCESSING_QUEUED);\n    };\n\n    const abortProcessing = () =>\n        new Promise(resolve => {\n            if (!state.activeProcessor) {\n                state.processingAborted = true;\n\n                setStatus(ItemStatus.IDLE);\n                fire('process-abort');\n\n                resolve();\n                return;\n            }\n\n            abortProcessingRequestComplete = () => {\n                resolve();\n            };\n\n            state.activeProcessor.abort();\n        });\n\n    //\n    // logic to revert a processed file\n    //\n    const revert = (revertFileUpload, forceRevert) =>\n        new Promise((resolve, reject) => {\n            // a completed upload will have a serverFileReference, a failed chunked upload where\n            // getting a serverId succeeded but >=0 chunks have been uploaded will have transferId set\n            const serverTransferId =\n                state.serverFileReference !== null ? state.serverFileReference : state.transferId;\n\n            // cannot revert without a server id for this process\n            if (serverTransferId === null) {\n                resolve();\n                return;\n            }\n\n            // revert the upload (fire and forget)\n            revertFileUpload(\n                serverTransferId,\n                () => {\n                    // reset file server id and transfer id as now it's not available on the server\n                    state.serverFileReference = null;\n                    state.transferId = null;\n                    resolve();\n                },\n                error => {\n                    // don't set error state when reverting is optional, it will always resolve\n                    if (!forceRevert) {\n                        resolve();\n                        return;\n                    }\n\n                    // oh no errors\n                    setStatus(ItemStatus.PROCESSING_REVERT_ERROR);\n                    fire('process-revert-error');\n                    reject(error);\n                }\n            );\n\n            // fire event\n            setStatus(ItemStatus.IDLE);\n            fire('process-revert');\n        });\n\n    // exposed methods\n    const setMetadata = (key, value, silent) => {\n        const keys = key.split('.');\n        const root = keys[0];\n        const last = keys.pop();\n        let data = metadata;\n        keys.forEach(key => (data = data[key]));\n\n        // compare old value against new value, if they're the same, we're not updating\n        if (JSON.stringify(data[last]) === JSON.stringify(value)) return;\n\n        // update value\n        data[last] = value;\n\n        // fire update\n        fire('metadata-update', {\n            key: root,\n            value: metadata[root],\n            silent,\n        });\n    };\n\n    const getMetadata = key => deepCloneObject(key ? metadata[key] : metadata);\n\n    const api = {\n        id: { get: () => id },\n        origin: { get: () => origin, set: value => (origin = value) },\n        serverId: { get: () => state.serverFileReference },\n        transferId: { get: () => state.transferId },\n        status: { get: () => state.status },\n        filename: { get: () => state.file.name },\n        filenameWithoutExtension: { get: () => getFilenameWithoutExtension(state.file.name) },\n        fileExtension: { get: getFileExtension },\n        fileType: { get: getFileType },\n        fileSize: { get: getFileSize },\n        file: { get: getFile },\n        relativePath: { get: () => state.file._relativePath },\n\n        source: { get: () => state.source },\n\n        getMetadata,\n        setMetadata: (key, value, silent) => {\n            if (isObject(key)) {\n                const data = key;\n                Object.keys(data).forEach(key => {\n                    setMetadata(key, data[key], value);\n                });\n                return key;\n            }\n            setMetadata(key, value, silent);\n            return value;\n        },\n\n        extend: (name, handler) => (itemAPI[name] = handler),\n\n        abortLoad,\n        retryLoad,\n        requestProcessing,\n        abortProcessing,\n\n        load,\n        process,\n        revert,\n\n        ...on(),\n\n        freeze: () => (state.frozen = true),\n\n        release: () => (state.released = true),\n        released: { get: () => state.released },\n\n        archive: () => (state.archived = true),\n        archived: { get: () => state.archived },\n\n        // replace source and file object\n        setFile: file => (state.file = file),\n    };\n\n    // create it here instead of returning it instantly so we can extend it later\n    const itemAPI = createObject(api);\n\n    return itemAPI;\n};\n\nconst getItemIndexByQuery = (items, query) => {\n    // just return first index\n    if (isEmpty(query)) {\n        return 0;\n    }\n\n    // invalid queries\n    if (!isString(query)) {\n        return -1;\n    }\n\n    // return item by id (or -1 if not found)\n    return items.findIndex(item => item.id === query);\n};\n\nconst getItemById = (items, itemId) => {\n    const index = getItemIndexByQuery(items, itemId);\n    if (index < 0) {\n        return;\n    }\n    return items[index] || null;\n};\n\nconst fetchBlob = (url, load, error, progress, abort, headers) => {\n    const request = sendRequest(null, url, {\n        method: 'GET',\n        responseType: 'blob',\n    });\n\n    request.onload = xhr => {\n        // get headers\n        const headers = xhr.getAllResponseHeaders();\n\n        // get filename\n        const filename = getFileInfoFromHeaders(headers).name || getFilenameFromURL(url);\n\n        // create response\n        load(createResponse('load', xhr.status, getFileFromBlob(xhr.response, filename), headers));\n    };\n\n    request.onerror = xhr => {\n        error(createResponse('error', xhr.status, xhr.statusText, xhr.getAllResponseHeaders()));\n    };\n\n    request.onheaders = xhr => {\n        headers(createResponse('headers', xhr.status, null, xhr.getAllResponseHeaders()));\n    };\n\n    request.ontimeout = createTimeoutResponse(error);\n    request.onprogress = progress;\n    request.onabort = abort;\n\n    // should return request\n    return request;\n};\n\nconst getDomainFromURL = url => {\n    if (url.indexOf('//') === 0) {\n        url = location.protocol + url;\n    }\n    return url\n        .toLowerCase()\n        .replace('blob:', '')\n        .replace(/([a-z])?:\\/\\//, '$1')\n        .split('/')[0];\n};\n\nconst isExternalURL = url =>\n    (url.indexOf(':') > -1 || url.indexOf('//') > -1) &&\n    getDomainFromURL(location.href) !== getDomainFromURL(url);\n\nconst dynamicLabel = label => (...params) => (isFunction(label) ? label(...params) : label);\n\nconst isMockItem = item => !isFile(item.file);\n\nconst listUpdated = (dispatch, state) => {\n    clearTimeout(state.listUpdateTimeout);\n    state.listUpdateTimeout = setTimeout(() => {\n        dispatch('DID_UPDATE_ITEMS', { items: getActiveItems(state.items) });\n    }, 0);\n};\n\nconst optionalPromise = (fn, ...params) =>\n    new Promise(resolve => {\n        if (!fn) {\n            return resolve(true);\n        }\n\n        const result = fn(...params);\n\n        if (result == null) {\n            return resolve(true);\n        }\n\n        if (typeof result === 'boolean') {\n            return resolve(result);\n        }\n\n        if (typeof result.then === 'function') {\n            result.then(resolve);\n        }\n    });\n\nconst sortItems = (state, compare) => {\n    state.items.sort((a, b) => compare(createItemAPI(a), createItemAPI(b)));\n};\n\n// returns item based on state\nconst getItemByQueryFromState = (state, itemHandler) => ({\n    query,\n    success = () => {},\n    failure = () => {},\n    ...options\n} = {}) => {\n    const item = getItemByQuery(state.items, query);\n    if (!item) {\n        failure({\n            error: createResponse('error', 0, 'Item not found'),\n            file: null,\n        });\n        return;\n    }\n    itemHandler(item, success, failure, options || {});\n};\n\nconst actions = (dispatch, query, state) => ({\n    /**\n     * Aborts all ongoing processes\n     */\n    ABORT_ALL: () => {\n        getActiveItems(state.items).forEach(item => {\n            item.freeze();\n            item.abortLoad();\n            item.abortProcessing();\n        });\n    },\n\n    /**\n     * Sets initial files\n     */\n    DID_SET_FILES: ({ value = [] }) => {\n        // map values to file objects\n        const files = value.map(file => ({\n            source: file.source ? file.source : file,\n            options: file.options,\n        }));\n\n        // loop over files, if file is in list, leave it be, if not, remove\n        // test if items should be moved\n        let activeItems = getActiveItems(state.items);\n\n        activeItems.forEach(item => {\n            // if item not is in new value, remove\n            if (!files.find(file => file.source === item.source || file.source === item.file)) {\n                dispatch('REMOVE_ITEM', { query: item, remove: false });\n            }\n        });\n\n        // add new files\n        activeItems = getActiveItems(state.items);\n        files.forEach((file, index) => {\n            // if file is already in list\n            if (activeItems.find(item => item.source === file.source || item.file === file.source))\n                return;\n\n            // not in list, add\n            dispatch('ADD_ITEM', {\n                ...file,\n                interactionMethod: InteractionMethod.NONE,\n                index,\n            });\n        });\n    },\n\n    DID_UPDATE_ITEM_METADATA: ({ id, action, change }) => {\n        // don't do anything\n        if (change.silent) return;\n\n        // if is called multiple times in close succession we combined all calls together to save resources\n        clearTimeout(state.itemUpdateTimeout);\n        state.itemUpdateTimeout = setTimeout(() => {\n            const item = getItemById(state.items, id);\n\n            // only revert and attempt to upload when we're uploading to a server\n            if (!query('IS_ASYNC')) {\n                // should we update the output data\n                applyFilterChain('SHOULD_PREPARE_OUTPUT', false, {\n                    item,\n                    query,\n                    action,\n                    change,\n                }).then(shouldPrepareOutput => {\n                    // plugins determined the output data should be prepared (or not), can be adjusted with beforePrepareOutput hook\n                    const beforePrepareFile = query('GET_BEFORE_PREPARE_FILE');\n                    if (beforePrepareFile)\n                        shouldPrepareOutput = beforePrepareFile(item, shouldPrepareOutput);\n\n                    if (!shouldPrepareOutput) return;\n\n                    dispatch(\n                        'REQUEST_PREPARE_OUTPUT',\n                        {\n                            query: id,\n                            item,\n                            success: file => {\n                                dispatch('DID_PREPARE_OUTPUT', { id, file });\n                            },\n                        },\n                        true\n                    );\n                });\n\n                return;\n            }\n\n            // if is local item we need to enable upload button so change can be propagated to server\n            if (item.origin === FileOrigin.LOCAL) {\n                dispatch('DID_LOAD_ITEM', {\n                    id: item.id,\n                    error: null,\n                    serverFileReference: item.source,\n                });\n            }\n\n            // for async scenarios\n            const upload = () => {\n                // we push this forward a bit so the interface is updated correctly\n                setTimeout(() => {\n                    dispatch('REQUEST_ITEM_PROCESSING', { query: id });\n                }, 32);\n            };\n\n            const revert = doUpload => {\n                item.revert(\n                    createRevertFunction(state.options.server.url, state.options.server.revert),\n                    query('GET_FORCE_REVERT')\n                )\n                    .then(doUpload ? upload : () => {})\n                    .catch(() => {});\n            };\n\n            const abort = doUpload => {\n                item.abortProcessing().then(doUpload ? upload : () => {});\n            };\n\n            // if we should re-upload the file immediately\n            if (item.status === ItemStatus.PROCESSING_COMPLETE) {\n                return revert(state.options.instantUpload);\n            }\n\n            // if currently uploading, cancel upload\n            if (item.status === ItemStatus.PROCESSING) {\n                return abort(state.options.instantUpload);\n            }\n\n            if (state.options.instantUpload) {\n                upload();\n            }\n        }, 0);\n    },\n\n    MOVE_ITEM: ({ query, index }) => {\n        const item = getItemByQuery(state.items, query);\n        if (!item) return;\n        const currentIndex = state.items.indexOf(item);\n        index = limit(index, 0, state.items.length - 1);\n        if (currentIndex === index) return;\n        state.items.splice(index, 0, state.items.splice(currentIndex, 1)[0]);\n    },\n\n    SORT: ({ compare }) => {\n        sortItems(state, compare);\n        dispatch('DID_SORT_ITEMS', {\n            items: query('GET_ACTIVE_ITEMS'),\n        });\n    },\n\n    ADD_ITEMS: ({ items, index, interactionMethod, success = () => {}, failure = () => {} }) => {\n        let currentIndex = index;\n\n        if (index === -1 || typeof index === 'undefined') {\n            const insertLocation = query('GET_ITEM_INSERT_LOCATION');\n            const totalItems = query('GET_TOTAL_ITEMS');\n            currentIndex = insertLocation === 'before' ? 0 : totalItems;\n        }\n\n        const ignoredFiles = query('GET_IGNORED_FILES');\n        const isValidFile = source =>\n            isFile(source) ? !ignoredFiles.includes(source.name.toLowerCase()) : !isEmpty(source);\n        const validItems = items.filter(isValidFile);\n\n        const promises = validItems.map(\n            source =>\n                new Promise((resolve, reject) => {\n                    dispatch('ADD_ITEM', {\n                        interactionMethod,\n                        source: source.source || source,\n                        success: resolve,\n                        failure: reject,\n                        index: currentIndex++,\n                        options: source.options || {},\n                    });\n                })\n        );\n\n        Promise.all(promises)\n            .then(success)\n            .catch(failure);\n    },\n\n    /**\n     * @param source\n     * @param index\n     * @param interactionMethod\n     */\n    ADD_ITEM: ({\n        source,\n        index = -1,\n        interactionMethod,\n        success = () => {},\n        failure = () => {},\n        options = {},\n    }) => {\n        // if no source supplied\n        if (isEmpty(source)) {\n            failure({\n                error: createResponse('error', 0, 'No source'),\n                file: null,\n            });\n            return;\n        }\n\n        // filter out invalid file items, used to filter dropped directory contents\n        if (isFile(source) && state.options.ignoredFiles.includes(source.name.toLowerCase())) {\n            // fail silently\n            return;\n        }\n\n        // test if there's still room in the list of files\n        if (!hasRoomForItem(state)) {\n            // if multiple allowed, we can't replace\n            // or if only a single item is allowed but we're not allowed to replace it we exit\n            if (\n                state.options.allowMultiple ||\n                (!state.options.allowMultiple && !state.options.allowReplace)\n            ) {\n                const error = createResponse('warning', 0, 'Max files');\n\n                dispatch('DID_THROW_MAX_FILES', {\n                    source,\n                    error,\n                });\n\n                failure({ error, file: null });\n\n                return;\n            }\n\n            // let's replace the item\n            // id of first item we're about to remove\n            const item = getActiveItems(state.items)[0];\n\n            // if has been processed remove it from the server as well\n            if (\n                item.status === ItemStatus.PROCESSING_COMPLETE ||\n                item.status === ItemStatus.PROCESSING_REVERT_ERROR\n            ) {\n                const forceRevert = query('GET_FORCE_REVERT');\n                item.revert(\n                    createRevertFunction(state.options.server.url, state.options.server.revert),\n                    forceRevert\n                )\n                    .then(() => {\n                        if (!forceRevert) return;\n\n                        // try to add now\n                        dispatch('ADD_ITEM', {\n                            source,\n                            index,\n                            interactionMethod,\n                            success,\n                            failure,\n                            options,\n                        });\n                    })\n                    .catch(() => {}); // no need to handle this catch state for now\n\n                if (forceRevert) return;\n            }\n\n            // remove first item as it will be replaced by this item\n            dispatch('REMOVE_ITEM', { query: item.id });\n        }\n\n        // where did the file originate\n        const origin =\n            options.type === 'local'\n                ? FileOrigin.LOCAL\n                : options.type === 'limbo'\n                ? FileOrigin.LIMBO\n                : FileOrigin.INPUT;\n\n        // create a new blank item\n        const item = createItem(\n            // where did this file come from\n            origin,\n\n            // an input file never has a server file reference\n            origin === FileOrigin.INPUT ? null : source,\n\n            // file mock data, if defined\n            options.file\n        );\n\n        // set initial meta data\n        Object.keys(options.metadata || {}).forEach(key => {\n            item.setMetadata(key, options.metadata[key]);\n        });\n\n        // created the item, let plugins add methods\n        applyFilters('DID_CREATE_ITEM', item, { query, dispatch });\n\n        // where to insert new items\n        const itemInsertLocation = query('GET_ITEM_INSERT_LOCATION');\n\n        // adjust index if is not allowed to pick location\n        if (!state.options.itemInsertLocationFreedom) {\n            index = itemInsertLocation === 'before' ? -1 : state.items.length;\n        }\n\n        // add item to list\n        insertItem(state.items, item, index);\n\n        // sort items in list\n        if (isFunction(itemInsertLocation) && source) {\n            sortItems(state, itemInsertLocation);\n        }\n\n        // get a quick reference to the item id\n        const id = item.id;\n\n        // observe item events\n        item.on('init', () => {\n            dispatch('DID_INIT_ITEM', { id });\n        });\n\n        item.on('load-init', () => {\n            dispatch('DID_START_ITEM_LOAD', { id });\n        });\n\n        item.on('load-meta', () => {\n            dispatch('DID_UPDATE_ITEM_META', { id });\n        });\n\n        item.on('load-progress', progress => {\n            dispatch('DID_UPDATE_ITEM_LOAD_PROGRESS', { id, progress });\n        });\n\n        item.on('load-request-error', error => {\n            const mainStatus = dynamicLabel(state.options.labelFileLoadError)(error);\n\n            // is client error, no way to recover\n            if (error.code >= 400 && error.code < 500) {\n                dispatch('DID_THROW_ITEM_INVALID', {\n                    id,\n                    error,\n                    status: {\n                        main: mainStatus,\n                        sub: `${error.code} (${error.body})`,\n                    },\n                });\n\n                // reject the file so can be dealt with through API\n                failure({ error, file: createItemAPI(item) });\n                return;\n            }\n\n            // is possible server error, so might be possible to retry\n            dispatch('DID_THROW_ITEM_LOAD_ERROR', {\n                id,\n                error,\n                status: {\n                    main: mainStatus,\n                    sub: state.options.labelTapToRetry,\n                },\n            });\n        });\n\n        item.on('load-file-error', error => {\n            dispatch('DID_THROW_ITEM_INVALID', {\n                id,\n                error: error.status,\n                status: error.status,\n            });\n            failure({ error: error.status, file: createItemAPI(item) });\n        });\n\n        item.on('load-abort', () => {\n            dispatch('REMOVE_ITEM', { query: id });\n        });\n\n        item.on('load-skip', () => {\n            item.on('metadata-update', change => {\n                if (!isFile(item.file)) return;\n                dispatch('DID_UPDATE_ITEM_METADATA', { id, change });\n            });\n\n            dispatch('COMPLETE_LOAD_ITEM', {\n                query: id,\n                item,\n                data: {\n                    source,\n                    success,\n                },\n            });\n        });\n\n        item.on('load', () => {\n            const handleAdd = shouldAdd => {\n                // no should not add this file\n                if (!shouldAdd) {\n                    dispatch('REMOVE_ITEM', {\n                        query: id,\n                    });\n                    return;\n                }\n\n                // now interested in metadata updates\n                item.on('metadata-update', change => {\n                    dispatch('DID_UPDATE_ITEM_METADATA', { id, change });\n                });\n\n                // let plugins decide if the output data should be prepared at this point\n                // means we'll do this and wait for idle state\n                applyFilterChain('SHOULD_PREPARE_OUTPUT', false, { item, query }).then(\n                    shouldPrepareOutput => {\n                        // plugins determined the output data should be prepared (or not), can be adjusted with beforePrepareOutput hook\n                        const beforePrepareFile = query('GET_BEFORE_PREPARE_FILE');\n                        if (beforePrepareFile)\n                            shouldPrepareOutput = beforePrepareFile(item, shouldPrepareOutput);\n\n                        const loadComplete = () => {\n                            dispatch('COMPLETE_LOAD_ITEM', {\n                                query: id,\n                                item,\n                                data: {\n                                    source,\n                                    success,\n                                },\n                            });\n\n                            listUpdated(dispatch, state);\n                        };\n\n                        // exit\n                        if (shouldPrepareOutput) {\n                            // wait for idle state and then run PREPARE_OUTPUT\n                            dispatch(\n                                'REQUEST_PREPARE_OUTPUT',\n                                {\n                                    query: id,\n                                    item,\n                                    success: file => {\n                                        dispatch('DID_PREPARE_OUTPUT', { id, file });\n                                        loadComplete();\n                                    },\n                                },\n                                true\n                            );\n\n                            return;\n                        }\n\n                        loadComplete();\n                    }\n                );\n            };\n\n            // item loaded, allow plugins to\n            // - read data (quickly)\n            // - add metadata\n            applyFilterChain('DID_LOAD_ITEM', item, { query, dispatch })\n                .then(() => {\n                    optionalPromise(query('GET_BEFORE_ADD_FILE'), createItemAPI(item)).then(\n                        handleAdd\n                    );\n                })\n                .catch(e => {\n                    if (!e || !e.error || !e.status) return handleAdd(false);\n                    dispatch('DID_THROW_ITEM_INVALID', {\n                        id,\n                        error: e.error,\n                        status: e.status,\n                    });\n                });\n        });\n\n        item.on('process-start', () => {\n            dispatch('DID_START_ITEM_PROCESSING', { id });\n        });\n\n        item.on('process-progress', progress => {\n            dispatch('DID_UPDATE_ITEM_PROCESS_PROGRESS', { id, progress });\n        });\n\n        item.on('process-error', error => {\n            dispatch('DID_THROW_ITEM_PROCESSING_ERROR', {\n                id,\n                error,\n                status: {\n                    main: dynamicLabel(state.options.labelFileProcessingError)(error),\n                    sub: state.options.labelTapToRetry,\n                },\n            });\n        });\n\n        item.on('process-revert-error', error => {\n            dispatch('DID_THROW_ITEM_PROCESSING_REVERT_ERROR', {\n                id,\n                error,\n                status: {\n                    main: dynamicLabel(state.options.labelFileProcessingRevertError)(error),\n                    sub: state.options.labelTapToRetry,\n                },\n            });\n        });\n\n        item.on('process-complete', serverFileReference => {\n            dispatch('DID_COMPLETE_ITEM_PROCESSING', {\n                id,\n                error: null,\n                serverFileReference,\n            });\n            dispatch('DID_DEFINE_VALUE', { id, value: serverFileReference });\n        });\n\n        item.on('process-abort', () => {\n            dispatch('DID_ABORT_ITEM_PROCESSING', { id });\n        });\n\n        item.on('process-revert', () => {\n            dispatch('DID_REVERT_ITEM_PROCESSING', { id });\n            dispatch('DID_DEFINE_VALUE', { id, value: null });\n        });\n\n        // let view know the item has been inserted\n        dispatch('DID_ADD_ITEM', { id, index, interactionMethod });\n\n        listUpdated(dispatch, state);\n\n        // start loading the source\n        const { url, load, restore, fetch } = state.options.server || {};\n\n        item.load(\n            source,\n\n            // this creates a function that loads the file based on the type of file (string, base64, blob, file) and location of file (local, remote, limbo)\n            createFileLoader(\n                origin === FileOrigin.INPUT\n                    ? // input, if is remote, see if should use custom fetch, else use default fetchBlob\n                      isString(source) && isExternalURL(source)\n                        ? fetch\n                            ? createFetchFunction(url, fetch)\n                            : fetchBlob // remote url\n                        : fetchBlob // try to fetch url\n                    : // limbo or local\n                    origin === FileOrigin.LIMBO\n                    ? createFetchFunction(url, restore) // limbo\n                    : createFetchFunction(url, load) // local\n            ),\n\n            // called when the file is loaded so it can be piped through the filters\n            (file, success, error) => {\n                // let's process the file\n                applyFilterChain('LOAD_FILE', file, { query })\n                    .then(success)\n                    .catch(error);\n            }\n        );\n    },\n\n    REQUEST_PREPARE_OUTPUT: ({ item, success, failure = () => {} }) => {\n        // error response if item archived\n        const err = {\n            error: createResponse('error', 0, 'Item not found'),\n            file: null,\n        };\n\n        // don't handle archived items, an item could have been archived (load aborted) while waiting to be prepared\n        if (item.archived) return failure(err);\n\n        // allow plugins to alter the file data\n        applyFilterChain('PREPARE_OUTPUT', item.file, { query, item }).then(result => {\n            applyFilterChain('COMPLETE_PREPARE_OUTPUT', result, { query, item }).then(result => {\n                // don't handle archived items, an item could have been archived (load aborted) while being prepared\n                if (item.archived) return failure(err);\n\n                // we done!\n                success(result);\n            });\n        });\n    },\n\n    COMPLETE_LOAD_ITEM: ({ item, data }) => {\n        const { success, source } = data;\n\n        // sort items in list\n        const itemInsertLocation = query('GET_ITEM_INSERT_LOCATION');\n        if (isFunction(itemInsertLocation) && source) {\n            sortItems(state, itemInsertLocation);\n        }\n\n        // let interface know the item has loaded\n        dispatch('DID_LOAD_ITEM', {\n            id: item.id,\n            error: null,\n            serverFileReference: item.origin === FileOrigin.INPUT ? null : source,\n        });\n\n        // item has been successfully loaded and added to the\n        // list of items so can now be safely returned for use\n        success(createItemAPI(item));\n\n        // if this is a local server file we need to show a different state\n        if (item.origin === FileOrigin.LOCAL) {\n            dispatch('DID_LOAD_LOCAL_ITEM', { id: item.id });\n            return;\n        }\n\n        // if is a temp server file we prevent async upload call here (as the file is already on the server)\n        if (item.origin === FileOrigin.LIMBO) {\n            dispatch('DID_COMPLETE_ITEM_PROCESSING', {\n                id: item.id,\n                error: null,\n                serverFileReference: source,\n            });\n\n            dispatch('DID_DEFINE_VALUE', {\n                id: item.id,\n                value: item.serverId || source,\n            });\n            return;\n        }\n\n        // id we are allowed to upload the file immediately, lets do it\n        if (query('IS_ASYNC') && state.options.instantUpload) {\n            dispatch('REQUEST_ITEM_PROCESSING', { query: item.id });\n        }\n    },\n\n    RETRY_ITEM_LOAD: getItemByQueryFromState(state, item => {\n        // try loading the source one more time\n        item.retryLoad();\n    }),\n\n    REQUEST_ITEM_PREPARE: getItemByQueryFromState(state, (item, success, failure) => {\n        dispatch(\n            'REQUEST_PREPARE_OUTPUT',\n            {\n                query: item.id,\n                item,\n                success: file => {\n                    dispatch('DID_PREPARE_OUTPUT', { id: item.id, file });\n                    success({\n                        file: item,\n                        output: file,\n                    });\n                },\n                failure,\n            },\n            true\n        );\n    }),\n\n    REQUEST_ITEM_PROCESSING: getItemByQueryFromState(state, (item, success, failure) => {\n        // cannot be queued (or is already queued)\n        const itemCanBeQueuedForProcessing =\n            // waiting for something\n            item.status === ItemStatus.IDLE ||\n            // processing went wrong earlier\n            item.status === ItemStatus.PROCESSING_ERROR;\n\n        // not ready to be processed\n        if (!itemCanBeQueuedForProcessing) {\n            const processNow = () =>\n                dispatch('REQUEST_ITEM_PROCESSING', { query: item, success, failure });\n\n            const process = () => (document.hidden ? processNow() : setTimeout(processNow, 32));\n\n            // if already done processing or tried to revert but didn't work, try again\n            if (\n                item.status === ItemStatus.PROCESSING_COMPLETE ||\n                item.status === ItemStatus.PROCESSING_REVERT_ERROR\n            ) {\n                item.revert(\n                    createRevertFunction(state.options.server.url, state.options.server.revert),\n                    query('GET_FORCE_REVERT')\n                )\n                    .then(process)\n                    .catch(() => {}); // don't continue with processing if something went wrong\n            } else if (item.status === ItemStatus.PROCESSING) {\n                item.abortProcessing().then(process);\n            }\n\n            return;\n        }\n\n        // already queued for processing\n        if (item.status === ItemStatus.PROCESSING_QUEUED) return;\n\n        item.requestProcessing();\n\n        dispatch('DID_REQUEST_ITEM_PROCESSING', { id: item.id });\n\n        dispatch('PROCESS_ITEM', { query: item, success, failure }, true);\n    }),\n\n    PROCESS_ITEM: getItemByQueryFromState(state, (item, success, failure) => {\n        const maxParallelUploads = query('GET_MAX_PARALLEL_UPLOADS');\n        const totalCurrentUploads = query('GET_ITEMS_BY_STATUS', ItemStatus.PROCESSING).length;\n\n        // queue and wait till queue is freed up\n        if (totalCurrentUploads === maxParallelUploads) {\n            // queue for later processing\n            state.processingQueue.push({\n                id: item.id,\n                success,\n                failure,\n            });\n\n            // stop it!\n            return;\n        }\n\n        // if was not queued or is already processing exit here\n        if (item.status === ItemStatus.PROCESSING) return;\n\n        const processNext = () => {\n            // process queueud items\n            const queueEntry = state.processingQueue.shift();\n\n            // no items left\n            if (!queueEntry) return;\n\n            // get item reference\n            const { id, success, failure } = queueEntry;\n            const itemReference = getItemByQuery(state.items, id);\n\n            // if item was archived while in queue, jump to next\n            if (!itemReference || itemReference.archived) {\n                processNext();\n                return;\n            }\n\n            // process queued item\n            dispatch('PROCESS_ITEM', { query: id, success, failure }, true);\n        };\n\n        // we done function\n        item.onOnce('process-complete', () => {\n            success(createItemAPI(item));\n            processNext();\n\n            // if origin is local, and we're instant uploading, trigger remove of original\n            // as revert will remove file from list\n            const server = state.options.server;\n            const instantUpload = state.options.instantUpload;\n            if (instantUpload && item.origin === FileOrigin.LOCAL && isFunction(server.remove)) {\n                const noop = () => {};\n                item.origin = FileOrigin.LIMBO;\n                state.options.server.remove(item.source, noop, noop);\n            }\n\n            // All items processed? No errors?\n            const allItemsProcessed =\n                query('GET_ITEMS_BY_STATUS', ItemStatus.PROCESSING_COMPLETE).length ===\n                state.items.length;\n            if (allItemsProcessed) {\n                dispatch('DID_COMPLETE_ITEM_PROCESSING_ALL');\n            }\n        });\n\n        // we error function\n        item.onOnce('process-error', error => {\n            failure({ error, file: createItemAPI(item) });\n            processNext();\n        });\n\n        // start file processing\n        const options = state.options;\n        item.process(\n            createFileProcessor(\n                createProcessorFunction(options.server.url, options.server.process, options.name, {\n                    chunkTransferId: item.transferId,\n                    chunkServer: options.server.patch,\n                    chunkUploads: options.chunkUploads,\n                    chunkForce: options.chunkForce,\n                    chunkSize: options.chunkSize,\n                    chunkRetryDelays: options.chunkRetryDelays,\n                }),\n                {\n                    allowMinimumUploadDuration: query('GET_ALLOW_MINIMUM_UPLOAD_DURATION'),\n                }\n            ),\n            // called when the file is about to be processed so it can be piped through the transform filters\n            (file, success, error) => {\n                // allow plugins to alter the file data\n                applyFilterChain('PREPARE_OUTPUT', file, { query, item })\n                    .then(file => {\n                        dispatch('DID_PREPARE_OUTPUT', { id: item.id, file });\n\n                        success(file);\n                    })\n                    .catch(error);\n            }\n        );\n    }),\n\n    RETRY_ITEM_PROCESSING: getItemByQueryFromState(state, item => {\n        dispatch('REQUEST_ITEM_PROCESSING', { query: item });\n    }),\n\n    REQUEST_REMOVE_ITEM: getItemByQueryFromState(state, item => {\n        optionalPromise(query('GET_BEFORE_REMOVE_FILE'), createItemAPI(item)).then(shouldRemove => {\n            if (!shouldRemove) {\n                return;\n            }\n            dispatch('REMOVE_ITEM', { query: item });\n        });\n    }),\n\n    RELEASE_ITEM: getItemByQueryFromState(state, item => {\n        item.release();\n    }),\n\n    REMOVE_ITEM: getItemByQueryFromState(state, (item, success, failure, options) => {\n        const removeFromView = () => {\n            // get id reference\n            const id = item.id;\n\n            // archive the item, this does not remove it from the list\n            getItemById(state.items, id).archive();\n\n            // tell the view the item has been removed\n            dispatch('DID_REMOVE_ITEM', { error: null, id, item });\n\n            // now the list has been modified\n            listUpdated(dispatch, state);\n\n            // correctly removed\n            success(createItemAPI(item));\n        };\n\n        // if this is a local file and the `server.remove` function has been configured,\n        // send source there so dev can remove file from server\n        const server = state.options.server;\n        if (\n            item.origin === FileOrigin.LOCAL &&\n            server &&\n            isFunction(server.remove) &&\n            options.remove !== false\n        ) {\n            dispatch('DID_START_ITEM_REMOVE', { id: item.id });\n\n            server.remove(\n                item.source,\n                () => removeFromView(),\n                status => {\n                    dispatch('DID_THROW_ITEM_REMOVE_ERROR', {\n                        id: item.id,\n                        error: createResponse('error', 0, status, null),\n                        status: {\n                            main: dynamicLabel(state.options.labelFileRemoveError)(status),\n                            sub: state.options.labelTapToRetry,\n                        },\n                    });\n                }\n            );\n        } else {\n            // if is requesting revert and can revert need to call revert handler (not calling request_ because that would also trigger beforeRemoveHook)\n            if (\n                (options.revert && item.origin !== FileOrigin.LOCAL && item.serverId !== null) ||\n                // if chunked uploads are enabled and we're uploading in chunks for this specific file\n                // or if the file isn't big enough for chunked uploads but chunkForce is set then call\n                // revert before removing from the view...\n                (state.options.chunkUploads && item.file.size > state.options.chunkSize) ||\n                (state.options.chunkUploads && state.options.chunkForce)\n            ) {\n                item.revert(\n                    createRevertFunction(state.options.server.url, state.options.server.revert),\n                    query('GET_FORCE_REVERT')\n                );\n            }\n\n            // can now safely remove from view\n            removeFromView();\n        }\n    }),\n\n    ABORT_ITEM_LOAD: getItemByQueryFromState(state, item => {\n        item.abortLoad();\n    }),\n\n    ABORT_ITEM_PROCESSING: getItemByQueryFromState(state, item => {\n        // test if is already processed\n        if (item.serverId) {\n            dispatch('REVERT_ITEM_PROCESSING', { id: item.id });\n            return;\n        }\n\n        // abort\n        item.abortProcessing().then(() => {\n            const shouldRemove = state.options.instantUpload;\n            if (shouldRemove) {\n                dispatch('REMOVE_ITEM', { query: item.id });\n            }\n        });\n    }),\n\n    REQUEST_REVERT_ITEM_PROCESSING: getItemByQueryFromState(state, item => {\n        // not instant uploading, revert immediately\n        if (!state.options.instantUpload) {\n            dispatch('REVERT_ITEM_PROCESSING', { query: item });\n            return;\n        }\n\n        // if we're instant uploading the file will also be removed if we revert,\n        // so if a before remove file hook is defined we need to run it now\n        const handleRevert = shouldRevert => {\n            if (!shouldRevert) return;\n            dispatch('REVERT_ITEM_PROCESSING', { query: item });\n        };\n\n        const fn = query('GET_BEFORE_REMOVE_FILE');\n        if (!fn) {\n            return handleRevert(true);\n        }\n\n        const requestRemoveResult = fn(createItemAPI(item));\n        if (requestRemoveResult == null) {\n            // undefined or null\n            return handleRevert(true);\n        }\n\n        if (typeof requestRemoveResult === 'boolean') {\n            return handleRevert(requestRemoveResult);\n        }\n\n        if (typeof requestRemoveResult.then === 'function') {\n            requestRemoveResult.then(handleRevert);\n        }\n    }),\n\n    REVERT_ITEM_PROCESSING: getItemByQueryFromState(state, item => {\n        item.revert(\n            createRevertFunction(state.options.server.url, state.options.server.revert),\n            query('GET_FORCE_REVERT')\n        )\n            .then(() => {\n                const shouldRemove = state.options.instantUpload || isMockItem(item);\n                if (shouldRemove) {\n                    dispatch('REMOVE_ITEM', { query: item.id });\n                }\n            })\n            .catch(() => {});\n    }),\n\n    SET_OPTIONS: ({ options }) => {\n        // get all keys passed\n        const optionKeys = Object.keys(options);\n\n        // get prioritized keyed to include (remove once not in options object)\n        const prioritizedOptionKeys = PrioritizedOptions.filter(key => optionKeys.includes(key));\n\n        // order the keys, prioritized first, then rest\n        const orderedOptionKeys = [\n            // add prioritized first if passed to options, else remove\n            ...prioritizedOptionKeys,\n\n            // prevent duplicate keys\n            ...Object.keys(options).filter(key => !prioritizedOptionKeys.includes(key)),\n        ];\n\n        // dispatch set event for each option\n        orderedOptionKeys.forEach(key => {\n            dispatch(`SET_${fromCamels(key, '_').toUpperCase()}`, {\n                value: options[key],\n            });\n        });\n    },\n});\n\nconst PrioritizedOptions = [\n    'server', // must be processed before \"files\"\n];\n\nconst formatFilename = name => name;\n\nconst createElement$1 = tagName => {\n    return document.createElement(tagName);\n};\n\nconst text = (node, value) => {\n    let textNode = node.childNodes[0];\n    if (!textNode) {\n        textNode = document.createTextNode(value);\n        node.appendChild(textNode);\n    } else if (value !== textNode.nodeValue) {\n        textNode.nodeValue = value;\n    }\n};\n\nconst polarToCartesian = (centerX, centerY, radius, angleInDegrees) => {\n    const angleInRadians = (((angleInDegrees % 360) - 90) * Math.PI) / 180.0;\n    return {\n        x: centerX + radius * Math.cos(angleInRadians),\n        y: centerY + radius * Math.sin(angleInRadians),\n    };\n};\n\nconst describeArc = (x, y, radius, startAngle, endAngle, arcSweep) => {\n    const start = polarToCartesian(x, y, radius, endAngle);\n    const end = polarToCartesian(x, y, radius, startAngle);\n    return ['M', start.x, start.y, 'A', radius, radius, 0, arcSweep, 0, end.x, end.y].join(' ');\n};\n\nconst percentageArc = (x, y, radius, from, to) => {\n    let arcSweep = 1;\n    if (to > from && to - from <= 0.5) {\n        arcSweep = 0;\n    }\n    if (from > to && from - to >= 0.5) {\n        arcSweep = 0;\n    }\n    return describeArc(\n        x,\n        y,\n        radius,\n        Math.min(0.9999, from) * 360,\n        Math.min(0.9999, to) * 360,\n        arcSweep\n    );\n};\n\nconst create = ({ root, props }) => {\n    // start at 0\n    props.spin = false;\n    props.progress = 0;\n    props.opacity = 0;\n\n    // svg\n    const svg = createElement('svg');\n    root.ref.path = createElement('path', {\n        'stroke-width': 2,\n        'stroke-linecap': 'round',\n    });\n    svg.appendChild(root.ref.path);\n\n    root.ref.svg = svg;\n\n    root.appendChild(svg);\n};\n\nconst write = ({ root, props }) => {\n    if (props.opacity === 0) {\n        return;\n    }\n\n    if (props.align) {\n        root.element.dataset.align = props.align;\n    }\n\n    // get width of stroke\n    const ringStrokeWidth = parseInt(attr(root.ref.path, 'stroke-width'), 10);\n\n    // calculate size of ring\n    const size = root.rect.element.width * 0.5;\n\n    // ring state\n    let ringFrom = 0;\n    let ringTo = 0;\n\n    // now in busy mode\n    if (props.spin) {\n        ringFrom = 0;\n        ringTo = 0.5;\n    } else {\n        ringFrom = 0;\n        ringTo = props.progress;\n    }\n\n    // get arc path\n    const coordinates = percentageArc(size, size, size - ringStrokeWidth, ringFrom, ringTo);\n\n    // update progress bar\n    attr(root.ref.path, 'd', coordinates);\n\n    // hide while contains 0 value\n    attr(root.ref.path, 'stroke-opacity', props.spin || props.progress > 0 ? 1 : 0);\n};\n\nconst progressIndicator = createView({\n    tag: 'div',\n    name: 'progress-indicator',\n    ignoreRectUpdate: true,\n    ignoreRect: true,\n    create,\n    write,\n    mixins: {\n        apis: ['progress', 'spin', 'align'],\n        styles: ['opacity'],\n        animations: {\n            opacity: { type: 'tween', duration: 500 },\n            progress: {\n                type: 'spring',\n                stiffness: 0.95,\n                damping: 0.65,\n                mass: 10,\n            },\n        },\n    },\n});\n\nconst create$1 = ({ root, props }) => {\n    root.element.innerHTML = (props.icon || '') + `<span>${props.label}</span>`;\n\n    props.isDisabled = false;\n};\n\nconst write$1 = ({ root, props }) => {\n    const { isDisabled } = props;\n    const shouldDisable = root.query('GET_DISABLED') || props.opacity === 0;\n\n    if (shouldDisable && !isDisabled) {\n        props.isDisabled = true;\n        attr(root.element, 'disabled', 'disabled');\n    } else if (!shouldDisable && isDisabled) {\n        props.isDisabled = false;\n        root.element.removeAttribute('disabled');\n    }\n};\n\nconst fileActionButton = createView({\n    tag: 'button',\n    attributes: {\n        type: 'button',\n    },\n    ignoreRect: true,\n    ignoreRectUpdate: true,\n    name: 'file-action-button',\n    mixins: {\n        apis: ['label'],\n        styles: ['translateX', 'translateY', 'scaleX', 'scaleY', 'opacity'],\n        animations: {\n            scaleX: 'spring',\n            scaleY: 'spring',\n            translateX: 'spring',\n            translateY: 'spring',\n            opacity: { type: 'tween', duration: 250 },\n        },\n        listeners: true,\n    },\n    create: create$1,\n    write: write$1,\n});\n\nconst toNaturalFileSize = (bytes, decimalSeparator = '.', base = 1000, options = {}) => {\n    const {\n        labelBytes = 'bytes',\n        labelKilobytes = 'KB',\n        labelMegabytes = 'MB',\n        labelGigabytes = 'GB',\n    } = options;\n\n    // no negative byte sizes\n    bytes = Math.round(Math.abs(bytes));\n\n    const KB = base;\n    const MB = base * base;\n    const GB = base * base * base;\n\n    // just bytes\n    if (bytes < KB) {\n        return `${bytes} ${labelBytes}`;\n    }\n\n    // kilobytes\n    if (bytes < MB) {\n        return `${Math.floor(bytes / KB)} ${labelKilobytes}`;\n    }\n\n    // megabytes\n    if (bytes < GB) {\n        return `${removeDecimalsWhenZero(bytes / MB, 1, decimalSeparator)} ${labelMegabytes}`;\n    }\n\n    // gigabytes\n    return `${removeDecimalsWhenZero(bytes / GB, 2, decimalSeparator)} ${labelGigabytes}`;\n};\n\nconst removeDecimalsWhenZero = (value, decimalCount, separator) => {\n    return value\n        .toFixed(decimalCount)\n        .split('.')\n        .filter(part => part !== '0')\n        .join(separator);\n};\n\nconst create$2 = ({ root, props }) => {\n    // filename\n    const fileName = createElement$1('span');\n    fileName.className = 'filepond--file-info-main';\n    // hide for screenreaders\n    // the file is contained in a fieldset with legend that contains the filename\n    // no need to read it twice\n    attr(fileName, 'aria-hidden', 'true');\n    root.appendChild(fileName);\n    root.ref.fileName = fileName;\n\n    // filesize\n    const fileSize = createElement$1('span');\n    fileSize.className = 'filepond--file-info-sub';\n    root.appendChild(fileSize);\n    root.ref.fileSize = fileSize;\n\n    // set initial values\n    text(fileSize, root.query('GET_LABEL_FILE_WAITING_FOR_SIZE'));\n    text(fileName, formatFilename(root.query('GET_ITEM_NAME', props.id)));\n};\n\nconst updateFile = ({ root, props }) => {\n    text(\n        root.ref.fileSize,\n        toNaturalFileSize(\n            root.query('GET_ITEM_SIZE', props.id),\n            '.',\n            root.query('GET_FILE_SIZE_BASE'),\n            root.query('GET_FILE_SIZE_LABELS', root.query)\n        )\n    );\n    text(root.ref.fileName, formatFilename(root.query('GET_ITEM_NAME', props.id)));\n};\n\nconst updateFileSizeOnError = ({ root, props }) => {\n    // if size is available don't fallback to unknown size message\n    if (isInt(root.query('GET_ITEM_SIZE', props.id))) {\n        updateFile({ root, props });\n        return;\n    }\n\n    text(root.ref.fileSize, root.query('GET_LABEL_FILE_SIZE_NOT_AVAILABLE'));\n};\n\nconst fileInfo = createView({\n    name: 'file-info',\n    ignoreRect: true,\n    ignoreRectUpdate: true,\n    write: createRoute({\n        DID_LOAD_ITEM: updateFile,\n        DID_UPDATE_ITEM_META: updateFile,\n        DID_THROW_ITEM_LOAD_ERROR: updateFileSizeOnError,\n        DID_THROW_ITEM_INVALID: updateFileSizeOnError,\n    }),\n    didCreateView: root => {\n        applyFilters('CREATE_VIEW', { ...root, view: root });\n    },\n    create: create$2,\n    mixins: {\n        styles: ['translateX', 'translateY'],\n        animations: {\n            translateX: 'spring',\n            translateY: 'spring',\n        },\n    },\n});\n\nconst toPercentage = value => Math.round(value * 100);\n\nconst create$3 = ({ root }) => {\n    // main status\n    const main = createElement$1('span');\n    main.className = 'filepond--file-status-main';\n    root.appendChild(main);\n    root.ref.main = main;\n\n    // sub status\n    const sub = createElement$1('span');\n    sub.className = 'filepond--file-status-sub';\n    root.appendChild(sub);\n    root.ref.sub = sub;\n\n    didSetItemLoadProgress({ root, action: { progress: null } });\n};\n\nconst didSetItemLoadProgress = ({ root, action }) => {\n    const title =\n        action.progress === null\n            ? root.query('GET_LABEL_FILE_LOADING')\n            : `${root.query('GET_LABEL_FILE_LOADING')} ${toPercentage(action.progress)}%`;\n    text(root.ref.main, title);\n    text(root.ref.sub, root.query('GET_LABEL_TAP_TO_CANCEL'));\n};\n\nconst didSetItemProcessProgress = ({ root, action }) => {\n    const title =\n        action.progress === null\n            ? root.query('GET_LABEL_FILE_PROCESSING')\n            : `${root.query('GET_LABEL_FILE_PROCESSING')} ${toPercentage(action.progress)}%`;\n    text(root.ref.main, title);\n    text(root.ref.sub, root.query('GET_LABEL_TAP_TO_CANCEL'));\n};\n\nconst didRequestItemProcessing = ({ root }) => {\n    text(root.ref.main, root.query('GET_LABEL_FILE_PROCESSING'));\n    text(root.ref.sub, root.query('GET_LABEL_TAP_TO_CANCEL'));\n};\n\nconst didAbortItemProcessing = ({ root }) => {\n    text(root.ref.main, root.query('GET_LABEL_FILE_PROCESSING_ABORTED'));\n    text(root.ref.sub, root.query('GET_LABEL_TAP_TO_RETRY'));\n};\n\nconst didCompleteItemProcessing = ({ root }) => {\n    text(root.ref.main, root.query('GET_LABEL_FILE_PROCESSING_COMPLETE'));\n    text(root.ref.sub, root.query('GET_LABEL_TAP_TO_UNDO'));\n};\n\nconst clear = ({ root }) => {\n    text(root.ref.main, '');\n    text(root.ref.sub, '');\n};\n\nconst error = ({ root, action }) => {\n    text(root.ref.main, action.status.main);\n    text(root.ref.sub, action.status.sub);\n};\n\nconst fileStatus = createView({\n    name: 'file-status',\n    ignoreRect: true,\n    ignoreRectUpdate: true,\n    write: createRoute({\n        DID_LOAD_ITEM: clear,\n        DID_REVERT_ITEM_PROCESSING: clear,\n        DID_REQUEST_ITEM_PROCESSING: didRequestItemProcessing,\n        DID_ABORT_ITEM_PROCESSING: didAbortItemProcessing,\n        DID_COMPLETE_ITEM_PROCESSING: didCompleteItemProcessing,\n        DID_UPDATE_ITEM_PROCESS_PROGRESS: didSetItemProcessProgress,\n        DID_UPDATE_ITEM_LOAD_PROGRESS: didSetItemLoadProgress,\n        DID_THROW_ITEM_LOAD_ERROR: error,\n        DID_THROW_ITEM_INVALID: error,\n        DID_THROW_ITEM_PROCESSING_ERROR: error,\n        DID_THROW_ITEM_PROCESSING_REVERT_ERROR: error,\n        DID_THROW_ITEM_REMOVE_ERROR: error,\n    }),\n    didCreateView: root => {\n        applyFilters('CREATE_VIEW', { ...root, view: root });\n    },\n    create: create$3,\n    mixins: {\n        styles: ['translateX', 'translateY', 'opacity'],\n        animations: {\n            opacity: { type: 'tween', duration: 250 },\n            translateX: 'spring',\n            translateY: 'spring',\n        },\n    },\n});\n\n/**\n * Button definitions for the file view\n */\n\nconst Buttons = {\n    AbortItemLoad: {\n        label: 'GET_LABEL_BUTTON_ABORT_ITEM_LOAD',\n        action: 'ABORT_ITEM_LOAD',\n        className: 'filepond--action-abort-item-load',\n        align: 'LOAD_INDICATOR_POSITION', // right\n    },\n    RetryItemLoad: {\n        label: 'GET_LABEL_BUTTON_RETRY_ITEM_LOAD',\n        action: 'RETRY_ITEM_LOAD',\n        icon: 'GET_ICON_RETRY',\n        className: 'filepond--action-retry-item-load',\n        align: 'BUTTON_PROCESS_ITEM_POSITION', // right\n    },\n    RemoveItem: {\n        label: 'GET_LABEL_BUTTON_REMOVE_ITEM',\n        action: 'REQUEST_REMOVE_ITEM',\n        icon: 'GET_ICON_REMOVE',\n        className: 'filepond--action-remove-item',\n        align: 'BUTTON_REMOVE_ITEM_POSITION', // left\n    },\n    ProcessItem: {\n        label: 'GET_LABEL_BUTTON_PROCESS_ITEM',\n        action: 'REQUEST_ITEM_PROCESSING',\n        icon: 'GET_ICON_PROCESS',\n        className: 'filepond--action-process-item',\n        align: 'BUTTON_PROCESS_ITEM_POSITION', // right\n    },\n    AbortItemProcessing: {\n        label: 'GET_LABEL_BUTTON_ABORT_ITEM_PROCESSING',\n        action: 'ABORT_ITEM_PROCESSING',\n        className: 'filepond--action-abort-item-processing',\n        align: 'BUTTON_PROCESS_ITEM_POSITION', // right\n    },\n    RetryItemProcessing: {\n        label: 'GET_LABEL_BUTTON_RETRY_ITEM_PROCESSING',\n        action: 'RETRY_ITEM_PROCESSING',\n        icon: 'GET_ICON_RETRY',\n        className: 'filepond--action-retry-item-processing',\n        align: 'BUTTON_PROCESS_ITEM_POSITION', // right\n    },\n    RevertItemProcessing: {\n        label: 'GET_LABEL_BUTTON_UNDO_ITEM_PROCESSING',\n        action: 'REQUEST_REVERT_ITEM_PROCESSING',\n        icon: 'GET_ICON_UNDO',\n        className: 'filepond--action-revert-item-processing',\n        align: 'BUTTON_PROCESS_ITEM_POSITION', // right\n    },\n};\n\n// make a list of buttons, we can then remove buttons from this list if they're disabled\nconst ButtonKeys = [];\nforin(Buttons, key => {\n    ButtonKeys.push(key);\n});\n\nconst calculateFileInfoOffset = root => {\n    if (getRemoveIndicatorAligment(root) === 'right') return 0;\n    const buttonRect = root.ref.buttonRemoveItem.rect.element;\n    return buttonRect.hidden ? null : buttonRect.width + buttonRect.left;\n};\n\nconst calculateButtonWidth = root => {\n    const buttonRect = root.ref.buttonAbortItemLoad.rect.element;\n    return buttonRect.width;\n};\n\n// Force on full pixels so text stays crips\nconst calculateFileVerticalCenterOffset = root =>\n    Math.floor(root.ref.buttonRemoveItem.rect.element.height / 4);\nconst calculateFileHorizontalCenterOffset = root =>\n    Math.floor(root.ref.buttonRemoveItem.rect.element.left / 2);\n\nconst getLoadIndicatorAlignment = root => root.query('GET_STYLE_LOAD_INDICATOR_POSITION');\nconst getProcessIndicatorAlignment = root => root.query('GET_STYLE_PROGRESS_INDICATOR_POSITION');\nconst getRemoveIndicatorAligment = root => root.query('GET_STYLE_BUTTON_REMOVE_ITEM_POSITION');\n\nconst DefaultStyle = {\n    buttonAbortItemLoad: { opacity: 0 },\n    buttonRetryItemLoad: { opacity: 0 },\n    buttonRemoveItem: { opacity: 0 },\n    buttonProcessItem: { opacity: 0 },\n    buttonAbortItemProcessing: { opacity: 0 },\n    buttonRetryItemProcessing: { opacity: 0 },\n    buttonRevertItemProcessing: { opacity: 0 },\n    loadProgressIndicator: { opacity: 0, align: getLoadIndicatorAlignment },\n    processProgressIndicator: { opacity: 0, align: getProcessIndicatorAlignment },\n    processingCompleteIndicator: { opacity: 0, scaleX: 0.75, scaleY: 0.75 },\n    info: { translateX: 0, translateY: 0, opacity: 0 },\n    status: { translateX: 0, translateY: 0, opacity: 0 },\n};\n\nconst IdleStyle = {\n    buttonRemoveItem: { opacity: 1 },\n    buttonProcessItem: { opacity: 1 },\n    info: { translateX: calculateFileInfoOffset },\n    status: { translateX: calculateFileInfoOffset },\n};\n\nconst ProcessingStyle = {\n    buttonAbortItemProcessing: { opacity: 1 },\n    processProgressIndicator: { opacity: 1 },\n    status: { opacity: 1 },\n};\n\nconst StyleMap = {\n    DID_THROW_ITEM_INVALID: {\n        buttonRemoveItem: { opacity: 1 },\n        info: { translateX: calculateFileInfoOffset },\n        status: { translateX: calculateFileInfoOffset, opacity: 1 },\n    },\n    DID_START_ITEM_LOAD: {\n        buttonAbortItemLoad: { opacity: 1 },\n        loadProgressIndicator: { opacity: 1 },\n        status: { opacity: 1 },\n    },\n    DID_THROW_ITEM_LOAD_ERROR: {\n        buttonRetryItemLoad: { opacity: 1 },\n        buttonRemoveItem: { opacity: 1 },\n        info: { translateX: calculateFileInfoOffset },\n        status: { opacity: 1 },\n    },\n    DID_START_ITEM_REMOVE: {\n        processProgressIndicator: { opacity: 1, align: getRemoveIndicatorAligment },\n        info: { translateX: calculateFileInfoOffset },\n        status: { opacity: 0 },\n    },\n    DID_THROW_ITEM_REMOVE_ERROR: {\n        processProgressIndicator: { opacity: 0, align: getRemoveIndicatorAligment },\n        buttonRemoveItem: { opacity: 1 },\n        info: { translateX: calculateFileInfoOffset },\n        status: { opacity: 1, translateX: calculateFileInfoOffset },\n    },\n    DID_LOAD_ITEM: IdleStyle,\n    DID_LOAD_LOCAL_ITEM: {\n        buttonRemoveItem: { opacity: 1 },\n        info: { translateX: calculateFileInfoOffset },\n        status: { translateX: calculateFileInfoOffset },\n    },\n    DID_START_ITEM_PROCESSING: ProcessingStyle,\n    DID_REQUEST_ITEM_PROCESSING: ProcessingStyle,\n    DID_UPDATE_ITEM_PROCESS_PROGRESS: ProcessingStyle,\n    DID_COMPLETE_ITEM_PROCESSING: {\n        buttonRevertItemProcessing: { opacity: 1 },\n        info: { opacity: 1 },\n        status: { opacity: 1 },\n    },\n    DID_THROW_ITEM_PROCESSING_ERROR: {\n        buttonRemoveItem: { opacity: 1 },\n        buttonRetryItemProcessing: { opacity: 1 },\n        status: { opacity: 1 },\n        info: { translateX: calculateFileInfoOffset },\n    },\n    DID_THROW_ITEM_PROCESSING_REVERT_ERROR: {\n        buttonRevertItemProcessing: { opacity: 1 },\n        status: { opacity: 1 },\n        info: { opacity: 1 },\n    },\n    DID_ABORT_ITEM_PROCESSING: {\n        buttonRemoveItem: { opacity: 1 },\n        buttonProcessItem: { opacity: 1 },\n        info: { translateX: calculateFileInfoOffset },\n        status: { opacity: 1 },\n    },\n    DID_REVERT_ITEM_PROCESSING: IdleStyle,\n};\n\n// complete indicator view\nconst processingCompleteIndicatorView = createView({\n    create: ({ root }) => {\n        root.element.innerHTML = root.query('GET_ICON_DONE');\n    },\n    name: 'processing-complete-indicator',\n    ignoreRect: true,\n    mixins: {\n        styles: ['scaleX', 'scaleY', 'opacity'],\n        animations: {\n            scaleX: 'spring',\n            scaleY: 'spring',\n            opacity: { type: 'tween', duration: 250 },\n        },\n    },\n});\n\n/**\n * Creates the file view\n */\nconst create$4 = ({ root, props }) => {\n    // copy Buttons object\n    const LocalButtons = Object.keys(Buttons).reduce((prev, curr) => {\n        prev[curr] = { ...Buttons[curr] };\n        return prev;\n    }, {});\n\n    const { id } = props;\n\n    // allow reverting upload\n    const allowRevert = root.query('GET_ALLOW_REVERT');\n\n    // allow remove file\n    const allowRemove = root.query('GET_ALLOW_REMOVE');\n\n    // allow processing upload\n    const allowProcess = root.query('GET_ALLOW_PROCESS');\n\n    // is instant uploading, need this to determine the icon of the undo button\n    const instantUpload = root.query('GET_INSTANT_UPLOAD');\n\n    // is async set up\n    const isAsync = root.query('IS_ASYNC');\n\n    // should align remove item buttons\n    const alignRemoveItemButton = root.query('GET_STYLE_BUTTON_REMOVE_ITEM_ALIGN');\n\n    // enabled buttons array\n    let buttonFilter;\n    if (isAsync) {\n        if (allowProcess && !allowRevert) {\n            // only remove revert button\n            buttonFilter = key => !/RevertItemProcessing/.test(key);\n        } else if (!allowProcess && allowRevert) {\n            // only remove process button\n            buttonFilter = key => !/ProcessItem|RetryItemProcessing|AbortItemProcessing/.test(key);\n        } else if (!allowProcess && !allowRevert) {\n            // remove all process buttons\n            buttonFilter = key => !/Process/.test(key);\n        }\n    } else {\n        // no process controls available\n        buttonFilter = key => !/Process/.test(key);\n    }\n\n    const enabledButtons = buttonFilter ? ButtonKeys.filter(buttonFilter) : ButtonKeys.concat();\n\n    // update icon and label for revert button when instant uploading\n    if (instantUpload && allowRevert) {\n        LocalButtons['RevertItemProcessing'].label = 'GET_LABEL_BUTTON_REMOVE_ITEM';\n        LocalButtons['RevertItemProcessing'].icon = 'GET_ICON_REMOVE';\n    }\n\n    // remove last button (revert) if not allowed\n    if (isAsync && !allowRevert) {\n        const map = StyleMap['DID_COMPLETE_ITEM_PROCESSING'];\n        map.info.translateX = calculateFileHorizontalCenterOffset;\n        map.info.translateY = calculateFileVerticalCenterOffset;\n        map.status.translateY = calculateFileVerticalCenterOffset;\n        map.processingCompleteIndicator = { opacity: 1, scaleX: 1, scaleY: 1 };\n    }\n\n    // should align center\n    if (isAsync && !allowProcess) {\n        [\n            'DID_START_ITEM_PROCESSING',\n            'DID_REQUEST_ITEM_PROCESSING',\n            'DID_UPDATE_ITEM_PROCESS_PROGRESS',\n            'DID_THROW_ITEM_PROCESSING_ERROR',\n        ].forEach(key => {\n            StyleMap[key].status.translateY = calculateFileVerticalCenterOffset;\n        });\n        StyleMap['DID_THROW_ITEM_PROCESSING_ERROR'].status.translateX = calculateButtonWidth;\n    }\n\n    // move remove button to right\n    if (alignRemoveItemButton && allowRevert) {\n        LocalButtons['RevertItemProcessing'].align = 'BUTTON_REMOVE_ITEM_POSITION';\n        const map = StyleMap['DID_COMPLETE_ITEM_PROCESSING'];\n        map.info.translateX = calculateFileInfoOffset;\n        map.status.translateY = calculateFileVerticalCenterOffset;\n        map.processingCompleteIndicator = { opacity: 1, scaleX: 1, scaleY: 1 };\n    }\n\n    // show/hide RemoveItem button\n    if (!allowRemove) {\n        LocalButtons['RemoveItem'].disabled = true;\n    }\n\n    // create the button views\n    forin(LocalButtons, (key, definition) => {\n        // create button\n        const buttonView = root.createChildView(fileActionButton, {\n            label: root.query(definition.label),\n            icon: root.query(definition.icon),\n            opacity: 0,\n        });\n\n        // should be appended?\n        if (enabledButtons.includes(key)) {\n            root.appendChildView(buttonView);\n        }\n\n        // toggle\n        if (definition.disabled) {\n            buttonView.element.setAttribute('disabled', 'disabled');\n            buttonView.element.setAttribute('hidden', 'hidden');\n        }\n\n        // add position attribute\n        buttonView.element.dataset.align = root.query(`GET_STYLE_${definition.align}`);\n\n        // add class\n        buttonView.element.classList.add(definition.className);\n\n        // handle interactions\n        buttonView.on('click', e => {\n            e.stopPropagation();\n            if (definition.disabled) return;\n            root.dispatch(definition.action, { query: id });\n        });\n\n        // set reference\n        root.ref[`button${key}`] = buttonView;\n    });\n\n    // checkmark\n    root.ref.processingCompleteIndicator = root.appendChildView(\n        root.createChildView(processingCompleteIndicatorView)\n    );\n    root.ref.processingCompleteIndicator.element.dataset.align = root.query(\n        `GET_STYLE_BUTTON_PROCESS_ITEM_POSITION`\n    );\n\n    // create file info view\n    root.ref.info = root.appendChildView(root.createChildView(fileInfo, { id }));\n\n    // create file status view\n    root.ref.status = root.appendChildView(root.createChildView(fileStatus, { id }));\n\n    // add progress indicators\n    const loadIndicatorView = root.appendChildView(\n        root.createChildView(progressIndicator, {\n            opacity: 0,\n            align: root.query(`GET_STYLE_LOAD_INDICATOR_POSITION`),\n        })\n    );\n    loadIndicatorView.element.classList.add('filepond--load-indicator');\n    root.ref.loadProgressIndicator = loadIndicatorView;\n\n    const progressIndicatorView = root.appendChildView(\n        root.createChildView(progressIndicator, {\n            opacity: 0,\n            align: root.query(`GET_STYLE_PROGRESS_INDICATOR_POSITION`),\n        })\n    );\n    progressIndicatorView.element.classList.add('filepond--process-indicator');\n    root.ref.processProgressIndicator = progressIndicatorView;\n\n    // current active styles\n    root.ref.activeStyles = [];\n};\n\nconst write$2 = ({ root, actions, props }) => {\n    // route actions\n    route({ root, actions, props });\n\n    // select last state change action\n    let action = actions\n        .concat()\n        .filter(action => /^DID_/.test(action.type))\n        .reverse()\n        .find(action => StyleMap[action.type]);\n\n    // a new action happened, let's get the matching styles\n    if (action) {\n        // define new active styles\n        root.ref.activeStyles = [];\n\n        const stylesToApply = StyleMap[action.type];\n        forin(DefaultStyle, (name, defaultStyles) => {\n            // get reference to control\n            const control = root.ref[name];\n\n            // loop over all styles for this control\n            forin(defaultStyles, (key, defaultValue) => {\n                const value =\n                    stylesToApply[name] && typeof stylesToApply[name][key] !== 'undefined'\n                        ? stylesToApply[name][key]\n                        : defaultValue;\n                root.ref.activeStyles.push({ control, key, value });\n            });\n        });\n    }\n\n    // apply active styles to element\n    root.ref.activeStyles.forEach(({ control, key, value }) => {\n        control[key] = typeof value === 'function' ? value(root) : value;\n    });\n};\n\nconst route = createRoute({\n    DID_SET_LABEL_BUTTON_ABORT_ITEM_PROCESSING: ({ root, action }) => {\n        root.ref.buttonAbortItemProcessing.label = action.value;\n    },\n    DID_SET_LABEL_BUTTON_ABORT_ITEM_LOAD: ({ root, action }) => {\n        root.ref.buttonAbortItemLoad.label = action.value;\n    },\n    DID_SET_LABEL_BUTTON_ABORT_ITEM_REMOVAL: ({ root, action }) => {\n        root.ref.buttonAbortItemRemoval.label = action.value;\n    },\n    DID_REQUEST_ITEM_PROCESSING: ({ root }) => {\n        root.ref.processProgressIndicator.spin = true;\n        root.ref.processProgressIndicator.progress = 0;\n    },\n    DID_START_ITEM_LOAD: ({ root }) => {\n        root.ref.loadProgressIndicator.spin = true;\n        root.ref.loadProgressIndicator.progress = 0;\n    },\n    DID_START_ITEM_REMOVE: ({ root }) => {\n        root.ref.processProgressIndicator.spin = true;\n        root.ref.processProgressIndicator.progress = 0;\n    },\n    DID_UPDATE_ITEM_LOAD_PROGRESS: ({ root, action }) => {\n        root.ref.loadProgressIndicator.spin = false;\n        root.ref.loadProgressIndicator.progress = action.progress;\n    },\n    DID_UPDATE_ITEM_PROCESS_PROGRESS: ({ root, action }) => {\n        root.ref.processProgressIndicator.spin = false;\n        root.ref.processProgressIndicator.progress = action.progress;\n    },\n});\n\nconst file = createView({\n    create: create$4,\n    write: write$2,\n    didCreateView: root => {\n        applyFilters('CREATE_VIEW', { ...root, view: root });\n    },\n    name: 'file',\n});\n\n/**\n * Creates the file view\n */\nconst create$5 = ({ root, props }) => {\n    // filename\n    root.ref.fileName = createElement$1('legend');\n    root.appendChild(root.ref.fileName);\n\n    // file appended\n    root.ref.file = root.appendChildView(root.createChildView(file, { id: props.id }));\n\n    // data has moved to data.js\n    root.ref.data = false;\n};\n\n/**\n * Data storage\n */\nconst didLoadItem = ({ root, props }) => {\n    // updates the legend of the fieldset so screenreaders can better group buttons\n    text(root.ref.fileName, formatFilename(root.query('GET_ITEM_NAME', props.id)));\n};\n\nconst fileWrapper = createView({\n    create: create$5,\n    ignoreRect: true,\n    write: createRoute({\n        DID_LOAD_ITEM: didLoadItem,\n    }),\n    didCreateView: root => {\n        applyFilters('CREATE_VIEW', { ...root, view: root });\n    },\n    tag: 'fieldset',\n    name: 'file-wrapper',\n});\n\nconst PANEL_SPRING_PROPS = { type: 'spring', damping: 0.6, mass: 7 };\n\nconst create$6 = ({ root, props }) => {\n    [\n        {\n            name: 'top',\n        },\n        {\n            name: 'center',\n            props: {\n                translateY: null,\n                scaleY: null,\n            },\n            mixins: {\n                animations: {\n                    scaleY: PANEL_SPRING_PROPS,\n                },\n                styles: ['translateY', 'scaleY'],\n            },\n        },\n        {\n            name: 'bottom',\n            props: {\n                translateY: null,\n            },\n            mixins: {\n                animations: {\n                    translateY: PANEL_SPRING_PROPS,\n                },\n                styles: ['translateY'],\n            },\n        },\n    ].forEach(section => {\n        createSection(root, section, props.name);\n    });\n\n    root.element.classList.add(`filepond--${props.name}`);\n\n    root.ref.scalable = null;\n};\n\nconst createSection = (root, section, className) => {\n    const viewConstructor = createView({\n        name: `panel-${section.name} filepond--${className}`,\n        mixins: section.mixins,\n        ignoreRectUpdate: true,\n    });\n\n    const view = root.createChildView(viewConstructor, section.props);\n\n    root.ref[section.name] = root.appendChildView(view);\n};\n\nconst write$3 = ({ root, props }) => {\n    // update scalable state\n    if (root.ref.scalable === null || props.scalable !== root.ref.scalable) {\n        root.ref.scalable = isBoolean(props.scalable) ? props.scalable : true;\n        root.element.dataset.scalable = root.ref.scalable;\n    }\n\n    // no height, can't set\n    if (!props.height) return;\n\n    // get child rects\n    const topRect = root.ref.top.rect.element;\n    const bottomRect = root.ref.bottom.rect.element;\n\n    // make sure height never is smaller than bottom and top seciton heights combined (will probably never happen, but who knows)\n    const height = Math.max(topRect.height + bottomRect.height, props.height);\n\n    // offset center part\n    root.ref.center.translateY = topRect.height;\n\n    // scale center part\n    // use math ceil to prevent transparent lines because of rounding errors\n    root.ref.center.scaleY = (height - topRect.height - bottomRect.height) / 100;\n\n    // offset bottom part\n    root.ref.bottom.translateY = height - bottomRect.height;\n};\n\nconst panel = createView({\n    name: 'panel',\n    read: ({ root, props }) => (props.heightCurrent = root.ref.bottom.translateY),\n    write: write$3,\n    create: create$6,\n    ignoreRect: true,\n    mixins: {\n        apis: ['height', 'heightCurrent', 'scalable'],\n    },\n});\n\nconst createDragHelper = items => {\n    const itemIds = items.map(item => item.id);\n    let prevIndex = undefined;\n    return {\n        setIndex: index => {\n            prevIndex = index;\n        },\n        getIndex: () => prevIndex,\n        getItemIndex: item => itemIds.indexOf(item.id),\n    };\n};\n\nconst ITEM_TRANSLATE_SPRING = {\n    type: 'spring',\n    stiffness: 0.75,\n    damping: 0.45,\n    mass: 10,\n};\n\nconst ITEM_SCALE_SPRING = 'spring';\n\nconst StateMap = {\n    DID_START_ITEM_LOAD: 'busy',\n    DID_UPDATE_ITEM_LOAD_PROGRESS: 'loading',\n    DID_THROW_ITEM_INVALID: 'load-invalid',\n    DID_THROW_ITEM_LOAD_ERROR: 'load-error',\n    DID_LOAD_ITEM: 'idle',\n    DID_THROW_ITEM_REMOVE_ERROR: 'remove-error',\n    DID_START_ITEM_REMOVE: 'busy',\n    DID_START_ITEM_PROCESSING: 'busy processing',\n    DID_REQUEST_ITEM_PROCESSING: 'busy processing',\n    DID_UPDATE_ITEM_PROCESS_PROGRESS: 'processing',\n    DID_COMPLETE_ITEM_PROCESSING: 'processing-complete',\n    DID_THROW_ITEM_PROCESSING_ERROR: 'processing-error',\n    DID_THROW_ITEM_PROCESSING_REVERT_ERROR: 'processing-revert-error',\n    DID_ABORT_ITEM_PROCESSING: 'cancelled',\n    DID_REVERT_ITEM_PROCESSING: 'idle',\n};\n\n/**\n * Creates the file view\n */\nconst create$7 = ({ root, props }) => {\n    // select\n    root.ref.handleClick = e => root.dispatch('DID_ACTIVATE_ITEM', { id: props.id });\n\n    // set id\n    root.element.id = `filepond--item-${props.id}`;\n    root.element.addEventListener('click', root.ref.handleClick);\n\n    // file view\n    root.ref.container = root.appendChildView(root.createChildView(fileWrapper, { id: props.id }));\n\n    // file panel\n    root.ref.panel = root.appendChildView(root.createChildView(panel, { name: 'item-panel' }));\n\n    // default start height\n    root.ref.panel.height = null;\n\n    // by default not marked for removal\n    props.markedForRemoval = false;\n\n    // if not allowed to reorder file items, exit here\n    if (!root.query('GET_ALLOW_REORDER')) return;\n\n    // set to idle so shows grab cursor\n    root.element.dataset.dragState = 'idle';\n\n    const grab = e => {\n        if (!e.isPrimary) return;\n\n        let removedActivateListener = false;\n\n        const origin = {\n            x: e.pageX,\n            y: e.pageY,\n        };\n\n        props.dragOrigin = {\n            x: root.translateX,\n            y: root.translateY,\n        };\n\n        props.dragCenter = {\n            x: e.offsetX,\n            y: e.offsetY,\n        };\n\n        const dragState = createDragHelper(root.query('GET_ACTIVE_ITEMS'));\n\n        root.dispatch('DID_GRAB_ITEM', { id: props.id, dragState });\n\n        const drag = e => {\n            if (!e.isPrimary) return;\n\n            e.stopPropagation();\n            e.preventDefault();\n\n            props.dragOffset = {\n                x: e.pageX - origin.x,\n                y: e.pageY - origin.y,\n            };\n\n            // if dragged stop listening to clicks, will re-add when done dragging\n            const dist =\n                props.dragOffset.x * props.dragOffset.x + props.dragOffset.y * props.dragOffset.y;\n            if (dist > 16 && !removedActivateListener) {\n                removedActivateListener = true;\n                root.element.removeEventListener('click', root.ref.handleClick);\n            }\n\n            root.dispatch('DID_DRAG_ITEM', { id: props.id, dragState });\n        };\n\n        const drop = e => {\n            if (!e.isPrimary) return;\n\n            props.dragOffset = {\n                x: e.pageX - origin.x,\n                y: e.pageY - origin.y,\n            };\n\n            reset();\n        };\n\n        const cancel = () => {\n            reset();\n        };\n\n        const reset = () => {\n            document.removeEventListener('pointercancel', cancel);\n            document.removeEventListener('pointermove', drag);\n            document.removeEventListener('pointerup', drop);\n\n            root.dispatch('DID_DROP_ITEM', { id: props.id, dragState });\n\n            // start listening to clicks again\n            if (removedActivateListener) {\n                setTimeout(() => root.element.addEventListener('click', root.ref.handleClick), 0);\n            }\n        };\n\n        document.addEventListener('pointercancel', cancel);\n        document.addEventListener('pointermove', drag);\n        document.addEventListener('pointerup', drop);\n    };\n\n    root.element.addEventListener('pointerdown', grab);\n};\n\nconst route$1 = createRoute({\n    DID_UPDATE_PANEL_HEIGHT: ({ root, action }) => {\n        root.height = action.height;\n    },\n});\n\nconst write$4 = createRoute(\n    {\n        DID_GRAB_ITEM: ({ root, props }) => {\n            props.dragOrigin = {\n                x: root.translateX,\n                y: root.translateY,\n            };\n        },\n        DID_DRAG_ITEM: ({ root }) => {\n            root.element.dataset.dragState = 'drag';\n        },\n        DID_DROP_ITEM: ({ root, props }) => {\n            props.dragOffset = null;\n            props.dragOrigin = null;\n            root.element.dataset.dragState = 'drop';\n        },\n    },\n    ({ root, actions, props, shouldOptimize }) => {\n        if (root.element.dataset.dragState === 'drop') {\n            if (root.scaleX <= 1) {\n                root.element.dataset.dragState = 'idle';\n            }\n        }\n\n        // select last state change action\n        let action = actions\n            .concat()\n            .filter(action => /^DID_/.test(action.type))\n            .reverse()\n            .find(action => StateMap[action.type]);\n\n        // no need to set same state twice\n        if (action && action.type !== props.currentState) {\n            // set current state\n            props.currentState = action.type;\n\n            // set state\n            root.element.dataset.filepondItemState = StateMap[props.currentState] || '';\n        }\n\n        // route actions\n        const aspectRatio =\n            root.query('GET_ITEM_PANEL_ASPECT_RATIO') || root.query('GET_PANEL_ASPECT_RATIO');\n        if (!aspectRatio) {\n            route$1({ root, actions, props });\n            if (!root.height && root.ref.container.rect.element.height > 0) {\n                root.height = root.ref.container.rect.element.height;\n            }\n        } else if (!shouldOptimize) {\n            root.height = root.rect.element.width * aspectRatio;\n        }\n\n        // sync panel height with item height\n        if (shouldOptimize) {\n            root.ref.panel.height = null;\n        }\n\n        root.ref.panel.height = root.height;\n    }\n);\n\nconst item = createView({\n    create: create$7,\n    write: write$4,\n    destroy: ({ root, props }) => {\n        root.element.removeEventListener('click', root.ref.handleClick);\n        root.dispatch('RELEASE_ITEM', { query: props.id });\n    },\n    tag: 'li',\n    name: 'item',\n    mixins: {\n        apis: [\n            'id',\n            'interactionMethod',\n            'markedForRemoval',\n            'spawnDate',\n            'dragCenter',\n            'dragOrigin',\n            'dragOffset',\n        ],\n        styles: ['translateX', 'translateY', 'scaleX', 'scaleY', 'opacity', 'height'],\n        animations: {\n            scaleX: ITEM_SCALE_SPRING,\n            scaleY: ITEM_SCALE_SPRING,\n            translateX: ITEM_TRANSLATE_SPRING,\n            translateY: ITEM_TRANSLATE_SPRING,\n            opacity: { type: 'tween', duration: 150 },\n        },\n    },\n});\n\nvar getItemsPerRow = (horizontalSpace, itemWidth) => {\n    // add one pixel leeway, when using percentages for item width total items can be 1.99 per row\n\n    return Math.max(1, Math.floor((horizontalSpace + 1) / itemWidth));\n};\n\nconst getItemIndexByPosition = (view, children, positionInView) => {\n    if (!positionInView) return;\n\n    const horizontalSpace = view.rect.element.width;\n    // const children = view.childViews;\n    const l = children.length;\n    let last = null;\n\n    // -1, don't move items to accomodate (either add to top or bottom)\n    if (l === 0 || positionInView.top < children[0].rect.element.top) return -1;\n\n    // let's get the item width\n    const item = children[0];\n    const itemRect = item.rect.element;\n    const itemHorizontalMargin = itemRect.marginLeft + itemRect.marginRight;\n    const itemWidth = itemRect.width + itemHorizontalMargin;\n    const itemsPerRow = getItemsPerRow(horizontalSpace, itemWidth);\n\n    // stack\n    if (itemsPerRow === 1) {\n        for (let index = 0; index < l; index++) {\n            const child = children[index];\n            const childMid = child.rect.outer.top + child.rect.element.height * 0.5;\n            if (positionInView.top < childMid) {\n                return index;\n            }\n        }\n        return l;\n    }\n\n    // grid\n    const itemVerticalMargin = itemRect.marginTop + itemRect.marginBottom;\n    const itemHeight = itemRect.height + itemVerticalMargin;\n    for (let index = 0; index < l; index++) {\n        const indexX = index % itemsPerRow;\n        const indexY = Math.floor(index / itemsPerRow);\n\n        const offsetX = indexX * itemWidth;\n        const offsetY = indexY * itemHeight;\n\n        const itemTop = offsetY - itemRect.marginTop;\n        const itemRight = offsetX + itemWidth;\n        const itemBottom = offsetY + itemHeight + itemRect.marginBottom;\n\n        if (positionInView.top < itemBottom && positionInView.top > itemTop) {\n            if (positionInView.left < itemRight) {\n                return index;\n            } else if (index !== l - 1) {\n                last = index;\n            } else {\n                last = null;\n            }\n        }\n    }\n\n    if (last !== null) {\n        return last;\n    }\n\n    return l;\n};\n\nconst dropAreaDimensions = {\n    height: 0,\n    width: 0,\n    get getHeight() {\n        return this.height;\n    },\n    set setHeight(val) {\n        if (this.height === 0 || val === 0) this.height = val;\n    },\n    get getWidth() {\n        return this.width;\n    },\n    set setWidth(val) {\n        if (this.width === 0 || val === 0) this.width = val;\n    },\n    setDimensions: function(height, width) {\n        if (this.height === 0 || height === 0) this.height = height;\n        if (this.width === 0 || width === 0) this.width = width;\n    },\n};\n\nconst create$8 = ({ root }) => {\n    // need to set role to list as otherwise it won't be read as a list by VoiceOver\n    attr(root.element, 'role', 'list');\n\n    root.ref.lastItemSpanwDate = Date.now();\n};\n\n/**\n * Inserts a new item\n * @param root\n * @param action\n */\nconst addItemView = ({ root, action }) => {\n    const { id, index, interactionMethod } = action;\n\n    root.ref.addIndex = index;\n\n    const now = Date.now();\n    let spawnDate = now;\n    let opacity = 1;\n\n    if (interactionMethod !== InteractionMethod.NONE) {\n        opacity = 0;\n        const cooldown = root.query('GET_ITEM_INSERT_INTERVAL');\n        const dist = now - root.ref.lastItemSpanwDate;\n        spawnDate = dist < cooldown ? now + (cooldown - dist) : now;\n    }\n\n    root.ref.lastItemSpanwDate = spawnDate;\n\n    root.appendChildView(\n        root.createChildView(\n            // view type\n            item,\n\n            // props\n            {\n                spawnDate,\n                id,\n                opacity,\n                interactionMethod,\n            }\n        ),\n        index\n    );\n};\n\nconst moveItem = (item, x, y, vx = 0, vy = 1) => {\n    // set to null to remove animation while dragging\n    if (item.dragOffset) {\n        item.translateX = null;\n        item.translateY = null;\n        item.translateX = item.dragOrigin.x + item.dragOffset.x;\n        item.translateY = item.dragOrigin.y + item.dragOffset.y;\n        item.scaleX = 1.025;\n        item.scaleY = 1.025;\n    } else {\n        item.translateX = x;\n        item.translateY = y;\n\n        if (Date.now() > item.spawnDate) {\n            // reveal element\n            if (item.opacity === 0) {\n                introItemView(item, x, y, vx, vy);\n            }\n\n            // make sure is default scale every frame\n            item.scaleX = 1;\n            item.scaleY = 1;\n            item.opacity = 1;\n        }\n    }\n};\n\nconst introItemView = (item, x, y, vx, vy) => {\n    if (item.interactionMethod === InteractionMethod.NONE) {\n        item.translateX = null;\n        item.translateX = x;\n        item.translateY = null;\n        item.translateY = y;\n    } else if (item.interactionMethod === InteractionMethod.DROP) {\n        item.translateX = null;\n        item.translateX = x - vx * 20;\n\n        item.translateY = null;\n        item.translateY = y - vy * 10;\n\n        item.scaleX = 0.8;\n        item.scaleY = 0.8;\n    } else if (item.interactionMethod === InteractionMethod.BROWSE) {\n        item.translateY = null;\n        item.translateY = y - 30;\n    } else if (item.interactionMethod === InteractionMethod.API) {\n        item.translateX = null;\n        item.translateX = x - 30;\n        item.translateY = null;\n    }\n};\n\n/**\n * Removes an existing item\n * @param root\n * @param action\n */\nconst removeItemView = ({ root, action }) => {\n    const { id } = action;\n\n    // get the view matching the given id\n    const view = root.childViews.find(child => child.id === id);\n\n    // if no view found, exit\n    if (!view) {\n        return;\n    }\n\n    // animate view out of view\n    view.scaleX = 0.9;\n    view.scaleY = 0.9;\n    view.opacity = 0;\n\n    // mark for removal\n    view.markedForRemoval = true;\n};\n\nconst getItemHeight = child =>\n    child.rect.element.height +\n    child.rect.element.marginBottom * 0.5 +\n    child.rect.element.marginTop * 0.5;\nconst getItemWidth = child =>\n    child.rect.element.width +\n    child.rect.element.marginLeft * 0.5 +\n    child.rect.element.marginRight * 0.5;\n\nconst dragItem = ({ root, action }) => {\n    const { id, dragState } = action;\n\n    // reference to item\n    const item = root.query('GET_ITEM', { id });\n\n    // get the view matching the given id\n    const view = root.childViews.find(child => child.id === id);\n\n    const numItems = root.childViews.length;\n    const oldIndex = dragState.getItemIndex(item);\n\n    // if no view found, exit\n    if (!view) return;\n\n    const dragPosition = {\n        x: view.dragOrigin.x + view.dragOffset.x + view.dragCenter.x,\n        y: view.dragOrigin.y + view.dragOffset.y + view.dragCenter.y,\n    };\n\n    // get drag area dimensions\n    const dragHeight = getItemHeight(view);\n    const dragWidth = getItemWidth(view);\n\n    // get rows and columns (There will always be at least one row and one column if a file is present)\n    let cols = Math.floor(root.rect.outer.width / dragWidth);\n    if (cols > numItems) cols = numItems;\n\n    // rows are used to find when we have left the preview area bounding box\n    const rows = Math.floor(numItems / cols + 1);\n\n    dropAreaDimensions.setHeight = dragHeight * rows;\n    dropAreaDimensions.setWidth = dragWidth * cols;\n\n    // get new index of dragged item\n    var location = {\n        y: Math.floor(dragPosition.y / dragHeight),\n        x: Math.floor(dragPosition.x / dragWidth),\n        getGridIndex: function getGridIndex() {\n            if (\n                dragPosition.y > dropAreaDimensions.getHeight ||\n                dragPosition.y < 0 ||\n                dragPosition.x > dropAreaDimensions.getWidth ||\n                dragPosition.x < 0\n            )\n                return oldIndex;\n            return this.y * cols + this.x;\n        },\n        getColIndex: function getColIndex() {\n            const items = root.query('GET_ACTIVE_ITEMS');\n            const visibleChildren = root.childViews.filter(child => child.rect.element.height);\n            const children = items.map(item =>\n                visibleChildren.find(childView => childView.id === item.id)\n            );\n            const currentIndex = children.findIndex(child => child === view);\n            const dragHeight = getItemHeight(view);\n            const l = children.length;\n            let idx = l;\n            let childHeight = 0;\n            let childBottom = 0;\n            let childTop = 0;\n            for (let i = 0; i < l; i++) {\n                childHeight = getItemHeight(children[i]);\n                childTop = childBottom;\n                childBottom = childTop + childHeight;\n                if (dragPosition.y < childBottom) {\n                    if (currentIndex > i) {\n                        if (dragPosition.y < childTop + dragHeight) {\n                            idx = i;\n                            break;\n                        }\n                        continue;\n                    }\n                    idx = i;\n                    break;\n                }\n            }\n            return idx;\n        },\n    };\n\n    // get new index\n    const index = cols > 1 ? location.getGridIndex() : location.getColIndex();\n    root.dispatch('MOVE_ITEM', { query: view, index });\n\n    // if the index of the item changed, dispatch reorder action\n    const currentIndex = dragState.getIndex();\n\n    if (currentIndex === undefined || currentIndex !== index) {\n        dragState.setIndex(index);\n\n        if (currentIndex === undefined) return;\n\n        root.dispatch('DID_REORDER_ITEMS', {\n            items: root.query('GET_ACTIVE_ITEMS'),\n            origin: oldIndex,\n            target: index,\n        });\n    }\n};\n\n/**\n * Setup action routes\n */\nconst route$2 = createRoute({\n    DID_ADD_ITEM: addItemView,\n    DID_REMOVE_ITEM: removeItemView,\n    DID_DRAG_ITEM: dragItem,\n});\n\n/**\n * Write to view\n * @param root\n * @param actions\n * @param props\n */\nconst write$5 = ({ root, props, actions, shouldOptimize }) => {\n    // route actions\n    route$2({ root, props, actions });\n\n    const { dragCoordinates } = props;\n\n    // available space on horizontal axis\n    const horizontalSpace = root.rect.element.width;\n\n    // only draw children that have dimensions\n    const visibleChildren = root.childViews.filter(child => child.rect.element.height);\n\n    // sort based on current active items\n    const children = root\n        .query('GET_ACTIVE_ITEMS')\n        .map(item => visibleChildren.find(child => child.id === item.id))\n        .filter(item => item);\n\n    // get index\n    const dragIndex = dragCoordinates\n        ? getItemIndexByPosition(root, children, dragCoordinates)\n        : null;\n\n    // add index is used to reserve the dropped/added item index till the actual item is rendered\n    const addIndex = root.ref.addIndex || null;\n\n    // add index no longer needed till possibly next draw\n    root.ref.addIndex = null;\n\n    let dragIndexOffset = 0;\n    let removeIndexOffset = 0;\n    let addIndexOffset = 0;\n\n    if (children.length === 0) return;\n\n    const childRect = children[0].rect.element;\n    const itemVerticalMargin = childRect.marginTop + childRect.marginBottom;\n    const itemHorizontalMargin = childRect.marginLeft + childRect.marginRight;\n    const itemWidth = childRect.width + itemHorizontalMargin;\n    const itemHeight = childRect.height + itemVerticalMargin;\n    const itemsPerRow = getItemsPerRow(horizontalSpace, itemWidth);\n\n    // stack\n    if (itemsPerRow === 1) {\n        let offsetY = 0;\n        let dragOffset = 0;\n\n        children.forEach((child, index) => {\n            if (dragIndex) {\n                let dist = index - dragIndex;\n                if (dist === -2) {\n                    dragOffset = -itemVerticalMargin * 0.25;\n                } else if (dist === -1) {\n                    dragOffset = -itemVerticalMargin * 0.75;\n                } else if (dist === 0) {\n                    dragOffset = itemVerticalMargin * 0.75;\n                } else if (dist === 1) {\n                    dragOffset = itemVerticalMargin * 0.25;\n                } else {\n                    dragOffset = 0;\n                }\n            }\n\n            if (shouldOptimize) {\n                child.translateX = null;\n                child.translateY = null;\n            }\n\n            if (!child.markedForRemoval) {\n                moveItem(child, 0, offsetY + dragOffset);\n            }\n\n            let itemHeight = child.rect.element.height + itemVerticalMargin;\n\n            let visualHeight = itemHeight * (child.markedForRemoval ? child.opacity : 1);\n\n            offsetY += visualHeight;\n        });\n    }\n    // grid\n    else {\n        let prevX = 0;\n        let prevY = 0;\n\n        children.forEach((child, index) => {\n            if (index === dragIndex) {\n                dragIndexOffset = 1;\n            }\n\n            if (index === addIndex) {\n                addIndexOffset += 1;\n            }\n\n            if (child.markedForRemoval && child.opacity < 0.5) {\n                removeIndexOffset -= 1;\n            }\n\n            const visualIndex = index + addIndexOffset + dragIndexOffset + removeIndexOffset;\n\n            const indexX = visualIndex % itemsPerRow;\n            const indexY = Math.floor(visualIndex / itemsPerRow);\n\n            const offsetX = indexX * itemWidth;\n            const offsetY = indexY * itemHeight;\n\n            const vectorX = Math.sign(offsetX - prevX);\n            const vectorY = Math.sign(offsetY - prevY);\n\n            prevX = offsetX;\n            prevY = offsetY;\n\n            if (child.markedForRemoval) return;\n\n            if (shouldOptimize) {\n                child.translateX = null;\n                child.translateY = null;\n            }\n\n            moveItem(child, offsetX, offsetY, vectorX, vectorY);\n        });\n    }\n};\n\n/**\n * Filters actions that are meant specifically for a certain child of the list\n * @param child\n * @param actions\n */\nconst filterSetItemActions = (child, actions) =>\n    actions.filter(action => {\n        // if action has an id, filter out actions that don't have this child id\n        if (action.data && action.data.id) {\n            return child.id === action.data.id;\n        }\n\n        // allow all other actions\n        return true;\n    });\n\nconst list = createView({\n    create: create$8,\n    write: write$5,\n    tag: 'ul',\n    name: 'list',\n    didWriteView: ({ root }) => {\n        root.childViews\n            .filter(view => view.markedForRemoval && view.opacity === 0 && view.resting)\n            .forEach(view => {\n                view._destroy();\n                root.removeChildView(view);\n            });\n    },\n    filterFrameActionsForChild: filterSetItemActions,\n    mixins: {\n        apis: ['dragCoordinates'],\n    },\n});\n\nconst create$9 = ({ root, props }) => {\n    root.ref.list = root.appendChildView(root.createChildView(list));\n    props.dragCoordinates = null;\n    props.overflowing = false;\n};\n\nconst storeDragCoordinates = ({ root, props, action }) => {\n    if (!root.query('GET_ITEM_INSERT_LOCATION_FREEDOM')) return;\n    props.dragCoordinates = {\n        left: action.position.scopeLeft - root.ref.list.rect.element.left,\n        top:\n            action.position.scopeTop -\n            (root.rect.outer.top + root.rect.element.marginTop + root.rect.element.scrollTop),\n    };\n};\n\nconst clearDragCoordinates = ({ props }) => {\n    props.dragCoordinates = null;\n};\n\nconst route$3 = createRoute({\n    DID_DRAG: storeDragCoordinates,\n    DID_END_DRAG: clearDragCoordinates,\n});\n\nconst write$6 = ({ root, props, actions }) => {\n    // route actions\n    route$3({ root, props, actions });\n\n    // current drag position\n    root.ref.list.dragCoordinates = props.dragCoordinates;\n\n    // if currently overflowing but no longer received overflow\n    if (props.overflowing && !props.overflow) {\n        props.overflowing = false;\n\n        // reset overflow state\n        root.element.dataset.state = '';\n        root.height = null;\n    }\n\n    // if is not overflowing currently but does receive overflow value\n    if (props.overflow) {\n        const newHeight = Math.round(props.overflow);\n        if (newHeight !== root.height) {\n            props.overflowing = true;\n            root.element.dataset.state = 'overflow';\n            root.height = newHeight;\n        }\n    }\n};\n\nconst listScroller = createView({\n    create: create$9,\n    write: write$6,\n    name: 'list-scroller',\n    mixins: {\n        apis: ['overflow', 'dragCoordinates'],\n        styles: ['height', 'translateY'],\n        animations: {\n            translateY: 'spring',\n        },\n    },\n});\n\nconst attrToggle = (element, name, state, enabledValue = '') => {\n    if (state) {\n        attr(element, name, enabledValue);\n    } else {\n        element.removeAttribute(name);\n    }\n};\n\nconst resetFileInput = input => {\n    // no value, no need to reset\n    if (!input || input.value === '') {\n        return;\n    }\n\n    try {\n        // for modern browsers\n        input.value = '';\n    } catch (err) {}\n\n    // for IE10\n    if (input.value) {\n        // quickly append input to temp form and reset form\n        const form = createElement$1('form');\n        const parentNode = input.parentNode;\n        const ref = input.nextSibling;\n        form.appendChild(input);\n        form.reset();\n\n        // re-inject input where it originally was\n        if (ref) {\n            parentNode.insertBefore(input, ref);\n        } else {\n            parentNode.appendChild(input);\n        }\n    }\n};\n\nconst create$a = ({ root, props }) => {\n    // set id so can be referenced from outside labels\n    root.element.id = `filepond--browser-${props.id}`;\n\n    // set name of element (is removed when a value is set)\n    attr(root.element, 'name', root.query('GET_NAME'));\n\n    // we have to link this element to the status element\n    attr(root.element, 'aria-controls', `filepond--assistant-${props.id}`);\n\n    // set label, we use labelled by as otherwise the screenreader does not read the \"browse\" text in the label (as it has tabindex: 0)\n    attr(root.element, 'aria-labelledby', `filepond--drop-label-${props.id}`);\n\n    // set configurable props\n    setAcceptedFileTypes({ root, action: { value: root.query('GET_ACCEPTED_FILE_TYPES') } });\n    toggleAllowMultiple({ root, action: { value: root.query('GET_ALLOW_MULTIPLE') } });\n    toggleDirectoryFilter({ root, action: { value: root.query('GET_ALLOW_DIRECTORIES_ONLY') } });\n    toggleDisabled({ root });\n    toggleRequired({ root, action: { value: root.query('GET_REQUIRED') } });\n    setCaptureMethod({ root, action: { value: root.query('GET_CAPTURE_METHOD') } });\n\n    // handle changes to the input field\n    root.ref.handleChange = e => {\n        if (!root.element.value) {\n            return;\n        }\n\n        // extract files and move value of webkitRelativePath path to _relativePath\n        const files = Array.from(root.element.files).map(file => {\n            file._relativePath = file.webkitRelativePath;\n            return file;\n        });\n\n        // we add a little delay so the OS file select window can move out of the way before we add our file\n        setTimeout(() => {\n            // load files\n            props.onload(files);\n\n            // reset input, it's just for exposing a method to drop files, should not retain any state\n            resetFileInput(root.element);\n        }, 250);\n    };\n\n    root.element.addEventListener('change', root.ref.handleChange);\n};\n\nconst setAcceptedFileTypes = ({ root, action }) => {\n    if (!root.query('GET_ALLOW_SYNC_ACCEPT_ATTRIBUTE')) return;\n    attrToggle(root.element, 'accept', !!action.value, action.value ? action.value.join(',') : '');\n};\n\nconst toggleAllowMultiple = ({ root, action }) => {\n    attrToggle(root.element, 'multiple', action.value);\n};\n\nconst toggleDirectoryFilter = ({ root, action }) => {\n    attrToggle(root.element, 'webkitdirectory', action.value);\n};\n\nconst toggleDisabled = ({ root }) => {\n    const isDisabled = root.query('GET_DISABLED');\n    const doesAllowBrowse = root.query('GET_ALLOW_BROWSE');\n    const disableField = isDisabled || !doesAllowBrowse;\n    attrToggle(root.element, 'disabled', disableField);\n};\n\nconst toggleRequired = ({ root, action }) => {\n    // want to remove required, always possible\n    if (!action.value) {\n        attrToggle(root.element, 'required', false);\n    }\n    // if want to make required, only possible when zero items\n    else if (root.query('GET_TOTAL_ITEMS') === 0) {\n        attrToggle(root.element, 'required', true);\n    }\n};\n\nconst setCaptureMethod = ({ root, action }) => {\n    attrToggle(root.element, 'capture', !!action.value, action.value === true ? '' : action.value);\n};\n\nconst updateRequiredStatus = ({ root }) => {\n    const { element } = root;\n    // always remove the required attribute when more than zero items\n    if (root.query('GET_TOTAL_ITEMS') > 0) {\n        attrToggle(element, 'required', false);\n        attrToggle(element, 'name', false);\n\n        // still has items\n        const activeItems = root.query('GET_ACTIVE_ITEMS');\n        let hasInvalidField = false;\n        for (let i = 0; i < activeItems.length; i++) {\n            if (activeItems[i].status === ItemStatus.LOAD_ERROR) {\n                hasInvalidField = true;\n            }\n        }\n        // set validity status\n        root.element.setCustomValidity(\n            hasInvalidField ? root.query('GET_LABEL_INVALID_FIELD') : ''\n        );\n    } else {\n        // add name attribute\n        attrToggle(element, 'name', true, root.query('GET_NAME'));\n\n        // remove any validation messages\n        const shouldCheckValidity = root.query('GET_CHECK_VALIDITY');\n        if (shouldCheckValidity) {\n            element.setCustomValidity('');\n        }\n\n        // we only add required if the field has been deemed required\n        if (root.query('GET_REQUIRED')) {\n            attrToggle(element, 'required', true);\n        }\n    }\n};\n\nconst updateFieldValidityStatus = ({ root }) => {\n    const shouldCheckValidity = root.query('GET_CHECK_VALIDITY');\n    if (!shouldCheckValidity) return;\n    root.element.setCustomValidity(root.query('GET_LABEL_INVALID_FIELD'));\n};\n\nconst browser = createView({\n    tag: 'input',\n    name: 'browser',\n    ignoreRect: true,\n    ignoreRectUpdate: true,\n    attributes: {\n        type: 'file',\n    },\n    create: create$a,\n    destroy: ({ root }) => {\n        root.element.removeEventListener('change', root.ref.handleChange);\n    },\n    write: createRoute({\n        DID_LOAD_ITEM: updateRequiredStatus,\n        DID_REMOVE_ITEM: updateRequiredStatus,\n        DID_THROW_ITEM_INVALID: updateFieldValidityStatus,\n\n        DID_SET_DISABLED: toggleDisabled,\n        DID_SET_ALLOW_BROWSE: toggleDisabled,\n        DID_SET_ALLOW_DIRECTORIES_ONLY: toggleDirectoryFilter,\n        DID_SET_ALLOW_MULTIPLE: toggleAllowMultiple,\n        DID_SET_ACCEPTED_FILE_TYPES: setAcceptedFileTypes,\n        DID_SET_CAPTURE_METHOD: setCaptureMethod,\n        DID_SET_REQUIRED: toggleRequired,\n    }),\n});\n\nconst Key = {\n    ENTER: 13,\n    SPACE: 32,\n};\n\nconst create$b = ({ root, props }) => {\n    // create the label and link it to the file browser\n    const label = createElement$1('label');\n    attr(label, 'for', `filepond--browser-${props.id}`);\n\n    // use for labeling file input (aria-labelledby on file input)\n    attr(label, 'id', `filepond--drop-label-${props.id}`);\n\n    // handle keys\n    root.ref.handleKeyDown = e => {\n        const isActivationKey = e.keyCode === Key.ENTER || e.keyCode === Key.SPACE;\n        if (!isActivationKey) return;\n        // stops from triggering the element a second time\n        e.preventDefault();\n\n        // click link (will then in turn activate file input)\n        root.ref.label.click();\n    };\n\n    root.ref.handleClick = e => {\n        const isLabelClick = e.target === label || label.contains(e.target);\n\n        // don't want to click twice\n        if (isLabelClick) return;\n\n        // click link (will then in turn activate file input)\n        root.ref.label.click();\n    };\n\n    // attach events\n    label.addEventListener('keydown', root.ref.handleKeyDown);\n    root.element.addEventListener('click', root.ref.handleClick);\n\n    // update\n    updateLabelValue(label, props.caption);\n\n    // add!\n    root.appendChild(label);\n    root.ref.label = label;\n};\n\nconst updateLabelValue = (label, value) => {\n    label.innerHTML = value;\n    const clickable = label.querySelector('.filepond--label-action');\n    if (clickable) {\n        attr(clickable, 'tabindex', '0');\n    }\n    return value;\n};\n\nconst dropLabel = createView({\n    name: 'drop-label',\n    ignoreRect: true,\n    create: create$b,\n    destroy: ({ root }) => {\n        root.ref.label.addEventListener('keydown', root.ref.handleKeyDown);\n        root.element.removeEventListener('click', root.ref.handleClick);\n    },\n    write: createRoute({\n        DID_SET_LABEL_IDLE: ({ root, action }) => {\n            updateLabelValue(root.ref.label, action.value);\n        },\n    }),\n    mixins: {\n        styles: ['opacity', 'translateX', 'translateY'],\n        animations: {\n            opacity: { type: 'tween', duration: 150 },\n            translateX: 'spring',\n            translateY: 'spring',\n        },\n    },\n});\n\nconst blob = createView({\n    name: 'drip-blob',\n    ignoreRect: true,\n    mixins: {\n        styles: ['translateX', 'translateY', 'scaleX', 'scaleY', 'opacity'],\n        animations: {\n            scaleX: 'spring',\n            scaleY: 'spring',\n            translateX: 'spring',\n            translateY: 'spring',\n            opacity: { type: 'tween', duration: 250 },\n        },\n    },\n});\n\nconst addBlob = ({ root }) => {\n    const centerX = root.rect.element.width * 0.5;\n    const centerY = root.rect.element.height * 0.5;\n\n    root.ref.blob = root.appendChildView(\n        root.createChildView(blob, {\n            opacity: 0,\n            scaleX: 2.5,\n            scaleY: 2.5,\n            translateX: centerX,\n            translateY: centerY,\n        })\n    );\n};\n\nconst moveBlob = ({ root, action }) => {\n    if (!root.ref.blob) {\n        addBlob({ root });\n        return;\n    }\n\n    root.ref.blob.translateX = action.position.scopeLeft;\n    root.ref.blob.translateY = action.position.scopeTop;\n    root.ref.blob.scaleX = 1;\n    root.ref.blob.scaleY = 1;\n    root.ref.blob.opacity = 1;\n};\n\nconst hideBlob = ({ root }) => {\n    if (!root.ref.blob) {\n        return;\n    }\n    root.ref.blob.opacity = 0;\n};\n\nconst explodeBlob = ({ root }) => {\n    if (!root.ref.blob) {\n        return;\n    }\n    root.ref.blob.scaleX = 2.5;\n    root.ref.blob.scaleY = 2.5;\n    root.ref.blob.opacity = 0;\n};\n\nconst write$7 = ({ root, props, actions }) => {\n    route$4({ root, props, actions });\n\n    const { blob } = root.ref;\n\n    if (actions.length === 0 && blob && blob.opacity === 0) {\n        root.removeChildView(blob);\n        root.ref.blob = null;\n    }\n};\n\nconst route$4 = createRoute({\n    DID_DRAG: moveBlob,\n    DID_DROP: explodeBlob,\n    DID_END_DRAG: hideBlob,\n});\n\nconst drip = createView({\n    ignoreRect: true,\n    ignoreRectUpdate: true,\n    name: 'drip',\n    write: write$7,\n});\n\nconst setInputFiles = (element, files) => {\n    try {\n        // Create a DataTransfer instance and add a newly created file\n        const dataTransfer = new DataTransfer();\n        files.forEach(file => {\n            if (file instanceof File) {\n                dataTransfer.items.add(file);\n            } else {\n                dataTransfer.items.add(\n                    new File([file], file.name, {\n                        type: file.type,\n                    })\n                );\n            }\n        });\n\n        // Assign the DataTransfer files list to the file input\n        element.files = dataTransfer.files;\n    } catch (err) {\n        return false;\n    }\n    return true;\n};\n\nconst create$c = ({ root }) => {\n    root.ref.fields = {};\n    const legend = document.createElement('legend');\n    legend.textContent = 'Files';\n    root.element.appendChild(legend);\n};\n\nconst getField = (root, id) => root.ref.fields[id];\n\nconst syncFieldPositionsWithItems = root => {\n    root.query('GET_ACTIVE_ITEMS').forEach(item => {\n        if (!root.ref.fields[item.id]) return;\n        root.element.appendChild(root.ref.fields[item.id]);\n    });\n};\n\nconst didReorderItems = ({ root }) => syncFieldPositionsWithItems(root);\n\nconst didAddItem = ({ root, action }) => {\n    const fileItem = root.query('GET_ITEM', action.id);\n    const isLocalFile = fileItem.origin === FileOrigin.LOCAL;\n    const shouldUseFileInput = !isLocalFile && root.query('SHOULD_UPDATE_FILE_INPUT');\n    const dataContainer = createElement$1('input');\n    dataContainer.type = shouldUseFileInput ? 'file' : 'hidden';\n    dataContainer.name = root.query('GET_NAME');\n    root.ref.fields[action.id] = dataContainer;\n    syncFieldPositionsWithItems(root);\n};\n\nconst didLoadItem$1 = ({ root, action }) => {\n    const field = getField(root, action.id);\n    if (!field) return;\n\n    // store server ref in hidden input\n    if (action.serverFileReference !== null) field.value = action.serverFileReference;\n\n    // store file item in file input\n    if (!root.query('SHOULD_UPDATE_FILE_INPUT')) return;\n\n    const fileItem = root.query('GET_ITEM', action.id);\n    setInputFiles(field, [fileItem.file]);\n};\n\nconst didPrepareOutput = ({ root, action }) => {\n    // this timeout pushes the handler after 'load'\n    if (!root.query('SHOULD_UPDATE_FILE_INPUT')) return;\n    setTimeout(() => {\n        const field = getField(root, action.id);\n        if (!field) return;\n        setInputFiles(field, [action.file]);\n    }, 0);\n};\n\nconst didSetDisabled = ({ root }) => {\n    root.element.disabled = root.query('GET_DISABLED');\n};\n\nconst didRemoveItem = ({ root, action }) => {\n    const field = getField(root, action.id);\n    if (!field) return;\n    if (field.parentNode) field.parentNode.removeChild(field);\n    delete root.ref.fields[action.id];\n};\n\n// only runs for server files. will refuse to update the value if the field\n// is a file field\nconst didDefineValue = ({ root, action }) => {\n    const field = getField(root, action.id);\n    if (!field) return;\n    if (action.value === null) {\n        // clear field value\n        field.removeAttribute('value');\n    } else {\n        // set field value\n        if (field.type != 'file') {\n            field.value = action.value;\n        }\n    }\n    syncFieldPositionsWithItems(root);\n};\n\nconst write$8 = createRoute({\n    DID_SET_DISABLED: didSetDisabled,\n    DID_ADD_ITEM: didAddItem,\n    DID_LOAD_ITEM: didLoadItem$1,\n    DID_REMOVE_ITEM: didRemoveItem,\n    DID_DEFINE_VALUE: didDefineValue,\n    DID_PREPARE_OUTPUT: didPrepareOutput,\n    DID_REORDER_ITEMS: didReorderItems,\n    DID_SORT_ITEMS: didReorderItems,\n});\n\nconst data = createView({\n    tag: 'fieldset',\n    name: 'data',\n    create: create$c,\n    write: write$8,\n    ignoreRect: true,\n});\n\nconst getRootNode = element => ('getRootNode' in element ? element.getRootNode() : document);\n\nconst images = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'tiff'];\nconst text$1 = ['css', 'csv', 'html', 'txt'];\nconst map = {\n    zip: 'zip|compressed',\n    epub: 'application/epub+zip',\n};\n\nconst guesstimateMimeType = (extension = '') => {\n    extension = extension.toLowerCase();\n    if (images.includes(extension)) {\n        return (\n            'image/' + (extension === 'jpg' ? 'jpeg' : extension === 'svg' ? 'svg+xml' : extension)\n        );\n    }\n    if (text$1.includes(extension)) {\n        return 'text/' + extension;\n    }\n\n    return map[extension] || '';\n};\n\nconst requestDataTransferItems = dataTransfer =>\n    new Promise((resolve, reject) => {\n        // try to get links from transfer, if found we'll exit immediately (unless a file is in the dataTransfer as well, this is because Firefox could represent the file as a URL and a file object at the same time)\n        const links = getLinks(dataTransfer);\n        if (links.length && !hasFiles(dataTransfer)) {\n            return resolve(links);\n        }\n        // try to get files from the transfer\n        getFiles(dataTransfer).then(resolve);\n    });\n\n/**\n * Test if datatransfer has files\n */\nconst hasFiles = dataTransfer => {\n    if (dataTransfer.files) return dataTransfer.files.length > 0;\n    return false;\n};\n\n/**\n * Extracts files from a DataTransfer object\n */\nconst getFiles = dataTransfer =>\n    new Promise((resolve, reject) => {\n        // get the transfer items as promises\n        const promisedFiles = (dataTransfer.items ? Array.from(dataTransfer.items) : [])\n\n            // only keep file system items (files and directories)\n            .filter(item => isFileSystemItem(item))\n\n            // map each item to promise\n            .map(item => getFilesFromItem(item));\n\n        // if is empty, see if we can extract some info from the files property as a fallback\n        if (!promisedFiles.length) {\n            // TODO: test for directories (should not be allowed)\n            // Use FileReader, problem is that the files property gets lost in the process\n            resolve(dataTransfer.files ? Array.from(dataTransfer.files) : []);\n            return;\n        }\n\n        // done!\n        Promise.all(promisedFiles)\n            .then(returnedFileGroups => {\n                // flatten groups\n                const files = [];\n                returnedFileGroups.forEach(group => {\n                    files.push.apply(files, group);\n                });\n\n                // done (filter out empty files)!\n                resolve(\n                    files\n                        .filter(file => file)\n                        .map(file => {\n                            if (!file._relativePath) file._relativePath = file.webkitRelativePath;\n                            return file;\n                        })\n                );\n            })\n            .catch(console.error);\n    });\n\nconst isFileSystemItem = item => {\n    if (isEntry(item)) {\n        const entry = getAsEntry(item);\n        if (entry) {\n            return entry.isFile || entry.isDirectory;\n        }\n    }\n    return item.kind === 'file';\n};\n\nconst getFilesFromItem = item =>\n    new Promise((resolve, reject) => {\n        if (isDirectoryEntry(item)) {\n            getFilesInDirectory(getAsEntry(item))\n                .then(resolve)\n                .catch(reject);\n            return;\n        }\n\n        resolve([item.getAsFile()]);\n    });\n\nconst getFilesInDirectory = entry =>\n    new Promise((resolve, reject) => {\n        const files = [];\n\n        // the total entries to read\n        let dirCounter = 0;\n        let fileCounter = 0;\n\n        const resolveIfDone = () => {\n            if (fileCounter === 0 && dirCounter === 0) {\n                resolve(files);\n            }\n        };\n\n        // the recursive function\n        const readEntries = dirEntry => {\n            dirCounter++;\n\n            const directoryReader = dirEntry.createReader();\n\n            // directories are returned in batches, we need to process all batches before we're done\n            const readBatch = () => {\n                directoryReader.readEntries(entries => {\n                    if (entries.length === 0) {\n                        dirCounter--;\n                        resolveIfDone();\n                        return;\n                    }\n\n                    entries.forEach(entry => {\n                        // recursively read more directories\n                        if (entry.isDirectory) {\n                            readEntries(entry);\n                        } else {\n                            // read as file\n                            fileCounter++;\n\n                            entry.file(file => {\n                                const correctedFile = correctMissingFileType(file);\n                                if (entry.fullPath) correctedFile._relativePath = entry.fullPath;\n                                files.push(correctedFile);\n                                fileCounter--;\n                                resolveIfDone();\n                            });\n                        }\n                    });\n\n                    // try to get next batch of files\n                    readBatch();\n                }, reject);\n            };\n\n            // read first batch of files\n            readBatch();\n        };\n\n        // go!\n        readEntries(entry);\n    });\n\nconst correctMissingFileType = file => {\n    if (file.type.length) return file;\n    const date = file.lastModifiedDate;\n    const name = file.name;\n    const type = guesstimateMimeType(getExtensionFromFilename(file.name));\n    if (!type.length) return file;\n    file = file.slice(0, file.size, type);\n    file.name = name;\n    file.lastModifiedDate = date;\n    return file;\n};\n\nconst isDirectoryEntry = item => isEntry(item) && (getAsEntry(item) || {}).isDirectory;\n\nconst isEntry = item => 'webkitGetAsEntry' in item;\n\nconst getAsEntry = item => item.webkitGetAsEntry();\n\n/**\n * Extracts links from a DataTransfer object\n */\nconst getLinks = dataTransfer => {\n    let links = [];\n    try {\n        // look in meta data property\n        links = getLinksFromTransferMetaData(dataTransfer);\n        if (links.length) {\n            return links;\n        }\n        links = getLinksFromTransferURLData(dataTransfer);\n    } catch (e) {\n        // nope nope nope (probably IE trouble)\n    }\n    return links;\n};\n\nconst getLinksFromTransferURLData = dataTransfer => {\n    let data = dataTransfer.getData('url');\n    if (typeof data === 'string' && data.length) {\n        return [data];\n    }\n    return [];\n};\n\nconst getLinksFromTransferMetaData = dataTransfer => {\n    let data = dataTransfer.getData('text/html');\n    if (typeof data === 'string' && data.length) {\n        const matches = data.match(/src\\s*=\\s*\"(.+?)\"/);\n        if (matches) {\n            return [matches[1]];\n        }\n    }\n    return [];\n};\n\nconst dragNDropObservers = [];\n\nconst eventPosition = e => ({\n    pageLeft: e.pageX,\n    pageTop: e.pageY,\n    scopeLeft: e.offsetX || e.layerX,\n    scopeTop: e.offsetY || e.layerY,\n});\n\nconst createDragNDropClient = (element, scopeToObserve, filterElement) => {\n    const observer = getDragNDropObserver(scopeToObserve);\n\n    const client = {\n        element,\n        filterElement,\n        state: null,\n        ondrop: () => {},\n        onenter: () => {},\n        ondrag: () => {},\n        onexit: () => {},\n        onload: () => {},\n        allowdrop: () => {},\n    };\n\n    client.destroy = observer.addListener(client);\n\n    return client;\n};\n\nconst getDragNDropObserver = element => {\n    // see if already exists, if so, return\n    const observer = dragNDropObservers.find(item => item.element === element);\n    if (observer) {\n        return observer;\n    }\n\n    // create new observer, does not yet exist for this element\n    const newObserver = createDragNDropObserver(element);\n    dragNDropObservers.push(newObserver);\n    return newObserver;\n};\n\nconst createDragNDropObserver = element => {\n    const clients = [];\n\n    const routes = {\n        dragenter,\n        dragover,\n        dragleave,\n        drop,\n    };\n\n    const handlers = {};\n\n    forin(routes, (event, createHandler) => {\n        handlers[event] = createHandler(element, clients);\n        element.addEventListener(event, handlers[event], false);\n    });\n\n    const observer = {\n        element,\n        addListener: client => {\n            // add as client\n            clients.push(client);\n\n            // return removeListener function\n            return () => {\n                // remove client\n                clients.splice(clients.indexOf(client), 1);\n\n                // if no more clients, clean up observer\n                if (clients.length === 0) {\n                    dragNDropObservers.splice(dragNDropObservers.indexOf(observer), 1);\n\n                    forin(routes, event => {\n                        element.removeEventListener(event, handlers[event], false);\n                    });\n                }\n            };\n        },\n    };\n\n    return observer;\n};\n\nconst elementFromPoint = (root, point) => {\n    if (!('elementFromPoint' in root)) {\n        root = document;\n    }\n    return root.elementFromPoint(point.x, point.y);\n};\n\nconst isEventTarget = (e, target) => {\n    // get root\n    const root = getRootNode(target);\n\n    // get element at position\n    // if root is not actual shadow DOM and does not have elementFromPoint method, use the one on document\n    const elementAtPosition = elementFromPoint(root, {\n        x: e.pageX - window.pageXOffset,\n        y: e.pageY - window.pageYOffset,\n    });\n\n    // test if target is the element or if one of its children is\n    return elementAtPosition === target || target.contains(elementAtPosition);\n};\n\nlet initialTarget = null;\n\nconst setDropEffect = (dataTransfer, effect) => {\n    // is in try catch as IE11 will throw error if not\n    try {\n        dataTransfer.dropEffect = effect;\n    } catch (e) {}\n};\n\nconst dragenter = (root, clients) => e => {\n    e.preventDefault();\n\n    initialTarget = e.target;\n\n    clients.forEach(client => {\n        const { element, onenter } = client;\n\n        if (isEventTarget(e, element)) {\n            client.state = 'enter';\n\n            // fire enter event\n            onenter(eventPosition(e));\n        }\n    });\n};\n\nconst dragover = (root, clients) => e => {\n    e.preventDefault();\n\n    const dataTransfer = e.dataTransfer;\n\n    requestDataTransferItems(dataTransfer).then(items => {\n        let overDropTarget = false;\n\n        clients.some(client => {\n            const { filterElement, element, onenter, onexit, ondrag, allowdrop } = client;\n\n            // by default we can drop\n            setDropEffect(dataTransfer, 'copy');\n\n            // allow transfer of these items\n            const allowsTransfer = allowdrop(items);\n\n            // only used when can be dropped on page\n            if (!allowsTransfer) {\n                setDropEffect(dataTransfer, 'none');\n                return;\n            }\n\n            // targetting this client\n            if (isEventTarget(e, element)) {\n                overDropTarget = true;\n\n                // had no previous state, means we are entering this client\n                if (client.state === null) {\n                    client.state = 'enter';\n                    onenter(eventPosition(e));\n                    return;\n                }\n\n                // now over element (no matter if it allows the drop or not)\n                client.state = 'over';\n\n                // needs to allow transfer\n                if (filterElement && !allowsTransfer) {\n                    setDropEffect(dataTransfer, 'none');\n                    return;\n                }\n\n                // dragging\n                ondrag(eventPosition(e));\n            } else {\n                // should be over an element to drop\n                if (filterElement && !overDropTarget) {\n                    setDropEffect(dataTransfer, 'none');\n                }\n\n                // might have just left this client?\n                if (client.state) {\n                    client.state = null;\n                    onexit(eventPosition(e));\n                }\n            }\n        });\n    });\n};\n\nconst drop = (root, clients) => e => {\n    e.preventDefault();\n\n    const dataTransfer = e.dataTransfer;\n\n    requestDataTransferItems(dataTransfer).then(items => {\n        clients.forEach(client => {\n            const { filterElement, element, ondrop, onexit, allowdrop } = client;\n\n            client.state = null;\n\n            // if we're filtering on element we need to be over the element to drop\n            if (filterElement && !isEventTarget(e, element)) return;\n\n            // no transfer for this client\n            if (!allowdrop(items)) return onexit(eventPosition(e));\n\n            // we can drop these items on this client\n            ondrop(eventPosition(e), items);\n        });\n    });\n};\n\nconst dragleave = (root, clients) => e => {\n    if (initialTarget !== e.target) {\n        return;\n    }\n\n    clients.forEach(client => {\n        const { onexit } = client;\n\n        client.state = null;\n\n        onexit(eventPosition(e));\n    });\n};\n\nconst createHopper = (scope, validateItems, options) => {\n    // is now hopper scope\n    scope.classList.add('filepond--hopper');\n\n    // shortcuts\n    const { catchesDropsOnPage, requiresDropOnElement, filterItems = items => items } = options;\n\n    // create a dnd client\n    const client = createDragNDropClient(\n        scope,\n        catchesDropsOnPage ? document.documentElement : scope,\n        requiresDropOnElement\n    );\n\n    // current client state\n    let lastState = '';\n    let currentState = '';\n\n    // determines if a file may be dropped\n    client.allowdrop = items => {\n        // TODO: if we can, throw error to indicate the items cannot by dropped\n\n        return validateItems(filterItems(items));\n    };\n\n    client.ondrop = (position, items) => {\n        const filteredItems = filterItems(items);\n\n        if (!validateItems(filteredItems)) {\n            api.ondragend(position);\n            return;\n        }\n\n        currentState = 'drag-drop';\n\n        api.onload(filteredItems, position);\n    };\n\n    client.ondrag = position => {\n        api.ondrag(position);\n    };\n\n    client.onenter = position => {\n        currentState = 'drag-over';\n\n        api.ondragstart(position);\n    };\n\n    client.onexit = position => {\n        currentState = 'drag-exit';\n\n        api.ondragend(position);\n    };\n\n    const api = {\n        updateHopperState: () => {\n            if (lastState !== currentState) {\n                scope.dataset.hopperState = currentState;\n                lastState = currentState;\n            }\n        },\n        onload: () => {},\n        ondragstart: () => {},\n        ondrag: () => {},\n        ondragend: () => {},\n        destroy: () => {\n            // destroy client\n            client.destroy();\n        },\n    };\n\n    return api;\n};\n\nlet listening = false;\nconst listeners$1 = [];\n\nconst handlePaste = e => {\n    // if is pasting in input or textarea and the target is outside of a filepond scope, ignore\n    const activeEl = document.activeElement;\n    const isActiveElementEditable =\n        activeEl &&\n        (/textarea|input/i.test(activeEl.nodeName) ||\n            activeEl.getAttribute('contenteditable') === 'true');\n\n    if (isActiveElementEditable) {\n        // test textarea or input is contained in filepond root\n        let inScope = false;\n        let element = activeEl;\n        while (element !== document.body) {\n            if (element.classList.contains('filepond--root')) {\n                inScope = true;\n                break;\n            }\n            element = element.parentNode;\n        }\n\n        if (!inScope) return;\n    }\n\n    requestDataTransferItems(e.clipboardData).then(files => {\n        // no files received\n        if (!files.length) {\n            return;\n        }\n\n        // notify listeners of received files\n        listeners$1.forEach(listener => listener(files));\n    });\n};\n\nconst listen = cb => {\n    // can't add twice\n    if (listeners$1.includes(cb)) {\n        return;\n    }\n\n    // add initial listener\n    listeners$1.push(cb);\n\n    // setup paste listener for entire page\n    if (listening) {\n        return;\n    }\n\n    listening = true;\n    document.addEventListener('paste', handlePaste);\n};\n\nconst unlisten = listener => {\n    arrayRemove(listeners$1, listeners$1.indexOf(listener));\n\n    // clean up\n    if (listeners$1.length === 0) {\n        document.removeEventListener('paste', handlePaste);\n        listening = false;\n    }\n};\n\nconst createPaster = () => {\n    const cb = files => {\n        api.onload(files);\n    };\n\n    const api = {\n        destroy: () => {\n            unlisten(cb);\n        },\n        onload: () => {},\n    };\n\n    listen(cb);\n\n    return api;\n};\n\n/**\n * Creates the file view\n */\nconst create$d = ({ root, props }) => {\n    root.element.id = `filepond--assistant-${props.id}`;\n    attr(root.element, 'role', 'alert');\n    attr(root.element, 'aria-live', 'polite');\n    attr(root.element, 'aria-relevant', 'additions');\n};\n\nlet addFilesNotificationTimeout = null;\nlet notificationClearTimeout = null;\n\nconst filenames = [];\n\nconst assist = (root, message) => {\n    root.element.textContent = message;\n};\n\nconst clear$1 = root => {\n    root.element.textContent = '';\n};\n\nconst listModified = (root, filename, label) => {\n    const total = root.query('GET_TOTAL_ITEMS');\n    assist(\n        root,\n        `${label} ${filename}, ${total} ${\n            total === 1\n                ? root.query('GET_LABEL_FILE_COUNT_SINGULAR')\n                : root.query('GET_LABEL_FILE_COUNT_PLURAL')\n        }`\n    );\n\n    // clear group after set amount of time so the status is not read twice\n    clearTimeout(notificationClearTimeout);\n    notificationClearTimeout = setTimeout(() => {\n        clear$1(root);\n    }, 1500);\n};\n\nconst isUsingFilePond = root => root.element.parentNode.contains(document.activeElement);\n\nconst itemAdded = ({ root, action }) => {\n    if (!isUsingFilePond(root)) {\n        return;\n    }\n\n    root.element.textContent = '';\n    const item = root.query('GET_ITEM', action.id);\n    filenames.push(item.filename);\n\n    clearTimeout(addFilesNotificationTimeout);\n    addFilesNotificationTimeout = setTimeout(() => {\n        listModified(root, filenames.join(', '), root.query('GET_LABEL_FILE_ADDED'));\n        filenames.length = 0;\n    }, 750);\n};\n\nconst itemRemoved = ({ root, action }) => {\n    if (!isUsingFilePond(root)) {\n        return;\n    }\n\n    const item = action.item;\n    listModified(root, item.filename, root.query('GET_LABEL_FILE_REMOVED'));\n};\n\nconst itemProcessed = ({ root, action }) => {\n    // will also notify the user when FilePond is not being used, as the user might be occupied with other activities while uploading a file\n\n    const item = root.query('GET_ITEM', action.id);\n    const filename = item.filename;\n    const label = root.query('GET_LABEL_FILE_PROCESSING_COMPLETE');\n\n    assist(root, `${filename} ${label}`);\n};\n\nconst itemProcessedUndo = ({ root, action }) => {\n    const item = root.query('GET_ITEM', action.id);\n    const filename = item.filename;\n    const label = root.query('GET_LABEL_FILE_PROCESSING_ABORTED');\n\n    assist(root, `${filename} ${label}`);\n};\n\nconst itemError = ({ root, action }) => {\n    const item = root.query('GET_ITEM', action.id);\n    const filename = item.filename;\n\n    // will also notify the user when FilePond is not being used, as the user might be occupied with other activities while uploading a file\n\n    assist(root, `${action.status.main} ${filename} ${action.status.sub}`);\n};\n\nconst assistant = createView({\n    create: create$d,\n    ignoreRect: true,\n    ignoreRectUpdate: true,\n    write: createRoute({\n        DID_LOAD_ITEM: itemAdded,\n        DID_REMOVE_ITEM: itemRemoved,\n        DID_COMPLETE_ITEM_PROCESSING: itemProcessed,\n\n        DID_ABORT_ITEM_PROCESSING: itemProcessedUndo,\n        DID_REVERT_ITEM_PROCESSING: itemProcessedUndo,\n\n        DID_THROW_ITEM_REMOVE_ERROR: itemError,\n        DID_THROW_ITEM_LOAD_ERROR: itemError,\n        DID_THROW_ITEM_INVALID: itemError,\n        DID_THROW_ITEM_PROCESSING_ERROR: itemError,\n    }),\n    tag: 'span',\n    name: 'assistant',\n});\n\nconst toCamels = (string, separator = '-') =>\n    string.replace(new RegExp(`${separator}.`, 'g'), sub => sub.charAt(1).toUpperCase());\n\nconst debounce = (func, interval = 16, immidiateOnly = true) => {\n    let last = Date.now();\n    let timeout = null;\n\n    return (...args) => {\n        clearTimeout(timeout);\n\n        const dist = Date.now() - last;\n\n        const fn = () => {\n            last = Date.now();\n            func(...args);\n        };\n\n        if (dist < interval) {\n            // we need to delay by the difference between interval and dist\n            // for example: if distance is 10 ms and interval is 16 ms,\n            // we need to wait an additional 6ms before calling the function)\n            if (!immidiateOnly) {\n                timeout = setTimeout(fn, interval - dist);\n            }\n        } else {\n            // go!\n            fn();\n        }\n    };\n};\n\nconst MAX_FILES_LIMIT = 1000000;\n\nconst prevent = e => e.preventDefault();\n\nconst create$e = ({ root, props }) => {\n    // Add id\n    const id = root.query('GET_ID');\n    if (id) {\n        root.element.id = id;\n    }\n\n    // Add className\n    const className = root.query('GET_CLASS_NAME');\n    if (className) {\n        className\n            .split(' ')\n            .filter(name => name.length)\n            .forEach(name => {\n                root.element.classList.add(name);\n            });\n    }\n\n    // Field label\n    root.ref.label = root.appendChildView(\n        root.createChildView(dropLabel, {\n            ...props,\n            translateY: null,\n            caption: root.query('GET_LABEL_IDLE'),\n        })\n    );\n\n    // List of items\n    root.ref.list = root.appendChildView(root.createChildView(listScroller, { translateY: null }));\n\n    // Background panel\n    root.ref.panel = root.appendChildView(root.createChildView(panel, { name: 'panel-root' }));\n\n    // Assistant notifies assistive tech when content changes\n    root.ref.assistant = root.appendChildView(root.createChildView(assistant, { ...props }));\n\n    // Data\n    root.ref.data = root.appendChildView(root.createChildView(data, { ...props }));\n\n    // Measure (tests if fixed height was set)\n    // DOCTYPE needs to be set for this to work\n    root.ref.measure = createElement$1('div');\n    root.ref.measure.style.height = '100%';\n    root.element.appendChild(root.ref.measure);\n\n    // information on the root height or fixed height status\n    root.ref.bounds = null;\n\n    // apply initial style properties\n    root.query('GET_STYLES')\n        .filter(style => !isEmpty(style.value))\n        .map(({ name, value }) => {\n            root.element.dataset[name] = value;\n        });\n\n    // determine if width changed\n    root.ref.widthPrevious = null;\n    root.ref.widthUpdated = debounce(() => {\n        root.ref.updateHistory = [];\n        root.dispatch('DID_RESIZE_ROOT');\n    }, 250);\n\n    // history of updates\n    root.ref.previousAspectRatio = null;\n    root.ref.updateHistory = [];\n\n    // prevent scrolling and zooming on iOS (only if supports pointer events, for then we can enable reorder)\n    const canHover = window.matchMedia('(pointer: fine) and (hover: hover)').matches;\n    const hasPointerEvents = 'PointerEvent' in window;\n    if (root.query('GET_ALLOW_REORDER') && hasPointerEvents && !canHover) {\n        root.element.addEventListener('touchmove', prevent, { passive: false });\n        root.element.addEventListener('gesturestart', prevent);\n    }\n\n    // add credits\n    const credits = root.query('GET_CREDITS');\n    const hasCredits = credits.length === 2;\n    if (hasCredits) {\n        const frag = document.createElement('a');\n        frag.className = 'filepond--credits';\n        frag.href = credits[0];\n        frag.tabIndex = -1;\n        frag.target = '_blank';\n        frag.rel = 'noopener noreferrer nofollow';\n        frag.textContent = credits[1];\n        root.element.appendChild(frag);\n        root.ref.credits = frag;\n    }\n};\n\nconst write$9 = ({ root, props, actions }) => {\n    // route actions\n    route$5({ root, props, actions });\n\n    // apply style properties\n    actions\n        .filter(action => /^DID_SET_STYLE_/.test(action.type))\n        .filter(action => !isEmpty(action.data.value))\n        .map(({ type, data }) => {\n            const name = toCamels(type.substring(8).toLowerCase(), '_');\n            root.element.dataset[name] = data.value;\n            root.invalidateLayout();\n        });\n\n    if (root.rect.element.hidden) return;\n\n    if (root.rect.element.width !== root.ref.widthPrevious) {\n        root.ref.widthPrevious = root.rect.element.width;\n        root.ref.widthUpdated();\n    }\n\n    // get box bounds, we do this only once\n    let bounds = root.ref.bounds;\n    if (!bounds) {\n        bounds = root.ref.bounds = calculateRootBoundingBoxHeight(root);\n\n        // destroy measure element\n        root.element.removeChild(root.ref.measure);\n        root.ref.measure = null;\n    }\n\n    // get quick references to various high level parts of the upload tool\n    const { hopper, label, list, panel } = root.ref;\n\n    // sets correct state to hopper scope\n    if (hopper) {\n        hopper.updateHopperState();\n    }\n\n    // bool to indicate if we're full or not\n    const aspectRatio = root.query('GET_PANEL_ASPECT_RATIO');\n    const isMultiItem = root.query('GET_ALLOW_MULTIPLE');\n    const totalItems = root.query('GET_TOTAL_ITEMS');\n    const maxItems = isMultiItem ? root.query('GET_MAX_FILES') || MAX_FILES_LIMIT : 1;\n    const atMaxCapacity = totalItems === maxItems;\n\n    // action used to add item\n    const addAction = actions.find(action => action.type === 'DID_ADD_ITEM');\n\n    // if reached max capacity and we've just reached it\n    if (atMaxCapacity && addAction) {\n        // get interaction type\n        const interactionMethod = addAction.data.interactionMethod;\n\n        // hide label\n        label.opacity = 0;\n\n        if (isMultiItem) {\n            label.translateY = -40;\n        } else {\n            if (interactionMethod === InteractionMethod.API) {\n                label.translateX = 40;\n            } else if (interactionMethod === InteractionMethod.BROWSE) {\n                label.translateY = 40;\n            } else {\n                label.translateY = 30;\n            }\n        }\n    } else if (!atMaxCapacity) {\n        label.opacity = 1;\n        label.translateX = 0;\n        label.translateY = 0;\n    }\n\n    const listItemMargin = calculateListItemMargin(root);\n\n    const listHeight = calculateListHeight(root);\n\n    const labelHeight = label.rect.element.height;\n    const currentLabelHeight = !isMultiItem || atMaxCapacity ? 0 : labelHeight;\n\n    const listMarginTop = atMaxCapacity ? list.rect.element.marginTop : 0;\n    const listMarginBottom = totalItems === 0 ? 0 : list.rect.element.marginBottom;\n\n    const visualHeight = currentLabelHeight + listMarginTop + listHeight.visual + listMarginBottom;\n    const boundsHeight = currentLabelHeight + listMarginTop + listHeight.bounds + listMarginBottom;\n\n    // link list to label bottom position\n    list.translateY =\n        Math.max(0, currentLabelHeight - list.rect.element.marginTop) - listItemMargin.top;\n\n    if (aspectRatio) {\n        // fixed aspect ratio\n\n        // calculate height based on width\n        const width = root.rect.element.width;\n        const height = width * aspectRatio;\n\n        // clear history if aspect ratio has changed\n        if (aspectRatio !== root.ref.previousAspectRatio) {\n            root.ref.previousAspectRatio = aspectRatio;\n            root.ref.updateHistory = [];\n        }\n\n        // remember this width\n        const history = root.ref.updateHistory;\n        history.push(width);\n\n        const MAX_BOUNCES = 2;\n        if (history.length > MAX_BOUNCES * 2) {\n            const l = history.length;\n            const bottom = l - 10;\n            let bounces = 0;\n            for (let i = l; i >= bottom; i--) {\n                if (history[i] === history[i - 2]) {\n                    bounces++;\n                }\n\n                if (bounces >= MAX_BOUNCES) {\n                    // dont adjust height\n                    return;\n                }\n            }\n        }\n\n        // fix height of panel so it adheres to aspect ratio\n        panel.scalable = false;\n        panel.height = height;\n\n        // available height for list\n        const listAvailableHeight =\n            // the height of the panel minus the label height\n            height -\n            currentLabelHeight -\n            // the room we leave open between the end of the list and the panel bottom\n            (listMarginBottom - listItemMargin.bottom) -\n            // if we're full we need to leave some room between the top of the panel and the list\n            (atMaxCapacity ? listMarginTop : 0);\n\n        if (listHeight.visual > listAvailableHeight) {\n            list.overflow = listAvailableHeight;\n        } else {\n            list.overflow = null;\n        }\n\n        // set container bounds (so pushes siblings downwards)\n        root.height = height;\n    } else if (bounds.fixedHeight) {\n        // fixed height\n\n        // fix height of panel\n        panel.scalable = false;\n\n        // available height for list\n        const listAvailableHeight =\n            // the height of the panel minus the label height\n            bounds.fixedHeight -\n            currentLabelHeight -\n            // the room we leave open between the end of the list and the panel bottom\n            (listMarginBottom - listItemMargin.bottom) -\n            // if we're full we need to leave some room between the top of the panel and the list\n            (atMaxCapacity ? listMarginTop : 0);\n\n        // set list height\n        if (listHeight.visual > listAvailableHeight) {\n            list.overflow = listAvailableHeight;\n        } else {\n            list.overflow = null;\n        }\n\n        // no need to set container bounds as these are handles by CSS fixed height\n    } else if (bounds.cappedHeight) {\n        // max-height\n\n        // not a fixed height panel\n        const isCappedHeight = visualHeight >= bounds.cappedHeight;\n        const panelHeight = Math.min(bounds.cappedHeight, visualHeight);\n        panel.scalable = true;\n        panel.height = isCappedHeight\n            ? panelHeight\n            : panelHeight - listItemMargin.top - listItemMargin.bottom;\n\n        // available height for list\n        const listAvailableHeight =\n            // the height of the panel minus the label height\n            panelHeight -\n            currentLabelHeight -\n            // the room we leave open between the end of the list and the panel bottom\n            (listMarginBottom - listItemMargin.bottom) -\n            // if we're full we need to leave some room between the top of the panel and the list\n            (atMaxCapacity ? listMarginTop : 0);\n\n        // set list height (if is overflowing)\n        if (visualHeight > bounds.cappedHeight && listHeight.visual > listAvailableHeight) {\n            list.overflow = listAvailableHeight;\n        } else {\n            list.overflow = null;\n        }\n\n        // set container bounds (so pushes siblings downwards)\n        root.height = Math.min(\n            bounds.cappedHeight,\n            boundsHeight - listItemMargin.top - listItemMargin.bottom\n        );\n    } else {\n        // flexible height\n\n        // not a fixed height panel\n        const itemMargin = totalItems > 0 ? listItemMargin.top + listItemMargin.bottom : 0;\n        panel.scalable = true;\n        panel.height = Math.max(labelHeight, visualHeight - itemMargin);\n\n        // set container bounds (so pushes siblings downwards)\n        root.height = Math.max(labelHeight, boundsHeight - itemMargin);\n    }\n\n    // move credits to bottom\n    if (root.ref.credits && panel.heightCurrent)\n        root.ref.credits.style.transform = `translateY(${panel.heightCurrent}px)`;\n};\n\nconst calculateListItemMargin = root => {\n    const item = root.ref.list.childViews[0].childViews[0];\n    return item\n        ? {\n              top: item.rect.element.marginTop,\n              bottom: item.rect.element.marginBottom,\n          }\n        : {\n              top: 0,\n              bottom: 0,\n          };\n};\n\nconst calculateListHeight = root => {\n    let visual = 0;\n    let bounds = 0;\n\n    // get file list reference\n    const scrollList = root.ref.list;\n    const itemList = scrollList.childViews[0];\n    const visibleChildren = itemList.childViews.filter(child => child.rect.element.height);\n    const children = root\n        .query('GET_ACTIVE_ITEMS')\n        .map(item => visibleChildren.find(child => child.id === item.id))\n        .filter(item => item);\n\n    // no children, done!\n    if (children.length === 0) return { visual, bounds };\n\n    const horizontalSpace = itemList.rect.element.width;\n    const dragIndex = getItemIndexByPosition(itemList, children, scrollList.dragCoordinates);\n\n    const childRect = children[0].rect.element;\n\n    const itemVerticalMargin = childRect.marginTop + childRect.marginBottom;\n    const itemHorizontalMargin = childRect.marginLeft + childRect.marginRight;\n\n    const itemWidth = childRect.width + itemHorizontalMargin;\n    const itemHeight = childRect.height + itemVerticalMargin;\n\n    const newItem = typeof dragIndex !== 'undefined' && dragIndex >= 0 ? 1 : 0;\n    const removedItem = children.find(child => child.markedForRemoval && child.opacity < 0.45)\n        ? -1\n        : 0;\n    const verticalItemCount = children.length + newItem + removedItem;\n    const itemsPerRow = getItemsPerRow(horizontalSpace, itemWidth);\n\n    // stack\n    if (itemsPerRow === 1) {\n        children.forEach(item => {\n            const height = item.rect.element.height + itemVerticalMargin;\n            bounds += height;\n            visual += height * item.opacity;\n        });\n    }\n    // grid\n    else {\n        bounds = Math.ceil(verticalItemCount / itemsPerRow) * itemHeight;\n        visual = bounds;\n    }\n\n    return { visual, bounds };\n};\n\nconst calculateRootBoundingBoxHeight = root => {\n    const height = root.ref.measureHeight || null;\n    const cappedHeight = parseInt(root.style.maxHeight, 10) || null;\n    const fixedHeight = height === 0 ? null : height;\n\n    return {\n        cappedHeight,\n        fixedHeight,\n    };\n};\n\nconst exceedsMaxFiles = (root, items) => {\n    const allowReplace = root.query('GET_ALLOW_REPLACE');\n    const allowMultiple = root.query('GET_ALLOW_MULTIPLE');\n    const totalItems = root.query('GET_TOTAL_ITEMS');\n    let maxItems = root.query('GET_MAX_FILES');\n\n    // total amount of items being dragged\n    const totalBrowseItems = items.length;\n\n    // if does not allow multiple items and dragging more than one item\n    if (!allowMultiple && totalBrowseItems > 1) {\n        root.dispatch('DID_THROW_MAX_FILES', {\n            source: items,\n            error: createResponse('warning', 0, 'Max files'),\n        });\n        return true;\n    }\n\n    // limit max items to one if not allowed to drop multiple items\n    maxItems = allowMultiple ? maxItems : 1;\n\n    if (!allowMultiple && allowReplace) {\n        // There is only one item, so there is room to replace or add an item\n        return false;\n    }\n\n    // no more room?\n    const hasMaxItems = isInt(maxItems);\n    if (hasMaxItems && totalItems + totalBrowseItems > maxItems) {\n        root.dispatch('DID_THROW_MAX_FILES', {\n            source: items,\n            error: createResponse('warning', 0, 'Max files'),\n        });\n        return true;\n    }\n\n    return false;\n};\n\nconst getDragIndex = (list, children, position) => {\n    const itemList = list.childViews[0];\n    return getItemIndexByPosition(itemList, children, {\n        left: position.scopeLeft - itemList.rect.element.left,\n        top:\n            position.scopeTop -\n            (list.rect.outer.top + list.rect.element.marginTop + list.rect.element.scrollTop),\n    });\n};\n\n/**\n * Enable or disable file drop functionality\n */\nconst toggleDrop = root => {\n    const isAllowed = root.query('GET_ALLOW_DROP');\n    const isDisabled = root.query('GET_DISABLED');\n    const enabled = isAllowed && !isDisabled;\n    if (enabled && !root.ref.hopper) {\n        const hopper = createHopper(\n            root.element,\n            items => {\n                // allow quick validation of dropped items\n                const beforeDropFile = root.query('GET_BEFORE_DROP_FILE') || (() => true);\n\n                // all items should be validated by all filters as valid\n                const dropValidation = root.query('GET_DROP_VALIDATION');\n                return dropValidation\n                    ? items.every(\n                          item =>\n                              applyFilters('ALLOW_HOPPER_ITEM', item, {\n                                  query: root.query,\n                              }).every(result => result === true) && beforeDropFile(item)\n                      )\n                    : true;\n            },\n            {\n                filterItems: items => {\n                    const ignoredFiles = root.query('GET_IGNORED_FILES');\n                    return items.filter(item => {\n                        if (isFile(item)) {\n                            return !ignoredFiles.includes(item.name.toLowerCase());\n                        }\n                        return true;\n                    });\n                },\n                catchesDropsOnPage: root.query('GET_DROP_ON_PAGE'),\n                requiresDropOnElement: root.query('GET_DROP_ON_ELEMENT'),\n            }\n        );\n\n        hopper.onload = (items, position) => {\n            // get item children elements and sort based on list sort\n            const list = root.ref.list.childViews[0];\n            const visibleChildren = list.childViews.filter(child => child.rect.element.height);\n            const children = root\n                .query('GET_ACTIVE_ITEMS')\n                .map(item => visibleChildren.find(child => child.id === item.id))\n                .filter(item => item);\n\n            applyFilterChain('ADD_ITEMS', items, { dispatch: root.dispatch }).then(queue => {\n                // these files don't fit so stop here\n                if (exceedsMaxFiles(root, queue)) return false;\n\n                // go\n                root.dispatch('ADD_ITEMS', {\n                    items: queue,\n                    index: getDragIndex(root.ref.list, children, position),\n                    interactionMethod: InteractionMethod.DROP,\n                });\n            });\n\n            root.dispatch('DID_DROP', { position });\n\n            root.dispatch('DID_END_DRAG', { position });\n        };\n\n        hopper.ondragstart = position => {\n            root.dispatch('DID_START_DRAG', { position });\n        };\n\n        hopper.ondrag = debounce(position => {\n            root.dispatch('DID_DRAG', { position });\n        });\n\n        hopper.ondragend = position => {\n            root.dispatch('DID_END_DRAG', { position });\n        };\n\n        root.ref.hopper = hopper;\n\n        root.ref.drip = root.appendChildView(root.createChildView(drip));\n    } else if (!enabled && root.ref.hopper) {\n        root.ref.hopper.destroy();\n        root.ref.hopper = null;\n        root.removeChildView(root.ref.drip);\n    }\n};\n\n/**\n * Enable or disable browse functionality\n */\nconst toggleBrowse = (root, props) => {\n    const isAllowed = root.query('GET_ALLOW_BROWSE');\n    const isDisabled = root.query('GET_DISABLED');\n    const enabled = isAllowed && !isDisabled;\n    if (enabled && !root.ref.browser) {\n        root.ref.browser = root.appendChildView(\n            root.createChildView(browser, {\n                ...props,\n                onload: items => {\n                    applyFilterChain('ADD_ITEMS', items, {\n                        dispatch: root.dispatch,\n                    }).then(queue => {\n                        // these files don't fit so stop here\n                        if (exceedsMaxFiles(root, queue)) return false;\n\n                        // add items!\n                        root.dispatch('ADD_ITEMS', {\n                            items: queue,\n                            index: -1,\n                            interactionMethod: InteractionMethod.BROWSE,\n                        });\n                    });\n                },\n            }),\n            0\n        );\n    } else if (!enabled && root.ref.browser) {\n        root.removeChildView(root.ref.browser);\n        root.ref.browser = null;\n    }\n};\n\n/**\n * Enable or disable paste functionality\n */\nconst togglePaste = root => {\n    const isAllowed = root.query('GET_ALLOW_PASTE');\n    const isDisabled = root.query('GET_DISABLED');\n    const enabled = isAllowed && !isDisabled;\n    if (enabled && !root.ref.paster) {\n        root.ref.paster = createPaster();\n        root.ref.paster.onload = items => {\n            applyFilterChain('ADD_ITEMS', items, { dispatch: root.dispatch }).then(queue => {\n                // these files don't fit so stop here\n                if (exceedsMaxFiles(root, queue)) return false;\n\n                // add items!\n                root.dispatch('ADD_ITEMS', {\n                    items: queue,\n                    index: -1,\n                    interactionMethod: InteractionMethod.PASTE,\n                });\n            });\n        };\n    } else if (!enabled && root.ref.paster) {\n        root.ref.paster.destroy();\n        root.ref.paster = null;\n    }\n};\n\n/**\n * Route actions\n */\nconst route$5 = createRoute({\n    DID_SET_ALLOW_BROWSE: ({ root, props }) => {\n        toggleBrowse(root, props);\n    },\n    DID_SET_ALLOW_DROP: ({ root }) => {\n        toggleDrop(root);\n    },\n    DID_SET_ALLOW_PASTE: ({ root }) => {\n        togglePaste(root);\n    },\n    DID_SET_DISABLED: ({ root, props }) => {\n        toggleDrop(root);\n        togglePaste(root);\n        toggleBrowse(root, props);\n        const isDisabled = root.query('GET_DISABLED');\n        if (isDisabled) {\n            root.element.dataset.disabled = 'disabled';\n        } else {\n            // delete root.element.dataset.disabled; <= this does not work on iOS 10\n            root.element.removeAttribute('data-disabled');\n        }\n    },\n});\n\nconst root = createView({\n    name: 'root',\n    read: ({ root }) => {\n        if (root.ref.measure) {\n            root.ref.measureHeight = root.ref.measure.offsetHeight;\n        }\n    },\n    create: create$e,\n    write: write$9,\n    destroy: ({ root }) => {\n        if (root.ref.paster) {\n            root.ref.paster.destroy();\n        }\n        if (root.ref.hopper) {\n            root.ref.hopper.destroy();\n        }\n        root.element.removeEventListener('touchmove', prevent);\n        root.element.removeEventListener('gesturestart', prevent);\n    },\n    mixins: {\n        styles: ['height'],\n    },\n});\n\n// creates the app\nconst createApp = (initialOptions = {}) => {\n    // let element\n    let originalElement = null;\n\n    // get default options\n    const defaultOptions = getOptions();\n\n    // create the data store, this will contain all our app info\n    const store = createStore(\n        // initial state (should be serializable)\n        createInitialState(defaultOptions),\n\n        // queries\n        [queries, createOptionQueries(defaultOptions)],\n\n        // action handlers\n        [actions, createOptionActions(defaultOptions)]\n    );\n\n    // set initial options\n    store.dispatch('SET_OPTIONS', { options: initialOptions });\n\n    // kick thread if visibility changes\n    const visibilityHandler = () => {\n        if (document.hidden) return;\n        store.dispatch('KICK');\n    };\n    document.addEventListener('visibilitychange', visibilityHandler);\n\n    // re-render on window resize start and finish\n    let resizeDoneTimer = null;\n    let isResizing = false;\n    let isResizingHorizontally = false;\n    let initialWindowWidth = null;\n    let currentWindowWidth = null;\n    const resizeHandler = () => {\n        if (!isResizing) {\n            isResizing = true;\n        }\n        clearTimeout(resizeDoneTimer);\n        resizeDoneTimer = setTimeout(() => {\n            isResizing = false;\n            initialWindowWidth = null;\n            currentWindowWidth = null;\n            if (isResizingHorizontally) {\n                isResizingHorizontally = false;\n                store.dispatch('DID_STOP_RESIZE');\n            }\n        }, 500);\n    };\n    window.addEventListener('resize', resizeHandler);\n\n    // render initial view\n    const view = root(store, { id: getUniqueId() });\n\n    //\n    // PRIVATE API -------------------------------------------------------------------------------------\n    //\n    let isResting = false;\n    let isHidden = false;\n\n    const readWriteApi = {\n        // necessary for update loop\n\n        /**\n         * Reads from dom (never call manually)\n         * @private\n         */\n        _read: () => {\n            // test if we're resizing horizontally\n            // TODO: see if we can optimize this by measuring root rect\n            if (isResizing) {\n                currentWindowWidth = window.innerWidth;\n                if (!initialWindowWidth) {\n                    initialWindowWidth = currentWindowWidth;\n                }\n\n                if (!isResizingHorizontally && currentWindowWidth !== initialWindowWidth) {\n                    store.dispatch('DID_START_RESIZE');\n                    isResizingHorizontally = true;\n                }\n            }\n\n            if (isHidden && isResting) {\n                // test if is no longer hidden\n                isResting = view.element.offsetParent === null;\n            }\n\n            // if resting, no need to read as numbers will still all be correct\n            if (isResting) return;\n\n            // read view data\n            view._read();\n\n            // if is hidden we need to know so we exit rest mode when revealed\n            isHidden = view.rect.element.hidden;\n        },\n\n        /**\n         * Writes to dom (never call manually)\n         * @private\n         */\n        _write: ts => {\n            // get all actions from store\n            const actions = store\n                .processActionQueue()\n\n                // filter out set actions (these will automatically trigger DID_SET)\n                .filter(action => !/^SET_/.test(action.type));\n\n            // if was idling and no actions stop here\n            if (isResting && !actions.length) return;\n\n            // some actions might trigger events\n            routeActionsToEvents(actions);\n\n            // update the view\n            isResting = view._write(ts, actions, isResizingHorizontally);\n\n            // will clean up all archived items\n            removeReleasedItems(store.query('GET_ITEMS'));\n\n            // now idling\n            if (isResting) {\n                store.processDispatchQueue();\n            }\n        },\n    };\n\n    //\n    // EXPOSE EVENTS -------------------------------------------------------------------------------------\n    //\n    const createEvent = name => data => {\n        // create default event\n        const event = {\n            type: name,\n        };\n\n        // no data to add\n        if (!data) {\n            return event;\n        }\n\n        // copy relevant props\n        if (data.hasOwnProperty('error')) {\n            event.error = data.error ? { ...data.error } : null;\n        }\n\n        if (data.status) {\n            event.status = { ...data.status };\n        }\n\n        if (data.file) {\n            event.output = data.file;\n        }\n\n        // only source is available, else add item if possible\n        if (data.source) {\n            event.file = data.source;\n        } else if (data.item || data.id) {\n            const item = data.item ? data.item : store.query('GET_ITEM', data.id);\n            event.file = item ? createItemAPI(item) : null;\n        }\n\n        // map all items in a possible items array\n        if (data.items) {\n            event.items = data.items.map(createItemAPI);\n        }\n\n        // if this is a progress event add the progress amount\n        if (/progress/.test(name)) {\n            event.progress = data.progress;\n        }\n\n        // copy relevant props\n        if (data.hasOwnProperty('origin') && data.hasOwnProperty('target')) {\n            event.origin = data.origin;\n            event.target = data.target;\n        }\n\n        return event;\n    };\n\n    const eventRoutes = {\n        DID_DESTROY: createEvent('destroy'),\n\n        DID_INIT: createEvent('init'),\n\n        DID_THROW_MAX_FILES: createEvent('warning'),\n\n        DID_INIT_ITEM: createEvent('initfile'),\n        DID_START_ITEM_LOAD: createEvent('addfilestart'),\n        DID_UPDATE_ITEM_LOAD_PROGRESS: createEvent('addfileprogress'),\n        DID_LOAD_ITEM: createEvent('addfile'),\n\n        DID_THROW_ITEM_INVALID: [createEvent('error'), createEvent('addfile')],\n\n        DID_THROW_ITEM_LOAD_ERROR: [createEvent('error'), createEvent('addfile')],\n\n        DID_THROW_ITEM_REMOVE_ERROR: [createEvent('error'), createEvent('removefile')],\n\n        DID_PREPARE_OUTPUT: createEvent('preparefile'),\n\n        DID_START_ITEM_PROCESSING: createEvent('processfilestart'),\n        DID_UPDATE_ITEM_PROCESS_PROGRESS: createEvent('processfileprogress'),\n        DID_ABORT_ITEM_PROCESSING: createEvent('processfileabort'),\n        DID_COMPLETE_ITEM_PROCESSING: createEvent('processfile'),\n        DID_COMPLETE_ITEM_PROCESSING_ALL: createEvent('processfiles'),\n        DID_REVERT_ITEM_PROCESSING: createEvent('processfilerevert'),\n\n        DID_THROW_ITEM_PROCESSING_ERROR: [createEvent('error'), createEvent('processfile')],\n\n        DID_REMOVE_ITEM: createEvent('removefile'),\n\n        DID_UPDATE_ITEMS: createEvent('updatefiles'),\n\n        DID_ACTIVATE_ITEM: createEvent('activatefile'),\n\n        DID_REORDER_ITEMS: createEvent('reorderfiles'),\n    };\n\n    const exposeEvent = event => {\n        // create event object to be dispatched\n        const detail = { pond: exports, ...event };\n        delete detail.type;\n        view.element.dispatchEvent(\n            new CustomEvent(`FilePond:${event.type}`, {\n                // event info\n                detail,\n\n                // event behaviour\n                bubbles: true,\n                cancelable: true,\n                composed: true, // triggers listeners outside of shadow root\n            })\n        );\n\n        // event object to params used for `on()` event handlers and callbacks `oninit()`\n        const params = [];\n\n        // if is possible error event, make it the first param\n        if (event.hasOwnProperty('error')) {\n            params.push(event.error);\n        }\n\n        // file is always section\n        if (event.hasOwnProperty('file')) {\n            params.push(event.file);\n        }\n\n        // append other props\n        const filtered = ['type', 'error', 'file'];\n        Object.keys(event)\n            .filter(key => !filtered.includes(key))\n            .forEach(key => params.push(event[key]));\n\n        // on(type, () => { })\n        exports.fire(event.type, ...params);\n\n        // oninit = () => {}\n        const handler = store.query(`GET_ON${event.type.toUpperCase()}`);\n        if (handler) {\n            handler(...params);\n        }\n    };\n\n    const routeActionsToEvents = actions => {\n        if (!actions.length) return;\n        actions\n            .filter(action => eventRoutes[action.type])\n            .forEach(action => {\n                const routes = eventRoutes[action.type];\n                (Array.isArray(routes) ? routes : [routes]).forEach(route => {\n                    // this isn't fantastic, but because of the stacking of settimeouts plugins can handle the did_load before the did_init\n                    if (action.type === 'DID_INIT_ITEM') {\n                        exposeEvent(route(action.data));\n                    } else {\n                        setTimeout(() => {\n                            exposeEvent(route(action.data));\n                        }, 0);\n                    }\n                });\n            });\n    };\n\n    //\n    // PUBLIC API -------------------------------------------------------------------------------------\n    //\n    const setOptions = options => store.dispatch('SET_OPTIONS', { options });\n\n    const getFile = query => store.query('GET_ACTIVE_ITEM', query);\n\n    const prepareFile = query =>\n        new Promise((resolve, reject) => {\n            store.dispatch('REQUEST_ITEM_PREPARE', {\n                query,\n                success: item => {\n                    resolve(item);\n                },\n                failure: error => {\n                    reject(error);\n                },\n            });\n        });\n\n    const addFile = (source, options = {}) =>\n        new Promise((resolve, reject) => {\n            addFiles([{ source, options }], { index: options.index })\n                .then(items => resolve(items && items[0]))\n                .catch(reject);\n        });\n\n    const isFilePondFile = obj => obj.file && obj.id;\n\n    const removeFile = (query, options) => {\n        // if only passed options\n        if (typeof query === 'object' && !isFilePondFile(query) && !options) {\n            options = query;\n            query = undefined;\n        }\n\n        // request item removal\n        store.dispatch('REMOVE_ITEM', { ...options, query });\n\n        // see if item has been removed\n        return store.query('GET_ACTIVE_ITEM', query) === null;\n    };\n\n    const addFiles = (...args) =>\n        new Promise((resolve, reject) => {\n            const sources = [];\n            const options = {};\n\n            // user passed a sources array\n            if (isArray(args[0])) {\n                sources.push.apply(sources, args[0]);\n                Object.assign(options, args[1] || {});\n            } else {\n                // user passed sources as arguments, last one might be options object\n                const lastArgument = args[args.length - 1];\n                if (typeof lastArgument === 'object' && !(lastArgument instanceof Blob)) {\n                    Object.assign(options, args.pop());\n                }\n\n                // add rest to sources\n                sources.push(...args);\n            }\n\n            store.dispatch('ADD_ITEMS', {\n                items: sources,\n                index: options.index,\n                interactionMethod: InteractionMethod.API,\n                success: resolve,\n                failure: reject,\n            });\n        });\n\n    const getFiles = () => store.query('GET_ACTIVE_ITEMS');\n\n    const processFile = query =>\n        new Promise((resolve, reject) => {\n            store.dispatch('REQUEST_ITEM_PROCESSING', {\n                query,\n                success: item => {\n                    resolve(item);\n                },\n                failure: error => {\n                    reject(error);\n                },\n            });\n        });\n\n    const prepareFiles = (...args) => {\n        const queries = Array.isArray(args[0]) ? args[0] : args;\n        const items = queries.length ? queries : getFiles();\n        return Promise.all(items.map(prepareFile));\n    };\n\n    const processFiles = (...args) => {\n        const queries = Array.isArray(args[0]) ? args[0] : args;\n        if (!queries.length) {\n            const files = getFiles().filter(\n                item =>\n                    !(item.status === ItemStatus.IDLE && item.origin === FileOrigin.LOCAL) &&\n                    item.status !== ItemStatus.PROCESSING &&\n                    item.status !== ItemStatus.PROCESSING_COMPLETE &&\n                    item.status !== ItemStatus.PROCESSING_REVERT_ERROR\n            );\n            return Promise.all(files.map(processFile));\n        }\n        return Promise.all(queries.map(processFile));\n    };\n\n    const removeFiles = (...args) => {\n        const queries = Array.isArray(args[0]) ? args[0] : args;\n\n        let options;\n        if (typeof queries[queries.length - 1] === 'object') {\n            options = queries.pop();\n        } else if (Array.isArray(args[0])) {\n            options = args[1];\n        }\n\n        const files = getFiles();\n\n        if (!queries.length) return Promise.all(files.map(file => removeFile(file, options)));\n\n        // when removing by index the indexes shift after each file removal so we need to convert indexes to ids\n        const mappedQueries = queries\n            .map(query => (isNumber(query) ? (files[query] ? files[query].id : null) : query))\n            .filter(query => query);\n\n        return mappedQueries.map(q => removeFile(q, options));\n    };\n\n    const exports = {\n        // supports events\n        ...on(),\n\n        // inject private api methods\n        ...readWriteApi,\n\n        // inject all getters and setters\n        ...createOptionAPI(store, defaultOptions),\n\n        /**\n         * Override options defined in options object\n         * @param options\n         */\n        setOptions,\n\n        /**\n         * Load the given file\n         * @param source - the source of the file (either a File, base64 data uri or url)\n         * @param options - object, { index: 0 }\n         */\n        addFile,\n\n        /**\n         * Load the given files\n         * @param sources - the sources of the files to load\n         * @param options - object, { index: 0 }\n         */\n        addFiles,\n\n        /**\n         * Returns the file objects matching the given query\n         * @param query { string, number, null }\n         */\n        getFile,\n\n        /**\n         * Upload file with given name\n         * @param query { string, number, null  }\n         */\n        processFile,\n\n        /**\n         * Request prepare output for file with given name\n         * @param query { string, number, null  }\n         */\n        prepareFile,\n\n        /**\n         * Removes a file by its name\n         * @param query { string, number, null  }\n         */\n        removeFile,\n\n        /**\n         * Moves a file to a new location in the files list\n         */\n        moveFile: (query, index) => store.dispatch('MOVE_ITEM', { query, index }),\n\n        /**\n         * Returns all files (wrapped in public api)\n         */\n        getFiles,\n\n        /**\n         * Starts uploading all files\n         */\n        processFiles,\n\n        /**\n         * Clears all files from the files list\n         */\n        removeFiles,\n\n        /**\n         * Starts preparing output of all files\n         */\n        prepareFiles,\n\n        /**\n         * Sort list of files\n         */\n        sort: compare => store.dispatch('SORT', { compare }),\n\n        /**\n         * Browse the file system for a file\n         */\n        browse: () => {\n            // needs to be trigger directly as user action needs to be traceable (is not traceable in requestAnimationFrame)\n            var input = view.element.querySelector('input[type=file]');\n            if (input) {\n                input.click();\n            }\n        },\n\n        /**\n         * Destroys the app\n         */\n        destroy: () => {\n            // request destruction\n            exports.fire('destroy', view.element);\n\n            // stop active processes (file uploads, fetches, stuff like that)\n            // loop over items and depending on states call abort for ongoing processes\n            store.dispatch('ABORT_ALL');\n\n            // destroy view\n            view._destroy();\n\n            // stop listening to resize\n            window.removeEventListener('resize', resizeHandler);\n\n            // stop listening to the visiblitychange event\n            document.removeEventListener('visibilitychange', visibilityHandler);\n\n            // dispatch destroy\n            store.dispatch('DID_DESTROY');\n        },\n\n        /**\n         * Inserts the plugin before the target element\n         */\n        insertBefore: element => insertBefore(view.element, element),\n\n        /**\n         * Inserts the plugin after the target element\n         */\n        insertAfter: element => insertAfter(view.element, element),\n\n        /**\n         * Appends the plugin to the target element\n         */\n        appendTo: element => element.appendChild(view.element),\n\n        /**\n         * Replaces an element with the app\n         */\n        replaceElement: element => {\n            // insert the app before the element\n            insertBefore(view.element, element);\n\n            // remove the original element\n            element.parentNode.removeChild(element);\n\n            // remember original element\n            originalElement = element;\n        },\n\n        /**\n         * Restores the original element\n         */\n        restoreElement: () => {\n            if (!originalElement) {\n                return; // no element to restore\n            }\n\n            // restore original element\n            insertAfter(originalElement, view.element);\n\n            // remove our element\n            view.element.parentNode.removeChild(view.element);\n\n            // remove reference\n            originalElement = null;\n        },\n\n        /**\n         * Returns true if the app root is attached to given element\n         * @param element\n         */\n        isAttachedTo: element => view.element === element || originalElement === element,\n\n        /**\n         * Returns the root element\n         */\n        element: {\n            get: () => view.element,\n        },\n\n        /**\n         * Returns the current pond status\n         */\n        status: {\n            get: () => store.query('GET_STATUS'),\n        },\n    };\n\n    // Done!\n    store.dispatch('DID_INIT');\n\n    // create actual api object\n    return createObject(exports);\n};\n\nconst createAppObject = (customOptions = {}) => {\n    // default options\n    const defaultOptions = {};\n    forin(getOptions(), (key, value) => {\n        defaultOptions[key] = value[0];\n    });\n\n    // set app options\n    const app = createApp({\n        // default options\n        ...defaultOptions,\n\n        // custom options\n        ...customOptions,\n    });\n\n    // return the plugin instance\n    return app;\n};\n\nconst lowerCaseFirstLetter = string => string.charAt(0).toLowerCase() + string.slice(1);\n\nconst attributeNameToPropertyName = attributeName => toCamels(attributeName.replace(/^data-/, ''));\n\nconst mapObject = (object, propertyMap) => {\n    // remove unwanted\n    forin(propertyMap, (selector, mapping) => {\n        forin(object, (property, value) => {\n            // create regexp shortcut\n            const selectorRegExp = new RegExp(selector);\n\n            // tests if\n            const matches = selectorRegExp.test(property);\n\n            // no match, skip\n            if (!matches) {\n                return;\n            }\n\n            // if there's a mapping, the original property is always removed\n            delete object[property];\n\n            // should only remove, we done!\n            if (mapping === false) {\n                return;\n            }\n\n            // move value to new property\n            if (isString(mapping)) {\n                object[mapping] = value;\n                return;\n            }\n\n            // move to group\n            const group = mapping.group;\n            if (isObject(mapping) && !object[group]) {\n                object[group] = {};\n            }\n\n            object[group][lowerCaseFirstLetter(property.replace(selectorRegExp, ''))] = value;\n        });\n\n        // do submapping\n        if (mapping.mapping) {\n            mapObject(object[mapping.group], mapping.mapping);\n        }\n    });\n};\n\nconst getAttributesAsObject = (node, attributeMapping = {}) => {\n    // turn attributes into object\n    const attributes = [];\n    forin(node.attributes, index => {\n        attributes.push(node.attributes[index]);\n    });\n\n    const output = attributes\n        .filter(attribute => attribute.name)\n        .reduce((obj, attribute) => {\n            const value = attr(node, attribute.name);\n\n            obj[attributeNameToPropertyName(attribute.name)] =\n                value === attribute.name ? true : value;\n            return obj;\n        }, {});\n\n    // do mapping of object properties\n    mapObject(output, attributeMapping);\n\n    return output;\n};\n\nconst createAppAtElement = (element, options = {}) => {\n    // how attributes of the input element are mapped to the options for the plugin\n    const attributeMapping = {\n        // translate to other name\n        '^class$': 'className',\n        '^multiple$': 'allowMultiple',\n        '^capture$': 'captureMethod',\n        '^webkitdirectory$': 'allowDirectoriesOnly',\n\n        // group under single property\n        '^server': {\n            group: 'server',\n            mapping: {\n                '^process': {\n                    group: 'process',\n                },\n                '^revert': {\n                    group: 'revert',\n                },\n                '^fetch': {\n                    group: 'fetch',\n                },\n                '^restore': {\n                    group: 'restore',\n                },\n                '^load': {\n                    group: 'load',\n                },\n            },\n        },\n\n        // don't include in object\n        '^type$': false,\n        '^files$': false,\n    };\n\n    // add additional option translators\n    applyFilters('SET_ATTRIBUTE_TO_OPTION_MAP', attributeMapping);\n\n    // create final options object by setting options object and then overriding options supplied on element\n    const mergedOptions = {\n        ...options,\n    };\n\n    const attributeOptions = getAttributesAsObject(\n        element.nodeName === 'FIELDSET' ? element.querySelector('input[type=file]') : element,\n        attributeMapping\n    );\n\n    // merge with options object\n    Object.keys(attributeOptions).forEach(key => {\n        if (isObject(attributeOptions[key])) {\n            if (!isObject(mergedOptions[key])) {\n                mergedOptions[key] = {};\n            }\n            Object.assign(mergedOptions[key], attributeOptions[key]);\n        } else {\n            mergedOptions[key] = attributeOptions[key];\n        }\n    });\n\n    // if parent is a fieldset, get files from parent by selecting all input fields that are not file upload fields\n    // these will then be automatically set to the initial files\n    mergedOptions.files = (options.files || []).concat(\n        Array.from(element.querySelectorAll('input:not([type=file])')).map(input => ({\n            source: input.value,\n            options: {\n                type: input.dataset.type,\n            },\n        }))\n    );\n\n    // build plugin\n    const app = createAppObject(mergedOptions);\n\n    // add already selected files\n    if (element.files) {\n        Array.from(element.files).forEach(file => {\n            app.addFile(file);\n        });\n    }\n\n    // replace the target element\n    app.replaceElement(element);\n\n    // expose\n    return app;\n};\n\n// if an element is passed, we create the instance at that element, if not, we just create an up object\nconst createApp$1 = (...args) =>\n    isNode(args[0]) ? createAppAtElement(...args) : createAppObject(...args);\n\nconst PRIVATE_METHODS = ['fire', '_read', '_write'];\n\nconst createAppAPI = app => {\n    const api = {};\n\n    copyObjectPropertiesToObject(app, api, PRIVATE_METHODS);\n\n    return api;\n};\n\n/**\n * Replaces placeholders in given string with replacements\n * @param string - \"Foo {bar}\"\"\n * @param replacements - { \"bar\": 10 }\n */\nconst replaceInString = (string, replacements) =>\n    string.replace(/(?:{([a-zA-Z]+)})/g, (match, group) => replacements[group]);\n\nconst createWorker = fn => {\n    const workerBlob = new Blob(['(', fn.toString(), ')()'], {\n        type: 'application/javascript',\n    });\n    const workerURL = URL.createObjectURL(workerBlob);\n    const worker = new Worker(workerURL);\n\n    return {\n        transfer: (message, cb) => {},\n        post: (message, cb, transferList) => {\n            const id = getUniqueId();\n\n            worker.onmessage = e => {\n                if (e.data.id === id) {\n                    cb(e.data.message);\n                }\n            };\n\n            worker.postMessage(\n                {\n                    id,\n                    message,\n                },\n                transferList\n            );\n        },\n        terminate: () => {\n            worker.terminate();\n            URL.revokeObjectURL(workerURL);\n        },\n    };\n};\n\nconst loadImage = url =>\n    new Promise((resolve, reject) => {\n        const img = new Image();\n        img.onload = () => {\n            resolve(img);\n        };\n        img.onerror = e => {\n            reject(e);\n        };\n        img.src = url;\n    });\n\nconst renameFile = (file, name) => {\n    const renamedFile = file.slice(0, file.size, file.type);\n    renamedFile.lastModifiedDate = file.lastModifiedDate;\n    renamedFile.name = name;\n    return renamedFile;\n};\n\nconst copyFile = file => renameFile(file, file.name);\n\n// already registered plugins (can't register twice)\nconst registeredPlugins = [];\n\n// pass utils to plugin\nconst createAppPlugin = plugin => {\n    // already registered\n    if (registeredPlugins.includes(plugin)) {\n        return;\n    }\n\n    // remember this plugin\n    registeredPlugins.push(plugin);\n\n    // setup!\n    const pluginOutline = plugin({\n        addFilter,\n        utils: {\n            Type,\n            forin,\n            isString,\n            isFile,\n            toNaturalFileSize,\n            replaceInString,\n            getExtensionFromFilename,\n            getFilenameWithoutExtension,\n            guesstimateMimeType,\n            getFileFromBlob,\n            getFilenameFromURL,\n            createRoute,\n            createWorker,\n            createView,\n            createItemAPI,\n            loadImage,\n            copyFile,\n            renameFile,\n            createBlob,\n            applyFilterChain,\n            text,\n            getNumericAspectRatioFromString,\n        },\n        views: {\n            fileActionButton,\n        },\n    });\n\n    // add plugin options to default options\n    extendDefaultOptions(pluginOutline.options);\n};\n\n// feature detection used by supported() method\nconst isOperaMini = () => Object.prototype.toString.call(window.operamini) === '[object OperaMini]';\nconst hasPromises = () => 'Promise' in window;\nconst hasBlobSlice = () => 'slice' in Blob.prototype;\nconst hasCreateObjectURL = () => 'URL' in window && 'createObjectURL' in window.URL;\nconst hasVisibility = () => 'visibilityState' in document;\nconst hasTiming = () => 'performance' in window; // iOS 8.x\nconst hasCSSSupports = () => 'supports' in (window.CSS || {}); // use to detect Safari 9+\nconst isIE11 = () => /MSIE|Trident/.test(window.navigator.userAgent);\n\nconst supported = (() => {\n    // Runs immediately and then remembers result for subsequent calls\n    const isSupported =\n        // Has to be a browser\n        isBrowser() &&\n        // Can't run on Opera Mini due to lack of everything\n        !isOperaMini() &&\n        // Require these APIs to feature detect a modern browser\n        hasVisibility() &&\n        hasPromises() &&\n        hasBlobSlice() &&\n        hasCreateObjectURL() &&\n        hasTiming() &&\n        // doesn't need CSSSupports but is a good way to detect Safari 9+ (we do want to support IE11 though)\n        (hasCSSSupports() || isIE11());\n\n    return () => isSupported;\n})();\n\n/**\n * Plugin internal state (over all instances)\n */\nconst state = {\n    // active app instances, used to redraw the apps and to find the later\n    apps: [],\n};\n\n// plugin name\nconst name = 'filepond';\n\n/**\n * Public Plugin methods\n */\nconst fn = () => {};\nlet Status$1 = {};\nlet FileStatus = {};\nlet FileOrigin$1 = {};\nlet OptionTypes = {};\nlet create$f = fn;\nlet destroy = fn;\nlet parse = fn;\nlet find = fn;\nlet registerPlugin = fn;\nlet getOptions$1 = fn;\nlet setOptions$1 = fn;\n\n// if not supported, no API\nif (supported()) {\n    // start painter and fire load event\n    createPainter(\n        () => {\n            state.apps.forEach(app => app._read());\n        },\n        ts => {\n            state.apps.forEach(app => app._write(ts));\n        }\n    );\n\n    // fire loaded event so we know when FilePond is available\n    const dispatch = () => {\n        // let others know we have area ready\n        document.dispatchEvent(\n            new CustomEvent('FilePond:loaded', {\n                detail: {\n                    supported,\n                    create: create$f,\n                    destroy,\n                    parse,\n                    find,\n                    registerPlugin,\n                    setOptions: setOptions$1,\n                },\n            })\n        );\n\n        // clean up event\n        document.removeEventListener('DOMContentLoaded', dispatch);\n    };\n\n    if (document.readyState !== 'loading') {\n        // move to back of execution queue, FilePond should have been exported by then\n        setTimeout(() => dispatch(), 0);\n    } else {\n        document.addEventListener('DOMContentLoaded', dispatch);\n    }\n\n    // updates the OptionTypes object based on the current options\n    const updateOptionTypes = () =>\n        forin(getOptions(), (key, value) => {\n            OptionTypes[key] = value[1];\n        });\n\n    Status$1 = { ...Status };\n    FileOrigin$1 = { ...FileOrigin };\n    FileStatus = { ...ItemStatus };\n\n    OptionTypes = {};\n    updateOptionTypes();\n\n    // create method, creates apps and adds them to the app array\n    create$f = (...args) => {\n        const app = createApp$1(...args);\n        app.on('destroy', destroy);\n        state.apps.push(app);\n        return createAppAPI(app);\n    };\n\n    // destroys apps and removes them from the app array\n    destroy = hook => {\n        // returns true if the app was destroyed successfully\n        const indexToRemove = state.apps.findIndex(app => app.isAttachedTo(hook));\n        if (indexToRemove >= 0) {\n            // remove from apps\n            const app = state.apps.splice(indexToRemove, 1)[0];\n\n            // restore original dom element\n            app.restoreElement();\n\n            return true;\n        }\n\n        return false;\n    };\n\n    // parses the given context for plugins (does not include the context element itself)\n    parse = context => {\n        // get all possible hooks\n        const matchedHooks = Array.from(context.querySelectorAll(`.${name}`));\n\n        // filter out already active hooks\n        const newHooks = matchedHooks.filter(\n            newHook => !state.apps.find(app => app.isAttachedTo(newHook))\n        );\n\n        // create new instance for each hook\n        return newHooks.map(hook => create$f(hook));\n    };\n\n    // returns an app based on the given element hook\n    find = hook => {\n        const app = state.apps.find(app => app.isAttachedTo(hook));\n        if (!app) {\n            return null;\n        }\n        return createAppAPI(app);\n    };\n\n    // adds a plugin extension\n    registerPlugin = (...plugins) => {\n        // register plugins\n        plugins.forEach(createAppPlugin);\n\n        // update OptionTypes, each plugin might have extended the default options\n        updateOptionTypes();\n    };\n\n    getOptions$1 = () => {\n        const opts = {};\n        forin(getOptions(), (key, value) => {\n            opts[key] = value[0];\n        });\n        return opts;\n    };\n\n    setOptions$1 = opts => {\n        if (isObject(opts)) {\n            // update existing plugins\n            state.apps.forEach(app => {\n                app.setOptions(opts);\n            });\n\n            // override defaults\n            setOptions(opts);\n        }\n\n        // return new options\n        return getOptions$1();\n    };\n}\n\nexport {\n    FileOrigin$1 as FileOrigin,\n    FileStatus,\n    OptionTypes,\n    Status$1 as Status,\n    create$f as create,\n    destroy,\n    find,\n    getOptions$1 as getOptions,\n    parse,\n    registerPlugin,\n    setOptions$1 as setOptions,\n    supported,\n};\n"], "mappings": ";;;;;;;;AAUA,mBAAkD;;;ACFlD,IAAM,SAAS,WAAS,iBAAiB;AAEzC,IAAM,cAAc,CAAC,cAAcA,WAAU,CAAC,GAAGC,WAAU,CAAC,MAAM;AAE9D,QAAMC,SAAQ;AAAA,IACV,GAAG;AAAA,EACP;AAGA,QAAM,cAAc,CAAC;AACrB,QAAM,gBAAgB,CAAC;AAGvB,QAAM,WAAW,OAAO,EAAE,GAAGA,OAAM;AAGnC,QAAM,qBAAqB,MAAM;AAE7B,UAAM,QAAQ,CAAC,GAAG,WAAW;AAG7B,gBAAY,SAAS;AAErB,WAAO;AAAA,EACX;AAGA,QAAM,uBAAuB,MAAM;AAE/B,UAAM,QAAQ,CAAC,GAAG,aAAa;AAG/B,kBAAc,SAAS;AAGvB,UAAM,QAAQ,CAAC,EAAE,MAAM,MAAAC,MAAK,MAAM;AAC9B,eAAS,MAAMA,KAAI;AAAA,IACvB,CAAC;AAAA,EACL;AAGA,QAAM,WAAW,CAAC,MAAMA,OAAM,eAAe;AAEzC,QAAI,cAAc,CAAC,SAAS,QAAQ;AAChC,oBAAc,KAAK,EAAE,MAAM,MAAAA,MAAK,CAAC;AACjC;AAAA,IACJ;AAGA,QAAI,eAAe,IAAI,GAAG;AACtB,qBAAe,IAAI,EAAEA,KAAI;AAAA,IAC7B;AAGA,gBAAY,KAAK;AAAA,MACb;AAAA,MACA,MAAAA;AAAA,IACJ,CAAC;AAAA,EACL;AAEA,QAAM,QAAQ,CAAC,QAAQ,SAAU,aAAa,GAAG,IAAI,aAAa,GAAG,EAAE,GAAG,IAAI,IAAI;AAElF,QAAM,MAAM;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAEA,MAAI,eAAe,CAAC;AACpB,EAAAH,SAAQ,QAAQ,CAAAI,WAAS;AACrB,mBAAe;AAAA,MACX,GAAGA,OAAMF,MAAK;AAAA,MACd,GAAG;AAAA,IACP;AAAA,EACJ,CAAC;AAED,MAAI,iBAAiB,CAAC;AACtB,EAAAD,SAAQ,QAAQ,YAAU;AACtB,qBAAiB;AAAA,MACb,GAAG,OAAO,UAAU,OAAOC,MAAK;AAAA,MAChC,GAAG;AAAA,IACP;AAAA,EACJ,CAAC;AAED,SAAO;AACX;AAEA,IAAM,iBAAiB,CAAC,KAAK,UAAU,eAAe;AAClD,MAAI,OAAO,eAAe,YAAY;AAClC,QAAI,QAAQ,IAAI;AAChB;AAAA,EACJ;AACA,SAAO,eAAe,KAAK,UAAU,EAAE,GAAG,WAAW,CAAC;AAC1D;AAEA,IAAM,QAAQ,CAAC,KAAK,OAAO;AACvB,aAAW,OAAO,KAAK;AACnB,QAAI,CAAC,IAAI,eAAe,GAAG,GAAG;AAC1B;AAAA,IACJ;AAEA,OAAG,KAAK,IAAI,GAAG,CAAC;AAAA,EACpB;AACJ;AAEA,IAAM,eAAe,gBAAc;AAC/B,QAAM,MAAM,CAAC;AACb,QAAM,YAAY,cAAY;AAC1B,mBAAe,KAAK,UAAU,WAAW,QAAQ,CAAC;AAAA,EACtD,CAAC;AACD,SAAO;AACX;AAEA,IAAM,OAAO,CAAC,MAAMG,OAAM,QAAQ,SAAS;AACvC,MAAI,UAAU,MAAM;AAChB,WAAO,KAAK,aAAaA,KAAI,KAAK,KAAK,aAAaA,KAAI;AAAA,EAC5D;AACA,OAAK,aAAaA,OAAM,KAAK;AACjC;AAEA,IAAM,KAAK;AACX,IAAM,cAAc,CAAC,OAAO,MAAM;AAElC,IAAM,eAAe,SAAO,YAAY,SAAS,GAAG;AAEpD,IAAM,gBAAgB,CAAC,KAAK,WAAW,aAAa,CAAC,MAAM;AACvD,MAAI,OAAO,cAAc,UAAU;AAC/B,iBAAa;AACb,gBAAY;AAAA,EAChB;AACA,QAAM,UAAU,aAAa,GAAG,IAC1B,SAAS,gBAAgB,IAAI,GAAG,IAChC,SAAS,cAAc,GAAG;AAChC,MAAI,WAAW;AACX,QAAI,aAAa,GAAG,GAAG;AACnB,WAAK,SAAS,SAAS,SAAS;AAAA,IACpC,OAAO;AACH,cAAQ,YAAY;AAAA,IACxB;AAAA,EACJ;AACA,QAAM,YAAY,CAACA,OAAM,UAAU;AAC/B,SAAK,SAASA,OAAM,KAAK;AAAA,EAC7B,CAAC;AACD,SAAO;AACX;AAEA,IAAM,cAAc,YAAU,CAAC,OAAO,UAAU;AAC5C,MAAI,OAAO,UAAU,eAAe,OAAO,SAAS,KAAK,GAAG;AACxD,WAAO,aAAa,OAAO,OAAO,SAAS,KAAK,CAAC;AAAA,EACrD,OAAO;AACH,WAAO,YAAY,KAAK;AAAA,EAC5B;AACJ;AAEA,IAAM,kBAAkB,CAAC,QAAQ,eAAe,CAAC,MAAM,UAAU;AAC7D,MAAI,OAAO,UAAU,aAAa;AAC9B,eAAW,OAAO,OAAO,GAAG,IAAI;AAAA,EACpC,OAAO;AACH,eAAW,KAAK,IAAI;AAAA,EACxB;AAEA,SAAO;AACX;AAEA,IAAM,kBAAkB,CAAC,QAAQ,eAAe,UAAQ;AAEpD,aAAW,OAAO,WAAW,QAAQ,IAAI,GAAG,CAAC;AAG7C,MAAI,KAAK,QAAQ,YAAY;AACzB,WAAO,YAAY,KAAK,OAAO;AAAA,EACnC;AAEA,SAAO;AACX;AAEA,IAAM,cAAc,MAChB,OAAO,WAAW,eAAe,OAAO,OAAO,aAAa,aAAa;AAC7E,IAAM,YAAY,MAAM;AAExB,IAAM,cAAc,UAAU,IAAI,cAAc,KAAK,IAAI,CAAC;AAC1D,IAAM,gBACF,cAAc,cAAc,QAAM,GAAG,SAAS,SAAS,QAAM,GAAG,WAAW;AAE/E,IAAM,cAAc,CAAC,aAAa,YAAY,QAAQ,UAAU;AAC5D,QAAM,OAAO,OAAO,CAAC,KAAK,YAAY;AACtC,QAAM,MAAM,OAAO,CAAC,KAAK,YAAY;AACrC,QAAM,QAAQ,OAAO,YAAY;AACjC,QAAM,SAAS,MAAM,YAAY,UAAU,MAAM,CAAC,KAAK;AAEvD,QAAM,OAAO;AAAA;AAAA,IAET,SAAS;AAAA,MACL,GAAG;AAAA,IACP;AAAA;AAAA,IAGA,OAAO;AAAA,MACH,MAAM,YAAY;AAAA,MAClB,KAAK,YAAY;AAAA,MACjB,OAAO,YAAY;AAAA,MACnB,QAAQ,YAAY;AAAA,IACxB;AAAA;AAAA;AAAA,IAIA,OAAO;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAGA,aACK,OAAO,eAAa,CAAC,UAAU,cAAc,CAAC,EAC9C,IAAI,eAAa,UAAU,IAAI,EAC/B,QAAQ,mBAAiB;AACtB,eAAW,KAAK,OAAO,EAAE,GAAG,cAAc,MAAM,CAAC;AACjD,eAAW,KAAK,OAAO,EAAE,GAAG,cAAc,MAAM,CAAC;AAAA,EACrD,CAAC;AAGL,oBAAkB,KAAK,KAAK;AAG5B,OAAK,MAAM,UAAU,KAAK,QAAQ;AAClC,OAAK,MAAM,SAAS,KAAK,QAAQ;AAGjC,oBAAkB,KAAK,KAAK;AAE5B,SAAO;AACX;AAEA,IAAM,aAAa,CAAC,QAAQ,UAAU;AAElC,QAAM,OAAO,OAAO;AACpB,QAAM,SAAS,OAAO;AACtB,QAAM,UAAU,OAAO;AACvB,QAAM,QAAQ,OAAO;AAErB,MAAI,MAAM,SAAS,OAAO,QAAQ;AAC9B,WAAO,SAAS,MAAM;AAAA,EAC1B;AAEA,MAAI,MAAM,QAAQ,OAAO,OAAO;AAC5B,WAAO,QAAQ,MAAM;AAAA,EACzB;AACJ;AAEA,IAAM,oBAAoB,UAAQ;AAC9B,OAAK,QAAQ,KAAK,QAAQ,KAAK;AAC/B,OAAK,SAAS,KAAK,SAAS,KAAK;AACrC;AAEA,IAAM,WAAW,WAAS,OAAO,UAAU;AAU3C,IAAM,WAAW,CAAC,UAAU,aAAa,UAAU,cAAc,SAAU;AACvE,SAAO,KAAK,IAAI,WAAW,WAAW,IAAI,eAAe,KAAK,IAAI,QAAQ,IAAI;AAClF;AAKA,IAAM;AAAA;AAAA,EAEF,CAAC,EAAE,YAAY,KAAK,UAAU,MAAM,OAAO,GAAG,IAAI,CAAC,MAE/C;AACI,QAAI,SAAS;AACb,QAAI,WAAW;AACf,QAAI,WAAW;AACf,QAAI,UAAU;AAGd,UAAM,cAAc,CAAC,IAAI,mBAAmB;AAExC,UAAI,QAAS;AAGb,UAAI,EAAE,SAAS,MAAM,KAAK,SAAS,QAAQ,IAAI;AAC3C,kBAAU;AACV,mBAAW;AACX;AAAA,MACJ;AAGA,YAAM,IAAI,EAAE,WAAW,UAAU;AAGjC,kBAAY,IAAI;AAGhB,kBAAY;AAGZ,kBAAY;AAGZ,UAAI,SAAS,UAAU,QAAQ,QAAQ,KAAK,gBAAgB;AACxD,mBAAW;AACX,mBAAW;AACX,kBAAU;AAGV,YAAI,SAAS,QAAQ;AACrB,YAAI,WAAW,QAAQ;AAAA,MAC3B,OAAO;AAEH,YAAI,SAAS,QAAQ;AAAA,MACzB;AAAA,IACJ;AAMA,UAAM,YAAY,WAAS;AAEvB,UAAI,SAAS,KAAK,KAAK,CAAC,SAAS,QAAQ,GAAG;AACxC,mBAAW;AAAA,MACf;AAGA,UAAI,WAAW,MAAM;AACjB,iBAAS;AACT,mBAAW;AAAA,MACf;AAGA,eAAS;AAGT,UAAI,aAAa,UAAU,OAAO,WAAW,aAAa;AAEtD,kBAAU;AACV,mBAAW;AAGX,YAAI,SAAS,QAAQ;AACrB,YAAI,WAAW,QAAQ;AAEvB;AAAA,MACJ;AAEA,gBAAU;AAAA,IACd;AAGA,UAAM,MAAM,aAAa;AAAA,MACrB;AAAA,MACA,QAAQ;AAAA,QACJ,KAAK;AAAA,QACL,KAAK,MAAM;AAAA,MACf;AAAA,MACA,SAAS;AAAA,QACL,KAAK,MAAM;AAAA,MACf;AAAA,MACA,UAAU,WAAS;AAAA,MAAC;AAAA,MACpB,YAAY,WAAS;AAAA,MAAC;AAAA,IAC1B,CAAC;AAED,WAAO;AAAA,EACX;AAAA;AAGR,IAAM,gBAAgB,OAAM,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI,KAAK;AAErE,IAAM;AAAA;AAAA,EAEF,CAAC,EAAE,WAAW,KAAK,SAAS,eAAe,QAAQ,EAAE,IAAI,CAAC,MAEtD;AACI,QAAI,QAAQ;AACZ,QAAI;AACJ,QAAI;AACJ,QAAI,UAAU;AACd,QAAI,UAAU;AACd,QAAI,SAAS;AAEb,UAAM,cAAc,CAAC,IAAI,mBAAmB;AACxC,UAAI,WAAW,WAAW,KAAM;AAEhC,UAAI,UAAU,MAAM;AAChB,gBAAQ;AAAA,MACZ;AAEA,UAAI,KAAK,QAAQ,MAAO;AAExB,UAAI,KAAK,QAAQ;AAEjB,UAAI,KAAK,YAAY,gBAAgB;AACjC,YAAI;AACJ,YAAI,UAAU,IAAI;AAClB,YAAI,SAAS,IAAI,MAAM;AACvB,YAAI,WAAW,IAAI,MAAM;AACzB,kBAAU;AAAA,MACd,OAAO;AACH,YAAI,IAAI;AACR,YAAI,UAAU,KAAK,IAAI,OAAO,UAAU,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM;AAAA,MACpE;AAAA,IACJ;AAGA,UAAM,MAAM,aAAa;AAAA,MACrB;AAAA,MACA,QAAQ;AAAA,QACJ,KAAK,MAAO,UAAU,IAAI;AAAA,QAC1B,KAAK,WAAS;AAEV,cAAI,WAAW,MAAM;AACjB,qBAAS;AACT,gBAAI,SAAS,KAAK;AAClB,gBAAI,WAAW,KAAK;AACpB;AAAA,UACJ;AAGA,cAAI,QAAQ,QAAQ;AAChB,qBAAS;AACT,sBAAU;AAAA,UACd,OAAO;AAEH,sBAAU;AACV,qBAAS;AAAA,UACb;AAGA,oBAAU;AACV,kBAAQ;AAAA,QACZ;AAAA,MACJ;AAAA,MACA,SAAS;AAAA,QACL,KAAK,MAAM;AAAA,MACf;AAAA,MACA,UAAU,WAAS;AAAA,MAAC;AAAA,MACpB,YAAY,WAAS;AAAA,MAAC;AAAA,IAC1B,CAAC;AAED,WAAO;AAAA,EACX;AAAA;AAER,IAAM,WAAW;AAAA,EACb;AAAA,EACA;AACJ;AAOA,IAAM,iBAAiB,CAAC,YAAY,UAAU,aAAa;AAGvD,QAAM,MACF,WAAW,QAAQ,KAAK,OAAO,WAAW,QAAQ,EAAE,QAAQ,MAAM,WAC5D,WAAW,QAAQ,EAAE,QAAQ,IAC7B,WAAW,QAAQ,KAAK;AAElC,QAAM,OAAO,OAAO,QAAQ,WAAW,MAAM,IAAI;AACjD,QAAM,QAAQ,OAAO,QAAQ,WAAW,EAAE,GAAG,IAAI,IAAI,CAAC;AAEtD,SAAO,SAAS,IAAI,IAAI,SAAS,IAAI,EAAE,KAAK,IAAI;AACpD;AAEA,IAAM,YAAY,CAAC,MAAM,KAAK,OAAO,YAAY,UAAU;AACvD,QAAM,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,GAAG;AACrC,MAAI,QAAQ,OAAK;AACb,SAAK,QAAQ,SAAO;AAChB,UAAIC,QAAO;AACX,UAAI,SAAS,MAAM,MAAM,GAAG;AAC5B,UAAI,SAAS,WAAU,MAAM,GAAG,IAAI;AAEpC,UAAI,OAAO,QAAQ,UAAU;AACzB,QAAAA,QAAO,IAAI;AACX,iBAAS,IAAI,UAAU;AACvB,iBAAS,IAAI,UAAU;AAAA,MAC3B;AAEA,UAAI,EAAEA,KAAI,KAAK,CAAC,WAAW;AACvB;AAAA,MACJ;AAEA,QAAEA,KAAI,IAAI;AAAA,QACN,KAAK;AAAA,QACL,KAAK;AAAA,MACT;AAAA,IACJ,CAAC;AAAA,EACL,CAAC;AACL;AAMA,IAAM,aAAa,CAAC,EAAE,aAAa,WAAW,iBAAiB,gBAAgB,MAAM;AAEjF,QAAM,eAAe,EAAE,GAAG,UAAU;AAGpC,QAAMC,cAAa,CAAC;AAGpB,QAAM,aAAa,CAAC,UAAU,cAAc;AACxC,UAAMC,YAAW,eAAe,SAAS;AACzC,QAAI,CAACA,WAAU;AACX;AAAA,IACJ;AAGA,IAAAA,UAAS,WAAW,WAAS;AACzB,gBAAU,QAAQ,IAAI;AAAA,IAC1B;AAGA,IAAAA,UAAS,SAAS,aAAa,QAAQ;AAGvC,UAAM,OAAO;AAAA,MACT,KAAK;AAAA,MACL,QAAQ,WAAS;AAEb,YAAIA,UAAS,WAAW,OAAO;AAC3B;AAAA,QACJ;AAEA,QAAAA,UAAS,SAAS;AAAA,MACtB;AAAA,MACA,QAAQ,MAAM,UAAU,QAAQ;AAAA,IACpC;AAGA,cAAU,CAAC,IAAI,GAAG,CAAC,iBAAiB,eAAe,GAAG,WAAW,IAAI;AAGrE,IAAAD,YAAW,KAAKC,SAAQ;AAAA,EAC5B,CAAC;AAGD,SAAO;AAAA,IACH,OAAO,QAAM;AACT,UAAI,iBAAiB,SAAS;AAC9B,UAAI,UAAU;AACd,MAAAD,YAAW,QAAQ,eAAa;AAC5B,YAAI,CAAC,UAAU,QAAS,WAAU;AAClC,kBAAU,YAAY,IAAI,cAAc;AAAA,MAC5C,CAAC;AACD,aAAO;AAAA,IACX;AAAA,IACA,SAAS,MAAM;AAAA,IAAC;AAAA,EACpB;AACJ;AAEA,IAAM,WAAW,aAAW,CAAC,MAAME,QAAO;AACtC,UAAQ,iBAAiB,MAAMA,GAAE;AACrC;AAEA,IAAM,cAAc,aAAW,CAAC,MAAMA,QAAO;AACzC,UAAQ,oBAAoB,MAAMA,GAAE;AACxC;AAGA,IAAM,YAAY,CAAC;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ,MAAM;AACF,QAAM,SAAS,CAAC;AAEhB,QAAM,MAAM,SAAS,KAAK,OAAO;AACjC,QAAM,SAAS,YAAY,KAAK,OAAO;AAEvC,kBAAgB,KAAK,CAAC,MAAMA,QAAO;AAC/B,WAAO,KAAK;AAAA,MACR;AAAA,MACA,IAAAA;AAAA,IACJ,CAAC;AACD,QAAI,MAAMA,GAAE;AAAA,EAChB;AAEA,kBAAgB,MAAM,CAAC,MAAMA,QAAO;AAChC,WAAO,OAAO,OAAO,UAAU,WAAS,MAAM,SAAS,QAAQ,MAAM,OAAOA,GAAE,GAAG,CAAC;AAClF,WAAO,MAAMA,GAAE;AAAA,EACnB;AAEA,SAAO;AAAA,IACH,OAAO,MAAM;AAET,aAAO;AAAA,IACX;AAAA,IACA,SAAS,MAAM;AACX,aAAO,QAAQ,WAAS;AACpB,eAAO,MAAM,MAAM,MAAM,EAAE;AAAA,MAC/B,CAAC;AAAA,IACL;AAAA,EACJ;AACJ;AAIA,IAAM,OAAO,CAAC,EAAE,aAAa,WAAW,gBAAgB,MAAM;AAC1D,YAAU,aAAa,iBAAiB,SAAS;AACrD;AAEA,IAAM,YAAY,WAAS,SAAS;AAOpC,IAAM,WAAW;AAAA,EACb,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;AAEA,IAAM,SAAS,CAAC,EAAE,aAAa,WAAW,iBAAiB,iBAAiB,KAAK,MAAM;AAEnF,QAAM,eAAe,EAAE,GAAG,UAAU;AAGpC,QAAM,eAAe,CAAC;AAGtB,YAAU,aAAa,CAAC,iBAAiB,eAAe,GAAG,SAAS;AAGpE,QAAM,YAAY,MAAM,CAAC,UAAU,YAAY,KAAK,GAAG,UAAU,YAAY,KAAK,CAAC;AACnF,QAAM,WAAW,MAAM,CAAC,UAAU,QAAQ,KAAK,GAAG,UAAU,QAAQ,KAAK,CAAC;AAC1E,QAAM,UAAU,MACZ,KAAK,OAAO,YAAY,KAAK,MAAM,KAAK,YAAY,UAAU,GAAG,SAAS,CAAC,IAAI;AACnF,kBAAgB,OAAO,EAAE,KAAK,QAAQ;AACtC,kBAAgB,OAAO,EAAE,KAAK,QAAQ;AAGtC,cAAY,QAAQ,SAAO;AACvB,cAAU,GAAG,IACT,OAAO,aAAa,GAAG,MAAM,cAAc,SAAS,GAAG,IAAI,aAAa,GAAG;AAAA,EACnF,CAAC;AAGD,SAAO;AAAA,IACH,OAAO,MAAM;AAET,UAAI,CAAC,iBAAiB,cAAc,SAAS,GAAG;AAC5C;AAAA,MACJ;AAGA,kBAAY,KAAK,SAAS,SAAS;AAGnC,aAAO,OAAO,cAAc,EAAE,GAAG,UAAU,CAAC;AAG5C,aAAO;AAAA,IACX;AAAA,IACA,SAAS,MAAM;AAAA,IAAC;AAAA,EACpB;AACJ;AAEA,IAAM,mBAAmB,CAAC,cAAc,aAAa;AAEjD,MAAI,OAAO,KAAK,YAAY,EAAE,WAAW,OAAO,KAAK,QAAQ,EAAE,QAAQ;AACnE,WAAO;AAAA,EACX;AAGA,aAAW,QAAQ,UAAU;AACzB,QAAI,SAAS,IAAI,MAAM,aAAa,IAAI,GAAG;AACvC,aAAO;AAAA,IACX;AAAA,EACJ;AAEA,SAAO;AACX;AAEA,IAAM,cAAc,CAChB,SACA;AAAA,EACI;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ,MACC;AACD,MAAI,aAAa;AACjB,MAAIC,UAAS;AAGb,MAAI,UAAU,OAAO,KAAK,UAAU,OAAO,GAAG;AAC1C,IAAAA,WAAU,qBAAqB,WAAW,CAAC,MAAM,WAAW,CAAC;AAAA,EACjE;AAIA,MAAI,UAAU,WAAW,GAAG;AACxB,kBAAc,eAAe,WAAW;AAAA,EAC5C;AAGA,MAAI,UAAU,UAAU,KAAK,UAAU,UAAU,GAAG;AAChD,kBAAc,eAAe,cAAc,CAAC,OAAO,cAAc,CAAC;AAAA,EACtE;AAGA,MAAI,UAAU,MAAM,KAAK,UAAU,MAAM,GAAG;AACxC,kBAAc,WAAW,UAAU,MAAM,IAAI,SAAS,CAAC,KACnD,UAAU,MAAM,IAAI,SAAS,CACjC;AAAA,EACJ;AAGA,MAAI,UAAU,OAAO,GAAG;AACpB,kBAAc,WAAW,OAAO;AAAA,EACpC;AAEA,MAAI,UAAU,OAAO,GAAG;AACpB,kBAAc,WAAW,OAAO;AAAA,EACpC;AAEA,MAAI,UAAU,OAAO,GAAG;AACpB,kBAAc,WAAW,OAAO;AAAA,EACpC;AAGA,MAAI,WAAW,QAAQ;AACnB,IAAAA,WAAU,aAAa,UAAU;AAAA,EACrC;AAGA,MAAI,UAAU,OAAO,GAAG;AACpB,IAAAA,WAAU,WAAW,OAAO;AAG5B,QAAI,YAAY,GAAG;AACf,MAAAA,WAAU;AAAA,IACd;AAGA,QAAI,UAAU,GAAG;AACb,MAAAA,WAAU;AAAA,IACd;AAAA,EACJ;AAGA,MAAI,UAAU,MAAM,GAAG;AACnB,IAAAA,WAAU,UAAU,MAAM;AAAA,EAC9B;AAGA,MAAI,UAAU,KAAK,GAAG;AAClB,IAAAA,WAAU,SAAS,KAAK;AAAA,EAC5B;AAGA,QAAM,sBAAsB,QAAQ,uBAAuB;AAG3D,MAAIA,QAAO,WAAW,oBAAoB,UAAUA,YAAW,qBAAqB;AAChF,YAAQ,MAAM,UAAUA;AAGxB,YAAQ,sBAAsBA;AAAA,EAClC;AACJ;AAEA,IAAM,SAAS;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AAEA,IAAM,aAAa,CAAC,OAAO,CAAC,GAAG,UAAU,CAAC,GAAG,QAAQ,CAAC,MAAM;AACxD,MAAI,CAAC,QAAQ,kBAAkB;AAC3B,SAAK,aAAa,SAAS,MAAM,YAAY,EAAE,KAAK;AACpD,SAAK,YAAY,SAAS,MAAM,WAAW,EAAE,KAAK;AAClD,SAAK,cAAc,SAAS,MAAM,aAAa,EAAE,KAAK;AACtD,SAAK,eAAe,SAAS,MAAM,cAAc,EAAE,KAAK;AACxD,SAAK,aAAa,SAAS,MAAM,YAAY,EAAE,KAAK;AACpD,YAAQ,mBAAmB;AAAA,EAC/B;AAEA,OAAK,OAAO,QAAQ,cAAc;AAClC,OAAK,MAAM,QAAQ,aAAa;AAChC,OAAK,QAAQ,QAAQ,eAAe;AACpC,OAAK,SAAS,QAAQ,gBAAgB;AAEtC,OAAK,QAAQ,KAAK,OAAO,KAAK;AAC9B,OAAK,SAAS,KAAK,MAAM,KAAK;AAE9B,OAAK,YAAY,QAAQ;AAEzB,OAAK,SAAS,QAAQ,iBAAiB;AAEvC,SAAO;AACX;AAEA,IAAM;AAAA;AAAA,EAEF,CAAC;AAAA;AAAA,IAEG,MAAM;AAAA,IACN,MAAAJ,QAAO;AAAA,IACP,aAAa,CAAC;AAAA;AAAA,IAGd,OAAO,MAAM;AAAA,IAAC;AAAA,IACd,OAAAK,SAAQ,MAAM;AAAA,IAAC;AAAA,IACf,QAAAC,UAAS,MAAM;AAAA,IAAC;AAAA,IAChB,SAAAC,WAAU,MAAM;AAAA,IAAC;AAAA;AAAA,IAGjB,6BAA6B,CAAC,OAAOC,aAAYA;AAAA,IACjD,gBAAgB,MAAM;AAAA,IAAC;AAAA,IACvB,eAAe,MAAM;AAAA,IAAC;AAAA;AAAA,IAGtB,aAAa;AAAA,IACb,mBAAmB;AAAA;AAAA,IAGnB,SAAS,CAAC;AAAA,EACd,IAAI,CAAC,MAAM,CAEP,OAEA,QAAQ,CAAC,MACR;AAED,UAAM,UAAU,cAAc,KAAK,aAAaR,KAAI,IAAI,UAAU;AAGlE,UAAM,QAAQ,OAAO,iBAAiB,SAAS,IAAI;AAGnD,UAAM,OAAO,WAAW;AACxB,QAAI,YAAY;AAGhB,QAAI,YAAY;AAGhB,UAAM,aAAa,CAAC;AAGpB,UAAM,eAAe,CAAC;AAGtB,UAAM,MAAM,CAAC;AAGb,UAAMS,SAAQ,CAAC;AAGf,UAAM,UAAU;AAAA,MACZJ;AAAA;AAAA,IACJ;AAEA,UAAM,UAAU;AAAA,MACZ;AAAA;AAAA,IACJ;AAEA,UAAM,aAAa;AAAA,MACfE;AAAA;AAAA,IACJ;AAGA,UAAM,aAAa,MAAM;AACzB,UAAM,gBAAgB,MAAM,WAAW,OAAO;AAC9C,UAAM,eAAe,MAAM;AAC3B,UAAM,kBAAkB,CAAAG,WAAS,CAAC,MAAMC,WAAU,KAAKD,QAAOC,MAAK;AACnE,UAAM,UAAU,MAAM;AAClB,UAAI,WAAW;AACX,eAAO;AAAA,MACX;AACA,kBAAY,YAAY,MAAM,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxD,aAAO;AAAA,IACX;AACA,UAAM,WAAW,MAAM;AAMvB,UAAM,QAAQ,MAAM;AAChB,kBAAY;AAGZ,iBAAW,QAAQ,WAAS,MAAM,MAAM,CAAC;AAEzC,YAAM,eAAe,EAAE,oBAAoB,KAAK,SAAS,KAAK;AAC9D,UAAI,cAAc;AACd,mBAAW,MAAM,SAAS,KAAK;AAAA,MACnC;AAGA,YAAM,MAAM,EAAE,MAAM,aAAa,OAAO,KAAK;AAC7C,cAAQ,QAAQ,YAAU,OAAO,GAAG,CAAC;AAAA,IACzC;AAMA,UAAM,SAAS,CAAC,IAAI,cAAc,mBAAmB;AAEjD,UAAI,UAAU,aAAa,WAAW;AAGtC,cAAQ,QAAQ,YAAU;AACtB,cAAM,gBAAgB,OAAO;AAAA,UACzB;AAAA,UACA,MAAM;AAAA,UACN,SAAS;AAAA,UACT,WAAW;AAAA,UACX;AAAA,QACJ,CAAC;AACD,YAAI,kBAAkB,OAAO;AACzB,oBAAU;AAAA,QACd;AAAA,MACJ,CAAC;AAGD,mBAAa,QAAQ,WAAS;AAE1B,cAAM,eAAe,MAAM,MAAM,EAAE;AACnC,YAAI,iBAAiB,OAAO;AACxB,oBAAU;AAAA,QACd;AAAA,MACJ,CAAC;AAGD,iBACK,OAAO,WAAS,CAAC,CAAC,MAAM,QAAQ,UAAU,EAC1C,QAAQ,WAAS;AAEd,cAAM,eAAe,MAAM;AAAA,UACvB;AAAA,UACA,2BAA2B,OAAO,YAAY;AAAA,UAC9C;AAAA,QACJ;AACA,YAAI,CAAC,cAAc;AACf,oBAAU;AAAA,QACd;AAAA,MACJ,CAAC;AAGL,iBAEK,QAAQ,CAAC,OAAO,UAAU;AAEvB,YAAI,MAAM,QAAQ,YAAY;AAC1B;AAAA,QACJ;AAGA,oBAAY,YAAY,MAAM,SAAS,KAAK;AAG5C,cAAM,MAAM;AAGZ,cAAM;AAAA,UACF;AAAA,UACA,2BAA2B,OAAO,YAAY;AAAA,UAC9C;AAAA,QACJ;AAGA,kBAAU;AAAA,MACd,CAAC;AAGL,kBAAY;AAEZ,mBAAa;AAAA,QACT;AAAA,QACA,MAAM;AAAA,QACN,SAAS;AAAA,QACT,WAAW;AAAA,MACf,CAAC;AAGD,aAAO;AAAA,IACX;AAEA,UAAM,WAAW,MAAM;AACnB,mBAAa,QAAQ,WAAS,MAAM,QAAQ,CAAC;AAC7C,iBAAW,QAAQ,eAAa;AAC5B,kBAAU,EAAE,MAAM,aAAa,MAAM,CAAC;AAAA,MAC1C,CAAC;AACD,iBAAW,QAAQ,WAAS,MAAM,SAAS,CAAC;AAAA,IAChD;AAGA,UAAM,sBAAsB;AAAA,MACxB,SAAS;AAAA,QACL,KAAK;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACH,KAAK;AAAA,MACT;AAAA,MACA,YAAY;AAAA,QACR,KAAK;AAAA,MACT;AAAA,IACJ;AAGA,UAAM,wBAAwB;AAAA,MAC1B,GAAG;AAAA,MACH,MAAM;AAAA,QACF,KAAK;AAAA,MACT;AAAA;AAAA,MAGA,KAAK;AAAA,QACD,KAAK;AAAA,MACT;AAAA;AAAA,MAGA,IAAI,YAAUX,UAAS;AAAA,MACvB,aAAa,YAAY,OAAO;AAAA,MAChC,iBAAiB,gBAAgB,KAAK;AAAA,MACtC,UAAU,UAAQ;AACd,mBAAW,KAAK,IAAI;AACpB,eAAO;AAAA,MACX;AAAA,MACA,YAAY,UAAQ;AAChB,mBAAW,OAAO,WAAW,QAAQ,IAAI,GAAG,CAAC;AAAA,MACjD;AAAA,MACA,iBAAiB,gBAAgB,SAAS,UAAU;AAAA,MACpD,iBAAiB,gBAAgB,SAAS,UAAU;AAAA,MACpD,gBAAgB,YAAU,QAAQ,KAAK,MAAM;AAAA,MAC7C,gBAAgB,YAAU,QAAQ,KAAK,MAAM;AAAA,MAC7C,mBAAmB,eAAa,WAAW,KAAK,SAAS;AAAA,MACzD,kBAAkB,MAAO,QAAQ,mBAAmB;AAAA;AAAA,MAGpD,UAAU,MAAM;AAAA,MAChB,OAAO,MAAM;AAAA,IACjB;AAGA,UAAM,wBAAwB;AAAA,MAC1B,SAAS;AAAA,QACL,KAAK;AAAA,MACT;AAAA,MACA,YAAY;AAAA,QACR,KAAK;AAAA,MACT;AAAA,MACA,MAAM;AAAA,QACF,KAAK;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACL,KAAK,MAAM;AAAA,MACf;AAAA,MACA,eAAe,MAAM;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAGA,UAAM,qBAAqB;AAAA,MACvB,GAAG;AAAA,MACH,MAAM;AAAA,QACF,KAAK,MAAM;AAAA,MACf;AAAA,IACJ;AAGA,WAAO,KAAK,MAAM,EACb,KAAK,CAAC,GAAG,MAAM;AAEZ,UAAI,MAAM,UAAU;AAChB,eAAO;AAAA,MACX,WAAW,MAAM,UAAU;AACvB,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX,CAAC,EACA,QAAQ,SAAO;AACZ,YAAM,WAAW,OAAO,GAAG,EAAE;AAAA,QACzB,aAAa,OAAO,GAAG;AAAA,QACvB,WAAW;AAAA,QACX,WAAWS;AAAA,QACX,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,MAAM,aAAa,kBAAkB;AAAA,MACzC,CAAC;AAED,UAAI,UAAU;AACV,qBAAa,KAAK,QAAQ;AAAA,MAC9B;AAAA,IACJ,CAAC;AAGL,UAAM,cAAc,aAAa,qBAAqB;AAGtD,IAAAH,QAAO;AAAA,MACH,MAAM;AAAA,MACN;AAAA,IACJ,CAAC;AAGD,UAAM,aAAa,cAAc,OAAO;AACxC,eAAW,QAAQ,CAAC,OAAO,UAAU;AACjC,kBAAY,YAAY,MAAM,SAAS,aAAa,KAAK;AAAA,IAC7D,CAAC;AAGD,kBAAc,WAAW;AAGzB,WAAO,aAAa,qBAAqB;AAAA,EAC7C;AAAA;AAEJ,IAAM,gBAAgB,CAAC,MAAMD,QAAO,MAAM,OAAO;AAC7C,QAAML,QAAO;AAGb,MAAI,OAAOA,KAAI,GAAG;AACd,WAAOA,KAAI,EAAE,QAAQ,KAAK,IAAI;AAC9B,WAAOA,KAAI,EAAE,QAAQ,KAAKK,MAAK;AAC/B;AAAA,EACJ;AAEA,SAAOL,KAAI,IAAI;AAAA,IACX,SAAS,CAAC,IAAI;AAAA,IACd,SAAS,CAACK,MAAK;AAAA,EACnB;AAEA,QAAM,UAAU,OAAOL,KAAI;AAE3B,QAAM,WAAW,MAAO;AACxB,MAAI,OAAO;AACX,MAAI,KAAK;AACT,MAAI,cAAc;AAClB,MAAI,aAAa;AAEjB,QAAM,eAAe,MAAM;AACvB,QAAI,SAAS,QAAQ;AACjB,oBAAc,MAAM,OAAO,WAAW,MAAM,KAAK,YAAY,IAAI,CAAC,GAAG,QAAQ;AAC7E,mBAAa,MAAM,OAAO,aAAa,EAAE;AAAA,IAC7C,OAAO;AACH,oBAAc,MAAM,OAAO,sBAAsB,IAAI;AACrD,mBAAa,MAAM,OAAO,qBAAqB,EAAE;AAAA,IACrD;AAAA,EACJ;AAEA,WAAS,iBAAiB,oBAAoB,MAAM;AAChD,QAAI,WAAY,YAAW;AAC3B,iBAAa;AACb,SAAK,YAAY,IAAI,CAAC;AAAA,EAC1B,CAAC;AAED,QAAM,OAAO,QAAM;AAEf,SAAK,YAAY,IAAI;AAGrB,QAAI,CAAC,MAAM;AACP,aAAO;AAAA,IACX;AAEA,UAAM,QAAQ,KAAK;AAEnB,QAAI,SAAS,UAAU;AAEnB;AAAA,IACJ;AAGA,WAAO,KAAM,QAAQ;AAGrB,YAAQ,QAAQ,QAAQ,CAAAY,UAAQA,MAAK,CAAC;AACtC,YAAQ,QAAQ,QAAQ,CAAAP,WAASA,OAAM,EAAE,CAAC;AAAA,EAC9C;AAEA,eAAa;AACb,OAAK,YAAY,IAAI,CAAC;AAEtB,SAAO;AAAA,IACH,OAAO,MAAM;AACT,iBAAW,EAAE;AAAA,IACjB;AAAA,EACJ;AACJ;AAEA,IAAM,cAAc,CAAC,QAAQF,QAAO,CAAC,EAAE,MAAAU,OAAM,OAAO,SAAAL,WAAU,CAAC,GAAG,WAAW,eAAe,MAAM;AAC9F,EAAAA,SACK,OAAO,YAAU,OAAO,OAAO,IAAI,CAAC,EACpC;AAAA,IAAQ,YACL,OAAO,OAAO,IAAI,EAAE,EAAE,MAAAK,OAAM,OAAO,QAAQ,OAAO,MAAM,WAAW,eAAe,CAAC;AAAA,EACvF;AACJ,MAAIV,KAAI;AACJ,IAAAA,IAAG,EAAE,MAAAU,OAAM,OAAO,SAAAL,UAAS,WAAW,eAAe,CAAC;AAAA,EAC1D;AACJ;AAEA,IAAM,eAAe,CAAC,SAAS,kBAC3B,cAAc,WAAW,aAAa,SAAS,aAAa;AAEhE,IAAM,cAAc,CAAC,SAAS,kBAAkB;AAC5C,SAAO,cAAc,WAAW,aAAa,SAAS,cAAc,WAAW;AACnF;AAEA,IAAM,UAAU,WAAS,MAAM,QAAQ,KAAK;AAE5C,IAAM,UAAU,WAAS,SAAS;AAElC,IAAM,OAAO,SAAO,IAAI,KAAK;AAE7B,IAAM,WAAW,WAAS,KAAK;AAE/B,IAAM,UAAU,CAAC,OAAO,WAAW,QAAQ;AACvC,MAAI,QAAQ,KAAK,GAAG;AAChB,WAAO,CAAC;AAAA,EACZ;AACA,MAAI,QAAQ,KAAK,GAAG;AAChB,WAAO;AAAA,EACX;AACA,SAAO,SAAS,KAAK,EAChB,MAAM,QAAQ,EACd,IAAI,IAAI,EACR,OAAO,SAAO,IAAI,MAAM;AACjC;AAEA,IAAM,YAAY,WAAS,OAAO,UAAU;AAE5C,IAAM,YAAY,WAAU,UAAU,KAAK,IAAI,QAAQ,UAAU;AAEjE,IAAM,WAAW,WAAS,OAAO,UAAU;AAE3C,IAAM,WAAW,WACb,SAAS,KAAK,IAAI,QAAQ,SAAS,KAAK,IAAI,SAAS,KAAK,EAAE,QAAQ,YAAY,EAAE,IAAI;AAE1F,IAAM,QAAQ,WAAS,SAAS,SAAS,KAAK,GAAG,EAAE;AAEnD,IAAM,UAAU,WAAS,WAAW,SAAS,KAAK,CAAC;AAEnD,IAAM,QAAQ,WAAS,SAAS,KAAK,KAAK,SAAS,KAAK,KAAK,KAAK,MAAM,KAAK,MAAM;AAEnF,IAAM,UAAU,CAAC,OAAO,OAAO,QAAS;AAEpC,MAAI,MAAM,KAAK,GAAG;AACd,WAAO;AAAA,EACX;AAGA,MAAI,kBAAkB,SAAS,KAAK,EAAE,KAAK;AAG3C,MAAI,OAAO,KAAK,eAAe,GAAG;AAC9B,sBAAkB,gBAAgB,QAAQ,QAAQ,EAAE,EAAE,KAAK;AAC3D,WAAO,MAAM,eAAe,IAAI,OAAO;AAAA,EAC3C;AAGA,MAAI,MAAM,KAAK,eAAe,GAAG;AAC7B,sBAAkB,gBAAgB,QAAQ,QAAQ,EAAE,EAAE,KAAK;AAC3D,WAAO,MAAM,eAAe,IAAI;AAAA,EACpC;AAEA,SAAO,MAAM,eAAe;AAChC;AAEA,IAAM,aAAa,WAAS,OAAO,UAAU;AAE7C,IAAM,sBAAsB,YAAU;AAClC,MAAI,MAAM;AACV,MAAI,SAAS,OAAO,MAAM,GAAG;AAC7B,MAAI,QAAQ;AACZ,SAAQ,QAAQ,OAAO,MAAM,GAAI;AAC7B,UAAM,IAAI,KAAK;AACf,QAAI,CAAC,KAAK;AACN,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AAEA,IAAM,UAAU;AAAA,EACZ,SAAS;AAAA,EACT,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,SAAS;AAAA,EACT,MAAM;AACV;AAEA,IAAM,kBAAkB,aAAW;AAC/B,QAAM,MAAM,CAAC;AAEb,MAAI,MAAM,SAAS,OAAO,IAAI,UAAU,QAAQ,OAAO;AACvD,MAAI,UAAU,QAAQ,UAAU,SAAS,QAAQ,SAAS,EAAE,IAAI;AAChE,MAAI,UAAU,QAAQ,UAAU,QAAQ,UAAU,CAAC;AAEnD,QAAM,SAAS,SAAO;AAClB,QAAI,GAAG,IAAI,aAAa,KAAK,QAAQ,GAAG,GAAG,QAAQ,GAAG,GAAG,IAAI,SAAS,IAAI,OAAO;AAAA,EACrF,CAAC;AAGD,MAAI,UAAU,QAAQ,WAAW,SAAS,OAAO,KAAK,QAAQ,MAAM,IAAI,UAAU;AAGlF,MAAI,SAAS,QAAQ,UAAU;AAG/B,SAAO,IAAI;AAEX,SAAO;AACX;AAEA,IAAM,eAAe,CAACR,OAAM,SAAS,QAAQ,SAAS,YAAY;AAE9D,MAAI,YAAY,MAAM;AAClB,WAAO;AAAA,EACX;AAGA,MAAI,OAAO,YAAY,YAAY;AAC/B,WAAO;AAAA,EACX;AAGA,QAAM,SAAS;AAAA,IACX,KAAK,WAAW,SAAS,WAAW,UAAU,IAAIA,KAAI,MAAM;AAAA,IAC5D;AAAA,IACA;AAAA,IACA,iBAAiB;AAAA,IACjB;AAAA,IACA,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,SAAS;AAAA,EACb;AAGA,MAAI,SAAS,OAAO,GAAG;AACnB,WAAO,MAAM;AACb,WAAO;AAAA,EACX;AAGA,SAAO,OAAO,QAAQ,OAAO;AAG7B,MAAI,SAAS,OAAO,OAAO,GAAG;AAC1B,UAAM,QAAQ,OAAO,QAAQ,MAAM,OAAO;AAC1C,WAAO,UAAU;AAAA,MACb,QAAQ,MAAM,CAAC;AAAA,MACf,OAAO,MAAM,CAAC;AAAA,IAClB;AAAA,EACJ;AAGA,SAAO,kBAAkB,UAAU,OAAO,eAAe;AAEzD,SAAO;AACX;AAEA,IAAM,cAAc,WAAS,gBAAgB,KAAK;AAElD,IAAM,SAAS,WAAS,UAAU;AAElC,IAAM,WAAW,WAAS,OAAO,UAAU,YAAY,UAAU;AAEjE,IAAM,QAAQ,WAAS;AACnB,SACI,SAAS,KAAK,KACd,SAAS,MAAM,GAAG,KAClB,SAAS,MAAM,OAAO,KACtB,SAAS,MAAM,MAAM,KACrB,SAAS,MAAM,OAAO,KACtB,SAAS,MAAM,KAAK;AAE5B;AAEA,IAAM,UAAU,WAAS;AACrB,MAAI,QAAQ,KAAK,GAAG;AAChB,WAAO;AAAA,EACX;AAEA,MAAI,OAAO,KAAK,GAAG;AACf,WAAO;AAAA,EACX;AAEA,MAAI,MAAM,KAAK,GAAG;AACd,WAAO;AAAA,EACX;AAEA,MAAI,2BAA2B,KAAK,KAAK,GAAG;AACxC,WAAO;AAAA,EACX;AAEA,MAAI,MAAM,KAAK,GAAG;AACd,WAAO;AAAA,EACX;AAEA,SAAO,OAAO;AAClB;AAEA,IAAM,sBAAsB,SACxB,IACK,QAAQ,UAAU,IAAI,EACtB,QAAQ,UAAU,IAAI,EACtB,QAAQ,UAAU,IAAI,EACtB,QAAQ,UAAU,IAAI,EACtB,QAAQ,UAAU,IAAI,EACtB,QAAQ,UAAU,IAAI;AAE/B,IAAM,kBAAkB;AAAA,EACpB,OAAO;AAAA,EACP,SAAS;AAAA,EACT,KAAK,WAAU,QAAQ,KAAK,MAAM,UAAU,QAAQ,KAAK,IAAI,MAAM,KAAK;AAAA,EACxE,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ,WAAU,WAAW,KAAK,IAAI,QAAQ,SAAS,KAAK;AAAA,EAC5D,UAAU,WAAS,oBAAoB,KAAK;AAAA,EAC5C,WAAW;AAAA,EACX,QAAQ,WAAS;AACb,QAAI;AACA,aAAO,KAAK,MAAM,oBAAoB,KAAK,CAAC;AAAA,IAChD,SAAS,GAAG;AACR,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AAEA,IAAM,YAAY,CAAC,OAAO,SAAS,gBAAgB,IAAI,EAAE,KAAK;AAE9D,IAAM,iBAAiB,CAAC,UAAU,cAAc,cAAc;AAE1D,MAAI,aAAa,cAAc;AAC3B,WAAO;AAAA,EACX;AAGA,MAAI,eAAe,QAAQ,QAAQ;AAGnC,MAAI,iBAAiB,WAAW;AAE5B,UAAM,iBAAiB,UAAU,UAAU,SAAS;AAGpD,mBAAe,QAAQ,cAAc;AAGrC,QAAI,mBAAmB,MAAM;AACzB,YAAM,kDAAkD,MAAM,qBAAqB,SAAS;AAAA,IAChG,OAAO;AACH,iBAAW;AAAA,IACf;AAAA,EACJ;AAGA,SAAO;AACX;AAEA,IAAM,eAAe,CAAC,cAAc,cAAc;AAC9C,MAAI,eAAe;AACnB,SAAO;AAAA,IACH,YAAY;AAAA,IACZ,KAAK,MAAM;AAAA,IACX,KAAK,cAAY;AACb,qBAAe,eAAe,UAAU,cAAc,SAAS;AAAA,IACnE;AAAA,EACJ;AACJ;AAEA,IAAM,gBAAgB,aAAW;AAC7B,QAAM,MAAM,CAAC;AACb,QAAM,SAAS,UAAQ;AACnB,UAAM,mBAAmB,QAAQ,IAAI;AACrC,QAAI,IAAI,IAAI,aAAa,iBAAiB,CAAC,GAAG,iBAAiB,CAAC,CAAC;AAAA,EACrE,CAAC;AACD,SAAO,aAAa,GAAG;AAC3B;AAEA,IAAM,qBAAqB,cAAY;AAAA;AAAA,EAEnC,OAAO,CAAC;AAAA;AAAA,EAGR,mBAAmB;AAAA;AAAA,EAGnB,mBAAmB;AAAA;AAAA,EAGnB,iBAAiB,CAAC;AAAA;AAAA,EAGlB,SAAS,cAAc,OAAO;AAClC;AAEA,IAAM,aAAa,CAAC,QAAQ,YAAY,QACpC,OACK,MAAM,WAAW,EACjB,IAAI,UAAQ,KAAK,YAAY,CAAC,EAC9B,KAAK,SAAS;AAEvB,IAAM,kBAAkB,CAAC,OAAO,YAAY;AACxC,QAAM,MAAM,CAAC;AACb,QAAM,SAAS,SAAO;AAClB,QAAI,GAAG,IAAI;AAAA,MACP,KAAK,MAAM,MAAM,SAAS,EAAE,QAAQ,GAAG;AAAA,MACvC,KAAK,WAAS;AACV,cAAM,SAAS,OAAO,WAAW,KAAK,GAAG,EAAE,YAAY,CAAC,IAAI;AAAA,UACxD;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AAEA,IAAM,sBAAsB,aAAW,CAAC,UAAU,OAAOS,WAAU;AAC/D,QAAM,MAAM,CAAC;AACb,QAAM,SAAS,SAAO;AAClB,UAAMT,QAAO,WAAW,KAAK,GAAG,EAAE,YAAY;AAE9C,QAAI,OAAOA,KAAI,EAAE,IAAI,YAAU;AAC3B,UAAI;AACA,QAAAS,OAAM,QAAQ,GAAG,IAAI,OAAO;AAAA,MAChC,SAAS,GAAG;AAAA,MAEZ;AAGA,eAAS,WAAWT,KAAI,IAAI,EAAE,OAAOS,OAAM,QAAQ,GAAG,EAAE,CAAC;AAAA,IAC7D;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AAEA,IAAM,sBAAsB,aAAW,CAAAA,WAAS;AAC5C,QAAM,MAAM,CAAC;AACb,QAAM,SAAS,SAAO;AAClB,QAAI,OAAO,WAAW,KAAK,GAAG,EAAE,YAAY,CAAC,EAAE,IAAI,YAAUA,OAAM,QAAQ,GAAG;AAAA,EAClF,CAAC;AACD,SAAO;AACX;AAEA,IAAM,oBAAoB;AAAA,EACtB,KAAK;AAAA,EACL,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,MAAM;AACV;AAEA,IAAM,cAAc,MAChB,KAAK,OAAO,EACP,SAAS,EAAE,EACX,UAAU,GAAG,EAAE;AAExB,IAAM,cAAc,CAAC,KAAK,UAAU,IAAI,OAAO,OAAO,CAAC;AAEvD,IAAM,MAAM,CAAC,IAAI,SAAS;AACtB,MAAI,MAAM;AACN,OAAG;AAAA,EACP,WAAW,SAAS,QAAQ;AACxB,YAAQ,QAAQ,CAAC,EAAE,KAAK,EAAE;AAAA,EAC9B,OAAO;AACH,eAAW,IAAI,CAAC;AAAA,EACpB;AACJ;AAEA,IAAM,KAAK,MAAM;AACb,QAAMK,aAAY,CAAC;AACnB,QAAM,MAAM,CAAC,OAAO,OAAO;AACvB;AAAA,MACIA;AAAA,MACAA,WAAU,UAAU,cAAY,SAAS,UAAU,UAAU,SAAS,OAAO,MAAM,CAAC,GAAG;AAAA,IAC3F;AAAA,EACJ;AACA,QAAM,OAAO,CAAC,OAAO,MAAM,SAAS;AAChC,IAAAA,WACK,OAAO,cAAY,SAAS,UAAU,KAAK,EAC3C,IAAI,cAAY,SAAS,EAAE,EAC3B,QAAQ,QAAM,IAAI,MAAM,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC;AAAA,EACnD;AACA,SAAO;AAAA,IACH,UAAU,CAAC,UAAU,SAAS;AAC1B,WAAK,OAAO,MAAM,IAAI;AAAA,IAC1B;AAAA,IACA,MAAM,CAAC,UAAU,SAAS;AACtB,WAAK,OAAO,MAAM,KAAK;AAAA,IAC3B;AAAA,IACA,IAAI,CAAC,OAAO,OAAO;AACf,MAAAA,WAAU,KAAK,EAAE,OAAO,GAAG,CAAC;AAAA,IAChC;AAAA,IACA,QAAQ,CAAC,OAAO,OAAO;AACnB,MAAAA,WAAU,KAAK;AAAA,QACX;AAAA,QACA,IAAI,IAAI,SAAS;AACb,cAAI,OAAO,EAAE;AACb,aAAG,GAAG,IAAI;AAAA,QACd;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,IACA;AAAA,EACJ;AACJ;AAEA,IAAM,+BAA+B,CAAC,KAAK,QAAQ,aAAa;AAC5D,SAAO,oBAAoB,GAAG,EACzB,OAAO,cAAY,CAAC,SAAS,SAAS,QAAQ,CAAC,EAC/C;AAAA,IAAQ,SACL,OAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,KAAK,GAAG,CAAC;AAAA,EAChF;AACR;AAEA,IAAM,UAAU;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AAEA,IAAM,gBAAgB,CAAAC,UAAQ;AAC1B,QAAM,MAAM,CAAC;AACb,+BAA6BA,OAAM,KAAK,OAAO;AAC/C,SAAO;AACX;AAEA,IAAM,sBAAsB,WAAS;AACjC,QAAM,QAAQ,CAACA,OAAM,UAAU;AAC3B,QAAIA,MAAK,UAAU;AACf,kBAAY,OAAO,KAAK;AAAA,IAC5B;AAAA,EACJ,CAAC;AACL;AAEA,IAAM,aAAa;AAAA,EACf,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,qBAAqB;AAAA,EACrB,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,SAAS;AAAA,EACT,YAAY;AAChB;AAEA,IAAM,aAAa;AAAA,EACf,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AACX;AAEA,IAAM,gBAAgB,SAAO,UAAU,KAAK,GAAG;AAE/C,IAAM,sBAAsB,MAAM,cAAe,IAAK,eAAe,CAAC,EAAE,CAAC;AAEzE,IAAM,wBAAwB,MAAM;AAGhC,QAAM,mBAAmB,oBAAoB;AAC7C,QAAM,+BAAgC,IAAQ,eAAe;AAC7D,QAAM,kCAAmC,IAAQ,SAAS;AAC1D,MAAI,iCAAiC,iCAAiC;AAClE,WAAO,cAAc,4BAA4B,EAAE,CAAC;AAAA,EACxD;AACA,SAAO,qBAAqB,MAAM,MAAM;AAC5C;AAEA,IAAM,OAAO;AAAA,EACT,SAAS;AAAA,EACT,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,OAAO;AACX;AAGA,IAAM,UAAU,CAAC;AAGjB,IAAM,mBAAmB,CAAC,KAAK,OAAO,UAClC,IAAI,QAAQ,CAAC,SAAS,WAAW;AAE7B,QAAM,kBAAkB,QAAQ,OAAO,OAAK,EAAE,QAAQ,GAAG,EAAE,IAAI,OAAK,EAAE,EAAE;AAGxE,MAAI,gBAAgB,WAAW,GAAG;AAC9B,YAAQ,KAAK;AACb;AAAA,EACJ;AAGA,QAAM,gBAAgB,gBAAgB,MAAM;AAG5C,kBACK;AAAA;AAAA,IAEG,CAAC,SAAS,SAAS,QAAQ,KAAK,CAAAC,WAAS,KAAKA,QAAO,KAAK,CAAC;AAAA;AAAA,IAG3D,cAAc,OAAO,KAAK;AAAA;AAAA,EAG9B,EACC,KAAK,CAAAA,WAAS,QAAQA,MAAK,CAAC,EAC5B,MAAM,CAAAC,WAAS,OAAOA,MAAK,CAAC;AACrC,CAAC;AAEL,IAAM,eAAe,CAAC,KAAK,OAAO,UAC9B,QAAQ,OAAO,OAAK,EAAE,QAAQ,GAAG,EAAE,IAAI,OAAK,EAAE,GAAG,OAAO,KAAK,CAAC;AAGlE,IAAM,YAAY,CAAC,KAAK,OAAO,QAAQ,KAAK,EAAE,KAAK,GAAG,CAAC;AAEvD,IAAM,uBAAuB,uBAAqB,OAAO,OAAO,gBAAgB,iBAAiB;AAEjG,IAAM,aAAa,OAAO,EAAE,GAAG,eAAe;AAE9C,IAAM,aAAa,UAAQ;AACvB,QAAM,MAAM,CAAC,KAAK,UAAU;AAExB,QAAI,CAAC,eAAe,GAAG,GAAG;AACtB;AAAA,IACJ;AACA,mBAAe,GAAG,EAAE,CAAC,IAAI;AAAA,MACrB;AAAA,MACA,eAAe,GAAG,EAAE,CAAC;AAAA,MACrB,eAAe,GAAG,EAAE,CAAC;AAAA,IACzB;AAAA,EACJ,CAAC;AACL;AAGA,IAAM,iBAAiB;AAAA;AAAA,EAEnB,IAAI,CAAC,MAAM,KAAK,MAAM;AAAA;AAAA,EAGtB,MAAM,CAAC,YAAY,KAAK,MAAM;AAAA;AAAA,EAG9B,UAAU,CAAC,OAAO,KAAK,OAAO;AAAA;AAAA,EAG9B,WAAW,CAAC,MAAM,KAAK,MAAM;AAAA;AAAA,EAG7B,UAAU,CAAC,OAAO,KAAK,OAAO;AAAA;AAAA,EAG9B,eAAe,CAAC,MAAM,KAAK,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAMjC,0BAA0B,CAAC,MAAM,KAAK,OAAO;AAAA;AAAA,EAG7C,WAAW,CAAC,MAAM,KAAK,OAAO;AAAA;AAAA,EAC9B,aAAa,CAAC,MAAM,KAAK,OAAO;AAAA;AAAA,EAChC,YAAY,CAAC,MAAM,KAAK,OAAO;AAAA;AAAA,EAC/B,eAAe,CAAC,OAAO,KAAK,OAAO;AAAA;AAAA,EACnC,cAAc,CAAC,MAAM,KAAK,OAAO;AAAA;AAAA,EACjC,aAAa,CAAC,MAAM,KAAK,OAAO;AAAA;AAAA,EAChC,aAAa,CAAC,MAAM,KAAK,OAAO;AAAA;AAAA,EAChC,cAAc,CAAC,MAAM,KAAK,OAAO;AAAA;AAAA,EACjC,cAAc,CAAC,OAAO,KAAK,OAAO;AAAA;AAAA,EAClC,sBAAsB,CAAC,OAAO,KAAK,OAAO;AAAA;AAAA;AAAA,EAG1C,aAAa,CAAC,OAAO,KAAK,OAAO;AAAA;AAAA,EAGjC,aAAa,CAAC,OAAO,KAAK,OAAO;AAAA;AAAA;AAAA,EAGjC,UAAU,CAAC,MAAM,KAAK,GAAG;AAAA;AAAA,EACzB,eAAe,CAAC,OAAO,KAAK,OAAO;AAAA;AAAA;AAAA,EAGnC,2BAA2B,CAAC,MAAM,KAAK,OAAO;AAAA;AAAA,EAC9C,oBAAoB,CAAC,UAAU,KAAK,MAAM;AAAA;AAAA,EAC1C,oBAAoB,CAAC,IAAI,KAAK,GAAG;AAAA;AAAA,EAGjC,YAAY,CAAC,OAAO,KAAK,OAAO;AAAA;AAAA,EAChC,eAAe,CAAC,MAAM,KAAK,OAAO;AAAA;AAAA,EAClC,gBAAgB,CAAC,OAAO,KAAK,OAAO;AAAA;AAAA,EACpC,cAAc,CAAC,CAAC,aAAa,aAAa,aAAa,GAAG,KAAK,KAAK;AAAA;AAAA,EAGpE,eAAe,CAAC,MAAM,KAAK,OAAO;AAAA;AAAA,EAClC,oBAAoB,CAAC,GAAG,KAAK,GAAG;AAAA;AAAA,EAChC,4BAA4B,CAAC,MAAM,KAAK,OAAO;AAAA;AAAA;AAAA,EAG/C,cAAc,CAAC,OAAO,KAAK,OAAO;AAAA;AAAA,EAClC,YAAY,CAAC,OAAO,KAAK,OAAO;AAAA;AAAA,EAChC,WAAW,CAAC,KAAS,KAAK,GAAG;AAAA;AAAA,EAC7B,kBAAkB,CAAC,CAAC,KAAK,KAAM,GAAI,GAAG,KAAK,KAAK;AAAA;AAAA;AAAA,EAGhD,QAAQ,CAAC,MAAM,KAAK,UAAU;AAAA;AAAA,EAG9B,cAAc,CAAC,KAAM,KAAK,GAAG;AAAA;AAAA,EAG7B,oBAAoB,CAAC,SAAS,KAAK,MAAM;AAAA,EACzC,wBAAwB,CAAC,MAAM,KAAK,MAAM;AAAA,EAC1C,wBAAwB,CAAC,MAAM,KAAK,MAAM;AAAA,EAC1C,wBAAwB,CAAC,MAAM,KAAK,MAAM;AAAA,EAE1C,uBAAuB,CAAC,oBAAoB,GAAG,KAAK,MAAM;AAAA;AAAA,EAC1D,yBAAyB,CAAC,sBAAsB,GAAG,KAAK,MAAM;AAAA;AAAA,EAE9D,WAAW;AAAA,IACP;AAAA,IACA,KAAK;AAAA,EACT;AAAA,EACA,mBAAmB,CAAC,gCAAgC,KAAK,MAAM;AAAA,EAC/D,yBAAyB,CAAC,oBAAoB,KAAK,MAAM;AAAA,EACzD,2BAA2B,CAAC,sBAAsB,KAAK,MAAM;AAAA,EAC7D,wBAAwB,CAAC,gBAAgB,KAAK,MAAM;AAAA,EACpD,sBAAsB,CAAC,iBAAiB,KAAK,MAAM;AAAA,EACnD,kBAAkB,CAAC,WAAW,KAAK,MAAM;AAAA,EACzC,gBAAgB,CAAC,SAAS,KAAK,MAAM;AAAA;AAAA,EACrC,oBAAoB,CAAC,qBAAqB,KAAK,MAAM;AAAA,EACrD,kBAAkB,CAAC,WAAW,KAAK,MAAM;AAAA;AAAA,EACzC,sBAAsB,CAAC,uBAAuB,KAAK,MAAM;AAAA,EACzD,qBAAqB,CAAC,aAAa,KAAK,MAAM;AAAA,EAC9C,6BAA6B,CAAC,mBAAmB,KAAK,MAAM;AAAA,EAC5D,4BAA4B,CAAC,oBAAoB,KAAK,MAAM;AAAA,EAC5D,0BAA0B,CAAC,uBAAuB,KAAK,MAAM;AAAA,EAC7D,gCAAgC,CAAC,uBAAuB,KAAK,MAAM;AAAA,EAEnE,kBAAkB,CAAC,iBAAiB,KAAK,MAAM;AAAA,EAC/C,iBAAiB,CAAC,gBAAgB,KAAK,MAAM;AAAA,EAC7C,gBAAgB,CAAC,eAAe,KAAK,MAAM;AAAA,EAE3C,uBAAuB,CAAC,UAAU,KAAK,MAAM;AAAA,EAC7C,0BAA0B,CAAC,SAAS,KAAK,MAAM;AAAA,EAC/C,0BAA0B,CAAC,SAAS,KAAK,MAAM;AAAA,EAC/C,gCAAgC,CAAC,UAAU,KAAK,MAAM;AAAA,EACtD,+BAA+B,CAAC,QAAQ,KAAK,MAAM;AAAA,EACnD,gCAAgC,CAAC,SAAS,KAAK,MAAM;AAAA,EACrD,wBAAwB,CAAC,UAAU,KAAK,MAAM;AAAA;AAAA,EAG9C,YAAY;AAAA,IACR;AAAA,IACA,KAAK;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACT;AAAA,IACA,KAAK;AAAA,EACT;AAAA,EACA,WAAW;AAAA,IACP;AAAA,IACA,KAAK;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACN;AAAA,IACA,KAAK;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACN;AAAA,IACA,KAAK;AAAA,EACT;AAAA;AAAA,EAGA,QAAQ,CAAC,MAAM,KAAK,QAAQ;AAAA,EAC5B,WAAW,CAAC,MAAM,KAAK,QAAQ;AAAA,EAC/B,SAAS,CAAC,MAAM,KAAK,QAAQ;AAAA,EAC7B,gBAAgB,CAAC,MAAM,KAAK,QAAQ;AAAA,EACpC,YAAY,CAAC,MAAM,KAAK,QAAQ;AAAA,EAChC,gBAAgB,CAAC,MAAM,KAAK,QAAQ;AAAA,EACpC,mBAAmB,CAAC,MAAM,KAAK,QAAQ;AAAA,EACvC,WAAW,CAAC,MAAM,KAAK,QAAQ;AAAA,EAC/B,oBAAoB,CAAC,MAAM,KAAK,QAAQ;AAAA,EACxC,uBAAuB,CAAC,MAAM,KAAK,QAAQ;AAAA,EAC3C,oBAAoB,CAAC,MAAM,KAAK,QAAQ;AAAA,EACxC,qBAAqB,CAAC,MAAM,KAAK,QAAQ;AAAA,EACzC,eAAe,CAAC,MAAM,KAAK,QAAQ;AAAA,EACnC,gBAAgB,CAAC,MAAM,KAAK,QAAQ;AAAA,EACpC,cAAc,CAAC,MAAM,KAAK,QAAQ;AAAA,EAClC,eAAe,CAAC,MAAM,KAAK,QAAQ;AAAA,EACnC,eAAe,CAAC,MAAM,KAAK,QAAQ;AAAA,EACnC,gBAAgB,CAAC,MAAM,KAAK,QAAQ;AAAA;AAAA,EAGpC,gBAAgB,CAAC,MAAM,KAAK,QAAQ;AAAA,EACpC,eAAe,CAAC,MAAM,KAAK,QAAQ;AAAA,EACnC,kBAAkB,CAAC,MAAM,KAAK,QAAQ;AAAA,EACtC,mBAAmB,CAAC,MAAM,KAAK,QAAQ;AAAA;AAAA,EAGvC,kBAAkB,CAAC,MAAM,KAAK,MAAM;AAAA;AAAA,EACpC,uBAAuB,CAAC,MAAM,KAAK,MAAM;AAAA;AAAA,EACzC,2BAA2B,CAAC,MAAM,KAAK,MAAM;AAAA,EAC7C,+BAA+B,CAAC,QAAQ,KAAK,MAAM;AAAA,EACnD,gCAAgC,CAAC,SAAS,KAAK,MAAM;AAAA,EACrD,4BAA4B,CAAC,SAAS,KAAK,MAAM;AAAA,EACjD,gCAAgC,CAAC,SAAS,KAAK,MAAM;AAAA,EACrD,4BAA4B,CAAC,OAAO,KAAK,OAAO;AAAA;AAAA,EAGhD,OAAO,CAAC,CAAC,GAAG,KAAK,KAAK;AAAA;AAAA,EAGtB,SAAS,CAAC,CAAC,qBAAqB,kBAAkB,GAAG,KAAK,KAAK;AACnE;AAEA,IAAM,iBAAiB,CAAC,OAAO,UAAU;AAErC,MAAI,QAAQ,KAAK,GAAG;AAChB,WAAO,MAAM,CAAC,KAAK;AAAA,EACvB;AAGA,MAAI,MAAM,KAAK,GAAG;AACd,WAAO,MAAM,KAAK,KAAK;AAAA,EAC3B;AAGA,MAAI,OAAO,UAAU,UAAU;AAC3B,YAAQ,MAAM;AAAA,EAClB;AAGA,SAAO,MAAM,KAAK,CAAAF,UAAQA,MAAK,OAAO,KAAK,KAAK;AACpD;AAEA,IAAM,kCAAkC,iBAAe;AACnD,MAAI,QAAQ,WAAW,GAAG;AACtB,WAAO;AAAA,EACX;AACA,MAAI,IAAI,KAAK,WAAW,GAAG;AACvB,UAAM,QAAQ,YAAY,MAAM,GAAG;AACnC,WAAO,MAAM,CAAC,IAAI,MAAM,CAAC;AAAA,EAC7B;AACA,SAAO,WAAW,WAAW;AACjC;AAEA,IAAM,iBAAiB,WAAS,MAAM,OAAO,CAAAA,UAAQ,CAACA,MAAK,QAAQ;AAEnE,IAAM,SAAS;AAAA,EACX,OAAO;AAAA,EACP,MAAM;AAAA;AAAA,EACN,OAAO;AAAA;AAAA,EACP,MAAM;AAAA;AAAA,EACN,OAAO;AAAA;AACX;AAEA,IAAI,MAAM;AACV,IAAM,qBAAqB,MAAM;AAC7B,MAAI,QAAQ,MAAM;AACd,QAAI;AACA,YAAM,eAAe,IAAI,aAAa;AACtC,mBAAa,MAAM,IAAI,IAAI,KAAK,CAAC,aAAa,GAAG,gBAAgB,CAAC;AAClE,YAAM,KAAK,SAAS,cAAc,OAAO;AACzC,SAAG,aAAa,QAAQ,MAAM;AAC9B,SAAG,QAAQ,aAAa;AACxB,YAAM,GAAG,MAAM,WAAW;AAAA,IAC9B,SAAS,KAAK;AACV,YAAM;AAAA,IACV;AAAA,EACJ;AACA,SAAO;AACX;AAEA,IAAM,aAAa;AAAA,EACf,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACf;AACA,IAAM,YAAY;AAAA,EACd,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACf;AACA,IAAM,aAAa,CAAC,WAAW,mBAAmB;AAElD,IAAM,qBAAqB,CAAAA,UAAQ,WAAW,SAASA,MAAK,MAAM;AAClE,IAAM,oBAAoB,CAAAA,UAAQ,UAAU,SAASA,MAAK,MAAM;AAChE,IAAM,qBAAqB,CAAAA,UAAQ,WAAW,SAASA,MAAK,MAAM;AAElE,IAAM,UAAU,CAAAN,WACZ,SAASA,OAAM,QAAQ,MAAM,MAC5B,SAASA,OAAM,QAAQ,OAAO,OAAO,KAAK,WAAWA,OAAM,QAAQ,OAAO,OAAO;AAEtF,IAAM,UAAU,CAAAA,YAAU;AAAA,EACtB,YAAY,MAAM;AACd,UAAM,QAAQ,eAAeA,OAAM,KAAK;AAExC,UAAM,EAAE,OAAO,OAAO,MAAM,MAAM,MAAM,IAAI;AAE5C,QAAI,MAAM,WAAW,EAAG,QAAO;AAE/B,QAAI,MAAM,KAAK,kBAAkB,EAAG,QAAO;AAE3C,QAAI,MAAM,KAAK,iBAAiB,EAAG,QAAO;AAE1C,QAAI,MAAM,KAAK,kBAAkB,EAAG,QAAO;AAE3C,WAAO;AAAA,EACX;AAAA,EAEA,UAAU,WAAS,eAAeA,OAAM,OAAO,KAAK;AAAA,EAEpD,iBAAiB,WAAS,eAAe,eAAeA,OAAM,KAAK,GAAG,KAAK;AAAA,EAE3E,kBAAkB,MAAM,eAAeA,OAAM,KAAK;AAAA,EAElD,WAAW,MAAMA,OAAM;AAAA,EAEvB,eAAe,WAAS;AACpB,UAAMM,QAAO,eAAeN,OAAM,OAAO,KAAK;AAC9C,WAAOM,QAAOA,MAAK,WAAW;AAAA,EAClC;AAAA,EAEA,eAAe,WAAS;AACpB,UAAMA,QAAO,eAAeN,OAAM,OAAO,KAAK;AAC9C,WAAOM,QAAOA,MAAK,WAAW;AAAA,EAClC;AAAA,EAEA,YAAY,MACR,OAAO,KAAKN,OAAM,OAAO,EACpB,OAAO,SAAO,SAAS,KAAK,GAAG,CAAC,EAChC,IAAI,CAAAS,aAAW;AAAA,IACZ,MAAMA;AAAA,IACN,OAAOT,OAAM,QAAQS,OAAM;AAAA,EAC/B,EAAE;AAAA,EAEV,wBAAwB,MAAM;AAC1B,UAAM,gBAAgB,SAAS,KAAKT,OAAM,QAAQ,gBAAgB;AAClE,UAAM,cAAc,gBACd,IACA,gCAAgCA,OAAM,QAAQ,qBAAqB;AACzE,WAAO;AAAA,EACX;AAAA,EAEA,6BAA6B,MAAMA,OAAM,QAAQ;AAAA,EAEjD,qBAAqB,YACjB,eAAeA,OAAM,KAAK,EAAE,OAAO,CAAAM,UAAQA,MAAK,WAAW,MAAM;AAAA,EAErE,iBAAiB,MAAM,eAAeN,OAAM,KAAK,EAAE;AAAA,EAEnD,0BAA0B,MACtBA,OAAM,QAAQ,eAAe,mBAAmB,KAAK,CAAC,QAAQA,MAAK;AAAA,EAEvE,UAAU,MAAM,QAAQA,MAAK;AAAA,EAE7B,sBAAsB,YAAU;AAAA,IAC5B,YAAY,MAAM,2BAA2B,KAAK;AAAA,IAClD,gBAAgB,MAAM,+BAA+B,KAAK;AAAA,IAC1D,gBAAgB,MAAM,+BAA+B,KAAK;AAAA,IAC1D,gBAAgB,MAAM,+BAA+B,KAAK;AAAA,EAC9D;AACJ;AAEA,IAAM,iBAAiB,CAAAA,WAAS;AAC5B,QAAM,QAAQ,eAAeA,OAAM,KAAK,EAAE;AAG1C,MAAI,CAACA,OAAM,QAAQ,eAAe;AAC9B,WAAO,UAAU;AAAA,EACrB;AAGA,QAAM,eAAeA,OAAM,QAAQ;AACnC,MAAI,iBAAiB,MAAM;AACvB,WAAO;AAAA,EACX;AAGA,MAAI,QAAQ,cAAc;AACtB,WAAO;AAAA,EACX;AAGA,SAAO;AACX;AAEA,IAAM,QAAQ,CAAC,OAAO,KAAK,QAAQ,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,GAAG,GAAG;AAErE,IAAM,cAAc,CAAC,KAAK,OAAOM,UAAS,IAAI,OAAO,OAAO,GAAGA,KAAI;AAEnE,IAAM,aAAa,CAAC,OAAOA,OAAM,UAAU;AACvC,MAAI,QAAQA,KAAI,GAAG;AACf,WAAO;AAAA,EACX;AAGA,MAAI,OAAO,UAAU,aAAa;AAC9B,UAAM,KAAKA,KAAI;AACf,WAAOA;AAAA,EACX;AAGA,UAAQ,MAAM,OAAO,GAAG,MAAM,MAAM;AAGpC,cAAY,OAAO,OAAOA,KAAI;AAG9B,SAAOA;AACX;AAEA,IAAM,kBAAkB,SACpB,4GAA4G;AAAA,EACxG;AACJ;AAEJ,IAAM,qBAAqB,SACvB,GAAG,GAAG,GACD,MAAM,GAAG,EACT,IAAI,EACJ,MAAM,GAAG,EACT,MAAM;AAEf,IAAM,2BAA2B,CAAAf,UAAQA,MAAK,MAAM,GAAG,EAAE,IAAI;AAE7D,IAAM,uBAAuB,UAAQ;AAEjC,MAAI,OAAO,SAAS,UAAU;AAC1B,WAAO;AAAA,EACX;AAGA,QAAM,UAAU,KAAK,MAAM,GAAG,EAAE,IAAI;AAGpC,MAAI,MAAM,KAAK,OAAO,GAAG;AACrB,WAAO;AAAA,EACX;AAEA,MAAI,iBAAiB,KAAK,OAAO,GAAG;AAChC,WAAO;AAAA,EACX;AAEA,MAAI,QAAQ,KAAK,OAAO,GAAG;AACvB,WAAO;AAAA,EACX;AAEA,MAAI,SAAS,KAAK,OAAO,GAAG;AACxB,WAAO;AAAA,EACX;AAGA,MAAI,SAAS,KAAK,OAAO,GAAG;AAExB,QAAI,YAAY,QAAQ;AACpB,aAAO;AAAA,IACX;AAGA,WAAO;AAAA,EACX;AAEA,SAAO;AACX;AAEA,IAAM,UAAU,CAAC,OAAO,UAAU,QAAQ,UAAU,OAAO,MAAM,CAAC,QAAQ,MAAM;AAEhF,IAAM,gBAAgB,CAAC,OAAO,oBAAI,KAAK,MACnC,GAAG,KAAK,YAAY,CAAC,IAAI,QAAQ,KAAK,SAAS,IAAI,GAAG,IAAI,CAAC,IAAI;AAAA,EAC3D,KAAK,QAAQ;AAAA,EACb;AACJ,CAAC,IAAI,QAAQ,KAAK,SAAS,GAAG,IAAI,CAAC,IAAI,QAAQ,KAAK,WAAW,GAAG,IAAI,CAAC,IAAI;AAAA,EACvE,KAAK,WAAW;AAAA,EAChB;AACJ,CAAC;AAEL,IAAM,kBAAkB,CAACmB,OAAM,UAAU,OAAO,MAAM,YAAY,SAAS;AACvE,QAAMC,QACF,OAAO,SAAS,WACVD,MAAK,MAAM,GAAGA,MAAK,MAAM,IAAI,IAC7BA,MAAK,MAAM,GAAGA,MAAK,MAAMA,MAAK,IAAI;AAC5C,EAAAC,MAAK,mBAAmB,oBAAI,KAAK;AAGjC,MAAID,MAAK,cAAe,CAAAC,MAAK,gBAAgBD,MAAK;AAGlD,MAAI,CAAC,SAAS,QAAQ,GAAG;AACrB,eAAW,cAAc;AAAA,EAC7B;AAGA,MAAI,YAAY,cAAc,QAAQ,yBAAyB,QAAQ,GAAG;AACtE,IAAAC,MAAK,OAAO;AAAA,EAChB,OAAO;AACH,gBAAY,aAAa,qBAAqBA,MAAK,IAAI;AACvD,IAAAA,MAAK,OAAO,YAAY,YAAY,MAAM,YAAY;AAAA,EAC1D;AAEA,SAAOA;AACX;AAEA,IAAM,iBAAiB,MAAM;AACzB,SAAQ,OAAO,cACX,OAAO,eACP,OAAO,qBACP,OAAO,kBACP,OAAO;AACf;AAEA,IAAM,aAAa,CAAC,aAAa,aAAa;AAC1C,QAAM,KAAK,eAAe;AAE1B,MAAI,IAAI;AACJ,UAAM,KAAK,IAAI,GAAG;AAClB,OAAG,OAAO,WAAW;AACrB,WAAO,GAAG,QAAQ,QAAQ;AAAA,EAC9B;AAEA,SAAO,IAAI,KAAK,CAAC,WAAW,GAAG;AAAA,IAC3B,MAAM;AAAA,EACV,CAAC;AACL;AAEA,IAAM,oCAAoC,CAAC,YAAY,aAAa;AAChE,QAAM,KAAK,IAAI,YAAY,WAAW,MAAM;AAC5C,QAAM,KAAK,IAAI,WAAW,EAAE;AAE5B,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACxC,OAAG,CAAC,IAAI,WAAW,WAAW,CAAC;AAAA,EACnC;AAEA,SAAO,WAAW,IAAI,QAAQ;AAClC;AAEA,IAAM,+BAA+B,aAAW;AAC5C,UAAQ,cAAc,KAAK,OAAO,KAAK,CAAC,GAAG,CAAC,KAAK;AACrD;AAEA,IAAM,iCAAiC,aAAW;AAE9C,QAAMC,QAAO,QAAQ,MAAM,GAAG,EAAE,CAAC;AAGjC,SAAOA,MAAK,QAAQ,OAAO,EAAE;AACjC;AAEA,IAAM,iCAAiC,aAAW;AAC9C,SAAO,KAAK,+BAA+B,OAAO,CAAC;AACvD;AAEA,IAAM,2BAA2B,aAAW;AACxC,QAAM,WAAW,6BAA6B,OAAO;AACrD,QAAM,aAAa,+BAA+B,OAAO;AAEzD,SAAO,kCAAkC,YAAY,QAAQ;AACjE;AAEA,IAAM,2BAA2B,CAAC,SAAS,UAAU,cAAc;AAC/D,SAAO,gBAAgB,yBAAyB,OAAO,GAAG,UAAU,MAAM,SAAS;AACvF;AAEA,IAAM,wBAAwB,YAAU;AAEpC,MAAI,CAAC,yBAAyB,KAAK,MAAM,EAAG,QAAO;AAGnD,QAAM,UAAU,OACX,MAAM,2BAA2B,EACjC,OAAO,CAAC,EACR,IAAI,CAAArB,UAAQA,MAAK,KAAK,EAAE,QAAQ,sBAAsB,EAAE,CAAC,EACzD,OAAO,CAAAA,UAAQA,MAAK,MAAM;AAE/B,SAAO,QAAQ,SAAS,UAAU,QAAQ,QAAQ,SAAS,CAAC,CAAC,IAAI;AACrE;AAEA,IAAM,wBAAwB,YAAU;AACpC,MAAI,mBAAmB,KAAK,MAAM,GAAG;AACjC,UAAM,OAAO,OAAO,MAAM,QAAQ,EAAE,CAAC;AACrC,WAAO,OAAO,SAAS,MAAM,EAAE,IAAI;AAAA,EACvC;AACA,SAAO;AACX;AAEA,IAAM,0BAA0B,YAAU;AACtC,MAAI,0BAA0B,KAAK,MAAM,GAAG;AACxC,UAAM,MAAM,OAAO,MAAM,GAAG,EAAE,CAAC,KAAK,IAAI,KAAK;AAC7C,WAAO,MAAM;AAAA,EACjB;AACA,SAAO;AACX;AAEA,IAAM,yBAAyB,aAAW;AACtC,QAAM,OAAO;AAAA,IACT,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,MAAM;AAAA,EACV;AAEA,QAAM,OAAO,QAAQ,MAAM,IAAI;AAC/B,WAAS,UAAU,MAAM;AACrB,UAAMA,QAAO,sBAAsB,MAAM;AACzC,QAAIA,OAAM;AACN,WAAK,OAAOA;AACZ;AAAA,IACJ;AAEA,UAAM,OAAO,sBAAsB,MAAM;AACzC,QAAI,MAAM;AACN,WAAK,OAAO;AACZ;AAAA,IACJ;AAEA,UAAM,SAAS,wBAAwB,MAAM;AAC7C,QAAI,QAAQ;AACR,WAAK,SAAS;AACd;AAAA,IACJ;AAAA,EACJ;AAEA,SAAO;AACX;AAEA,IAAM,mBAAmB,aAAW;AAChC,QAAMS,SAAQ;AAAA,IACV,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,UAAU;AAAA,IACV,MAAM;AAAA,IACN,WAAW;AAAA,IACX,UAAU;AAAA,IACV,SAAS;AAAA,EACb;AAEA,QAAM,cAAc,MAAMA,OAAM;AAChC,QAAM,QAAQ,MAAM;AAChB,QAAIA,OAAM,WAAWA,OAAM,QAAQ,OAAO;AACtC,MAAAA,OAAM,QAAQ,MAAM;AAAA,IACxB;AAAA,EACJ;AAGA,QAAM,OAAO,MAAM;AAEf,UAAM,SAASA,OAAM;AAErB,QAAI,KAAK,QAAQ,MAAM;AAGvB,QAAI,kBAAkB,MAAM;AACxB,UAAI,KAAK,QAAQ,MAAM;AAAA,IAC3B,WAAW,kBAAkB,MAAM;AAE/B,UAAI,KAAK,QAAQ,gBAAgB,QAAQ,OAAO,IAAI,CAAC;AAAA,IACzD,WAAW,gBAAgB,MAAM,GAAG;AAEhC,UAAI,KAAK,QAAQ,yBAAyB,MAAM,CAAC;AAAA,IACrD,OAAO;AAEH,cAAQ,MAAM;AAAA,IAClB;AAAA,EACJ;AAGA,QAAM,UAAU,SAAO;AAEnB,QAAI,CAAC,SAAS;AACV,UAAI,KAAK,SAAS;AAAA,QACd,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,MACV,CAAC;AACD;AAAA,IACJ;AAGA,IAAAA,OAAM,YAAY,KAAK,IAAI;AAG3B,IAAAA,OAAM,UAAU;AAAA,MACZ;AAAA,MACA,cAAY;AAER,QAAAA,OAAM,WAAW,KAAK,IAAI,IAAIA,OAAM;AAGpC,QAAAA,OAAM,WAAW;AAGjB,YAAI,oBAAoB,MAAM;AAC1B,qBAAW,gBAAgB,UAAU,SAAS,QAAQ,mBAAmB,GAAG,CAAC;AAAA,QACjF;AAEA,YAAI;AAAA,UACA;AAAA;AAAA,UAEA,oBAAoB,OAAO,WAAW,WAAW,SAAS,OAAO;AAAA,QACrE;AAAA,MACJ;AAAA,MACA,CAAAQ,WAAS;AACL,YAAI;AAAA,UACA;AAAA,UACA,OAAOA,WAAU,WACX;AAAA,YACI,MAAM;AAAA,YACN,MAAM;AAAA,YACN,MAAMA;AAAA,UACV,IACAA;AAAA,QACV;AAAA,MACJ;AAAA,MACA,CAAC,YAAY,SAAS,UAAU;AAE5B,YAAI,OAAO;AACP,UAAAR,OAAM,OAAO;AAAA,QACjB;AAGA,QAAAA,OAAM,WAAW,KAAK,IAAI,IAAIA,OAAM;AAGpC,YAAI,CAAC,YAAY;AACb,UAAAA,OAAM,WAAW;AACjB;AAAA,QACJ;AAGA,QAAAA,OAAM,WAAW,UAAU;AAG3B,YAAI,KAAK,YAAYA,OAAM,QAAQ;AAAA,MACvC;AAAA,MACA,MAAM;AACF,YAAI,KAAK,OAAO;AAAA,MACpB;AAAA,MACA,cAAY;AACR,cAAM,WAAW;AAAA,UACb,OAAO,aAAa,WAAW,WAAW,SAAS;AAAA,QACvD;AACA,YAAI,KAAK,QAAQ;AAAA,UACb,MAAMA,OAAM,QAAQ,SAAS;AAAA,UAC7B,UAAU,SAAS;AAAA,UACnB,QAAQ,SAAS;AAAA,QACrB,CAAC;AAAA,MACL;AAAA,IACJ;AAAA,EACJ;AAEA,QAAM,MAAM;AAAA,IACR,GAAG,GAAG;AAAA,IACN,WAAW,YAAWA,OAAM,SAAS;AAAA,IACrC;AAAA;AAAA,IACA;AAAA;AAAA,IACA;AAAA;AAAA,EACJ;AAEA,SAAO;AACX;AAEA,IAAM,QAAQ,YAAU,WAAW,KAAK,MAAM;AAE9C,IAAM,cAAc,CAACY,OAAM,KAAK,YAAY;AACxC,QAAM,MAAM;AAAA,IACR,WAAW,MAAM;AAAA,IAAC;AAAA,IAClB,YAAY,MAAM;AAAA,IAAC;AAAA,IACnB,QAAQ,MAAM;AAAA,IAAC;AAAA,IACf,WAAW,MAAM;AAAA,IAAC;AAAA,IAClB,SAAS,MAAM;AAAA,IAAC;AAAA,IAChB,SAAS,MAAM;AAAA,IAAC;AAAA,IAChB,OAAO,MAAM;AACT,gBAAU;AACV,UAAI,MAAM;AAAA,IACd;AAAA,EACJ;AAGA,MAAI,UAAU;AACd,MAAI,kBAAkB;AAGtB,YAAU;AAAA,IACN,QAAQ;AAAA,IACR,SAAS,CAAC;AAAA,IACV,iBAAiB;AAAA,IACjB,GAAG;AAAA,EACP;AAGA,QAAM,UAAU,GAAG;AAInB,MAAI,MAAM,QAAQ,MAAM,KAAKA,OAAM;AAC/B,UAAM,GAAG,GAAG,GAAG,mBAAmB,OAAOA,UAAS,WAAWA,QAAO,KAAK,UAAUA,KAAI,CAAC,CAAC;AAAA,EAC7F;AAGA,QAAM,MAAM,IAAI,eAAe;AAG/B,QAAM,UAAU,MAAM,QAAQ,MAAM,IAAI,MAAM,IAAI;AAClD,UAAQ,aAAa,OAAK;AAEtB,QAAI,SAAS;AACT;AAAA,IACJ;AAEA,QAAI,WAAW,EAAE,kBAAkB,EAAE,QAAQ,EAAE,KAAK;AAAA,EACxD;AAGA,MAAI,qBAAqB,MAAM;AAE3B,QAAI,IAAI,aAAa,GAAG;AACpB;AAAA,IACJ;AAGA,QAAI,IAAI,eAAe,KAAK,IAAI,WAAW,GAAG;AAC1C;AAAA,IACJ;AAEA,QAAI,iBAAiB;AACjB;AAAA,IACJ;AAEA,sBAAkB;AAGlB,QAAI,UAAU,GAAG;AAAA,EACrB;AAGA,MAAI,SAAS,MAAM;AAEf,QAAI,IAAI,UAAU,OAAO,IAAI,SAAS,KAAK;AACvC,UAAI,OAAO,GAAG;AAAA,IAClB,OAAO;AACH,UAAI,QAAQ,GAAG;AAAA,IACnB;AAAA,EACJ;AAGA,MAAI,UAAU,MAAM,IAAI,QAAQ,GAAG;AAGnC,MAAI,UAAU,MAAM;AAChB,cAAU;AACV,QAAI,QAAQ;AAAA,EAChB;AAGA,MAAI,YAAY,MAAM,IAAI,UAAU,GAAG;AAGvC,MAAI,KAAK,QAAQ,QAAQ,KAAK,IAAI;AAGlC,MAAI,MAAM,QAAQ,OAAO,GAAG;AACxB,QAAI,UAAU,QAAQ;AAAA,EAC1B;AAGA,SAAO,KAAK,QAAQ,OAAO,EAAE,QAAQ,SAAO;AACxC,UAAM,QAAQ,SAAS,mBAAmB,QAAQ,QAAQ,GAAG,CAAC,CAAC;AAC/D,QAAI,iBAAiB,KAAK,KAAK;AAAA,EACnC,CAAC;AAGD,MAAI,QAAQ,cAAc;AACtB,QAAI,eAAe,QAAQ;AAAA,EAC/B;AAGA,MAAI,QAAQ,iBAAiB;AACzB,QAAI,kBAAkB;AAAA,EAC1B;AAGA,MAAI,KAAKA,KAAI;AAEb,SAAO;AACX;AAEA,IAAM,iBAAiB,CAAC,MAAM,MAAM,MAAM,aAAa;AAAA,EACnD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AAEA,IAAM,wBAAwB,QAAM,SAAO;AACvC,KAAG,eAAe,SAAS,GAAG,WAAW,IAAI,sBAAsB,CAAC,CAAC;AACzE;AAEA,IAAM,QAAQ,SAAO,KAAK,KAAK,GAAG;AAClC,IAAM,WAAW,IAAI,UAAU;AAC3B,MAAI,MAAM;AACV,QAAM,QAAQ,UAAQ;AAClB,WAAO,MAAM,GAAG,KAAK,MAAM,IAAI,IAAI,KAAK,QAAQ,MAAM,GAAG,IAAI;AAAA,EACjE,CAAC;AACD,SAAO;AACX;AAEA,IAAM,sBAAsB,CAAC,SAAS,IAAI,WAAW;AAEjD,MAAI,OAAO,WAAW,YAAY;AAC9B,WAAO;AAAA,EACX;AAGA,MAAI,CAAC,UAAU,CAAC,SAAS,OAAO,GAAG,GAAG;AAClC,WAAO;AAAA,EACX;AAGA,QAAM,SAAS,OAAO,WAAW,CAAAC,SAAOA;AACxC,QAAM,UAAU,OAAO,YAAY,CAAAA,SAAO;AAG1C,SAAO,CAAC,KAAK,MAAML,QAAO,UAAU,OAAO,YAAY;AAEnD,UAAM,UAAU,YAAY,KAAK,SAAS,QAAQ,OAAO,GAAG,GAAG;AAAA,MAC3D,GAAG;AAAA,MACH,cAAc;AAAA,IAClB,CAAC;AAED,YAAQ,SAAS,SAAO;AAEpB,YAAMM,WAAU,IAAI,sBAAsB;AAG1C,YAAM,WAAW,uBAAuBA,QAAO,EAAE,QAAQ,mBAAmB,GAAG;AAG/E;AAAA,QACI;AAAA,UACI;AAAA,UACA,IAAI;AAAA,UACJ,OAAO,WAAW,SACZ,OACA,gBAAgB,OAAO,IAAI,QAAQ,GAAG,QAAQ;AAAA,UACpDA;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAEA,YAAQ,UAAU,SAAO;AACrB,MAAAN;AAAA,QACI;AAAA,UACI;AAAA,UACA,IAAI;AAAA,UACJ,QAAQ,IAAI,QAAQ,KAAK,IAAI;AAAA,UAC7B,IAAI,sBAAsB;AAAA,QAC9B;AAAA,MACJ;AAAA,IACJ;AAEA,YAAQ,YAAY,SAAO;AACvB,cAAQ,eAAe,WAAW,IAAI,QAAQ,MAAM,IAAI,sBAAsB,CAAC,CAAC;AAAA,IACpF;AAEA,YAAQ,YAAY,sBAAsBA,MAAK;AAC/C,YAAQ,aAAa;AACrB,YAAQ,UAAU;AAGlB,WAAO;AAAA,EACX;AACJ;AAEA,IAAM,cAAc;AAAA,EAChB,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,SAAS;AACb;AAYA,IAAM,qBAAqB,CACvB,QACA,QACAjB,OACAoB,OACA,UACA,MACAH,QACA,UACA,OACA,UACA,YACC;AAED,QAAM,SAAS,CAAC;AAChB,QAAM,EAAE,iBAAiB,aAAa,WAAW,iBAAiB,IAAI;AAGtE,QAAMR,SAAQ;AAAA,IACV,UAAU;AAAA,IACV,SAAS;AAAA,EACb;AAGA,QAAM,SAAS,OAAO,WAAW,QAAM;AACvC,QAAM,SACF,OAAO,WACN,CAAC,KAAK,WACH,WAAW,SAAS,IAAI,kBAAkB,eAAe,IAAI,IAAI;AACzE,QAAM,UAAU,OAAO,YAAY,CAAAa,SAAO;AAG1C,QAAM,oBAAoB,QAAM;AAC5B,UAAM,WAAW,IAAI,SAAS;AAG9B,QAAI,SAAS,QAAQ,EAAG,UAAS,OAAOtB,OAAM,KAAK,UAAU,QAAQ,CAAC;AAEtE,UAAM,UACF,OAAO,OAAO,YAAY,aACpB,OAAO,QAAQoB,OAAM,QAAQ,IAC7B;AAAA,MACI,GAAG,OAAO;AAAA,MACV,iBAAiBA,MAAK;AAAA,IAC1B;AAEV,UAAM,gBAAgB;AAAA,MAClB,GAAG;AAAA,MACH;AAAA,IACJ;AAGA,UAAM,UAAU,YAAY,OAAO,QAAQ,GAAG,SAAS,QAAQ,OAAO,GAAG,GAAG,aAAa;AAEzF,YAAQ,SAAS,SAAO,GAAG,OAAO,KAAK,cAAc,MAAM,CAAC;AAE5D,YAAQ,UAAU,SACdH;AAAA,MACI;AAAA,QACI;AAAA,QACA,IAAI;AAAA,QACJ,QAAQ,IAAI,QAAQ,KAAK,IAAI;AAAA,QAC7B,IAAI,sBAAsB;AAAA,MAC9B;AAAA,IACJ;AAEJ,YAAQ,YAAY,sBAAsBA,MAAK;AAAA,EACnD;AAEA,QAAM,wBAAwB,QAAM;AAChC,UAAM,aAAa,SAAS,QAAQ,YAAY,KAAKR,OAAM,QAAQ;AAEnE,UAAM,UACF,OAAO,OAAO,YAAY,aACpB,OAAO,QAAQA,OAAM,QAAQ,IAC7B;AAAA,MACI,GAAG,OAAO;AAAA,IACd;AAEV,UAAM,gBAAgB;AAAA,MAClB;AAAA,MACA,QAAQ;AAAA,IACZ;AAEA,UAAM,UAAU,YAAY,MAAM,YAAY,aAAa;AAE3D,YAAQ,SAAS,SAAO,GAAG,OAAO,KAAK,cAAc,MAAM,CAAC;AAE5D,YAAQ,UAAU,SACdQ;AAAA,MACI;AAAA,QACI;AAAA,QACA,IAAI;AAAA,QACJ,QAAQ,IAAI,QAAQ,KAAK,IAAI;AAAA,QAC7B,IAAI,sBAAsB;AAAA,MAC9B;AAAA,IACJ;AAEJ,YAAQ,YAAY,sBAAsBA,MAAK;AAAA,EACnD;AAGA,QAAM,iBAAiB,KAAK,MAAMG,MAAK,OAAO,SAAS;AACvD,WAAS,IAAI,GAAG,KAAK,gBAAgB,KAAK;AACtC,UAAM,SAAS,IAAI;AACnB,UAAMC,QAAOD,MAAK,MAAM,QAAQ,SAAS,WAAW,iCAAiC;AACrF,WAAO,CAAC,IAAI;AAAA,MACR,OAAO;AAAA,MACP,MAAMC,MAAK;AAAA,MACX;AAAA,MACA,MAAAA;AAAA,MACA,MAAAD;AAAA,MACA,UAAU;AAAA,MACV,SAAS,CAAC,GAAG,gBAAgB;AAAA,MAC7B,QAAQ,YAAY;AAAA,MACpB,OAAO;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,IACb;AAAA,EACJ;AAEA,QAAM,2BAA2B,MAAM,KAAKX,OAAM,QAAQ;AAE1D,QAAM,kBAAkB,WACpB,MAAM,WAAW,YAAY,UAAU,MAAM,WAAW,YAAY;AAExE,QAAM,eAAe,WAAS;AAE1B,QAAIA,OAAM,QAAS;AAGnB,YAAQ,SAAS,OAAO,KAAK,eAAe;AAG5C,QAAI,CAAC,OAAO;AAER,UAAI,OAAO,MAAM,CAAAe,WAASA,OAAM,WAAW,YAAY,QAAQ,GAAG;AAC9D,iCAAyB;AAAA,MAC7B;AAGA;AAAA,IACJ;AAGA,UAAM,SAAS,YAAY;AAC3B,UAAM,WAAW;AAGjB,UAAMC,UAAS,YAAY,WAAW,QAAM;AAC5C,UAAMC,WAAU,YAAY,YAAY,CAAAJ,SAAO;AAC/C,UAAMK,UAAS,YAAY,WAAW,MAAM;AAAA,IAAC;AAG7C,UAAM,aAAa,SAAS,QAAQ,YAAY,KAAKlB,OAAM,QAAQ;AAEnE,UAAM,UACF,OAAO,YAAY,YAAY,aACzB,YAAY,QAAQ,KAAK,IACzB;AAAA,MACI,GAAG,YAAY;AAAA,MACf,gBAAgB;AAAA,MAChB,iBAAiB,MAAM;AAAA,MACvB,iBAAiBW,MAAK;AAAA,MACtB,eAAeA,MAAK;AAAA,IACxB;AAEV,UAAM,UAAW,MAAM,UAAU,YAAYK,QAAO,MAAM,IAAI,GAAG,YAAY;AAAA,MACzE,GAAG;AAAA,MACH;AAAA,IACJ,CAAC;AAED,YAAQ,SAAS,SAAO;AAEpB,MAAAE,QAAO,KAAK,MAAM,OAAO,OAAO,MAAM;AAGtC,YAAM,SAAS,YAAY;AAG3B,YAAM,UAAU;AAGhB,oBAAc;AAAA,IAClB;AAEA,YAAQ,aAAa,CAAC,kBAAkB,QAAQ,UAAU;AACtD,YAAM,WAAW,mBAAmB,SAAS;AAC7C,0BAAoB;AAAA,IACxB;AAEA,YAAQ,UAAU,SAAO;AACrB,YAAM,SAAS,YAAY;AAC3B,YAAM,UAAU;AAChB,YAAM,QAAQD,SAAQ,IAAI,QAAQ,KAAK,IAAI;AAC3C,UAAI,CAAC,kBAAkB,KAAK,GAAG;AAC3B,QAAAT;AAAA,UACI;AAAA,YACI;AAAA,YACA,IAAI;AAAA,YACJS,SAAQ,IAAI,QAAQ,KAAK,IAAI;AAAA,YAC7B,IAAI,sBAAsB;AAAA,UAC9B;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAEA,YAAQ,YAAY,SAAO;AACvB,YAAM,SAAS,YAAY;AAC3B,YAAM,UAAU;AAChB,UAAI,CAAC,kBAAkB,KAAK,GAAG;AAC3B,8BAAsBT,MAAK,EAAE,GAAG;AAAA,MACpC;AAAA,IACJ;AAEA,YAAQ,UAAU,MAAM;AACpB,YAAM,SAAS,YAAY;AAC3B,YAAM,UAAU;AAChB,YAAM;AAAA,IACV;AAAA,EACJ;AAEA,QAAM,oBAAoB,WAAS;AAE/B,QAAI,MAAM,QAAQ,WAAW,EAAG,QAAO;AAGvC,UAAM,SAAS,YAAY;AAC3B,iBAAa,MAAM,OAAO;AAC1B,UAAM,UAAU,WAAW,MAAM;AAC7B,mBAAa,KAAK;AAAA,IACtB,GAAG,MAAM,QAAQ,MAAM,CAAC;AAGxB,WAAO;AAAA,EACX;AAEA,QAAM,sBAAsB,MAAM;AAE9B,UAAM,uBAAuB,OAAO,OAAO,CAAC,GAAG,UAAU;AACrD,UAAI,MAAM,QAAQ,MAAM,aAAa,KAAM,QAAO;AAClD,aAAO,IAAI,MAAM;AAAA,IACrB,GAAG,CAAC;AAGJ,QAAI,yBAAyB,KAAM,QAAO,SAAS,OAAO,GAAG,CAAC;AAG9D,UAAM,YAAY,OAAO,OAAO,CAAC,OAAO,UAAU,QAAQ,MAAM,MAAM,CAAC;AAGvE,aAAS,MAAM,sBAAsB,SAAS;AAAA,EAClD;AAGA,QAAM,gBAAgB,MAAM;AACxB,UAAM,kBAAkB,OAAO,OAAO,WAAS,MAAM,WAAW,YAAY,UAAU,EACjF;AACL,QAAI,mBAAmB,EAAG;AAC1B,iBAAa;AAAA,EACjB;AAEA,QAAM,cAAc,MAAM;AACtB,WAAO,QAAQ,WAAS;AACpB,mBAAa,MAAM,OAAO;AAC1B,UAAI,MAAM,SAAS;AACf,cAAM,QAAQ,MAAM;AAAA,MACxB;AAAA,IACJ,CAAC;AAAA,EACL;AAGA,MAAI,CAACR,OAAM,UAAU;AACjB,sBAAkB,cAAY;AAE1B,UAAIA,OAAM,QAAS;AAGnB,eAAS,QAAQ;AAGjB,MAAAA,OAAM,WAAW;AACjB,oBAAc;AAAA,IAClB,CAAC;AAAA,EACL,OAAO;AACH,0BAAsB,YAAU;AAE5B,UAAIA,OAAM,QAAS;AAGnB,aACK,OAAO,WAAS,MAAM,SAAS,MAAM,EACrC,QAAQ,WAAS;AACd,cAAM,SAAS,YAAY;AAC3B,cAAM,WAAW,MAAM;AAAA,MAC3B,CAAC;AAGL,oBAAc;AAAA,IAClB,CAAC;AAAA,EACL;AAEA,SAAO;AAAA,IACH,OAAO,MAAM;AACT,MAAAA,OAAM,UAAU;AAChB,kBAAY;AAAA,IAChB;AAAA,EACJ;AACJ;AAUA,IAAM,8BAA8B,CAAC,QAAQ,QAAQT,OAAM,YAAY,CACnEoB,OACA,UACA,MACAH,QACA,UACA,OACA,aACC;AAED,MAAI,CAACG,MAAM;AAGX,QAAM,iBAAiB,QAAQ;AAC/B,QAAM,oBAAoB,kBAAkBA,MAAK,OAAO,QAAQ;AAChE,QAAM,kBAAkB,mBAAmB,qBAAqB,QAAQ;AACxE,MAAIA,iBAAgB,QAAQ;AACxB,WAAO;AAAA,MACH;AAAA,MACA;AAAA,MACApB;AAAA,MACAoB;AAAA,MACA;AAAA,MACA;AAAA,MACAH;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAGJ,QAAM,SAAS,OAAO,WAAW,QAAM;AACvC,QAAM,SAAS,OAAO,WAAW,CAAAK,SAAOA;AACxC,QAAM,UAAU,OAAO,YAAY,CAAAA,SAAO;AAE1C,QAAM,UACF,OAAO,OAAO,YAAY,aACpB,OAAO,QAAQF,OAAM,QAAQ,KAAK,CAAC,IACnC;AAAA,IACI,GAAG,OAAO;AAAA,EACd;AAEV,QAAM,gBAAgB;AAAA,IAClB,GAAG;AAAA,IACH;AAAA,EACJ;AAGA,MAAI,WAAW,IAAI,SAAS;AAG5B,MAAI,SAAS,QAAQ,GAAG;AACpB,aAAS,OAAOpB,OAAM,KAAK,UAAU,QAAQ,CAAC;AAAA,EAClD;AAGA,GAACoB,iBAAgB,OAAO,CAAC,EAAE,MAAM,MAAM,MAAAA,MAAK,CAAC,IAAIA,OAAM,QAAQ,CAAAL,UAAQ;AACnE,aAAS;AAAA,MACLf;AAAA,MACAe,MAAK;AAAA,MACLA,MAAK,SAAS,OAAOA,MAAK,KAAK,OAAO,GAAGA,MAAK,IAAI,GAAGA,MAAK,KAAK,IAAI;AAAA,IACvE;AAAA,EACJ,CAAC;AAGD,QAAM,UAAU,YAAY,OAAO,QAAQ,GAAG,SAAS,QAAQ,OAAO,GAAG,GAAG,aAAa;AACzF,UAAQ,SAAS,SAAO;AACpB,SAAK,eAAe,QAAQ,IAAI,QAAQ,OAAO,IAAI,QAAQ,GAAG,IAAI,sBAAsB,CAAC,CAAC;AAAA,EAC9F;AAEA,UAAQ,UAAU,SAAO;AACrB,IAAAE;AAAA,MACI;AAAA,QACI;AAAA,QACA,IAAI;AAAA,QACJ,QAAQ,IAAI,QAAQ,KAAK,IAAI;AAAA,QAC7B,IAAI,sBAAsB;AAAA,MAC9B;AAAA,IACJ;AAAA,EACJ;AAEA,UAAQ,YAAY,sBAAsBA,MAAK;AAC/C,UAAQ,aAAa;AACrB,UAAQ,UAAU;AAGlB,SAAO;AACX;AAEA,IAAM,0BAA0B,CAAC,SAAS,IAAI,QAAQjB,OAAM,YAAY;AAEpE,MAAI,OAAO,WAAW,WAAY,QAAO,IAAI,WAAW,OAAOA,OAAM,GAAG,QAAQ,OAAO;AAGvF,MAAI,CAAC,UAAU,CAAC,SAAS,OAAO,GAAG,EAAG,QAAO;AAG7C,SAAO,4BAA4B,QAAQ,QAAQA,OAAM,OAAO;AACpE;AAMA,IAAM,uBAAuB,CAAC,SAAS,IAAI,WAAW;AAElD,MAAI,OAAO,WAAW,YAAY;AAC9B,WAAO;AAAA,EACX;AAGA,MAAI,CAAC,UAAU,CAAC,SAAS,OAAO,GAAG,GAAG;AAClC,WAAO,CAAC,cAAc,SAAS,KAAK;AAAA,EACxC;AAGA,QAAM,SAAS,OAAO,WAAW,CAAAsB,SAAOA;AACxC,QAAM,UAAU,OAAO,YAAY,CAAAA,SAAO;AAG1C,SAAO,CAAC,cAAc,MAAML,WAAU;AAClC,UAAM,UAAU;AAAA,MACZ;AAAA,MACA,SAAS,OAAO;AAAA,MAChB;AAAA;AAAA,IACJ;AACA,YAAQ,SAAS,SAAO;AACpB;AAAA,QACI;AAAA,UACI;AAAA,UACA,IAAI;AAAA,UACJ,OAAO,IAAI,QAAQ;AAAA,UACnB,IAAI,sBAAsB;AAAA,QAC9B;AAAA,MACJ;AAAA,IACJ;AAEA,YAAQ,UAAU,SAAO;AACrB,MAAAA;AAAA,QACI;AAAA,UACI;AAAA,UACA,IAAI;AAAA,UACJ,QAAQ,IAAI,QAAQ,KAAK,IAAI;AAAA,UAC7B,IAAI,sBAAsB;AAAA,QAC9B;AAAA,MACJ;AAAA,IACJ;AAEA,YAAQ,YAAY,sBAAsBA,MAAK;AAE/C,WAAO;AAAA,EACX;AACJ;AAEA,IAAM,kBAAkB,CAAC,MAAM,GAAG,MAAM,MAAM,MAAM,KAAK,OAAO,KAAK,MAAM;AAE3E,IAAM,oCAAoC,CACtC,IACA,WAAW,KACX,SAAS,GACT,UAAU,IACV,UAAU,QACT;AACD,MAAI,UAAU;AACd,QAAM,QAAQ,KAAK,IAAI;AAEvB,QAAM,OAAO,MAAM;AACf,QAAI,UAAU,KAAK,IAAI,IAAI;AAC3B,QAAI,QAAQ,gBAAgB,SAAS,OAAO;AAE5C,QAAI,UAAU,QAAQ,UAAU;AAC5B,cAAQ,UAAU,QAAQ;AAAA,IAC9B;AAEA,QAAI,WAAW,UAAU;AACzB,QAAI,YAAY,KAAK,SAAS,QAAQ;AAClC,SAAG,CAAC;AACJ;AAAA,IACJ;AAEA,OAAG,QAAQ;AAEX,cAAU,WAAW,MAAM,KAAK;AAAA,EACpC;AAEA,MAAI,WAAW,EAAG,MAAK;AAEvB,SAAO;AAAA,IACH,OAAO,MAAM;AACT,mBAAa,OAAO;AAAA,IACxB;AAAA,EACJ;AACJ;AAEA,IAAM,sBAAsB,CAAC,WAAW,YAAY;AAChD,QAAMR,SAAQ;AAAA,IACV,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,6BAA6B;AAAA,IAC7B,UAAU;AAAA,IACV,WAAW;AAAA,IACX,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,SAAS;AAAA,IACT,UAAU;AAAA,EACd;AAEA,QAAM,EAAE,2BAA2B,IAAI;AAEvC,QAAM,UAAU,CAACW,OAAM,aAAa;AAChC,UAAM,aAAa,MAAM;AAIrB,UAAIX,OAAM,aAAa,KAAKA,OAAM,aAAa,KAAM;AAGrD,UAAI,KAAK,YAAY,IAAI,YAAY,CAAC;AAAA,IAC1C;AAEA,UAAM,aAAa,MAAM;AACrB,MAAAA,OAAM,WAAW;AACjB,UAAI,KAAK,kBAAkBA,OAAM,SAAS,IAAI;AAAA,IAClD;AAGA,QAAI,KAAK,OAAO;AAGhB,IAAAA,OAAM,YAAY,KAAK,IAAI;AAG3B,IAAAA,OAAM,8BAA8B;AAAA,MAChC,cAAY;AACR,QAAAA,OAAM,oBAAoB;AAC1B,QAAAA,OAAM,oBAAoB,KAAK,IAAI,IAAIA,OAAM;AAE7C,mBAAW;AAIX,YAAIA,OAAM,YAAYA,OAAM,sBAAsB,KAAK,CAACA,OAAM,UAAU;AAEpE,qBAAW;AAAA,QACf;AAAA,MACJ;AAAA;AAAA;AAAA,MAGA,6BAA6B,gBAAgB,KAAK,IAAI,IAAI;AAAA,IAC9D;AAGA,IAAAA,OAAM,UAAU;AAAA;AAAA,MAEZW;AAAA;AAAA,MAGA;AAAA;AAAA;AAAA;AAAA,MAKA,cAAY;AAGR,QAAAX,OAAM,WAAW,SAAS,QAAQ,IAC5B,WACA;AAAA,UACI,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM,GAAG,QAAQ;AAAA,UACjB,SAAS,CAAC;AAAA,QACd;AAGN,QAAAA,OAAM,WAAW,KAAK,IAAI,IAAIA,OAAM;AAGpC,QAAAA,OAAM,WAAW;AAGjB,YAAI,KAAK,QAAQA,OAAM,SAAS,IAAI;AAKpC,YACI,CAAC,8BACA,8BAA8BA,OAAM,sBAAsB,GAC7D;AACE,qBAAW;AAAA,QACf;AAAA,MACJ;AAAA;AAAA,MAGA,CAAAQ,WAAS;AAEL,QAAAR,OAAM,4BAA4B,MAAM;AAGxC,YAAI;AAAA,UACA;AAAA,UACA,SAASQ,MAAK,IACRA,SACA;AAAA,YACI,MAAM;AAAA,YACN,MAAM;AAAA,YACN,MAAM,GAAGA,MAAK;AAAA,UAClB;AAAA,QACV;AAAA,MACJ;AAAA;AAAA,MAGA,CAAC,YAAY,SAAS,UAAU;AAE5B,QAAAR,OAAM,WAAW,KAAK,IAAI,IAAIA,OAAM;AAGpC,QAAAA,OAAM,WAAW,aAAa,UAAU,QAAQ;AAEhD,mBAAW;AAAA,MACf;AAAA;AAAA,MAGA,MAAM;AAEF,QAAAA,OAAM,4BAA4B,MAAM;AAGxC,YAAI,KAAK,SAASA,OAAM,WAAWA,OAAM,SAAS,OAAO,IAAI;AAAA,MACjE;AAAA;AAAA,MAGA,gBAAc;AACV,YAAI,KAAK,YAAY,UAAU;AAAA,MACnC;AAAA,IACJ;AAAA,EACJ;AAEA,QAAM,QAAQ,MAAM;AAEhB,QAAI,CAACA,OAAM,QAAS;AAGpB,IAAAA,OAAM,4BAA4B,MAAM;AAGxC,QAAIA,OAAM,QAAQ,MAAO,CAAAA,OAAM,QAAQ,MAAM;AAG7C,IAAAA,OAAM,WAAW;AAAA,EACrB;AAEA,QAAM,QAAQ,MAAM;AAChB,UAAM;AACN,IAAAA,OAAM,WAAW;AACjB,IAAAA,OAAM,oBAAoB;AAC1B,IAAAA,OAAM,WAAW;AACjB,IAAAA,OAAM,YAAY;AAClB,IAAAA,OAAM,oBAAoB;AAC1B,IAAAA,OAAM,WAAW;AACjB,IAAAA,OAAM,UAAU;AAChB,IAAAA,OAAM,WAAW;AAAA,EACrB;AAEA,QAAM,cAAc,6BACd,MAAOA,OAAM,WAAW,KAAK,IAAIA,OAAM,UAAUA,OAAM,iBAAiB,IAAI,OAC5E,MAAMA,OAAM,YAAY;AAE9B,QAAM,cAAc,6BACd,MAAM,KAAK,IAAIA,OAAM,UAAUA,OAAM,iBAAiB,IACtD,MAAMA,OAAM;AAElB,QAAM,MAAM;AAAA,IACR,GAAG,GAAG;AAAA,IACN;AAAA;AAAA,IACA;AAAA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAEA,SAAO;AACX;AAEA,IAAM,8BAA8B,CAAAT,UAAQA,MAAK,UAAU,GAAGA,MAAK,YAAY,GAAG,CAAC,KAAKA;AAExF,IAAM,iBAAiB,YAAU;AAC7B,MAAIqB,QAAO,CAAC,OAAO,MAAM,OAAO,MAAM,OAAO,IAAI;AAGjD,MAAI,kBAAkB,QAAQ,gBAAgB,MAAM,GAAG;AACnD,IAAAA,MAAK,CAAC,IAAI,OAAO,QAAQ,cAAc;AAAA,EAC3C,WAAW,gBAAgB,MAAM,GAAG;AAEhC,IAAAA,MAAK,CAAC,IAAI,OAAO;AACjB,IAAAA,MAAK,CAAC,IAAI,6BAA6B,MAAM;AAAA,EACjD,WAAW,SAAS,MAAM,GAAG;AAEzB,IAAAA,MAAK,CAAC,IAAI,mBAAmB,MAAM;AACnC,IAAAA,MAAK,CAAC,IAAI;AACV,IAAAA,MAAK,CAAC,IAAI;AAAA,EACd;AAEA,SAAO;AAAA,IACH,MAAMA,MAAK,CAAC;AAAA,IACZ,MAAMA,MAAK,CAAC;AAAA,IACZ,MAAMA,MAAK,CAAC;AAAA,EAChB;AACJ;AAEA,IAAM,SAAS,WAAS,CAAC,EAAE,iBAAiB,QAAS,iBAAiB,QAAQ,MAAM;AAEpF,IAAM,kBAAkB,SAAO;AAC3B,MAAI,CAAC,SAAS,GAAG,EAAG,QAAO;AAC3B,QAAM,SAAS,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;AACpC,aAAW,OAAO,KAAK;AACnB,QAAI,CAAC,IAAI,eAAe,GAAG,EAAG;AAC9B,UAAM,IAAI,IAAI,GAAG;AACjB,WAAO,GAAG,IAAI,KAAK,SAAS,CAAC,IAAI,gBAAgB,CAAC,IAAI;AAAA,EAC1D;AACA,SAAO;AACX;AAEA,IAAM,aAAa,CAAC,SAAS,MAAM,sBAAsB,MAAMD,QAAO,SAAS;AAE3E,QAAM,KAAK,YAAY;AAKvB,QAAMX,SAAQ;AAAA;AAAA,IAEV,UAAU;AAAA;AAAA,IAGV,QAAQ;AAAA;AAAA,IAGR,UAAU;AAAA;AAAA,IAGV,QAAQ;AAAA;AAAA,IAGR,MAAAW;AAAA;AAAA,IAGA;AAAA;AAAA,IAGA,YAAY;AAAA;AAAA,IAGZ,mBAAmB;AAAA;AAAA,IAGnB,QAAQ,sBAAsB,WAAW,sBAAsB,WAAW;AAAA;AAAA,IAG1E,cAAc;AAAA,IACd,iBAAiB;AAAA,EACrB;AAGA,MAAI,iCAAiC;AAKrC,QAAM,WAAW,CAAC;AAGlB,QAAM,YAAY,YAAWX,OAAM,SAAS;AAG5C,QAAM,OAAO,CAAC,UAAU,WAAW;AAC/B,QAAIA,OAAM,YAAYA,OAAM,OAAQ;AACpC,QAAI,KAAK,OAAO,GAAG,MAAM;AAAA,EAC7B;AAGA,QAAM,mBAAmB,MAAM,yBAAyBA,OAAM,KAAK,IAAI;AACvE,QAAM,cAAc,MAAMA,OAAM,KAAK;AACrC,QAAM,cAAc,MAAMA,OAAM,KAAK;AACrC,QAAM,UAAU,MAAMA,OAAM;AAK5B,QAAM,OAAO,CAAC,QAAQ,QAAQ,WAAW;AAErC,IAAAA,OAAM,SAAS;AAGf,QAAI,SAAS,MAAM;AAGnB,QAAIA,OAAM,MAAM;AACZ,UAAI,SAAS,WAAW;AACxB;AAAA,IACJ;AAGA,IAAAA,OAAM,OAAO,eAAe,MAAM;AAGlC,WAAO,GAAG,QAAQ,MAAM;AACpB,WAAK,WAAW;AAAA,IACpB,CAAC;AAGD,WAAO,GAAG,QAAQ,UAAQ;AAEtB,MAAAA,OAAM,KAAK,OAAO,KAAK;AAGvB,MAAAA,OAAM,KAAK,WAAW,KAAK;AAG3B,UAAI,KAAK,QAAQ;AACb,iBAAS,WAAW;AACpB,QAAAA,OAAM,sBAAsB,KAAK;AACjC,QAAAA,OAAM,SAAS,WAAW;AAAA,MAC9B;AAGA,WAAK,WAAW;AAAA,IACpB,CAAC;AAGD,WAAO,GAAG,YAAY,cAAY;AAC9B,gBAAU,WAAW,OAAO;AAE5B,WAAK,iBAAiB,QAAQ;AAAA,IAClC,CAAC;AAGD,WAAO,GAAG,SAAS,CAAAQ,WAAS;AACxB,gBAAU,WAAW,UAAU;AAE/B,WAAK,sBAAsBA,MAAK;AAAA,IACpC,CAAC;AAGD,WAAO,GAAG,SAAS,MAAM;AACrB,gBAAU,WAAW,IAAI;AACzB,WAAK,YAAY;AAAA,IACrB,CAAC;AAGD,WAAO,GAAG,QAAQ,CAAAG,UAAQ;AAEtB,MAAAX,OAAM,eAAe;AAGrB,YAAM,UAAU,YAAU;AAEtB,QAAAA,OAAM,OAAO,OAAO,MAAM,IAAI,SAASA,OAAM;AAG7C,YAAI,WAAW,WAAW,SAASA,OAAM,qBAAqB;AAC1D,oBAAU,WAAW,mBAAmB;AAAA,QAC5C,OAAO;AACH,oBAAU,WAAW,IAAI;AAAA,QAC7B;AAEA,aAAK,MAAM;AAAA,MACf;AAEA,YAAMQ,SAAQ,YAAU;AAEpB,QAAAR,OAAM,OAAOW;AACb,aAAK,WAAW;AAEhB,kBAAU,WAAW,UAAU;AAC/B,aAAK,mBAAmB,MAAM;AAAA,MAClC;AAGA,UAAIX,OAAM,qBAAqB;AAC3B,gBAAQW,KAAI;AACZ;AAAA,MACJ;AAGA,aAAOA,OAAM,SAASH,MAAK;AAAA,IAC/B,CAAC;AAGD,WAAO,UAAU,MAAM;AAGvB,IAAAR,OAAM,eAAe;AAGrB,WAAO,KAAK;AAAA,EAChB;AAEA,QAAM,YAAY,MAAM;AACpB,QAAI,CAACA,OAAM,cAAc;AACrB;AAAA,IACJ;AACA,IAAAA,OAAM,aAAa,KAAK;AAAA,EAC5B;AAEA,QAAM,YAAY,MAAM;AACpB,QAAIA,OAAM,cAAc;AACpB,MAAAA,OAAM,aAAa,MAAM;AACzB;AAAA,IACJ;AACA,cAAU,WAAW,IAAI;AACzB,SAAK,YAAY;AAAA,EACrB;AAKA,QAAM,UAAU,CAAC,WAAW,cAAc;AAEtC,QAAIA,OAAM,mBAAmB;AACzB,MAAAA,OAAM,oBAAoB;AAC1B;AAAA,IACJ;AAGA,cAAU,WAAW,UAAU;AAG/B,qCAAiC;AAGjC,QAAI,EAAEA,OAAM,gBAAgB,OAAO;AAC/B,UAAI,GAAG,QAAQ,MAAM;AACjB,gBAAQ,WAAW,SAAS;AAAA,MAChC,CAAC;AACD;AAAA,IACJ;AAGA,cAAU,GAAG,QAAQ,CAAAmB,yBAAuB;AAExC,MAAAnB,OAAM,aAAa;AACnB,MAAAA,OAAM,sBAAsBmB;AAAA,IAChC,CAAC;AAGD,cAAU,GAAG,YAAY,gBAAc;AAEnC,MAAAnB,OAAM,aAAa;AAAA,IACvB,CAAC;AAED,cAAU,GAAG,kBAAkB,CAAAmB,yBAAuB;AAElD,MAAAnB,OAAM,kBAAkB;AAGxB,MAAAA,OAAM,aAAa;AACnB,MAAAA,OAAM,sBAAsBmB;AAE5B,gBAAU,WAAW,mBAAmB;AACxC,WAAK,oBAAoBA,oBAAmB;AAAA,IAChD,CAAC;AAED,cAAU,GAAG,SAAS,MAAM;AACxB,WAAK,eAAe;AAAA,IACxB,CAAC;AAED,cAAU,GAAG,SAAS,CAAAX,WAAS;AAC3B,MAAAR,OAAM,kBAAkB;AACxB,gBAAU,WAAW,gBAAgB;AACrC,WAAK,iBAAiBQ,MAAK;AAAA,IAC/B,CAAC;AAED,cAAU,GAAG,SAAS,CAAAW,yBAAuB;AACzC,MAAAnB,OAAM,kBAAkB;AAGxB,MAAAA,OAAM,sBAAsBmB;AAE5B,gBAAU,WAAW,IAAI;AACzB,WAAK,eAAe;AAGpB,UAAI,gCAAgC;AAChC,uCAA+B;AAAA,MACnC;AAAA,IACJ,CAAC;AAED,cAAU,GAAG,YAAY,cAAY;AACjC,WAAK,oBAAoB,QAAQ;AAAA,IACrC,CAAC;AAGD,UAAM,UAAU,CAAAR,UAAQ;AAEpB,UAAIX,OAAM,SAAU;AAGpB,gBAAU,QAAQW,OAAM,EAAE,GAAG,SAAS,CAAC;AAAA,IAC3C;AAGA,UAAMH,SAAQ,QAAQ;AAGtB,cAAUR,OAAM,MAAM,SAASQ,MAAK;AAGpC,IAAAR,OAAM,kBAAkB;AAAA,EAC5B;AAEA,QAAM,oBAAoB,MAAM;AAC5B,IAAAA,OAAM,oBAAoB;AAC1B,cAAU,WAAW,iBAAiB;AAAA,EAC1C;AAEA,QAAM,kBAAkB,MACpB,IAAI,QAAQ,aAAW;AACnB,QAAI,CAACA,OAAM,iBAAiB;AACxB,MAAAA,OAAM,oBAAoB;AAE1B,gBAAU,WAAW,IAAI;AACzB,WAAK,eAAe;AAEpB,cAAQ;AACR;AAAA,IACJ;AAEA,qCAAiC,MAAM;AACnC,cAAQ;AAAA,IACZ;AAEA,IAAAA,OAAM,gBAAgB,MAAM;AAAA,EAChC,CAAC;AAKL,QAAM,SAAS,CAAC,kBAAkB,gBAC9B,IAAI,QAAQ,CAAC,SAAS,WAAW;AAG7B,UAAM,mBACFA,OAAM,wBAAwB,OAAOA,OAAM,sBAAsBA,OAAM;AAG3E,QAAI,qBAAqB,MAAM;AAC3B,cAAQ;AACR;AAAA,IACJ;AAGA;AAAA,MACI;AAAA,MACA,MAAM;AAEF,QAAAA,OAAM,sBAAsB;AAC5B,QAAAA,OAAM,aAAa;AACnB,gBAAQ;AAAA,MACZ;AAAA,MACA,CAAAQ,WAAS;AAEL,YAAI,CAAC,aAAa;AACd,kBAAQ;AACR;AAAA,QACJ;AAGA,kBAAU,WAAW,uBAAuB;AAC5C,aAAK,sBAAsB;AAC3B,eAAOA,MAAK;AAAA,MAChB;AAAA,IACJ;AAGA,cAAU,WAAW,IAAI;AACzB,SAAK,gBAAgB;AAAA,EACzB,CAAC;AAGL,QAAM,cAAc,CAAC,KAAK,OAAO,WAAW;AACxC,UAAM,OAAO,IAAI,MAAM,GAAG;AAC1B,UAAMJ,QAAO,KAAK,CAAC;AACnB,UAAM,OAAO,KAAK,IAAI;AACtB,QAAIQ,QAAO;AACX,SAAK,QAAQ,CAAAQ,SAAQR,QAAOA,MAAKQ,IAAG,CAAE;AAGtC,QAAI,KAAK,UAAUR,MAAK,IAAI,CAAC,MAAM,KAAK,UAAU,KAAK,EAAG;AAG1D,IAAAA,MAAK,IAAI,IAAI;AAGb,SAAK,mBAAmB;AAAA,MACpB,KAAKR;AAAA,MACL,OAAO,SAASA,KAAI;AAAA,MACpB;AAAA,IACJ,CAAC;AAAA,EACL;AAEA,QAAM,cAAc,SAAO,gBAAgB,MAAM,SAAS,GAAG,IAAI,QAAQ;AAEzE,QAAM,MAAM;AAAA,IACR,IAAI,EAAE,KAAK,MAAM,GAAG;AAAA,IACpB,QAAQ,EAAE,KAAK,MAAM,QAAQ,KAAK,WAAU,SAAS,MAAO;AAAA,IAC5D,UAAU,EAAE,KAAK,MAAMJ,OAAM,oBAAoB;AAAA,IACjD,YAAY,EAAE,KAAK,MAAMA,OAAM,WAAW;AAAA,IAC1C,QAAQ,EAAE,KAAK,MAAMA,OAAM,OAAO;AAAA,IAClC,UAAU,EAAE,KAAK,MAAMA,OAAM,KAAK,KAAK;AAAA,IACvC,0BAA0B,EAAE,KAAK,MAAM,4BAA4BA,OAAM,KAAK,IAAI,EAAE;AAAA,IACpF,eAAe,EAAE,KAAK,iBAAiB;AAAA,IACvC,UAAU,EAAE,KAAK,YAAY;AAAA,IAC7B,UAAU,EAAE,KAAK,YAAY;AAAA,IAC7B,MAAM,EAAE,KAAK,QAAQ;AAAA,IACrB,cAAc,EAAE,KAAK,MAAMA,OAAM,KAAK,cAAc;AAAA,IAEpD,QAAQ,EAAE,KAAK,MAAMA,OAAM,OAAO;AAAA,IAElC;AAAA,IACA,aAAa,CAAC,KAAK,OAAO,WAAW;AACjC,UAAI,SAAS,GAAG,GAAG;AACf,cAAMY,QAAO;AACb,eAAO,KAAKA,KAAI,EAAE,QAAQ,CAAAQ,SAAO;AAC7B,sBAAYA,MAAKR,MAAKQ,IAAG,GAAG,KAAK;AAAA,QACrC,CAAC;AACD,eAAO;AAAA,MACX;AACA,kBAAY,KAAK,OAAO,MAAM;AAC9B,aAAO;AAAA,IACX;AAAA,IAEA,QAAQ,CAAC7B,OAAM,YAAa,QAAQA,KAAI,IAAI;AAAA,IAE5C;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IAEA,GAAG,GAAG;AAAA,IAEN,QAAQ,MAAOS,OAAM,SAAS;AAAA,IAE9B,SAAS,MAAOA,OAAM,WAAW;AAAA,IACjC,UAAU,EAAE,KAAK,MAAMA,OAAM,SAAS;AAAA,IAEtC,SAAS,MAAOA,OAAM,WAAW;AAAA,IACjC,UAAU,EAAE,KAAK,MAAMA,OAAM,SAAS;AAAA;AAAA,IAGtC,SAAS,CAAAW,UAASX,OAAM,OAAOW;AAAA,EACnC;AAGA,QAAM,UAAU,aAAa,GAAG;AAEhC,SAAO;AACX;AAEA,IAAM,sBAAsB,CAAC,OAAO,UAAU;AAE1C,MAAI,QAAQ,KAAK,GAAG;AAChB,WAAO;AAAA,EACX;AAGA,MAAI,CAAC,SAAS,KAAK,GAAG;AAClB,WAAO;AAAA,EACX;AAGA,SAAO,MAAM,UAAU,CAAAL,UAAQA,MAAK,OAAO,KAAK;AACpD;AAEA,IAAM,cAAc,CAAC,OAAO,WAAW;AACnC,QAAM,QAAQ,oBAAoB,OAAO,MAAM;AAC/C,MAAI,QAAQ,GAAG;AACX;AAAA,EACJ;AACA,SAAO,MAAM,KAAK,KAAK;AAC3B;AAEA,IAAM,YAAY,CAAC,KAAK,MAAME,QAAO,UAAU,OAAO,YAAY;AAC9D,QAAM,UAAU,YAAY,MAAM,KAAK;AAAA,IACnC,QAAQ;AAAA,IACR,cAAc;AAAA,EAClB,CAAC;AAED,UAAQ,SAAS,SAAO;AAEpB,UAAMM,WAAU,IAAI,sBAAsB;AAG1C,UAAM,WAAW,uBAAuBA,QAAO,EAAE,QAAQ,mBAAmB,GAAG;AAG/E,SAAK,eAAe,QAAQ,IAAI,QAAQ,gBAAgB,IAAI,UAAU,QAAQ,GAAGA,QAAO,CAAC;AAAA,EAC7F;AAEA,UAAQ,UAAU,SAAO;AACrB,IAAAN,OAAM,eAAe,SAAS,IAAI,QAAQ,IAAI,YAAY,IAAI,sBAAsB,CAAC,CAAC;AAAA,EAC1F;AAEA,UAAQ,YAAY,SAAO;AACvB,YAAQ,eAAe,WAAW,IAAI,QAAQ,MAAM,IAAI,sBAAsB,CAAC,CAAC;AAAA,EACpF;AAEA,UAAQ,YAAY,sBAAsBA,MAAK;AAC/C,UAAQ,aAAa;AACrB,UAAQ,UAAU;AAGlB,SAAO;AACX;AAEA,IAAM,mBAAmB,SAAO;AAC5B,MAAI,IAAI,QAAQ,IAAI,MAAM,GAAG;AACzB,UAAM,SAAS,WAAW;AAAA,EAC9B;AACA,SAAO,IACF,YAAY,EACZ,QAAQ,SAAS,EAAE,EACnB,QAAQ,iBAAiB,IAAI,EAC7B,MAAM,GAAG,EAAE,CAAC;AACrB;AAEA,IAAM,gBAAgB,UACjB,IAAI,QAAQ,GAAG,IAAI,MAAM,IAAI,QAAQ,IAAI,IAAI,OAC9C,iBAAiB,SAAS,IAAI,MAAM,iBAAiB,GAAG;AAE5D,IAAM,eAAe,WAAS,IAAI,WAAY,WAAW,KAAK,IAAI,MAAM,GAAG,MAAM,IAAI;AAErF,IAAM,aAAa,CAAAF,UAAQ,CAAC,OAAOA,MAAK,IAAI;AAE5C,IAAM,cAAc,CAAC,UAAUN,WAAU;AACrC,eAAaA,OAAM,iBAAiB;AACpC,EAAAA,OAAM,oBAAoB,WAAW,MAAM;AACvC,aAAS,oBAAoB,EAAE,OAAO,eAAeA,OAAM,KAAK,EAAE,CAAC;AAAA,EACvE,GAAG,CAAC;AACR;AAEA,IAAM,kBAAkB,CAACN,QAAO,WAC5B,IAAI,QAAQ,aAAW;AACnB,MAAI,CAACA,KAAI;AACL,WAAO,QAAQ,IAAI;AAAA,EACvB;AAEA,QAAM,SAASA,IAAG,GAAG,MAAM;AAE3B,MAAI,UAAU,MAAM;AAChB,WAAO,QAAQ,IAAI;AAAA,EACvB;AAEA,MAAI,OAAO,WAAW,WAAW;AAC7B,WAAO,QAAQ,MAAM;AAAA,EACzB;AAEA,MAAI,OAAO,OAAO,SAAS,YAAY;AACnC,WAAO,KAAK,OAAO;AAAA,EACvB;AACJ,CAAC;AAEL,IAAM,YAAY,CAACM,QAAO,YAAY;AAClC,EAAAA,OAAM,MAAM,KAAK,CAAC,GAAG,MAAM,QAAQ,cAAc,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC;AAC1E;AAGA,IAAM,0BAA0B,CAACA,QAAO,gBAAgB,CAAC;AAAA,EACrD;AAAA,EACA,UAAU,MAAM;AAAA,EAAC;AAAA,EACjB,UAAU,MAAM;AAAA,EAAC;AAAA,EACjB,GAAG;AACP,IAAI,CAAC,MAAM;AACP,QAAMM,QAAO,eAAeN,OAAM,OAAO,KAAK;AAC9C,MAAI,CAACM,OAAM;AACP,YAAQ;AAAA,MACJ,OAAO,eAAe,SAAS,GAAG,gBAAgB;AAAA,MAClD,MAAM;AAAA,IACV,CAAC;AACD;AAAA,EACJ;AACA,cAAYA,OAAM,SAAS,SAAS,WAAW,CAAC,CAAC;AACrD;AAEA,IAAM,UAAU,CAAC,UAAU,OAAON,YAAW;AAAA;AAAA;AAAA;AAAA,EAIzC,WAAW,MAAM;AACb,mBAAeA,OAAM,KAAK,EAAE,QAAQ,CAAAM,UAAQ;AACxC,MAAAA,MAAK,OAAO;AACZ,MAAAA,MAAK,UAAU;AACf,MAAAA,MAAK,gBAAgB;AAAA,IACzB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,CAAC,EAAE,QAAQ,CAAC,EAAE,MAAM;AAE/B,UAAM,QAAQ,MAAM,IAAI,CAAAK,WAAS;AAAA,MAC7B,QAAQA,MAAK,SAASA,MAAK,SAASA;AAAA,MACpC,SAASA,MAAK;AAAA,IAClB,EAAE;AAIF,QAAI,cAAc,eAAeX,OAAM,KAAK;AAE5C,gBAAY,QAAQ,CAAAM,UAAQ;AAExB,UAAI,CAAC,MAAM,KAAK,CAAAK,UAAQA,MAAK,WAAWL,MAAK,UAAUK,MAAK,WAAWL,MAAK,IAAI,GAAG;AAC/E,iBAAS,eAAe,EAAE,OAAOA,OAAM,QAAQ,MAAM,CAAC;AAAA,MAC1D;AAAA,IACJ,CAAC;AAGD,kBAAc,eAAeN,OAAM,KAAK;AACxC,UAAM,QAAQ,CAACW,OAAM,UAAU;AAE3B,UAAI,YAAY,KAAK,CAAAL,UAAQA,MAAK,WAAWK,MAAK,UAAUL,MAAK,SAASK,MAAK,MAAM;AACjF;AAGJ,eAAS,YAAY;AAAA,QACjB,GAAGA;AAAA,QACH,mBAAmB,kBAAkB;AAAA,QACrC;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AAAA,EAEA,0BAA0B,CAAC,EAAE,IAAI,QAAQ,OAAO,MAAM;AAElD,QAAI,OAAO,OAAQ;AAGnB,iBAAaX,OAAM,iBAAiB;AACpC,IAAAA,OAAM,oBAAoB,WAAW,MAAM;AACvC,YAAMM,QAAO,YAAYN,OAAM,OAAO,EAAE;AAGxC,UAAI,CAAC,MAAM,UAAU,GAAG;AAEpB,yBAAiB,yBAAyB,OAAO;AAAA,UAC7C,MAAAM;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACJ,CAAC,EAAE,KAAK,yBAAuB;AAE3B,gBAAM,oBAAoB,MAAM,yBAAyB;AACzD,cAAI;AACA,kCAAsB,kBAAkBA,OAAM,mBAAmB;AAErE,cAAI,CAAC,oBAAqB;AAE1B;AAAA,YACI;AAAA,YACA;AAAA,cACI,OAAO;AAAA,cACP,MAAAA;AAAA,cACA,SAAS,CAAAK,UAAQ;AACb,yBAAS,sBAAsB,EAAE,IAAI,MAAAA,MAAK,CAAC;AAAA,cAC/C;AAAA,YACJ;AAAA,YACA;AAAA,UACJ;AAAA,QACJ,CAAC;AAED;AAAA,MACJ;AAGA,UAAIL,MAAK,WAAW,WAAW,OAAO;AAClC,iBAAS,iBAAiB;AAAA,UACtB,IAAIA,MAAK;AAAA,UACT,OAAO;AAAA,UACP,qBAAqBA,MAAK;AAAA,QAC9B,CAAC;AAAA,MACL;AAGA,YAAM,SAAS,MAAM;AAEjB,mBAAW,MAAM;AACb,mBAAS,2BAA2B,EAAE,OAAO,GAAG,CAAC;AAAA,QACrD,GAAG,EAAE;AAAA,MACT;AAEA,YAAM,SAAS,cAAY;AACvB,QAAAA,MAAK;AAAA,UACD,qBAAqBN,OAAM,QAAQ,OAAO,KAAKA,OAAM,QAAQ,OAAO,MAAM;AAAA,UAC1E,MAAM,kBAAkB;AAAA,QAC5B,EACK,KAAK,WAAW,SAAS,MAAM;AAAA,QAAC,CAAC,EACjC,MAAM,MAAM;AAAA,QAAC,CAAC;AAAA,MACvB;AAEA,YAAM,QAAQ,cAAY;AACtB,QAAAM,MAAK,gBAAgB,EAAE,KAAK,WAAW,SAAS,MAAM;AAAA,QAAC,CAAC;AAAA,MAC5D;AAGA,UAAIA,MAAK,WAAW,WAAW,qBAAqB;AAChD,eAAO,OAAON,OAAM,QAAQ,aAAa;AAAA,MAC7C;AAGA,UAAIM,MAAK,WAAW,WAAW,YAAY;AACvC,eAAO,MAAMN,OAAM,QAAQ,aAAa;AAAA,MAC5C;AAEA,UAAIA,OAAM,QAAQ,eAAe;AAC7B,eAAO;AAAA,MACX;AAAA,IACJ,GAAG,CAAC;AAAA,EACR;AAAA,EAEA,WAAW,CAAC,EAAE,OAAAqB,QAAO,MAAM,MAAM;AAC7B,UAAMf,QAAO,eAAeN,OAAM,OAAOqB,MAAK;AAC9C,QAAI,CAACf,MAAM;AACX,UAAM,eAAeN,OAAM,MAAM,QAAQM,KAAI;AAC7C,YAAQ,MAAM,OAAO,GAAGN,OAAM,MAAM,SAAS,CAAC;AAC9C,QAAI,iBAAiB,MAAO;AAC5B,IAAAA,OAAM,MAAM,OAAO,OAAO,GAAGA,OAAM,MAAM,OAAO,cAAc,CAAC,EAAE,CAAC,CAAC;AAAA,EACvE;AAAA,EAEA,MAAM,CAAC,EAAE,QAAQ,MAAM;AACnB,cAAUA,QAAO,OAAO;AACxB,aAAS,kBAAkB;AAAA,MACvB,OAAO,MAAM,kBAAkB;AAAA,IACnC,CAAC;AAAA,EACL;AAAA,EAEA,WAAW,CAAC,EAAE,OAAO,OAAO,mBAAmB,UAAU,MAAM;AAAA,EAAC,GAAG,UAAU,MAAM;AAAA,EAAC,EAAE,MAAM;AACxF,QAAI,eAAe;AAEnB,QAAI,UAAU,MAAM,OAAO,UAAU,aAAa;AAC9C,YAAM,iBAAiB,MAAM,0BAA0B;AACvD,YAAM,aAAa,MAAM,iBAAiB;AAC1C,qBAAe,mBAAmB,WAAW,IAAI;AAAA,IACrD;AAEA,UAAM,eAAe,MAAM,mBAAmB;AAC9C,UAAM,cAAc,YAChB,OAAO,MAAM,IAAI,CAAC,aAAa,SAAS,OAAO,KAAK,YAAY,CAAC,IAAI,CAAC,QAAQ,MAAM;AACxF,UAAM,aAAa,MAAM,OAAO,WAAW;AAE3C,UAAM,WAAW,WAAW;AAAA,MACxB,YACI,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC7B,iBAAS,YAAY;AAAA,UACjB;AAAA,UACA,QAAQ,OAAO,UAAU;AAAA,UACzB,SAAS;AAAA,UACT,SAAS;AAAA,UACT,OAAO;AAAA,UACP,SAAS,OAAO,WAAW,CAAC;AAAA,QAChC,CAAC;AAAA,MACL,CAAC;AAAA,IACT;AAEA,YAAQ,IAAI,QAAQ,EACf,KAAK,OAAO,EACZ,MAAM,OAAO;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,CAAC;AAAA,IACP;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,IACA,UAAU,MAAM;AAAA,IAAC;AAAA,IACjB,UAAU,MAAM;AAAA,IAAC;AAAA,IACjB,UAAU,CAAC;AAAA,EACf,MAAM;AAEF,QAAI,QAAQ,MAAM,GAAG;AACjB,cAAQ;AAAA,QACJ,OAAO,eAAe,SAAS,GAAG,WAAW;AAAA,QAC7C,MAAM;AAAA,MACV,CAAC;AACD;AAAA,IACJ;AAGA,QAAI,OAAO,MAAM,KAAKA,OAAM,QAAQ,aAAa,SAAS,OAAO,KAAK,YAAY,CAAC,GAAG;AAElF;AAAA,IACJ;AAGA,QAAI,CAAC,eAAeA,MAAK,GAAG;AAGxB,UACIA,OAAM,QAAQ,iBACb,CAACA,OAAM,QAAQ,iBAAiB,CAACA,OAAM,QAAQ,cAClD;AACE,cAAMQ,SAAQ,eAAe,WAAW,GAAG,WAAW;AAEtD,iBAAS,uBAAuB;AAAA,UAC5B;AAAA,UACA,OAAAA;AAAA,QACJ,CAAC;AAED,gBAAQ,EAAE,OAAAA,QAAO,MAAM,KAAK,CAAC;AAE7B;AAAA,MACJ;AAIA,YAAMF,QAAO,eAAeN,OAAM,KAAK,EAAE,CAAC;AAG1C,UACIM,MAAK,WAAW,WAAW,uBAC3BA,MAAK,WAAW,WAAW,yBAC7B;AACE,cAAM,cAAc,MAAM,kBAAkB;AAC5C,QAAAA,MAAK;AAAA,UACD,qBAAqBN,OAAM,QAAQ,OAAO,KAAKA,OAAM,QAAQ,OAAO,MAAM;AAAA,UAC1E;AAAA,QACJ,EACK,KAAK,MAAM;AACR,cAAI,CAAC,YAAa;AAGlB,mBAAS,YAAY;AAAA,YACjB;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACJ,CAAC;AAAA,QACL,CAAC,EACA,MAAM,MAAM;AAAA,QAAC,CAAC;AAEnB,YAAI,YAAa;AAAA,MACrB;AAGA,eAAS,eAAe,EAAE,OAAOM,MAAK,GAAG,CAAC;AAAA,IAC9C;AAGA,UAAM,SACF,QAAQ,SAAS,UACX,WAAW,QACX,QAAQ,SAAS,UACjB,WAAW,QACX,WAAW;AAGrB,UAAMA,QAAO;AAAA;AAAA,MAET;AAAA;AAAA,MAGA,WAAW,WAAW,QAAQ,OAAO;AAAA;AAAA,MAGrC,QAAQ;AAAA,IACZ;AAGA,WAAO,KAAK,QAAQ,YAAY,CAAC,CAAC,EAAE,QAAQ,SAAO;AAC/C,MAAAA,MAAK,YAAY,KAAK,QAAQ,SAAS,GAAG,CAAC;AAAA,IAC/C,CAAC;AAGD,iBAAa,mBAAmBA,OAAM,EAAE,OAAO,SAAS,CAAC;AAGzD,UAAM,qBAAqB,MAAM,0BAA0B;AAG3D,QAAI,CAACN,OAAM,QAAQ,2BAA2B;AAC1C,cAAQ,uBAAuB,WAAW,KAAKA,OAAM,MAAM;AAAA,IAC/D;AAGA,eAAWA,OAAM,OAAOM,OAAM,KAAK;AAGnC,QAAI,WAAW,kBAAkB,KAAK,QAAQ;AAC1C,gBAAUN,QAAO,kBAAkB;AAAA,IACvC;AAGA,UAAM,KAAKM,MAAK;AAGhB,IAAAA,MAAK,GAAG,QAAQ,MAAM;AAClB,eAAS,iBAAiB,EAAE,GAAG,CAAC;AAAA,IACpC,CAAC;AAED,IAAAA,MAAK,GAAG,aAAa,MAAM;AACvB,eAAS,uBAAuB,EAAE,GAAG,CAAC;AAAA,IAC1C,CAAC;AAED,IAAAA,MAAK,GAAG,aAAa,MAAM;AACvB,eAAS,wBAAwB,EAAE,GAAG,CAAC;AAAA,IAC3C,CAAC;AAED,IAAAA,MAAK,GAAG,iBAAiB,cAAY;AACjC,eAAS,iCAAiC,EAAE,IAAI,SAAS,CAAC;AAAA,IAC9D,CAAC;AAED,IAAAA,MAAK,GAAG,sBAAsB,CAAAE,WAAS;AACnC,YAAM,aAAa,aAAaR,OAAM,QAAQ,kBAAkB,EAAEQ,MAAK;AAGvE,UAAIA,OAAM,QAAQ,OAAOA,OAAM,OAAO,KAAK;AACvC,iBAAS,0BAA0B;AAAA,UAC/B;AAAA,UACA,OAAAA;AAAA,UACA,QAAQ;AAAA,YACJ,MAAM;AAAA,YACN,KAAK,GAAGA,OAAM,IAAI,KAAKA,OAAM,IAAI;AAAA,UACrC;AAAA,QACJ,CAAC;AAGD,gBAAQ,EAAE,OAAAA,QAAO,MAAM,cAAcF,KAAI,EAAE,CAAC;AAC5C;AAAA,MACJ;AAGA,eAAS,6BAA6B;AAAA,QAClC;AAAA,QACA,OAAAE;AAAA,QACA,QAAQ;AAAA,UACJ,MAAM;AAAA,UACN,KAAKR,OAAM,QAAQ;AAAA,QACvB;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAED,IAAAM,MAAK,GAAG,mBAAmB,CAAAE,WAAS;AAChC,eAAS,0BAA0B;AAAA,QAC/B;AAAA,QACA,OAAOA,OAAM;AAAA,QACb,QAAQA,OAAM;AAAA,MAClB,CAAC;AACD,cAAQ,EAAE,OAAOA,OAAM,QAAQ,MAAM,cAAcF,KAAI,EAAE,CAAC;AAAA,IAC9D,CAAC;AAED,IAAAA,MAAK,GAAG,cAAc,MAAM;AACxB,eAAS,eAAe,EAAE,OAAO,GAAG,CAAC;AAAA,IACzC,CAAC;AAED,IAAAA,MAAK,GAAG,aAAa,MAAM;AACvB,MAAAA,MAAK,GAAG,mBAAmB,YAAU;AACjC,YAAI,CAAC,OAAOA,MAAK,IAAI,EAAG;AACxB,iBAAS,4BAA4B,EAAE,IAAI,OAAO,CAAC;AAAA,MACvD,CAAC;AAED,eAAS,sBAAsB;AAAA,QAC3B,OAAO;AAAA,QACP,MAAAA;AAAA,QACA,MAAM;AAAA,UACF;AAAA,UACA;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAED,IAAAA,MAAK,GAAG,QAAQ,MAAM;AAClB,YAAM,YAAY,eAAa;AAE3B,YAAI,CAAC,WAAW;AACZ,mBAAS,eAAe;AAAA,YACpB,OAAO;AAAA,UACX,CAAC;AACD;AAAA,QACJ;AAGA,QAAAA,MAAK,GAAG,mBAAmB,YAAU;AACjC,mBAAS,4BAA4B,EAAE,IAAI,OAAO,CAAC;AAAA,QACvD,CAAC;AAID,yBAAiB,yBAAyB,OAAO,EAAE,MAAAA,OAAM,MAAM,CAAC,EAAE;AAAA,UAC9D,yBAAuB;AAEnB,kBAAM,oBAAoB,MAAM,yBAAyB;AACzD,gBAAI;AACA,oCAAsB,kBAAkBA,OAAM,mBAAmB;AAErE,kBAAM,eAAe,MAAM;AACvB,uBAAS,sBAAsB;AAAA,gBAC3B,OAAO;AAAA,gBACP,MAAAA;AAAA,gBACA,MAAM;AAAA,kBACF;AAAA,kBACA;AAAA,gBACJ;AAAA,cACJ,CAAC;AAED,0BAAY,UAAUN,MAAK;AAAA,YAC/B;AAGA,gBAAI,qBAAqB;AAErB;AAAA,gBACI;AAAA,gBACA;AAAA,kBACI,OAAO;AAAA,kBACP,MAAAM;AAAA,kBACA,SAAS,CAAAK,UAAQ;AACb,6BAAS,sBAAsB,EAAE,IAAI,MAAAA,MAAK,CAAC;AAC3C,iCAAa;AAAA,kBACjB;AAAA,gBACJ;AAAA,gBACA;AAAA,cACJ;AAEA;AAAA,YACJ;AAEA,yBAAa;AAAA,UACjB;AAAA,QACJ;AAAA,MACJ;AAKA,uBAAiB,iBAAiBL,OAAM,EAAE,OAAO,SAAS,CAAC,EACtD,KAAK,MAAM;AACR,wBAAgB,MAAM,qBAAqB,GAAG,cAAcA,KAAI,CAAC,EAAE;AAAA,UAC/D;AAAA,QACJ;AAAA,MACJ,CAAC,EACA,MAAM,OAAK;AACR,YAAI,CAAC,KAAK,CAAC,EAAE,SAAS,CAAC,EAAE,OAAQ,QAAO,UAAU,KAAK;AACvD,iBAAS,0BAA0B;AAAA,UAC/B;AAAA,UACA,OAAO,EAAE;AAAA,UACT,QAAQ,EAAE;AAAA,QACd,CAAC;AAAA,MACL,CAAC;AAAA,IACT,CAAC;AAED,IAAAA,MAAK,GAAG,iBAAiB,MAAM;AAC3B,eAAS,6BAA6B,EAAE,GAAG,CAAC;AAAA,IAChD,CAAC;AAED,IAAAA,MAAK,GAAG,oBAAoB,cAAY;AACpC,eAAS,oCAAoC,EAAE,IAAI,SAAS,CAAC;AAAA,IACjE,CAAC;AAED,IAAAA,MAAK,GAAG,iBAAiB,CAAAE,WAAS;AAC9B,eAAS,mCAAmC;AAAA,QACxC;AAAA,QACA,OAAAA;AAAA,QACA,QAAQ;AAAA,UACJ,MAAM,aAAaR,OAAM,QAAQ,wBAAwB,EAAEQ,MAAK;AAAA,UAChE,KAAKR,OAAM,QAAQ;AAAA,QACvB;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAED,IAAAM,MAAK,GAAG,wBAAwB,CAAAE,WAAS;AACrC,eAAS,0CAA0C;AAAA,QAC/C;AAAA,QACA,OAAAA;AAAA,QACA,QAAQ;AAAA,UACJ,MAAM,aAAaR,OAAM,QAAQ,8BAA8B,EAAEQ,MAAK;AAAA,UACtE,KAAKR,OAAM,QAAQ;AAAA,QACvB;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAED,IAAAM,MAAK,GAAG,oBAAoB,yBAAuB;AAC/C,eAAS,gCAAgC;AAAA,QACrC;AAAA,QACA,OAAO;AAAA,QACP;AAAA,MACJ,CAAC;AACD,eAAS,oBAAoB,EAAE,IAAI,OAAO,oBAAoB,CAAC;AAAA,IACnE,CAAC;AAED,IAAAA,MAAK,GAAG,iBAAiB,MAAM;AAC3B,eAAS,6BAA6B,EAAE,GAAG,CAAC;AAAA,IAChD,CAAC;AAED,IAAAA,MAAK,GAAG,kBAAkB,MAAM;AAC5B,eAAS,8BAA8B,EAAE,GAAG,CAAC;AAC7C,eAAS,oBAAoB,EAAE,IAAI,OAAO,KAAK,CAAC;AAAA,IACpD,CAAC;AAGD,aAAS,gBAAgB,EAAE,IAAI,OAAO,kBAAkB,CAAC;AAEzD,gBAAY,UAAUN,MAAK;AAG3B,UAAM,EAAE,KAAK,MAAM,SAAS,MAAM,IAAIA,OAAM,QAAQ,UAAU,CAAC;AAE/D,IAAAM,MAAK;AAAA,MACD;AAAA;AAAA,MAGA;AAAA,QACI,WAAW,WAAW;AAAA;AAAA,UAEhB,SAAS,MAAM,KAAK,cAAc,MAAM,IACpC,QACI,oBAAoB,KAAK,KAAK,IAC9B,YACJ;AAAA;AAAA;AAAA,UAEN,WAAW,WAAW,QACpB,oBAAoB,KAAK,OAAO,IAChC,oBAAoB,KAAK,IAAI;AAAA;AAAA;AAAA,MACvC;AAAA;AAAA,MAGA,CAACK,OAAMW,UAASd,WAAU;AAEtB,yBAAiB,aAAaG,OAAM,EAAE,MAAM,CAAC,EACxC,KAAKW,QAAO,EACZ,MAAMd,MAAK;AAAA,MACpB;AAAA,IACJ;AAAA,EACJ;AAAA,EAEA,wBAAwB,CAAC,EAAE,MAAAF,OAAM,SAAS,UAAU,MAAM;AAAA,EAAC,EAAE,MAAM;AAE/D,UAAM,MAAM;AAAA,MACR,OAAO,eAAe,SAAS,GAAG,gBAAgB;AAAA,MAClD,MAAM;AAAA,IACV;AAGA,QAAIA,MAAK,SAAU,QAAO,QAAQ,GAAG;AAGrC,qBAAiB,kBAAkBA,MAAK,MAAM,EAAE,OAAO,MAAAA,MAAK,CAAC,EAAE,KAAK,YAAU;AAC1E,uBAAiB,2BAA2B,QAAQ,EAAE,OAAO,MAAAA,MAAK,CAAC,EAAE,KAAK,CAAAiB,YAAU;AAEhF,YAAIjB,MAAK,SAAU,QAAO,QAAQ,GAAG;AAGrC,gBAAQiB,OAAM;AAAA,MAClB,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AAAA,EAEA,oBAAoB,CAAC,EAAE,MAAAjB,OAAM,MAAAM,MAAK,MAAM;AACpC,UAAM,EAAE,SAAS,OAAO,IAAIA;AAG5B,UAAM,qBAAqB,MAAM,0BAA0B;AAC3D,QAAI,WAAW,kBAAkB,KAAK,QAAQ;AAC1C,gBAAUZ,QAAO,kBAAkB;AAAA,IACvC;AAGA,aAAS,iBAAiB;AAAA,MACtB,IAAIM,MAAK;AAAA,MACT,OAAO;AAAA,MACP,qBAAqBA,MAAK,WAAW,WAAW,QAAQ,OAAO;AAAA,IACnE,CAAC;AAID,YAAQ,cAAcA,KAAI,CAAC;AAG3B,QAAIA,MAAK,WAAW,WAAW,OAAO;AAClC,eAAS,uBAAuB,EAAE,IAAIA,MAAK,GAAG,CAAC;AAC/C;AAAA,IACJ;AAGA,QAAIA,MAAK,WAAW,WAAW,OAAO;AAClC,eAAS,gCAAgC;AAAA,QACrC,IAAIA,MAAK;AAAA,QACT,OAAO;AAAA,QACP,qBAAqB;AAAA,MACzB,CAAC;AAED,eAAS,oBAAoB;AAAA,QACzB,IAAIA,MAAK;AAAA,QACT,OAAOA,MAAK,YAAY;AAAA,MAC5B,CAAC;AACD;AAAA,IACJ;AAGA,QAAI,MAAM,UAAU,KAAKN,OAAM,QAAQ,eAAe;AAClD,eAAS,2BAA2B,EAAE,OAAOM,MAAK,GAAG,CAAC;AAAA,IAC1D;AAAA,EACJ;AAAA,EAEA,iBAAiB,wBAAwBN,QAAO,CAAAM,UAAQ;AAEpD,IAAAA,MAAK,UAAU;AAAA,EACnB,CAAC;AAAA,EAED,sBAAsB,wBAAwBN,QAAO,CAACM,OAAM,SAAS,YAAY;AAC7E;AAAA,MACI;AAAA,MACA;AAAA,QACI,OAAOA,MAAK;AAAA,QACZ,MAAAA;AAAA,QACA,SAAS,CAAAK,UAAQ;AACb,mBAAS,sBAAsB,EAAE,IAAIL,MAAK,IAAI,MAAAK,MAAK,CAAC;AACpD,kBAAQ;AAAA,YACJ,MAAML;AAAA,YACN,QAAQK;AAAA,UACZ,CAAC;AAAA,QACL;AAAA,QACA;AAAA,MACJ;AAAA,MACA;AAAA,IACJ;AAAA,EACJ,CAAC;AAAA,EAED,yBAAyB,wBAAwBX,QAAO,CAACM,OAAM,SAAS,YAAY;AAEhF,UAAM;AAAA;AAAA,MAEFA,MAAK,WAAW,WAAW;AAAA,MAE3BA,MAAK,WAAW,WAAW;AAAA;AAG/B,QAAI,CAAC,8BAA8B;AAC/B,YAAM,aAAa,MACf,SAAS,2BAA2B,EAAE,OAAOA,OAAM,SAAS,QAAQ,CAAC;AAEzE,YAAM,UAAU,MAAO,SAAS,SAAS,WAAW,IAAI,WAAW,YAAY,EAAE;AAGjF,UACIA,MAAK,WAAW,WAAW,uBAC3BA,MAAK,WAAW,WAAW,yBAC7B;AACE,QAAAA,MAAK;AAAA,UACD,qBAAqBN,OAAM,QAAQ,OAAO,KAAKA,OAAM,QAAQ,OAAO,MAAM;AAAA,UAC1E,MAAM,kBAAkB;AAAA,QAC5B,EACK,KAAK,OAAO,EACZ,MAAM,MAAM;AAAA,QAAC,CAAC;AAAA,MACvB,WAAWM,MAAK,WAAW,WAAW,YAAY;AAC9C,QAAAA,MAAK,gBAAgB,EAAE,KAAK,OAAO;AAAA,MACvC;AAEA;AAAA,IACJ;AAGA,QAAIA,MAAK,WAAW,WAAW,kBAAmB;AAElD,IAAAA,MAAK,kBAAkB;AAEvB,aAAS,+BAA+B,EAAE,IAAIA,MAAK,GAAG,CAAC;AAEvD,aAAS,gBAAgB,EAAE,OAAOA,OAAM,SAAS,QAAQ,GAAG,IAAI;AAAA,EACpE,CAAC;AAAA,EAED,cAAc,wBAAwBN,QAAO,CAACM,OAAM,SAAS,YAAY;AACrE,UAAM,qBAAqB,MAAM,0BAA0B;AAC3D,UAAM,sBAAsB,MAAM,uBAAuB,WAAW,UAAU,EAAE;AAGhF,QAAI,wBAAwB,oBAAoB;AAE5C,MAAAN,OAAM,gBAAgB,KAAK;AAAA,QACvB,IAAIM,MAAK;AAAA,QACT;AAAA,QACA;AAAA,MACJ,CAAC;AAGD;AAAA,IACJ;AAGA,QAAIA,MAAK,WAAW,WAAW,WAAY;AAE3C,UAAM,cAAc,MAAM;AAEtB,YAAM,aAAaN,OAAM,gBAAgB,MAAM;AAG/C,UAAI,CAAC,WAAY;AAGjB,YAAM,EAAE,IAAI,SAAAsB,UAAS,SAAAE,SAAQ,IAAI;AACjC,YAAM,gBAAgB,eAAexB,OAAM,OAAO,EAAE;AAGpD,UAAI,CAAC,iBAAiB,cAAc,UAAU;AAC1C,oBAAY;AACZ;AAAA,MACJ;AAGA,eAAS,gBAAgB,EAAE,OAAO,IAAI,SAAAsB,UAAS,SAAAE,SAAQ,GAAG,IAAI;AAAA,IAClE;AAGA,IAAAlB,MAAK,OAAO,oBAAoB,MAAM;AAClC,cAAQ,cAAcA,KAAI,CAAC;AAC3B,kBAAY;AAIZ,YAAM,SAASN,OAAM,QAAQ;AAC7B,YAAM,gBAAgBA,OAAM,QAAQ;AACpC,UAAI,iBAAiBM,MAAK,WAAW,WAAW,SAAS,WAAW,OAAO,MAAM,GAAG;AAChF,cAAM,OAAO,MAAM;AAAA,QAAC;AACpB,QAAAA,MAAK,SAAS,WAAW;AACzB,QAAAN,OAAM,QAAQ,OAAO,OAAOM,MAAK,QAAQ,MAAM,IAAI;AAAA,MACvD;AAGA,YAAM,oBACF,MAAM,uBAAuB,WAAW,mBAAmB,EAAE,WAC7DN,OAAM,MAAM;AAChB,UAAI,mBAAmB;AACnB,iBAAS,kCAAkC;AAAA,MAC/C;AAAA,IACJ,CAAC;AAGD,IAAAM,MAAK,OAAO,iBAAiB,CAAAE,WAAS;AAClC,cAAQ,EAAE,OAAAA,QAAO,MAAM,cAAcF,KAAI,EAAE,CAAC;AAC5C,kBAAY;AAAA,IAChB,CAAC;AAGD,UAAM,UAAUN,OAAM;AACtB,IAAAM,MAAK;AAAA,MACD;AAAA,QACI,wBAAwB,QAAQ,OAAO,KAAK,QAAQ,OAAO,SAAS,QAAQ,MAAM;AAAA,UAC9E,iBAAiBA,MAAK;AAAA,UACtB,aAAa,QAAQ,OAAO;AAAA,UAC5B,cAAc,QAAQ;AAAA,UACtB,YAAY,QAAQ;AAAA,UACpB,WAAW,QAAQ;AAAA,UACnB,kBAAkB,QAAQ;AAAA,QAC9B,CAAC;AAAA,QACD;AAAA,UACI,4BAA4B,MAAM,mCAAmC;AAAA,QACzE;AAAA,MACJ;AAAA;AAAA,MAEA,CAACK,OAAMW,UAASd,WAAU;AAEtB,yBAAiB,kBAAkBG,OAAM,EAAE,OAAO,MAAAL,MAAK,CAAC,EACnD,KAAK,CAAAK,UAAQ;AACV,mBAAS,sBAAsB,EAAE,IAAIL,MAAK,IAAI,MAAAK,MAAK,CAAC;AAEpD,UAAAW,SAAQX,KAAI;AAAA,QAChB,CAAC,EACA,MAAMH,MAAK;AAAA,MACpB;AAAA,IACJ;AAAA,EACJ,CAAC;AAAA,EAED,uBAAuB,wBAAwBR,QAAO,CAAAM,UAAQ;AAC1D,aAAS,2BAA2B,EAAE,OAAOA,MAAK,CAAC;AAAA,EACvD,CAAC;AAAA,EAED,qBAAqB,wBAAwBN,QAAO,CAAAM,UAAQ;AACxD,oBAAgB,MAAM,wBAAwB,GAAG,cAAcA,KAAI,CAAC,EAAE,KAAK,kBAAgB;AACvF,UAAI,CAAC,cAAc;AACf;AAAA,MACJ;AACA,eAAS,eAAe,EAAE,OAAOA,MAAK,CAAC;AAAA,IAC3C,CAAC;AAAA,EACL,CAAC;AAAA,EAED,cAAc,wBAAwBN,QAAO,CAAAM,UAAQ;AACjD,IAAAA,MAAK,QAAQ;AAAA,EACjB,CAAC;AAAA,EAED,aAAa,wBAAwBN,QAAO,CAACM,OAAM,SAAS,SAAS,YAAY;AAC7E,UAAM,iBAAiB,MAAM;AAEzB,YAAM,KAAKA,MAAK;AAGhB,kBAAYN,OAAM,OAAO,EAAE,EAAE,QAAQ;AAGrC,eAAS,mBAAmB,EAAE,OAAO,MAAM,IAAI,MAAAM,MAAK,CAAC;AAGrD,kBAAY,UAAUN,MAAK;AAG3B,cAAQ,cAAcM,KAAI,CAAC;AAAA,IAC/B;AAIA,UAAM,SAASN,OAAM,QAAQ;AAC7B,QACIM,MAAK,WAAW,WAAW,SAC3B,UACA,WAAW,OAAO,MAAM,KACxB,QAAQ,WAAW,OACrB;AACE,eAAS,yBAAyB,EAAE,IAAIA,MAAK,GAAG,CAAC;AAEjD,aAAO;AAAA,QACHA,MAAK;AAAA,QACL,MAAM,eAAe;AAAA,QACrB,YAAU;AACN,mBAAS,+BAA+B;AAAA,YACpC,IAAIA,MAAK;AAAA,YACT,OAAO,eAAe,SAAS,GAAG,QAAQ,IAAI;AAAA,YAC9C,QAAQ;AAAA,cACJ,MAAM,aAAaN,OAAM,QAAQ,oBAAoB,EAAE,MAAM;AAAA,cAC7D,KAAKA,OAAM,QAAQ;AAAA,YACvB;AAAA,UACJ,CAAC;AAAA,QACL;AAAA,MACJ;AAAA,IACJ,OAAO;AAEH,UACK,QAAQ,UAAUM,MAAK,WAAW,WAAW,SAASA,MAAK,aAAa;AAAA;AAAA;AAAA,MAIxEN,OAAM,QAAQ,gBAAgBM,MAAK,KAAK,OAAON,OAAM,QAAQ,aAC7DA,OAAM,QAAQ,gBAAgBA,OAAM,QAAQ,YAC/C;AACE,QAAAM,MAAK;AAAA,UACD,qBAAqBN,OAAM,QAAQ,OAAO,KAAKA,OAAM,QAAQ,OAAO,MAAM;AAAA,UAC1E,MAAM,kBAAkB;AAAA,QAC5B;AAAA,MACJ;AAGA,qBAAe;AAAA,IACnB;AAAA,EACJ,CAAC;AAAA,EAED,iBAAiB,wBAAwBA,QAAO,CAAAM,UAAQ;AACpD,IAAAA,MAAK,UAAU;AAAA,EACnB,CAAC;AAAA,EAED,uBAAuB,wBAAwBN,QAAO,CAAAM,UAAQ;AAE1D,QAAIA,MAAK,UAAU;AACf,eAAS,0BAA0B,EAAE,IAAIA,MAAK,GAAG,CAAC;AAClD;AAAA,IACJ;AAGA,IAAAA,MAAK,gBAAgB,EAAE,KAAK,MAAM;AAC9B,YAAM,eAAeN,OAAM,QAAQ;AACnC,UAAI,cAAc;AACd,iBAAS,eAAe,EAAE,OAAOM,MAAK,GAAG,CAAC;AAAA,MAC9C;AAAA,IACJ,CAAC;AAAA,EACL,CAAC;AAAA,EAED,gCAAgC,wBAAwBN,QAAO,CAAAM,UAAQ;AAEnE,QAAI,CAACN,OAAM,QAAQ,eAAe;AAC9B,eAAS,0BAA0B,EAAE,OAAOM,MAAK,CAAC;AAClD;AAAA,IACJ;AAIA,UAAM,eAAe,kBAAgB;AACjC,UAAI,CAAC,aAAc;AACnB,eAAS,0BAA0B,EAAE,OAAOA,MAAK,CAAC;AAAA,IACtD;AAEA,UAAMZ,MAAK,MAAM,wBAAwB;AACzC,QAAI,CAACA,KAAI;AACL,aAAO,aAAa,IAAI;AAAA,IAC5B;AAEA,UAAM,sBAAsBA,IAAG,cAAcY,KAAI,CAAC;AAClD,QAAI,uBAAuB,MAAM;AAE7B,aAAO,aAAa,IAAI;AAAA,IAC5B;AAEA,QAAI,OAAO,wBAAwB,WAAW;AAC1C,aAAO,aAAa,mBAAmB;AAAA,IAC3C;AAEA,QAAI,OAAO,oBAAoB,SAAS,YAAY;AAChD,0BAAoB,KAAK,YAAY;AAAA,IACzC;AAAA,EACJ,CAAC;AAAA,EAED,wBAAwB,wBAAwBN,QAAO,CAAAM,UAAQ;AAC3D,IAAAA,MAAK;AAAA,MACD,qBAAqBN,OAAM,QAAQ,OAAO,KAAKA,OAAM,QAAQ,OAAO,MAAM;AAAA,MAC1E,MAAM,kBAAkB;AAAA,IAC5B,EACK,KAAK,MAAM;AACR,YAAM,eAAeA,OAAM,QAAQ,iBAAiB,WAAWM,KAAI;AACnE,UAAI,cAAc;AACd,iBAAS,eAAe,EAAE,OAAOA,MAAK,GAAG,CAAC;AAAA,MAC9C;AAAA,IACJ,CAAC,EACA,MAAM,MAAM;AAAA,IAAC,CAAC;AAAA,EACvB,CAAC;AAAA,EAED,aAAa,CAAC,EAAE,QAAQ,MAAM;AAE1B,UAAM,aAAa,OAAO,KAAK,OAAO;AAGtC,UAAM,wBAAwB,mBAAmB,OAAO,SAAO,WAAW,SAAS,GAAG,CAAC;AAGvF,UAAM,oBAAoB;AAAA;AAAA,MAEtB,GAAG;AAAA;AAAA,MAGH,GAAG,OAAO,KAAK,OAAO,EAAE,OAAO,SAAO,CAAC,sBAAsB,SAAS,GAAG,CAAC;AAAA,IAC9E;AAGA,sBAAkB,QAAQ,SAAO;AAC7B,eAAS,OAAO,WAAW,KAAK,GAAG,EAAE,YAAY,CAAC,IAAI;AAAA,QAClD,OAAO,QAAQ,GAAG;AAAA,MACtB,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AACJ;AAEA,IAAM,qBAAqB;AAAA,EACvB;AAAA;AACJ;AAEA,IAAM,iBAAiB,CAAAf,UAAQA;AAE/B,IAAM,kBAAkB,aAAW;AAC/B,SAAO,SAAS,cAAc,OAAO;AACzC;AAEA,IAAM,OAAO,CAAC,MAAM,UAAU;AAC1B,MAAI,WAAW,KAAK,WAAW,CAAC;AAChC,MAAI,CAAC,UAAU;AACX,eAAW,SAAS,eAAe,KAAK;AACxC,SAAK,YAAY,QAAQ;AAAA,EAC7B,WAAW,UAAU,SAAS,WAAW;AACrC,aAAS,YAAY;AAAA,EACzB;AACJ;AAEA,IAAM,mBAAmB,CAAC,SAAS,SAAS,QAAQ,mBAAmB;AACnE,QAAM,kBAAoB,iBAAiB,MAAO,MAAM,KAAK,KAAM;AACnE,SAAO;AAAA,IACH,GAAG,UAAU,SAAS,KAAK,IAAI,cAAc;AAAA,IAC7C,GAAG,UAAU,SAAS,KAAK,IAAI,cAAc;AAAA,EACjD;AACJ;AAEA,IAAM,cAAc,CAAC,GAAG,GAAG,QAAQ,YAAY,UAAU,aAAa;AAClE,QAAM,QAAQ,iBAAiB,GAAG,GAAG,QAAQ,QAAQ;AACrD,QAAM,MAAM,iBAAiB,GAAG,GAAG,QAAQ,UAAU;AACrD,SAAO,CAAC,KAAK,MAAM,GAAG,MAAM,GAAG,KAAK,QAAQ,QAAQ,GAAG,UAAU,GAAG,IAAI,GAAG,IAAI,CAAC,EAAE,KAAK,GAAG;AAC9F;AAEA,IAAM,gBAAgB,CAAC,GAAG,GAAG,QAAQ,MAAM,OAAO;AAC9C,MAAI,WAAW;AACf,MAAI,KAAK,QAAQ,KAAK,QAAQ,KAAK;AAC/B,eAAW;AAAA,EACf;AACA,MAAI,OAAO,MAAM,OAAO,MAAM,KAAK;AAC/B,eAAW;AAAA,EACf;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAK,IAAI,QAAQ,IAAI,IAAI;AAAA,IACzB,KAAK,IAAI,QAAQ,EAAE,IAAI;AAAA,IACvB;AAAA,EACJ;AACJ;AAEA,IAAM,SAAS,CAAC,EAAE,MAAAa,OAAM,MAAM,MAAM;AAEhC,QAAM,OAAO;AACb,QAAM,WAAW;AACjB,QAAM,UAAU;AAGhB,QAAM,MAAM,cAAc,KAAK;AAC/B,EAAAA,MAAK,IAAI,OAAO,cAAc,QAAQ;AAAA,IAClC,gBAAgB;AAAA,IAChB,kBAAkB;AAAA,EACtB,CAAC;AACD,MAAI,YAAYA,MAAK,IAAI,IAAI;AAE7B,EAAAA,MAAK,IAAI,MAAM;AAEf,EAAAA,MAAK,YAAY,GAAG;AACxB;AAEA,IAAM,QAAQ,CAAC,EAAE,MAAAA,OAAM,MAAM,MAAM;AAC/B,MAAI,MAAM,YAAY,GAAG;AACrB;AAAA,EACJ;AAEA,MAAI,MAAM,OAAO;AACb,IAAAA,MAAK,QAAQ,QAAQ,QAAQ,MAAM;AAAA,EACvC;AAGA,QAAM,kBAAkB,SAAS,KAAKA,MAAK,IAAI,MAAM,cAAc,GAAG,EAAE;AAGxE,QAAM,OAAOA,MAAK,KAAK,QAAQ,QAAQ;AAGvC,MAAI,WAAW;AACf,MAAI,SAAS;AAGb,MAAI,MAAM,MAAM;AACZ,eAAW;AACX,aAAS;AAAA,EACb,OAAO;AACH,eAAW;AACX,aAAS,MAAM;AAAA,EACnB;AAGA,QAAM,cAAc,cAAc,MAAM,MAAM,OAAO,iBAAiB,UAAU,MAAM;AAGtF,OAAKA,MAAK,IAAI,MAAM,KAAK,WAAW;AAGpC,OAAKA,MAAK,IAAI,MAAM,kBAAkB,MAAM,QAAQ,MAAM,WAAW,IAAI,IAAI,CAAC;AAClF;AAEA,IAAM,oBAAoB,WAAW;AAAA,EACjC,KAAK;AAAA,EACL,MAAM;AAAA,EACN,kBAAkB;AAAA,EAClB,YAAY;AAAA,EACZ;AAAA,EACA;AAAA,EACA,QAAQ;AAAA,IACJ,MAAM,CAAC,YAAY,QAAQ,OAAO;AAAA,IAClC,QAAQ,CAAC,SAAS;AAAA,IAClB,YAAY;AAAA,MACR,SAAS,EAAE,MAAM,SAAS,UAAU,IAAI;AAAA,MACxC,UAAU;AAAA,QACN,MAAM;AAAA,QACN,WAAW;AAAA,QACX,SAAS;AAAA,QACT,MAAM;AAAA,MACV;AAAA,IACJ;AAAA,EACJ;AACJ,CAAC;AAED,IAAM,WAAW,CAAC,EAAE,MAAAA,OAAM,MAAM,MAAM;AAClC,EAAAA,MAAK,QAAQ,aAAa,MAAM,QAAQ,MAAM,SAAS,MAAM,KAAK;AAElE,QAAM,aAAa;AACvB;AAEA,IAAM,UAAU,CAAC,EAAE,MAAAA,OAAM,MAAM,MAAM;AACjC,QAAM,EAAE,WAAW,IAAI;AACvB,QAAM,gBAAgBA,MAAK,MAAM,cAAc,KAAK,MAAM,YAAY;AAEtE,MAAI,iBAAiB,CAAC,YAAY;AAC9B,UAAM,aAAa;AACnB,SAAKA,MAAK,SAAS,YAAY,UAAU;AAAA,EAC7C,WAAW,CAAC,iBAAiB,YAAY;AACrC,UAAM,aAAa;AACnB,IAAAA,MAAK,QAAQ,gBAAgB,UAAU;AAAA,EAC3C;AACJ;AAEA,IAAM,mBAAmB,WAAW;AAAA,EAChC,KAAK;AAAA,EACL,YAAY;AAAA,IACR,MAAM;AAAA,EACV;AAAA,EACA,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,MAAM;AAAA,EACN,QAAQ;AAAA,IACJ,MAAM,CAAC,OAAO;AAAA,IACd,QAAQ,CAAC,cAAc,cAAc,UAAU,UAAU,SAAS;AAAA,IAClE,YAAY;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,SAAS,EAAE,MAAM,SAAS,UAAU,IAAI;AAAA,IAC5C;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA,QAAQ;AAAA,EACR,OAAO;AACX,CAAC;AAED,IAAM,oBAAoB,CAAC,OAAO,mBAAmB,KAAK,OAAO,KAAM,UAAU,CAAC,MAAM;AACpF,QAAM;AAAA,IACF,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,EACrB,IAAI;AAGJ,UAAQ,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC;AAElC,QAAM,KAAK;AACX,QAAM,KAAK,OAAO;AAClB,QAAM,KAAK,OAAO,OAAO;AAGzB,MAAI,QAAQ,IAAI;AACZ,WAAO,GAAG,KAAK,IAAI,UAAU;AAAA,EACjC;AAGA,MAAI,QAAQ,IAAI;AACZ,WAAO,GAAG,KAAK,MAAM,QAAQ,EAAE,CAAC,IAAI,cAAc;AAAA,EACtD;AAGA,MAAI,QAAQ,IAAI;AACZ,WAAO,GAAG,uBAAuB,QAAQ,IAAI,GAAG,gBAAgB,CAAC,IAAI,cAAc;AAAA,EACvF;AAGA,SAAO,GAAG,uBAAuB,QAAQ,IAAI,GAAG,gBAAgB,CAAC,IAAI,cAAc;AACvF;AAEA,IAAM,yBAAyB,CAAC,OAAO,cAAc,cAAc;AAC/D,SAAO,MACF,QAAQ,YAAY,EACpB,MAAM,GAAG,EACT,OAAO,UAAQ,SAAS,GAAG,EAC3B,KAAK,SAAS;AACvB;AAEA,IAAM,WAAW,CAAC,EAAE,MAAAA,OAAM,MAAM,MAAM;AAElC,QAAM,WAAW,gBAAgB,MAAM;AACvC,WAAS,YAAY;AAIrB,OAAK,UAAU,eAAe,MAAM;AACpC,EAAAA,MAAK,YAAY,QAAQ;AACzB,EAAAA,MAAK,IAAI,WAAW;AAGpB,QAAM,WAAW,gBAAgB,MAAM;AACvC,WAAS,YAAY;AACrB,EAAAA,MAAK,YAAY,QAAQ;AACzB,EAAAA,MAAK,IAAI,WAAW;AAGpB,OAAK,UAAUA,MAAK,MAAM,iCAAiC,CAAC;AAC5D,OAAK,UAAU,eAAeA,MAAK,MAAM,iBAAiB,MAAM,EAAE,CAAC,CAAC;AACxE;AAEA,IAAM,aAAa,CAAC,EAAE,MAAAA,OAAM,MAAM,MAAM;AACpC;AAAA,IACIA,MAAK,IAAI;AAAA,IACT;AAAA,MACIA,MAAK,MAAM,iBAAiB,MAAM,EAAE;AAAA,MACpC;AAAA,MACAA,MAAK,MAAM,oBAAoB;AAAA,MAC/BA,MAAK,MAAM,wBAAwBA,MAAK,KAAK;AAAA,IACjD;AAAA,EACJ;AACA,OAAKA,MAAK,IAAI,UAAU,eAAeA,MAAK,MAAM,iBAAiB,MAAM,EAAE,CAAC,CAAC;AACjF;AAEA,IAAM,wBAAwB,CAAC,EAAE,MAAAA,OAAM,MAAM,MAAM;AAE/C,MAAI,MAAMA,MAAK,MAAM,iBAAiB,MAAM,EAAE,CAAC,GAAG;AAC9C,eAAW,EAAE,MAAAA,OAAM,MAAM,CAAC;AAC1B;AAAA,EACJ;AAEA,OAAKA,MAAK,IAAI,UAAUA,MAAK,MAAM,mCAAmC,CAAC;AAC3E;AAEA,IAAM,WAAW,WAAW;AAAA,EACxB,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,OAAO,YAAY;AAAA,IACf,eAAe;AAAA,IACf,sBAAsB;AAAA,IACtB,2BAA2B;AAAA,IAC3B,wBAAwB;AAAA,EAC5B,CAAC;AAAA,EACD,eAAe,CAAAA,UAAQ;AACnB,iBAAa,eAAe,EAAE,GAAGA,OAAM,MAAMA,MAAK,CAAC;AAAA,EACvD;AAAA,EACA,QAAQ;AAAA,EACR,QAAQ;AAAA,IACJ,QAAQ,CAAC,cAAc,YAAY;AAAA,IACnC,YAAY;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,IAChB;AAAA,EACJ;AACJ,CAAC;AAED,IAAM,eAAe,WAAS,KAAK,MAAM,QAAQ,GAAG;AAEpD,IAAM,WAAW,CAAC,EAAE,MAAAA,MAAK,MAAM;AAE3B,QAAM,OAAO,gBAAgB,MAAM;AACnC,OAAK,YAAY;AACjB,EAAAA,MAAK,YAAY,IAAI;AACrB,EAAAA,MAAK,IAAI,OAAO;AAGhB,QAAM,MAAM,gBAAgB,MAAM;AAClC,MAAI,YAAY;AAChB,EAAAA,MAAK,YAAY,GAAG;AACpB,EAAAA,MAAK,IAAI,MAAM;AAEf,yBAAuB,EAAE,MAAAA,OAAM,QAAQ,EAAE,UAAU,KAAK,EAAE,CAAC;AAC/D;AAEA,IAAM,yBAAyB,CAAC,EAAE,MAAAA,OAAM,OAAO,MAAM;AACjD,QAAM,QACF,OAAO,aAAa,OACdA,MAAK,MAAM,wBAAwB,IACnC,GAAGA,MAAK,MAAM,wBAAwB,CAAC,IAAI,aAAa,OAAO,QAAQ,CAAC;AAClF,OAAKA,MAAK,IAAI,MAAM,KAAK;AACzB,OAAKA,MAAK,IAAI,KAAKA,MAAK,MAAM,yBAAyB,CAAC;AAC5D;AAEA,IAAM,4BAA4B,CAAC,EAAE,MAAAA,OAAM,OAAO,MAAM;AACpD,QAAM,QACF,OAAO,aAAa,OACdA,MAAK,MAAM,2BAA2B,IACtC,GAAGA,MAAK,MAAM,2BAA2B,CAAC,IAAI,aAAa,OAAO,QAAQ,CAAC;AACrF,OAAKA,MAAK,IAAI,MAAM,KAAK;AACzB,OAAKA,MAAK,IAAI,KAAKA,MAAK,MAAM,yBAAyB,CAAC;AAC5D;AAEA,IAAM,2BAA2B,CAAC,EAAE,MAAAA,MAAK,MAAM;AAC3C,OAAKA,MAAK,IAAI,MAAMA,MAAK,MAAM,2BAA2B,CAAC;AAC3D,OAAKA,MAAK,IAAI,KAAKA,MAAK,MAAM,yBAAyB,CAAC;AAC5D;AAEA,IAAM,yBAAyB,CAAC,EAAE,MAAAA,MAAK,MAAM;AACzC,OAAKA,MAAK,IAAI,MAAMA,MAAK,MAAM,mCAAmC,CAAC;AACnE,OAAKA,MAAK,IAAI,KAAKA,MAAK,MAAM,wBAAwB,CAAC;AAC3D;AAEA,IAAM,4BAA4B,CAAC,EAAE,MAAAA,MAAK,MAAM;AAC5C,OAAKA,MAAK,IAAI,MAAMA,MAAK,MAAM,oCAAoC,CAAC;AACpE,OAAKA,MAAK,IAAI,KAAKA,MAAK,MAAM,uBAAuB,CAAC;AAC1D;AAEA,IAAM,QAAQ,CAAC,EAAE,MAAAA,MAAK,MAAM;AACxB,OAAKA,MAAK,IAAI,MAAM,EAAE;AACtB,OAAKA,MAAK,IAAI,KAAK,EAAE;AACzB;AAEA,IAAM,QAAQ,CAAC,EAAE,MAAAA,OAAM,OAAO,MAAM;AAChC,OAAKA,MAAK,IAAI,MAAM,OAAO,OAAO,IAAI;AACtC,OAAKA,MAAK,IAAI,KAAK,OAAO,OAAO,GAAG;AACxC;AAEA,IAAM,aAAa,WAAW;AAAA,EAC1B,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,OAAO,YAAY;AAAA,IACf,eAAe;AAAA,IACf,4BAA4B;AAAA,IAC5B,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,8BAA8B;AAAA,IAC9B,kCAAkC;AAAA,IAClC,+BAA+B;AAAA,IAC/B,2BAA2B;AAAA,IAC3B,wBAAwB;AAAA,IACxB,iCAAiC;AAAA,IACjC,wCAAwC;AAAA,IACxC,6BAA6B;AAAA,EACjC,CAAC;AAAA,EACD,eAAe,CAAAA,UAAQ;AACnB,iBAAa,eAAe,EAAE,GAAGA,OAAM,MAAMA,MAAK,CAAC;AAAA,EACvD;AAAA,EACA,QAAQ;AAAA,EACR,QAAQ;AAAA,IACJ,QAAQ,CAAC,cAAc,cAAc,SAAS;AAAA,IAC9C,YAAY;AAAA,MACR,SAAS,EAAE,MAAM,SAAS,UAAU,IAAI;AAAA,MACxC,YAAY;AAAA,MACZ,YAAY;AAAA,IAChB;AAAA,EACJ;AACJ,CAAC;AAMD,IAAM,UAAU;AAAA,EACZ,eAAe;AAAA,IACX,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,OAAO;AAAA;AAAA,EACX;AAAA,EACA,eAAe;AAAA,IACX,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,WAAW;AAAA,IACX,OAAO;AAAA;AAAA,EACX;AAAA,EACA,YAAY;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,WAAW;AAAA,IACX,OAAO;AAAA;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACT,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,WAAW;AAAA,IACX,OAAO;AAAA;AAAA,EACX;AAAA,EACA,qBAAqB;AAAA,IACjB,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,OAAO;AAAA;AAAA,EACX;AAAA,EACA,qBAAqB;AAAA,IACjB,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,WAAW;AAAA,IACX,OAAO;AAAA;AAAA,EACX;AAAA,EACA,sBAAsB;AAAA,IAClB,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,WAAW;AAAA,IACX,OAAO;AAAA;AAAA,EACX;AACJ;AAGA,IAAM,aAAa,CAAC;AACpB,MAAM,SAAS,SAAO;AAClB,aAAW,KAAK,GAAG;AACvB,CAAC;AAED,IAAM,0BAA0B,CAAAA,UAAQ;AACpC,MAAI,2BAA2BA,KAAI,MAAM,QAAS,QAAO;AACzD,QAAM,aAAaA,MAAK,IAAI,iBAAiB,KAAK;AAClD,SAAO,WAAW,SAAS,OAAO,WAAW,QAAQ,WAAW;AACpE;AAEA,IAAM,uBAAuB,CAAAA,UAAQ;AACjC,QAAM,aAAaA,MAAK,IAAI,oBAAoB,KAAK;AACrD,SAAO,WAAW;AACtB;AAGA,IAAM,oCAAoC,CAAAA,UACtC,KAAK,MAAMA,MAAK,IAAI,iBAAiB,KAAK,QAAQ,SAAS,CAAC;AAChE,IAAM,sCAAsC,CAAAA,UACxC,KAAK,MAAMA,MAAK,IAAI,iBAAiB,KAAK,QAAQ,OAAO,CAAC;AAE9D,IAAM,4BAA4B,CAAAA,UAAQA,MAAK,MAAM,mCAAmC;AACxF,IAAM,+BAA+B,CAAAA,UAAQA,MAAK,MAAM,uCAAuC;AAC/F,IAAM,6BAA6B,CAAAA,UAAQA,MAAK,MAAM,uCAAuC;AAE7F,IAAM,eAAe;AAAA,EACjB,qBAAqB,EAAE,SAAS,EAAE;AAAA,EAClC,qBAAqB,EAAE,SAAS,EAAE;AAAA,EAClC,kBAAkB,EAAE,SAAS,EAAE;AAAA,EAC/B,mBAAmB,EAAE,SAAS,EAAE;AAAA,EAChC,2BAA2B,EAAE,SAAS,EAAE;AAAA,EACxC,2BAA2B,EAAE,SAAS,EAAE;AAAA,EACxC,4BAA4B,EAAE,SAAS,EAAE;AAAA,EACzC,uBAAuB,EAAE,SAAS,GAAG,OAAO,0BAA0B;AAAA,EACtE,0BAA0B,EAAE,SAAS,GAAG,OAAO,6BAA6B;AAAA,EAC5E,6BAA6B,EAAE,SAAS,GAAG,QAAQ,MAAM,QAAQ,KAAK;AAAA,EACtE,MAAM,EAAE,YAAY,GAAG,YAAY,GAAG,SAAS,EAAE;AAAA,EACjD,QAAQ,EAAE,YAAY,GAAG,YAAY,GAAG,SAAS,EAAE;AACvD;AAEA,IAAM,YAAY;AAAA,EACd,kBAAkB,EAAE,SAAS,EAAE;AAAA,EAC/B,mBAAmB,EAAE,SAAS,EAAE;AAAA,EAChC,MAAM,EAAE,YAAY,wBAAwB;AAAA,EAC5C,QAAQ,EAAE,YAAY,wBAAwB;AAClD;AAEA,IAAM,kBAAkB;AAAA,EACpB,2BAA2B,EAAE,SAAS,EAAE;AAAA,EACxC,0BAA0B,EAAE,SAAS,EAAE;AAAA,EACvC,QAAQ,EAAE,SAAS,EAAE;AACzB;AAEA,IAAM,WAAW;AAAA,EACb,wBAAwB;AAAA,IACpB,kBAAkB,EAAE,SAAS,EAAE;AAAA,IAC/B,MAAM,EAAE,YAAY,wBAAwB;AAAA,IAC5C,QAAQ,EAAE,YAAY,yBAAyB,SAAS,EAAE;AAAA,EAC9D;AAAA,EACA,qBAAqB;AAAA,IACjB,qBAAqB,EAAE,SAAS,EAAE;AAAA,IAClC,uBAAuB,EAAE,SAAS,EAAE;AAAA,IACpC,QAAQ,EAAE,SAAS,EAAE;AAAA,EACzB;AAAA,EACA,2BAA2B;AAAA,IACvB,qBAAqB,EAAE,SAAS,EAAE;AAAA,IAClC,kBAAkB,EAAE,SAAS,EAAE;AAAA,IAC/B,MAAM,EAAE,YAAY,wBAAwB;AAAA,IAC5C,QAAQ,EAAE,SAAS,EAAE;AAAA,EACzB;AAAA,EACA,uBAAuB;AAAA,IACnB,0BAA0B,EAAE,SAAS,GAAG,OAAO,2BAA2B;AAAA,IAC1E,MAAM,EAAE,YAAY,wBAAwB;AAAA,IAC5C,QAAQ,EAAE,SAAS,EAAE;AAAA,EACzB;AAAA,EACA,6BAA6B;AAAA,IACzB,0BAA0B,EAAE,SAAS,GAAG,OAAO,2BAA2B;AAAA,IAC1E,kBAAkB,EAAE,SAAS,EAAE;AAAA,IAC/B,MAAM,EAAE,YAAY,wBAAwB;AAAA,IAC5C,QAAQ,EAAE,SAAS,GAAG,YAAY,wBAAwB;AAAA,EAC9D;AAAA,EACA,eAAe;AAAA,EACf,qBAAqB;AAAA,IACjB,kBAAkB,EAAE,SAAS,EAAE;AAAA,IAC/B,MAAM,EAAE,YAAY,wBAAwB;AAAA,IAC5C,QAAQ,EAAE,YAAY,wBAAwB;AAAA,EAClD;AAAA,EACA,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,IAC1B,4BAA4B,EAAE,SAAS,EAAE;AAAA,IACzC,MAAM,EAAE,SAAS,EAAE;AAAA,IACnB,QAAQ,EAAE,SAAS,EAAE;AAAA,EACzB;AAAA,EACA,iCAAiC;AAAA,IAC7B,kBAAkB,EAAE,SAAS,EAAE;AAAA,IAC/B,2BAA2B,EAAE,SAAS,EAAE;AAAA,IACxC,QAAQ,EAAE,SAAS,EAAE;AAAA,IACrB,MAAM,EAAE,YAAY,wBAAwB;AAAA,EAChD;AAAA,EACA,wCAAwC;AAAA,IACpC,4BAA4B,EAAE,SAAS,EAAE;AAAA,IACzC,QAAQ,EAAE,SAAS,EAAE;AAAA,IACrB,MAAM,EAAE,SAAS,EAAE;AAAA,EACvB;AAAA,EACA,2BAA2B;AAAA,IACvB,kBAAkB,EAAE,SAAS,EAAE;AAAA,IAC/B,mBAAmB,EAAE,SAAS,EAAE;AAAA,IAChC,MAAM,EAAE,YAAY,wBAAwB;AAAA,IAC5C,QAAQ,EAAE,SAAS,EAAE;AAAA,EACzB;AAAA,EACA,4BAA4B;AAChC;AAGA,IAAM,kCAAkC,WAAW;AAAA,EAC/C,QAAQ,CAAC,EAAE,MAAAA,MAAK,MAAM;AAClB,IAAAA,MAAK,QAAQ,YAAYA,MAAK,MAAM,eAAe;AAAA,EACvD;AAAA,EACA,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,QAAQ;AAAA,IACJ,QAAQ,CAAC,UAAU,UAAU,SAAS;AAAA,IACtC,YAAY;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,SAAS,EAAE,MAAM,SAAS,UAAU,IAAI;AAAA,IAC5C;AAAA,EACJ;AACJ,CAAC;AAKD,IAAM,WAAW,CAAC,EAAE,MAAAA,OAAM,MAAM,MAAM;AAElC,QAAM,eAAe,OAAO,KAAK,OAAO,EAAE,OAAO,CAAC,MAAM,SAAS;AAC7D,SAAK,IAAI,IAAI,EAAE,GAAG,QAAQ,IAAI,EAAE;AAChC,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AAEL,QAAM,EAAE,GAAG,IAAI;AAGf,QAAM,cAAcA,MAAK,MAAM,kBAAkB;AAGjD,QAAM,cAAcA,MAAK,MAAM,kBAAkB;AAGjD,QAAM,eAAeA,MAAK,MAAM,mBAAmB;AAGnD,QAAM,gBAAgBA,MAAK,MAAM,oBAAoB;AAGrD,QAAMqB,WAAUrB,MAAK,MAAM,UAAU;AAGrC,QAAM,wBAAwBA,MAAK,MAAM,oCAAoC;AAG7E,MAAI;AACJ,MAAIqB,UAAS;AACT,QAAI,gBAAgB,CAAC,aAAa;AAE9B,qBAAe,SAAO,CAAC,uBAAuB,KAAK,GAAG;AAAA,IAC1D,WAAW,CAAC,gBAAgB,aAAa;AAErC,qBAAe,SAAO,CAAC,sDAAsD,KAAK,GAAG;AAAA,IACzF,WAAW,CAAC,gBAAgB,CAAC,aAAa;AAEtC,qBAAe,SAAO,CAAC,UAAU,KAAK,GAAG;AAAA,IAC7C;AAAA,EACJ,OAAO;AAEH,mBAAe,SAAO,CAAC,UAAU,KAAK,GAAG;AAAA,EAC7C;AAEA,QAAM,iBAAiB,eAAe,WAAW,OAAO,YAAY,IAAI,WAAW,OAAO;AAG1F,MAAI,iBAAiB,aAAa;AAC9B,iBAAa,sBAAsB,EAAE,QAAQ;AAC7C,iBAAa,sBAAsB,EAAE,OAAO;AAAA,EAChD;AAGA,MAAIA,YAAW,CAAC,aAAa;AACzB,UAAMC,OAAM,SAAS,8BAA8B;AACnD,IAAAA,KAAI,KAAK,aAAa;AACtB,IAAAA,KAAI,KAAK,aAAa;AACtB,IAAAA,KAAI,OAAO,aAAa;AACxB,IAAAA,KAAI,8BAA8B,EAAE,SAAS,GAAG,QAAQ,GAAG,QAAQ,EAAE;AAAA,EACzE;AAGA,MAAID,YAAW,CAAC,cAAc;AAC1B;AAAA,MACI;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ,EAAE,QAAQ,SAAO;AACb,eAAS,GAAG,EAAE,OAAO,aAAa;AAAA,IACtC,CAAC;AACD,aAAS,iCAAiC,EAAE,OAAO,aAAa;AAAA,EACpE;AAGA,MAAI,yBAAyB,aAAa;AACtC,iBAAa,sBAAsB,EAAE,QAAQ;AAC7C,UAAMC,OAAM,SAAS,8BAA8B;AACnD,IAAAA,KAAI,KAAK,aAAa;AACtB,IAAAA,KAAI,OAAO,aAAa;AACxB,IAAAA,KAAI,8BAA8B,EAAE,SAAS,GAAG,QAAQ,GAAG,QAAQ,EAAE;AAAA,EACzE;AAGA,MAAI,CAAC,aAAa;AACd,iBAAa,YAAY,EAAE,WAAW;AAAA,EAC1C;AAGA,QAAM,cAAc,CAAC,KAAK,eAAe;AAErC,UAAM,aAAatB,MAAK,gBAAgB,kBAAkB;AAAA,MACtD,OAAOA,MAAK,MAAM,WAAW,KAAK;AAAA,MAClC,MAAMA,MAAK,MAAM,WAAW,IAAI;AAAA,MAChC,SAAS;AAAA,IACb,CAAC;AAGD,QAAI,eAAe,SAAS,GAAG,GAAG;AAC9B,MAAAA,MAAK,gBAAgB,UAAU;AAAA,IACnC;AAGA,QAAI,WAAW,UAAU;AACrB,iBAAW,QAAQ,aAAa,YAAY,UAAU;AACtD,iBAAW,QAAQ,aAAa,UAAU,QAAQ;AAAA,IACtD;AAGA,eAAW,QAAQ,QAAQ,QAAQA,MAAK,MAAM,aAAa,WAAW,KAAK,EAAE;AAG7E,eAAW,QAAQ,UAAU,IAAI,WAAW,SAAS;AAGrD,eAAW,GAAG,SAAS,OAAK;AACxB,QAAE,gBAAgB;AAClB,UAAI,WAAW,SAAU;AACzB,MAAAA,MAAK,SAAS,WAAW,QAAQ,EAAE,OAAO,GAAG,CAAC;AAAA,IAClD,CAAC;AAGD,IAAAA,MAAK,IAAI,SAAS,GAAG,EAAE,IAAI;AAAA,EAC/B,CAAC;AAGD,EAAAA,MAAK,IAAI,8BAA8BA,MAAK;AAAA,IACxCA,MAAK,gBAAgB,+BAA+B;AAAA,EACxD;AACA,EAAAA,MAAK,IAAI,4BAA4B,QAAQ,QAAQ,QAAQA,MAAK;AAAA,IAC9D;AAAA,EACJ;AAGA,EAAAA,MAAK,IAAI,OAAOA,MAAK,gBAAgBA,MAAK,gBAAgB,UAAU,EAAE,GAAG,CAAC,CAAC;AAG3E,EAAAA,MAAK,IAAI,SAASA,MAAK,gBAAgBA,MAAK,gBAAgB,YAAY,EAAE,GAAG,CAAC,CAAC;AAG/E,QAAM,oBAAoBA,MAAK;AAAA,IAC3BA,MAAK,gBAAgB,mBAAmB;AAAA,MACpC,SAAS;AAAA,MACT,OAAOA,MAAK,MAAM,mCAAmC;AAAA,IACzD,CAAC;AAAA,EACL;AACA,oBAAkB,QAAQ,UAAU,IAAI,0BAA0B;AAClE,EAAAA,MAAK,IAAI,wBAAwB;AAEjC,QAAM,wBAAwBA,MAAK;AAAA,IAC/BA,MAAK,gBAAgB,mBAAmB;AAAA,MACpC,SAAS;AAAA,MACT,OAAOA,MAAK,MAAM,uCAAuC;AAAA,IAC7D,CAAC;AAAA,EACL;AACA,wBAAsB,QAAQ,UAAU,IAAI,6BAA6B;AACzE,EAAAA,MAAK,IAAI,2BAA2B;AAGpC,EAAAA,MAAK,IAAI,eAAe,CAAC;AAC7B;AAEA,IAAM,UAAU,CAAC,EAAE,MAAAA,OAAM,SAAAL,UAAS,MAAM,MAAM;AAE1C,QAAM,EAAE,MAAAK,OAAM,SAAAL,UAAS,MAAM,CAAC;AAG9B,MAAI,SAASA,SACR,OAAO,EACP,OAAO,CAAA4B,YAAU,QAAQ,KAAKA,QAAO,IAAI,CAAC,EAC1C,QAAQ,EACR,KAAK,CAAAA,YAAU,SAASA,QAAO,IAAI,CAAC;AAGzC,MAAI,QAAQ;AAER,IAAAvB,MAAK,IAAI,eAAe,CAAC;AAEzB,UAAM,gBAAgB,SAAS,OAAO,IAAI;AAC1C,UAAM,cAAc,CAACb,OAAM,kBAAkB;AAEzC,YAAM,UAAUa,MAAK,IAAIb,KAAI;AAG7B,YAAM,eAAe,CAAC,KAAK,iBAAiB;AACxC,cAAM,QACF,cAAcA,KAAI,KAAK,OAAO,cAAcA,KAAI,EAAE,GAAG,MAAM,cACrD,cAAcA,KAAI,EAAE,GAAG,IACvB;AACV,QAAAa,MAAK,IAAI,aAAa,KAAK,EAAE,SAAS,KAAK,MAAM,CAAC;AAAA,MACtD,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AAGA,EAAAA,MAAK,IAAI,aAAa,QAAQ,CAAC,EAAE,SAAS,KAAK,MAAM,MAAM;AACvD,YAAQ,GAAG,IAAI,OAAO,UAAU,aAAa,MAAMA,KAAI,IAAI;AAAA,EAC/D,CAAC;AACL;AAEA,IAAM,QAAQ,YAAY;AAAA,EACtB,4CAA4C,CAAC,EAAE,MAAAA,OAAM,OAAO,MAAM;AAC9D,IAAAA,MAAK,IAAI,0BAA0B,QAAQ,OAAO;AAAA,EACtD;AAAA,EACA,sCAAsC,CAAC,EAAE,MAAAA,OAAM,OAAO,MAAM;AACxD,IAAAA,MAAK,IAAI,oBAAoB,QAAQ,OAAO;AAAA,EAChD;AAAA,EACA,yCAAyC,CAAC,EAAE,MAAAA,OAAM,OAAO,MAAM;AAC3D,IAAAA,MAAK,IAAI,uBAAuB,QAAQ,OAAO;AAAA,EACnD;AAAA,EACA,6BAA6B,CAAC,EAAE,MAAAA,MAAK,MAAM;AACvC,IAAAA,MAAK,IAAI,yBAAyB,OAAO;AACzC,IAAAA,MAAK,IAAI,yBAAyB,WAAW;AAAA,EACjD;AAAA,EACA,qBAAqB,CAAC,EAAE,MAAAA,MAAK,MAAM;AAC/B,IAAAA,MAAK,IAAI,sBAAsB,OAAO;AACtC,IAAAA,MAAK,IAAI,sBAAsB,WAAW;AAAA,EAC9C;AAAA,EACA,uBAAuB,CAAC,EAAE,MAAAA,MAAK,MAAM;AACjC,IAAAA,MAAK,IAAI,yBAAyB,OAAO;AACzC,IAAAA,MAAK,IAAI,yBAAyB,WAAW;AAAA,EACjD;AAAA,EACA,+BAA+B,CAAC,EAAE,MAAAA,OAAM,OAAO,MAAM;AACjD,IAAAA,MAAK,IAAI,sBAAsB,OAAO;AACtC,IAAAA,MAAK,IAAI,sBAAsB,WAAW,OAAO;AAAA,EACrD;AAAA,EACA,kCAAkC,CAAC,EAAE,MAAAA,OAAM,OAAO,MAAM;AACpD,IAAAA,MAAK,IAAI,yBAAyB,OAAO;AACzC,IAAAA,MAAK,IAAI,yBAAyB,WAAW,OAAO;AAAA,EACxD;AACJ,CAAC;AAED,IAAM,OAAO,WAAW;AAAA,EACpB,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,eAAe,CAAAA,UAAQ;AACnB,iBAAa,eAAe,EAAE,GAAGA,OAAM,MAAMA,MAAK,CAAC;AAAA,EACvD;AAAA,EACA,MAAM;AACV,CAAC;AAKD,IAAM,WAAW,CAAC,EAAE,MAAAA,OAAM,MAAM,MAAM;AAElC,EAAAA,MAAK,IAAI,WAAW,gBAAgB,QAAQ;AAC5C,EAAAA,MAAK,YAAYA,MAAK,IAAI,QAAQ;AAGlC,EAAAA,MAAK,IAAI,OAAOA,MAAK,gBAAgBA,MAAK,gBAAgB,MAAM,EAAE,IAAI,MAAM,GAAG,CAAC,CAAC;AAGjF,EAAAA,MAAK,IAAI,OAAO;AACpB;AAKA,IAAM,cAAc,CAAC,EAAE,MAAAA,OAAM,MAAM,MAAM;AAErC,OAAKA,MAAK,IAAI,UAAU,eAAeA,MAAK,MAAM,iBAAiB,MAAM,EAAE,CAAC,CAAC;AACjF;AAEA,IAAM,cAAc,WAAW;AAAA,EAC3B,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,OAAO,YAAY;AAAA,IACf,eAAe;AAAA,EACnB,CAAC;AAAA,EACD,eAAe,CAAAA,UAAQ;AACnB,iBAAa,eAAe,EAAE,GAAGA,OAAM,MAAMA,MAAK,CAAC;AAAA,EACvD;AAAA,EACA,KAAK;AAAA,EACL,MAAM;AACV,CAAC;AAED,IAAM,qBAAqB,EAAE,MAAM,UAAU,SAAS,KAAK,MAAM,EAAE;AAEnE,IAAM,WAAW,CAAC,EAAE,MAAAA,OAAM,MAAM,MAAM;AAClC;AAAA,IACI;AAAA,MACI,MAAM;AAAA,IACV;AAAA,IACA;AAAA,MACI,MAAM;AAAA,MACN,OAAO;AAAA,QACH,YAAY;AAAA,QACZ,QAAQ;AAAA,MACZ;AAAA,MACA,QAAQ;AAAA,QACJ,YAAY;AAAA,UACR,QAAQ;AAAA,QACZ;AAAA,QACA,QAAQ,CAAC,cAAc,QAAQ;AAAA,MACnC;AAAA,IACJ;AAAA,IACA;AAAA,MACI,MAAM;AAAA,MACN,OAAO;AAAA,QACH,YAAY;AAAA,MAChB;AAAA,MACA,QAAQ;AAAA,QACJ,YAAY;AAAA,UACR,YAAY;AAAA,QAChB;AAAA,QACA,QAAQ,CAAC,YAAY;AAAA,MACzB;AAAA,IACJ;AAAA,EACJ,EAAE,QAAQ,aAAW;AACjB,kBAAcA,OAAM,SAAS,MAAM,IAAI;AAAA,EAC3C,CAAC;AAED,EAAAA,MAAK,QAAQ,UAAU,IAAI,aAAa,MAAM,IAAI,EAAE;AAEpD,EAAAA,MAAK,IAAI,WAAW;AACxB;AAEA,IAAM,gBAAgB,CAACA,OAAM,SAAS,cAAc;AAChD,QAAM,kBAAkB,WAAW;AAAA,IAC/B,MAAM,SAAS,QAAQ,IAAI,cAAc,SAAS;AAAA,IAClD,QAAQ,QAAQ;AAAA,IAChB,kBAAkB;AAAA,EACtB,CAAC;AAED,QAAM,OAAOA,MAAK,gBAAgB,iBAAiB,QAAQ,KAAK;AAEhE,EAAAA,MAAK,IAAI,QAAQ,IAAI,IAAIA,MAAK,gBAAgB,IAAI;AACtD;AAEA,IAAM,UAAU,CAAC,EAAE,MAAAA,OAAM,MAAM,MAAM;AAEjC,MAAIA,MAAK,IAAI,aAAa,QAAQ,MAAM,aAAaA,MAAK,IAAI,UAAU;AACpE,IAAAA,MAAK,IAAI,WAAW,UAAU,MAAM,QAAQ,IAAI,MAAM,WAAW;AACjE,IAAAA,MAAK,QAAQ,QAAQ,WAAWA,MAAK,IAAI;AAAA,EAC7C;AAGA,MAAI,CAAC,MAAM,OAAQ;AAGnB,QAAM,UAAUA,MAAK,IAAI,IAAI,KAAK;AAClC,QAAM,aAAaA,MAAK,IAAI,OAAO,KAAK;AAGxC,QAAM,SAAS,KAAK,IAAI,QAAQ,SAAS,WAAW,QAAQ,MAAM,MAAM;AAGxE,EAAAA,MAAK,IAAI,OAAO,aAAa,QAAQ;AAIrC,EAAAA,MAAK,IAAI,OAAO,UAAU,SAAS,QAAQ,SAAS,WAAW,UAAU;AAGzE,EAAAA,MAAK,IAAI,OAAO,aAAa,SAAS,WAAW;AACrD;AAEA,IAAM,QAAQ,WAAW;AAAA,EACrB,MAAM;AAAA,EACN,MAAM,CAAC,EAAE,MAAAA,OAAM,MAAM,MAAO,MAAM,gBAAgBA,MAAK,IAAI,OAAO;AAAA,EAClE,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,QAAQ;AAAA,IACJ,MAAM,CAAC,UAAU,iBAAiB,UAAU;AAAA,EAChD;AACJ,CAAC;AAED,IAAM,mBAAmB,WAAS;AAC9B,QAAM,UAAU,MAAM,IAAI,CAAAE,UAAQA,MAAK,EAAE;AACzC,MAAI,YAAY;AAChB,SAAO;AAAA,IACH,UAAU,WAAS;AACf,kBAAY;AAAA,IAChB;AAAA,IACA,UAAU,MAAM;AAAA,IAChB,cAAc,CAAAA,UAAQ,QAAQ,QAAQA,MAAK,EAAE;AAAA,EACjD;AACJ;AAEA,IAAM,wBAAwB;AAAA,EAC1B,MAAM;AAAA,EACN,WAAW;AAAA,EACX,SAAS;AAAA,EACT,MAAM;AACV;AAEA,IAAM,oBAAoB;AAE1B,IAAM,WAAW;AAAA,EACb,qBAAqB;AAAA,EACrB,+BAA+B;AAAA,EAC/B,wBAAwB;AAAA,EACxB,2BAA2B;AAAA,EAC3B,eAAe;AAAA,EACf,6BAA6B;AAAA,EAC7B,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,iCAAiC;AAAA,EACjC,wCAAwC;AAAA,EACxC,2BAA2B;AAAA,EAC3B,4BAA4B;AAChC;AAKA,IAAM,WAAW,CAAC,EAAE,MAAAF,OAAM,MAAM,MAAM;AAElC,EAAAA,MAAK,IAAI,cAAc,OAAKA,MAAK,SAAS,qBAAqB,EAAE,IAAI,MAAM,GAAG,CAAC;AAG/E,EAAAA,MAAK,QAAQ,KAAK,kBAAkB,MAAM,EAAE;AAC5C,EAAAA,MAAK,QAAQ,iBAAiB,SAASA,MAAK,IAAI,WAAW;AAG3D,EAAAA,MAAK,IAAI,YAAYA,MAAK,gBAAgBA,MAAK,gBAAgB,aAAa,EAAE,IAAI,MAAM,GAAG,CAAC,CAAC;AAG7F,EAAAA,MAAK,IAAI,QAAQA,MAAK,gBAAgBA,MAAK,gBAAgB,OAAO,EAAE,MAAM,aAAa,CAAC,CAAC;AAGzF,EAAAA,MAAK,IAAI,MAAM,SAAS;AAGxB,QAAM,mBAAmB;AAGzB,MAAI,CAACA,MAAK,MAAM,mBAAmB,EAAG;AAGtC,EAAAA,MAAK,QAAQ,QAAQ,YAAY;AAEjC,QAAM,OAAO,OAAK;AACd,QAAI,CAAC,EAAE,UAAW;AAElB,QAAI,0BAA0B;AAE9B,UAAM,SAAS;AAAA,MACX,GAAG,EAAE;AAAA,MACL,GAAG,EAAE;AAAA,IACT;AAEA,UAAM,aAAa;AAAA,MACf,GAAGA,MAAK;AAAA,MACR,GAAGA,MAAK;AAAA,IACZ;AAEA,UAAM,aAAa;AAAA,MACf,GAAG,EAAE;AAAA,MACL,GAAG,EAAE;AAAA,IACT;AAEA,UAAM,YAAY,iBAAiBA,MAAK,MAAM,kBAAkB,CAAC;AAEjE,IAAAA,MAAK,SAAS,iBAAiB,EAAE,IAAI,MAAM,IAAI,UAAU,CAAC;AAE1D,UAAM,OAAO,CAAAwB,OAAK;AACd,UAAI,CAACA,GAAE,UAAW;AAElB,MAAAA,GAAE,gBAAgB;AAClB,MAAAA,GAAE,eAAe;AAEjB,YAAM,aAAa;AAAA,QACf,GAAGA,GAAE,QAAQ,OAAO;AAAA,QACpB,GAAGA,GAAE,QAAQ,OAAO;AAAA,MACxB;AAGA,YAAM,OACF,MAAM,WAAW,IAAI,MAAM,WAAW,IAAI,MAAM,WAAW,IAAI,MAAM,WAAW;AACpF,UAAI,OAAO,MAAM,CAAC,yBAAyB;AACvC,kCAA0B;AAC1B,QAAAxB,MAAK,QAAQ,oBAAoB,SAASA,MAAK,IAAI,WAAW;AAAA,MAClE;AAEA,MAAAA,MAAK,SAAS,iBAAiB,EAAE,IAAI,MAAM,IAAI,UAAU,CAAC;AAAA,IAC9D;AAEA,UAAMyB,QAAO,CAAAD,OAAK;AACd,UAAI,CAACA,GAAE,UAAW;AAElB,YAAM,aAAa;AAAA,QACf,GAAGA,GAAE,QAAQ,OAAO;AAAA,QACpB,GAAGA,GAAE,QAAQ,OAAO;AAAA,MACxB;AAEA,YAAM;AAAA,IACV;AAEA,UAAM,SAAS,MAAM;AACjB,YAAM;AAAA,IACV;AAEA,UAAM,QAAQ,MAAM;AAChB,eAAS,oBAAoB,iBAAiB,MAAM;AACpD,eAAS,oBAAoB,eAAe,IAAI;AAChD,eAAS,oBAAoB,aAAaC,KAAI;AAE9C,MAAAzB,MAAK,SAAS,iBAAiB,EAAE,IAAI,MAAM,IAAI,UAAU,CAAC;AAG1D,UAAI,yBAAyB;AACzB,mBAAW,MAAMA,MAAK,QAAQ,iBAAiB,SAASA,MAAK,IAAI,WAAW,GAAG,CAAC;AAAA,MACpF;AAAA,IACJ;AAEA,aAAS,iBAAiB,iBAAiB,MAAM;AACjD,aAAS,iBAAiB,eAAe,IAAI;AAC7C,aAAS,iBAAiB,aAAayB,KAAI;AAAA,EAC/C;AAEA,EAAAzB,MAAK,QAAQ,iBAAiB,eAAe,IAAI;AACrD;AAEA,IAAM,UAAU,YAAY;AAAA,EACxB,yBAAyB,CAAC,EAAE,MAAAA,OAAM,OAAO,MAAM;AAC3C,IAAAA,MAAK,SAAS,OAAO;AAAA,EACzB;AACJ,CAAC;AAED,IAAM,UAAU;AAAA,EACZ;AAAA,IACI,eAAe,CAAC,EAAE,MAAAA,OAAM,MAAM,MAAM;AAChC,YAAM,aAAa;AAAA,QACf,GAAGA,MAAK;AAAA,QACR,GAAGA,MAAK;AAAA,MACZ;AAAA,IACJ;AAAA,IACA,eAAe,CAAC,EAAE,MAAAA,MAAK,MAAM;AACzB,MAAAA,MAAK,QAAQ,QAAQ,YAAY;AAAA,IACrC;AAAA,IACA,eAAe,CAAC,EAAE,MAAAA,OAAM,MAAM,MAAM;AAChC,YAAM,aAAa;AACnB,YAAM,aAAa;AACnB,MAAAA,MAAK,QAAQ,QAAQ,YAAY;AAAA,IACrC;AAAA,EACJ;AAAA,EACA,CAAC,EAAE,MAAAA,OAAM,SAAAL,UAAS,OAAO,eAAe,MAAM;AAC1C,QAAIK,MAAK,QAAQ,QAAQ,cAAc,QAAQ;AAC3C,UAAIA,MAAK,UAAU,GAAG;AAClB,QAAAA,MAAK,QAAQ,QAAQ,YAAY;AAAA,MACrC;AAAA,IACJ;AAGA,QAAI,SAASL,SACR,OAAO,EACP,OAAO,CAAA4B,YAAU,QAAQ,KAAKA,QAAO,IAAI,CAAC,EAC1C,QAAQ,EACR,KAAK,CAAAA,YAAU,SAASA,QAAO,IAAI,CAAC;AAGzC,QAAI,UAAU,OAAO,SAAS,MAAM,cAAc;AAE9C,YAAM,eAAe,OAAO;AAG5B,MAAAvB,MAAK,QAAQ,QAAQ,oBAAoB,SAAS,MAAM,YAAY,KAAK;AAAA,IAC7E;AAGA,UAAM,cACFA,MAAK,MAAM,6BAA6B,KAAKA,MAAK,MAAM,wBAAwB;AACpF,QAAI,CAAC,aAAa;AACd,cAAQ,EAAE,MAAAA,OAAM,SAAAL,UAAS,MAAM,CAAC;AAChC,UAAI,CAACK,MAAK,UAAUA,MAAK,IAAI,UAAU,KAAK,QAAQ,SAAS,GAAG;AAC5D,QAAAA,MAAK,SAASA,MAAK,IAAI,UAAU,KAAK,QAAQ;AAAA,MAClD;AAAA,IACJ,WAAW,CAAC,gBAAgB;AACxB,MAAAA,MAAK,SAASA,MAAK,KAAK,QAAQ,QAAQ;AAAA,IAC5C;AAGA,QAAI,gBAAgB;AAChB,MAAAA,MAAK,IAAI,MAAM,SAAS;AAAA,IAC5B;AAEA,IAAAA,MAAK,IAAI,MAAM,SAASA,MAAK;AAAA,EACjC;AACJ;AAEA,IAAM,OAAO,WAAW;AAAA,EACpB,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,SAAS,CAAC,EAAE,MAAAA,OAAM,MAAM,MAAM;AAC1B,IAAAA,MAAK,QAAQ,oBAAoB,SAASA,MAAK,IAAI,WAAW;AAC9D,IAAAA,MAAK,SAAS,gBAAgB,EAAE,OAAO,MAAM,GAAG,CAAC;AAAA,EACrD;AAAA,EACA,KAAK;AAAA,EACL,MAAM;AAAA,EACN,QAAQ;AAAA,IACJ,MAAM;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,IACA,QAAQ,CAAC,cAAc,cAAc,UAAU,UAAU,WAAW,QAAQ;AAAA,IAC5E,YAAY;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,SAAS,EAAE,MAAM,SAAS,UAAU,IAAI;AAAA,IAC5C;AAAA,EACJ;AACJ,CAAC;AAED,IAAI,iBAAiB,CAAC,iBAAiB,cAAc;AAGjD,SAAO,KAAK,IAAI,GAAG,KAAK,OAAO,kBAAkB,KAAK,SAAS,CAAC;AACpE;AAEA,IAAM,yBAAyB,CAAC,MAAM,UAAU,mBAAmB;AAC/D,MAAI,CAAC,eAAgB;AAErB,QAAM,kBAAkB,KAAK,KAAK,QAAQ;AAE1C,QAAM,IAAI,SAAS;AACnB,MAAI,OAAO;AAGX,MAAI,MAAM,KAAK,eAAe,MAAM,SAAS,CAAC,EAAE,KAAK,QAAQ,IAAK,QAAO;AAGzE,QAAME,QAAO,SAAS,CAAC;AACvB,QAAM,WAAWA,MAAK,KAAK;AAC3B,QAAM,uBAAuB,SAAS,aAAa,SAAS;AAC5D,QAAM,YAAY,SAAS,QAAQ;AACnC,QAAM,cAAc,eAAe,iBAAiB,SAAS;AAG7D,MAAI,gBAAgB,GAAG;AACnB,aAAS,QAAQ,GAAG,QAAQ,GAAG,SAAS;AACpC,YAAM,QAAQ,SAAS,KAAK;AAC5B,YAAM,WAAW,MAAM,KAAK,MAAM,MAAM,MAAM,KAAK,QAAQ,SAAS;AACpE,UAAI,eAAe,MAAM,UAAU;AAC/B,eAAO;AAAA,MACX;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAGA,QAAM,qBAAqB,SAAS,YAAY,SAAS;AACzD,QAAM,aAAa,SAAS,SAAS;AACrC,WAAS,QAAQ,GAAG,QAAQ,GAAG,SAAS;AACpC,UAAM,SAAS,QAAQ;AACvB,UAAM,SAAS,KAAK,MAAM,QAAQ,WAAW;AAE7C,UAAM,UAAU,SAAS;AACzB,UAAM,UAAU,SAAS;AAEzB,UAAM,UAAU,UAAU,SAAS;AACnC,UAAM,YAAY,UAAU;AAC5B,UAAM,aAAa,UAAU,aAAa,SAAS;AAEnD,QAAI,eAAe,MAAM,cAAc,eAAe,MAAM,SAAS;AACjE,UAAI,eAAe,OAAO,WAAW;AACjC,eAAO;AAAA,MACX,WAAW,UAAU,IAAI,GAAG;AACxB,eAAO;AAAA,MACX,OAAO;AACH,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,EACJ;AAEA,MAAI,SAAS,MAAM;AACf,WAAO;AAAA,EACX;AAEA,SAAO;AACX;AAEA,IAAM,qBAAqB;AAAA,EACvB,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,IAAI,YAAY;AACZ,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,UAAU,KAAK;AACf,QAAI,KAAK,WAAW,KAAK,QAAQ,EAAG,MAAK,SAAS;AAAA,EACtD;AAAA,EACA,IAAI,WAAW;AACX,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,SAAS,KAAK;AACd,QAAI,KAAK,UAAU,KAAK,QAAQ,EAAG,MAAK,QAAQ;AAAA,EACpD;AAAA,EACA,eAAe,SAAS,QAAQ,OAAO;AACnC,QAAI,KAAK,WAAW,KAAK,WAAW,EAAG,MAAK,SAAS;AACrD,QAAI,KAAK,UAAU,KAAK,UAAU,EAAG,MAAK,QAAQ;AAAA,EACtD;AACJ;AAEA,IAAM,WAAW,CAAC,EAAE,MAAAF,MAAK,MAAM;AAE3B,OAAKA,MAAK,SAAS,QAAQ,MAAM;AAEjC,EAAAA,MAAK,IAAI,oBAAoB,KAAK,IAAI;AAC1C;AAOA,IAAM,cAAc,CAAC,EAAE,MAAAA,OAAM,OAAO,MAAM;AACtC,QAAM,EAAE,IAAI,OAAO,kBAAkB,IAAI;AAEzC,EAAAA,MAAK,IAAI,WAAW;AAEpB,QAAM,MAAM,KAAK,IAAI;AACrB,MAAI,YAAY;AAChB,MAAI,UAAU;AAEd,MAAI,sBAAsB,kBAAkB,MAAM;AAC9C,cAAU;AACV,UAAM,WAAWA,MAAK,MAAM,0BAA0B;AACtD,UAAM,OAAO,MAAMA,MAAK,IAAI;AAC5B,gBAAY,OAAO,WAAW,OAAO,WAAW,QAAQ;AAAA,EAC5D;AAEA,EAAAA,MAAK,IAAI,oBAAoB;AAE7B,EAAAA,MAAK;AAAA,IACDA,MAAK;AAAA;AAAA,MAED;AAAA;AAAA,MAGA;AAAA,QACI;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAAA,IACA;AAAA,EACJ;AACJ;AAEA,IAAM,WAAW,CAACE,OAAM,GAAG,GAAG,KAAK,GAAG,KAAK,MAAM;AAE7C,MAAIA,MAAK,YAAY;AACjB,IAAAA,MAAK,aAAa;AAClB,IAAAA,MAAK,aAAa;AAClB,IAAAA,MAAK,aAAaA,MAAK,WAAW,IAAIA,MAAK,WAAW;AACtD,IAAAA,MAAK,aAAaA,MAAK,WAAW,IAAIA,MAAK,WAAW;AACtD,IAAAA,MAAK,SAAS;AACd,IAAAA,MAAK,SAAS;AAAA,EAClB,OAAO;AACH,IAAAA,MAAK,aAAa;AAClB,IAAAA,MAAK,aAAa;AAElB,QAAI,KAAK,IAAI,IAAIA,MAAK,WAAW;AAE7B,UAAIA,MAAK,YAAY,GAAG;AACpB,sBAAcA,OAAM,GAAG,GAAG,IAAI,EAAE;AAAA,MACpC;AAGA,MAAAA,MAAK,SAAS;AACd,MAAAA,MAAK,SAAS;AACd,MAAAA,MAAK,UAAU;AAAA,IACnB;AAAA,EACJ;AACJ;AAEA,IAAM,gBAAgB,CAACA,OAAM,GAAG,GAAG,IAAI,OAAO;AAC1C,MAAIA,MAAK,sBAAsB,kBAAkB,MAAM;AACnD,IAAAA,MAAK,aAAa;AAClB,IAAAA,MAAK,aAAa;AAClB,IAAAA,MAAK,aAAa;AAClB,IAAAA,MAAK,aAAa;AAAA,EACtB,WAAWA,MAAK,sBAAsB,kBAAkB,MAAM;AAC1D,IAAAA,MAAK,aAAa;AAClB,IAAAA,MAAK,aAAa,IAAI,KAAK;AAE3B,IAAAA,MAAK,aAAa;AAClB,IAAAA,MAAK,aAAa,IAAI,KAAK;AAE3B,IAAAA,MAAK,SAAS;AACd,IAAAA,MAAK,SAAS;AAAA,EAClB,WAAWA,MAAK,sBAAsB,kBAAkB,QAAQ;AAC5D,IAAAA,MAAK,aAAa;AAClB,IAAAA,MAAK,aAAa,IAAI;AAAA,EAC1B,WAAWA,MAAK,sBAAsB,kBAAkB,KAAK;AACzD,IAAAA,MAAK,aAAa;AAClB,IAAAA,MAAK,aAAa,IAAI;AACtB,IAAAA,MAAK,aAAa;AAAA,EACtB;AACJ;AAOA,IAAM,iBAAiB,CAAC,EAAE,MAAAF,OAAM,OAAO,MAAM;AACzC,QAAM,EAAE,GAAG,IAAI;AAGf,QAAM,OAAOA,MAAK,WAAW,KAAK,WAAS,MAAM,OAAO,EAAE;AAG1D,MAAI,CAAC,MAAM;AACP;AAAA,EACJ;AAGA,OAAK,SAAS;AACd,OAAK,SAAS;AACd,OAAK,UAAU;AAGf,OAAK,mBAAmB;AAC5B;AAEA,IAAM,gBAAgB,WAClB,MAAM,KAAK,QAAQ,SACnB,MAAM,KAAK,QAAQ,eAAe,MAClC,MAAM,KAAK,QAAQ,YAAY;AACnC,IAAM,eAAe,WACjB,MAAM,KAAK,QAAQ,QACnB,MAAM,KAAK,QAAQ,aAAa,MAChC,MAAM,KAAK,QAAQ,cAAc;AAErC,IAAM,WAAW,CAAC,EAAE,MAAAA,OAAM,OAAO,MAAM;AACnC,QAAM,EAAE,IAAI,UAAU,IAAI;AAG1B,QAAME,QAAOF,MAAK,MAAM,YAAY,EAAE,GAAG,CAAC;AAG1C,QAAM,OAAOA,MAAK,WAAW,KAAK,WAAS,MAAM,OAAO,EAAE;AAE1D,QAAM,WAAWA,MAAK,WAAW;AACjC,QAAM,WAAW,UAAU,aAAaE,KAAI;AAG5C,MAAI,CAAC,KAAM;AAEX,QAAM,eAAe;AAAA,IACjB,GAAG,KAAK,WAAW,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW;AAAA,IAC3D,GAAG,KAAK,WAAW,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW;AAAA,EAC/D;AAGA,QAAM,aAAa,cAAc,IAAI;AACrC,QAAM,YAAY,aAAa,IAAI;AAGnC,MAAI,OAAO,KAAK,MAAMF,MAAK,KAAK,MAAM,QAAQ,SAAS;AACvD,MAAI,OAAO,SAAU,QAAO;AAG5B,QAAM,OAAO,KAAK,MAAM,WAAW,OAAO,CAAC;AAE3C,qBAAmB,YAAY,aAAa;AAC5C,qBAAmB,WAAW,YAAY;AAG1C,MAAI0B,YAAW;AAAA,IACX,GAAG,KAAK,MAAM,aAAa,IAAI,UAAU;AAAA,IACzC,GAAG,KAAK,MAAM,aAAa,IAAI,SAAS;AAAA,IACxC,cAAc,SAAS,eAAe;AAClC,UACI,aAAa,IAAI,mBAAmB,aACpC,aAAa,IAAI,KACjB,aAAa,IAAI,mBAAmB,YACpC,aAAa,IAAI;AAEjB,eAAO;AACX,aAAO,KAAK,IAAI,OAAO,KAAK;AAAA,IAChC;AAAA,IACA,aAAa,SAAS,cAAc;AAChC,YAAM,QAAQ1B,MAAK,MAAM,kBAAkB;AAC3C,YAAM,kBAAkBA,MAAK,WAAW,OAAO,WAAS,MAAM,KAAK,QAAQ,MAAM;AACjF,YAAM,WAAW,MAAM;AAAA,QAAI,CAAAE,UACvB,gBAAgB,KAAK,eAAa,UAAU,OAAOA,MAAK,EAAE;AAAA,MAC9D;AACA,YAAMyB,gBAAe,SAAS,UAAU,WAAS,UAAU,IAAI;AAC/D,YAAMC,cAAa,cAAc,IAAI;AACrC,YAAM,IAAI,SAAS;AACnB,UAAI,MAAM;AACV,UAAI,cAAc;AAClB,UAAI,cAAc;AAClB,UAAI,WAAW;AACf,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,sBAAc,cAAc,SAAS,CAAC,CAAC;AACvC,mBAAW;AACX,sBAAc,WAAW;AACzB,YAAI,aAAa,IAAI,aAAa;AAC9B,cAAID,gBAAe,GAAG;AAClB,gBAAI,aAAa,IAAI,WAAWC,aAAY;AACxC,oBAAM;AACN;AAAA,YACJ;AACA;AAAA,UACJ;AACA,gBAAM;AACN;AAAA,QACJ;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAGA,QAAM,QAAQ,OAAO,IAAIF,UAAS,aAAa,IAAIA,UAAS,YAAY;AACxE,EAAA1B,MAAK,SAAS,aAAa,EAAE,OAAO,MAAM,MAAM,CAAC;AAGjD,QAAM,eAAe,UAAU,SAAS;AAExC,MAAI,iBAAiB,UAAa,iBAAiB,OAAO;AACtD,cAAU,SAAS,KAAK;AAExB,QAAI,iBAAiB,OAAW;AAEhC,IAAAA,MAAK,SAAS,qBAAqB;AAAA,MAC/B,OAAOA,MAAK,MAAM,kBAAkB;AAAA,MACpC,QAAQ;AAAA,MACR,QAAQ;AAAA,IACZ,CAAC;AAAA,EACL;AACJ;AAKA,IAAM,UAAU,YAAY;AAAA,EACxB,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,eAAe;AACnB,CAAC;AAQD,IAAM,UAAU,CAAC,EAAE,MAAAA,OAAM,OAAO,SAAAL,UAAS,eAAe,MAAM;AAE1D,UAAQ,EAAE,MAAAK,OAAM,OAAO,SAAAL,SAAQ,CAAC;AAEhC,QAAM,EAAE,gBAAgB,IAAI;AAG5B,QAAM,kBAAkBK,MAAK,KAAK,QAAQ;AAG1C,QAAM,kBAAkBA,MAAK,WAAW,OAAO,WAAS,MAAM,KAAK,QAAQ,MAAM;AAGjF,QAAM,WAAWA,MACZ,MAAM,kBAAkB,EACxB,IAAI,CAAAE,UAAQ,gBAAgB,KAAK,WAAS,MAAM,OAAOA,MAAK,EAAE,CAAC,EAC/D,OAAO,CAAAA,UAAQA,KAAI;AAGxB,QAAM,YAAY,kBACZ,uBAAuBF,OAAM,UAAU,eAAe,IACtD;AAGN,QAAM,WAAWA,MAAK,IAAI,YAAY;AAGtC,EAAAA,MAAK,IAAI,WAAW;AAEpB,MAAI,kBAAkB;AACtB,MAAI,oBAAoB;AACxB,MAAI,iBAAiB;AAErB,MAAI,SAAS,WAAW,EAAG;AAE3B,QAAM,YAAY,SAAS,CAAC,EAAE,KAAK;AACnC,QAAM,qBAAqB,UAAU,YAAY,UAAU;AAC3D,QAAM,uBAAuB,UAAU,aAAa,UAAU;AAC9D,QAAM,YAAY,UAAU,QAAQ;AACpC,QAAM,aAAa,UAAU,SAAS;AACtC,QAAM,cAAc,eAAe,iBAAiB,SAAS;AAG7D,MAAI,gBAAgB,GAAG;AACnB,QAAI,UAAU;AACd,QAAI,aAAa;AAEjB,aAAS,QAAQ,CAAC,OAAO,UAAU;AAC/B,UAAI,WAAW;AACX,YAAI,OAAO,QAAQ;AACnB,YAAI,SAAS,IAAI;AACb,uBAAa,CAAC,qBAAqB;AAAA,QACvC,WAAW,SAAS,IAAI;AACpB,uBAAa,CAAC,qBAAqB;AAAA,QACvC,WAAW,SAAS,GAAG;AACnB,uBAAa,qBAAqB;AAAA,QACtC,WAAW,SAAS,GAAG;AACnB,uBAAa,qBAAqB;AAAA,QACtC,OAAO;AACH,uBAAa;AAAA,QACjB;AAAA,MACJ;AAEA,UAAI,gBAAgB;AAChB,cAAM,aAAa;AACnB,cAAM,aAAa;AAAA,MACvB;AAEA,UAAI,CAAC,MAAM,kBAAkB;AACzB,iBAAS,OAAO,GAAG,UAAU,UAAU;AAAA,MAC3C;AAEA,UAAI6B,cAAa,MAAM,KAAK,QAAQ,SAAS;AAE7C,UAAI,eAAeA,eAAc,MAAM,mBAAmB,MAAM,UAAU;AAE1E,iBAAW;AAAA,IACf,CAAC;AAAA,EACL,OAEK;AACD,QAAI,QAAQ;AACZ,QAAI,QAAQ;AAEZ,aAAS,QAAQ,CAAC,OAAO,UAAU;AAC/B,UAAI,UAAU,WAAW;AACrB,0BAAkB;AAAA,MACtB;AAEA,UAAI,UAAU,UAAU;AACpB,0BAAkB;AAAA,MACtB;AAEA,UAAI,MAAM,oBAAoB,MAAM,UAAU,KAAK;AAC/C,6BAAqB;AAAA,MACzB;AAEA,YAAM,cAAc,QAAQ,iBAAiB,kBAAkB;AAE/D,YAAM,SAAS,cAAc;AAC7B,YAAM,SAAS,KAAK,MAAM,cAAc,WAAW;AAEnD,YAAM,UAAU,SAAS;AACzB,YAAM,UAAU,SAAS;AAEzB,YAAM,UAAU,KAAK,KAAK,UAAU,KAAK;AACzC,YAAM,UAAU,KAAK,KAAK,UAAU,KAAK;AAEzC,cAAQ;AACR,cAAQ;AAER,UAAI,MAAM,iBAAkB;AAE5B,UAAI,gBAAgB;AAChB,cAAM,aAAa;AACnB,cAAM,aAAa;AAAA,MACvB;AAEA,eAAS,OAAO,SAAS,SAAS,SAAS,OAAO;AAAA,IACtD,CAAC;AAAA,EACL;AACJ;AAOA,IAAM,uBAAuB,CAAC,OAAOlC,aACjCA,SAAQ,OAAO,YAAU;AAErB,MAAI,OAAO,QAAQ,OAAO,KAAK,IAAI;AAC/B,WAAO,MAAM,OAAO,OAAO,KAAK;AAAA,EACpC;AAGA,SAAO;AACX,CAAC;AAEL,IAAM,OAAO,WAAW;AAAA,EACpB,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,KAAK;AAAA,EACL,MAAM;AAAA,EACN,cAAc,CAAC,EAAE,MAAAK,MAAK,MAAM;AACxB,IAAAA,MAAK,WACA,OAAO,UAAQ,KAAK,oBAAoB,KAAK,YAAY,KAAK,KAAK,OAAO,EAC1E,QAAQ,UAAQ;AACb,WAAK,SAAS;AACd,MAAAA,MAAK,gBAAgB,IAAI;AAAA,IAC7B,CAAC;AAAA,EACT;AAAA,EACA,4BAA4B;AAAA,EAC5B,QAAQ;AAAA,IACJ,MAAM,CAAC,iBAAiB;AAAA,EAC5B;AACJ,CAAC;AAED,IAAM,WAAW,CAAC,EAAE,MAAAA,OAAM,MAAM,MAAM;AAClC,EAAAA,MAAK,IAAI,OAAOA,MAAK,gBAAgBA,MAAK,gBAAgB,IAAI,CAAC;AAC/D,QAAM,kBAAkB;AACxB,QAAM,cAAc;AACxB;AAEA,IAAM,uBAAuB,CAAC,EAAE,MAAAA,OAAM,OAAO,OAAO,MAAM;AACtD,MAAI,CAACA,MAAK,MAAM,kCAAkC,EAAG;AACrD,QAAM,kBAAkB;AAAA,IACpB,MAAM,OAAO,SAAS,YAAYA,MAAK,IAAI,KAAK,KAAK,QAAQ;AAAA,IAC7D,KACI,OAAO,SAAS,YACfA,MAAK,KAAK,MAAM,MAAMA,MAAK,KAAK,QAAQ,YAAYA,MAAK,KAAK,QAAQ;AAAA,EAC/E;AACJ;AAEA,IAAM,uBAAuB,CAAC,EAAE,MAAM,MAAM;AACxC,QAAM,kBAAkB;AAC5B;AAEA,IAAM,UAAU,YAAY;AAAA,EACxB,UAAU;AAAA,EACV,cAAc;AAClB,CAAC;AAED,IAAM,UAAU,CAAC,EAAE,MAAAA,OAAM,OAAO,SAAAL,SAAQ,MAAM;AAE1C,UAAQ,EAAE,MAAAK,OAAM,OAAO,SAAAL,SAAQ,CAAC;AAGhC,EAAAK,MAAK,IAAI,KAAK,kBAAkB,MAAM;AAGtC,MAAI,MAAM,eAAe,CAAC,MAAM,UAAU;AACtC,UAAM,cAAc;AAGpB,IAAAA,MAAK,QAAQ,QAAQ,QAAQ;AAC7B,IAAAA,MAAK,SAAS;AAAA,EAClB;AAGA,MAAI,MAAM,UAAU;AAChB,UAAM,YAAY,KAAK,MAAM,MAAM,QAAQ;AAC3C,QAAI,cAAcA,MAAK,QAAQ;AAC3B,YAAM,cAAc;AACpB,MAAAA,MAAK,QAAQ,QAAQ,QAAQ;AAC7B,MAAAA,MAAK,SAAS;AAAA,IAClB;AAAA,EACJ;AACJ;AAEA,IAAM,eAAe,WAAW;AAAA,EAC5B,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ;AAAA,IACJ,MAAM,CAAC,YAAY,iBAAiB;AAAA,IACpC,QAAQ,CAAC,UAAU,YAAY;AAAA,IAC/B,YAAY;AAAA,MACR,YAAY;AAAA,IAChB;AAAA,EACJ;AACJ,CAAC;AAED,IAAM,aAAa,CAAC,SAASb,OAAMS,QAAO,eAAe,OAAO;AAC5D,MAAIA,QAAO;AACP,SAAK,SAAST,OAAM,YAAY;AAAA,EACpC,OAAO;AACH,YAAQ,gBAAgBA,KAAI;AAAA,EAChC;AACJ;AAEA,IAAM,iBAAiB,WAAS;AAE5B,MAAI,CAAC,SAAS,MAAM,UAAU,IAAI;AAC9B;AAAA,EACJ;AAEA,MAAI;AAEA,UAAM,QAAQ;AAAA,EAClB,SAAS,KAAK;AAAA,EAAC;AAGf,MAAI,MAAM,OAAO;AAEb,UAAM,OAAO,gBAAgB,MAAM;AACnC,UAAM,aAAa,MAAM;AACzB,UAAM,MAAM,MAAM;AAClB,SAAK,YAAY,KAAK;AACtB,SAAK,MAAM;AAGX,QAAI,KAAK;AACL,iBAAW,aAAa,OAAO,GAAG;AAAA,IACtC,OAAO;AACH,iBAAW,YAAY,KAAK;AAAA,IAChC;AAAA,EACJ;AACJ;AAEA,IAAM,WAAW,CAAC,EAAE,MAAAa,OAAM,MAAM,MAAM;AAElC,EAAAA,MAAK,QAAQ,KAAK,qBAAqB,MAAM,EAAE;AAG/C,OAAKA,MAAK,SAAS,QAAQA,MAAK,MAAM,UAAU,CAAC;AAGjD,OAAKA,MAAK,SAAS,iBAAiB,uBAAuB,MAAM,EAAE,EAAE;AAGrE,OAAKA,MAAK,SAAS,mBAAmB,wBAAwB,MAAM,EAAE,EAAE;AAGxE,uBAAqB,EAAE,MAAAA,OAAM,QAAQ,EAAE,OAAOA,MAAK,MAAM,yBAAyB,EAAE,EAAE,CAAC;AACvF,sBAAoB,EAAE,MAAAA,OAAM,QAAQ,EAAE,OAAOA,MAAK,MAAM,oBAAoB,EAAE,EAAE,CAAC;AACjF,wBAAsB,EAAE,MAAAA,OAAM,QAAQ,EAAE,OAAOA,MAAK,MAAM,4BAA4B,EAAE,EAAE,CAAC;AAC3F,iBAAe,EAAE,MAAAA,MAAK,CAAC;AACvB,iBAAe,EAAE,MAAAA,OAAM,QAAQ,EAAE,OAAOA,MAAK,MAAM,cAAc,EAAE,EAAE,CAAC;AACtE,mBAAiB,EAAE,MAAAA,OAAM,QAAQ,EAAE,OAAOA,MAAK,MAAM,oBAAoB,EAAE,EAAE,CAAC;AAG9E,EAAAA,MAAK,IAAI,eAAe,OAAK;AACzB,QAAI,CAACA,MAAK,QAAQ,OAAO;AACrB;AAAA,IACJ;AAGA,UAAM,QAAQ,MAAM,KAAKA,MAAK,QAAQ,KAAK,EAAE,IAAI,CAAAO,UAAQ;AACrD,MAAAA,MAAK,gBAAgBA,MAAK;AAC1B,aAAOA;AAAA,IACX,CAAC;AAGD,eAAW,MAAM;AAEb,YAAM,OAAO,KAAK;AAGlB,qBAAeP,MAAK,OAAO;AAAA,IAC/B,GAAG,GAAG;AAAA,EACV;AAEA,EAAAA,MAAK,QAAQ,iBAAiB,UAAUA,MAAK,IAAI,YAAY;AACjE;AAEA,IAAM,uBAAuB,CAAC,EAAE,MAAAA,OAAM,OAAO,MAAM;AAC/C,MAAI,CAACA,MAAK,MAAM,iCAAiC,EAAG;AACpD,aAAWA,MAAK,SAAS,UAAU,CAAC,CAAC,OAAO,OAAO,OAAO,QAAQ,OAAO,MAAM,KAAK,GAAG,IAAI,EAAE;AACjG;AAEA,IAAM,sBAAsB,CAAC,EAAE,MAAAA,OAAM,OAAO,MAAM;AAC9C,aAAWA,MAAK,SAAS,YAAY,OAAO,KAAK;AACrD;AAEA,IAAM,wBAAwB,CAAC,EAAE,MAAAA,OAAM,OAAO,MAAM;AAChD,aAAWA,MAAK,SAAS,mBAAmB,OAAO,KAAK;AAC5D;AAEA,IAAM,iBAAiB,CAAC,EAAE,MAAAA,MAAK,MAAM;AACjC,QAAM,aAAaA,MAAK,MAAM,cAAc;AAC5C,QAAM,kBAAkBA,MAAK,MAAM,kBAAkB;AACrD,QAAM,eAAe,cAAc,CAAC;AACpC,aAAWA,MAAK,SAAS,YAAY,YAAY;AACrD;AAEA,IAAM,iBAAiB,CAAC,EAAE,MAAAA,OAAM,OAAO,MAAM;AAEzC,MAAI,CAAC,OAAO,OAAO;AACf,eAAWA,MAAK,SAAS,YAAY,KAAK;AAAA,EAC9C,WAESA,MAAK,MAAM,iBAAiB,MAAM,GAAG;AAC1C,eAAWA,MAAK,SAAS,YAAY,IAAI;AAAA,EAC7C;AACJ;AAEA,IAAM,mBAAmB,CAAC,EAAE,MAAAA,OAAM,OAAO,MAAM;AAC3C,aAAWA,MAAK,SAAS,WAAW,CAAC,CAAC,OAAO,OAAO,OAAO,UAAU,OAAO,KAAK,OAAO,KAAK;AACjG;AAEA,IAAM,uBAAuB,CAAC,EAAE,MAAAA,MAAK,MAAM;AACvC,QAAM,EAAE,QAAQ,IAAIA;AAEpB,MAAIA,MAAK,MAAM,iBAAiB,IAAI,GAAG;AACnC,eAAW,SAAS,YAAY,KAAK;AACrC,eAAW,SAAS,QAAQ,KAAK;AAGjC,UAAM,cAAcA,MAAK,MAAM,kBAAkB;AACjD,QAAI,kBAAkB;AACtB,aAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AACzC,UAAI,YAAY,CAAC,EAAE,WAAW,WAAW,YAAY;AACjD,0BAAkB;AAAA,MACtB;AAAA,IACJ;AAEA,IAAAA,MAAK,QAAQ;AAAA,MACT,kBAAkBA,MAAK,MAAM,yBAAyB,IAAI;AAAA,IAC9D;AAAA,EACJ,OAAO;AAEH,eAAW,SAAS,QAAQ,MAAMA,MAAK,MAAM,UAAU,CAAC;AAGxD,UAAM,sBAAsBA,MAAK,MAAM,oBAAoB;AAC3D,QAAI,qBAAqB;AACrB,cAAQ,kBAAkB,EAAE;AAAA,IAChC;AAGA,QAAIA,MAAK,MAAM,cAAc,GAAG;AAC5B,iBAAW,SAAS,YAAY,IAAI;AAAA,IACxC;AAAA,EACJ;AACJ;AAEA,IAAM,4BAA4B,CAAC,EAAE,MAAAA,MAAK,MAAM;AAC5C,QAAM,sBAAsBA,MAAK,MAAM,oBAAoB;AAC3D,MAAI,CAAC,oBAAqB;AAC1B,EAAAA,MAAK,QAAQ,kBAAkBA,MAAK,MAAM,yBAAyB,CAAC;AACxE;AAEA,IAAM,UAAU,WAAW;AAAA,EACvB,KAAK;AAAA,EACL,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,YAAY;AAAA,IACR,MAAM;AAAA,EACV;AAAA,EACA,QAAQ;AAAA,EACR,SAAS,CAAC,EAAE,MAAAA,MAAK,MAAM;AACnB,IAAAA,MAAK,QAAQ,oBAAoB,UAAUA,MAAK,IAAI,YAAY;AAAA,EACpE;AAAA,EACA,OAAO,YAAY;AAAA,IACf,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,wBAAwB;AAAA,IAExB,kBAAkB;AAAA,IAClB,sBAAsB;AAAA,IACtB,gCAAgC;AAAA,IAChC,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,wBAAwB;AAAA,IACxB,kBAAkB;AAAA,EACtB,CAAC;AACL,CAAC;AAED,IAAM,MAAM;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AACX;AAEA,IAAM,WAAW,CAAC,EAAE,MAAAA,OAAM,MAAM,MAAM;AAElC,QAAM,QAAQ,gBAAgB,OAAO;AACrC,OAAK,OAAO,OAAO,qBAAqB,MAAM,EAAE,EAAE;AAGlD,OAAK,OAAO,MAAM,wBAAwB,MAAM,EAAE,EAAE;AAGpD,EAAAA,MAAK,IAAI,gBAAgB,OAAK;AAC1B,UAAM,kBAAkB,EAAE,YAAY,IAAI,SAAS,EAAE,YAAY,IAAI;AACrE,QAAI,CAAC,gBAAiB;AAEtB,MAAE,eAAe;AAGjB,IAAAA,MAAK,IAAI,MAAM,MAAM;AAAA,EACzB;AAEA,EAAAA,MAAK,IAAI,cAAc,OAAK;AACxB,UAAM,eAAe,EAAE,WAAW,SAAS,MAAM,SAAS,EAAE,MAAM;AAGlE,QAAI,aAAc;AAGlB,IAAAA,MAAK,IAAI,MAAM,MAAM;AAAA,EACzB;AAGA,QAAM,iBAAiB,WAAWA,MAAK,IAAI,aAAa;AACxD,EAAAA,MAAK,QAAQ,iBAAiB,SAASA,MAAK,IAAI,WAAW;AAG3D,mBAAiB,OAAO,MAAM,OAAO;AAGrC,EAAAA,MAAK,YAAY,KAAK;AACtB,EAAAA,MAAK,IAAI,QAAQ;AACrB;AAEA,IAAM,mBAAmB,CAAC,OAAO,UAAU;AACvC,QAAM,YAAY;AAClB,QAAM,YAAY,MAAM,cAAc,yBAAyB;AAC/D,MAAI,WAAW;AACX,SAAK,WAAW,YAAY,GAAG;AAAA,EACnC;AACA,SAAO;AACX;AAEA,IAAM,YAAY,WAAW;AAAA,EACzB,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,SAAS,CAAC,EAAE,MAAAA,MAAK,MAAM;AACnB,IAAAA,MAAK,IAAI,MAAM,iBAAiB,WAAWA,MAAK,IAAI,aAAa;AACjE,IAAAA,MAAK,QAAQ,oBAAoB,SAASA,MAAK,IAAI,WAAW;AAAA,EAClE;AAAA,EACA,OAAO,YAAY;AAAA,IACf,oBAAoB,CAAC,EAAE,MAAAA,OAAM,OAAO,MAAM;AACtC,uBAAiBA,MAAK,IAAI,OAAO,OAAO,KAAK;AAAA,IACjD;AAAA,EACJ,CAAC;AAAA,EACD,QAAQ;AAAA,IACJ,QAAQ,CAAC,WAAW,cAAc,YAAY;AAAA,IAC9C,YAAY;AAAA,MACR,SAAS,EAAE,MAAM,SAAS,UAAU,IAAI;AAAA,MACxC,YAAY;AAAA,MACZ,YAAY;AAAA,IAChB;AAAA,EACJ;AACJ,CAAC;AAED,IAAM,OAAO,WAAW;AAAA,EACpB,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,QAAQ;AAAA,IACJ,QAAQ,CAAC,cAAc,cAAc,UAAU,UAAU,SAAS;AAAA,IAClE,YAAY;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,SAAS,EAAE,MAAM,SAAS,UAAU,IAAI;AAAA,IAC5C;AAAA,EACJ;AACJ,CAAC;AAED,IAAM,UAAU,CAAC,EAAE,MAAAA,MAAK,MAAM;AAC1B,QAAM,UAAUA,MAAK,KAAK,QAAQ,QAAQ;AAC1C,QAAM,UAAUA,MAAK,KAAK,QAAQ,SAAS;AAE3C,EAAAA,MAAK,IAAI,OAAOA,MAAK;AAAA,IACjBA,MAAK,gBAAgB,MAAM;AAAA,MACvB,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,IAChB,CAAC;AAAA,EACL;AACJ;AAEA,IAAM,WAAW,CAAC,EAAE,MAAAA,OAAM,OAAO,MAAM;AACnC,MAAI,CAACA,MAAK,IAAI,MAAM;AAChB,YAAQ,EAAE,MAAAA,MAAK,CAAC;AAChB;AAAA,EACJ;AAEA,EAAAA,MAAK,IAAI,KAAK,aAAa,OAAO,SAAS;AAC3C,EAAAA,MAAK,IAAI,KAAK,aAAa,OAAO,SAAS;AAC3C,EAAAA,MAAK,IAAI,KAAK,SAAS;AACvB,EAAAA,MAAK,IAAI,KAAK,SAAS;AACvB,EAAAA,MAAK,IAAI,KAAK,UAAU;AAC5B;AAEA,IAAM,WAAW,CAAC,EAAE,MAAAA,MAAK,MAAM;AAC3B,MAAI,CAACA,MAAK,IAAI,MAAM;AAChB;AAAA,EACJ;AACA,EAAAA,MAAK,IAAI,KAAK,UAAU;AAC5B;AAEA,IAAM,cAAc,CAAC,EAAE,MAAAA,MAAK,MAAM;AAC9B,MAAI,CAACA,MAAK,IAAI,MAAM;AAChB;AAAA,EACJ;AACA,EAAAA,MAAK,IAAI,KAAK,SAAS;AACvB,EAAAA,MAAK,IAAI,KAAK,SAAS;AACvB,EAAAA,MAAK,IAAI,KAAK,UAAU;AAC5B;AAEA,IAAM,UAAU,CAAC,EAAE,MAAAA,OAAM,OAAO,SAAAL,SAAQ,MAAM;AAC1C,UAAQ,EAAE,MAAAK,OAAM,OAAO,SAAAL,SAAQ,CAAC;AAEhC,QAAM,EAAE,MAAAW,MAAK,IAAIN,MAAK;AAEtB,MAAIL,SAAQ,WAAW,KAAKW,SAAQA,MAAK,YAAY,GAAG;AACpD,IAAAN,MAAK,gBAAgBM,KAAI;AACzB,IAAAN,MAAK,IAAI,OAAO;AAAA,EACpB;AACJ;AAEA,IAAM,UAAU,YAAY;AAAA,EACxB,UAAU;AAAA,EACV,UAAU;AAAA,EACV,cAAc;AAClB,CAAC;AAED,IAAM,OAAO,WAAW;AAAA,EACpB,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,MAAM;AAAA,EACN,OAAO;AACX,CAAC;AAED,IAAM,gBAAgB,CAAC,SAAS,UAAU;AACtC,MAAI;AAEA,UAAM,eAAe,IAAI,aAAa;AACtC,UAAM,QAAQ,CAAAO,UAAQ;AAClB,UAAIA,iBAAgB,MAAM;AACtB,qBAAa,MAAM,IAAIA,KAAI;AAAA,MAC/B,OAAO;AACH,qBAAa,MAAM;AAAA,UACf,IAAI,KAAK,CAACA,KAAI,GAAGA,MAAK,MAAM;AAAA,YACxB,MAAMA,MAAK;AAAA,UACf,CAAC;AAAA,QACL;AAAA,MACJ;AAAA,IACJ,CAAC;AAGD,YAAQ,QAAQ,aAAa;AAAA,EACjC,SAAS,KAAK;AACV,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAEA,IAAM,WAAW,CAAC,EAAE,MAAAP,MAAK,MAAM;AAC3B,EAAAA,MAAK,IAAI,SAAS,CAAC;AACnB,QAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,SAAO,cAAc;AACrB,EAAAA,MAAK,QAAQ,YAAY,MAAM;AACnC;AAEA,IAAM,WAAW,CAACA,OAAM,OAAOA,MAAK,IAAI,OAAO,EAAE;AAEjD,IAAM,8BAA8B,CAAAA,UAAQ;AACxC,EAAAA,MAAK,MAAM,kBAAkB,EAAE,QAAQ,CAAAE,UAAQ;AAC3C,QAAI,CAACF,MAAK,IAAI,OAAOE,MAAK,EAAE,EAAG;AAC/B,IAAAF,MAAK,QAAQ,YAAYA,MAAK,IAAI,OAAOE,MAAK,EAAE,CAAC;AAAA,EACrD,CAAC;AACL;AAEA,IAAM,kBAAkB,CAAC,EAAE,MAAAF,MAAK,MAAM,4BAA4BA,KAAI;AAEtE,IAAM,aAAa,CAAC,EAAE,MAAAA,OAAM,OAAO,MAAM;AACrC,QAAM,WAAWA,MAAK,MAAM,YAAY,OAAO,EAAE;AACjD,QAAM,cAAc,SAAS,WAAW,WAAW;AACnD,QAAM,qBAAqB,CAAC,eAAeA,MAAK,MAAM,0BAA0B;AAChF,QAAM,gBAAgB,gBAAgB,OAAO;AAC7C,gBAAc,OAAO,qBAAqB,SAAS;AACnD,gBAAc,OAAOA,MAAK,MAAM,UAAU;AAC1C,EAAAA,MAAK,IAAI,OAAO,OAAO,EAAE,IAAI;AAC7B,8BAA4BA,KAAI;AACpC;AAEA,IAAM,gBAAgB,CAAC,EAAE,MAAAA,OAAM,OAAO,MAAM;AACxC,QAAM,QAAQ,SAASA,OAAM,OAAO,EAAE;AACtC,MAAI,CAAC,MAAO;AAGZ,MAAI,OAAO,wBAAwB,KAAM,OAAM,QAAQ,OAAO;AAG9D,MAAI,CAACA,MAAK,MAAM,0BAA0B,EAAG;AAE7C,QAAM,WAAWA,MAAK,MAAM,YAAY,OAAO,EAAE;AACjD,gBAAc,OAAO,CAAC,SAAS,IAAI,CAAC;AACxC;AAEA,IAAM,mBAAmB,CAAC,EAAE,MAAAA,OAAM,OAAO,MAAM;AAE3C,MAAI,CAACA,MAAK,MAAM,0BAA0B,EAAG;AAC7C,aAAW,MAAM;AACb,UAAM,QAAQ,SAASA,OAAM,OAAO,EAAE;AACtC,QAAI,CAAC,MAAO;AACZ,kBAAc,OAAO,CAAC,OAAO,IAAI,CAAC;AAAA,EACtC,GAAG,CAAC;AACR;AAEA,IAAM,iBAAiB,CAAC,EAAE,MAAAA,MAAK,MAAM;AACjC,EAAAA,MAAK,QAAQ,WAAWA,MAAK,MAAM,cAAc;AACrD;AAEA,IAAM,gBAAgB,CAAC,EAAE,MAAAA,OAAM,OAAO,MAAM;AACxC,QAAM,QAAQ,SAASA,OAAM,OAAO,EAAE;AACtC,MAAI,CAAC,MAAO;AACZ,MAAI,MAAM,WAAY,OAAM,WAAW,YAAY,KAAK;AACxD,SAAOA,MAAK,IAAI,OAAO,OAAO,EAAE;AACpC;AAIA,IAAM,iBAAiB,CAAC,EAAE,MAAAA,OAAM,OAAO,MAAM;AACzC,QAAM,QAAQ,SAASA,OAAM,OAAO,EAAE;AACtC,MAAI,CAAC,MAAO;AACZ,MAAI,OAAO,UAAU,MAAM;AAEvB,UAAM,gBAAgB,OAAO;AAAA,EACjC,OAAO;AAEH,QAAI,MAAM,QAAQ,QAAQ;AACtB,YAAM,QAAQ,OAAO;AAAA,IACzB;AAAA,EACJ;AACA,8BAA4BA,KAAI;AACpC;AAEA,IAAM,UAAU,YAAY;AAAA,EACxB,kBAAkB;AAAA,EAClB,cAAc;AAAA,EACd,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,gBAAgB;AACpB,CAAC;AAED,IAAM,OAAO,WAAW;AAAA,EACpB,KAAK;AAAA,EACL,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,YAAY;AAChB,CAAC;AAED,IAAM,cAAc,aAAY,iBAAiB,UAAU,QAAQ,YAAY,IAAI;AAEnF,IAAM,SAAS,CAAC,OAAO,QAAQ,OAAO,OAAO,OAAO,QAAQ,OAAO,MAAM;AACzE,IAAM,SAAS,CAAC,OAAO,OAAO,QAAQ,KAAK;AAC3C,IAAM,MAAM;AAAA,EACR,KAAK;AAAA,EACL,MAAM;AACV;AAEA,IAAM,sBAAsB,CAAC,YAAY,OAAO;AAC5C,cAAY,UAAU,YAAY;AAClC,MAAI,OAAO,SAAS,SAAS,GAAG;AAC5B,WACI,YAAY,cAAc,QAAQ,SAAS,cAAc,QAAQ,YAAY;AAAA,EAErF;AACA,MAAI,OAAO,SAAS,SAAS,GAAG;AAC5B,WAAO,UAAU;AAAA,EACrB;AAEA,SAAO,IAAI,SAAS,KAAK;AAC7B;AAEA,IAAM,2BAA2B,kBAC7B,IAAI,QAAQ,CAAC,SAAS,WAAW;AAE7B,QAAM,QAAQ,SAAS,YAAY;AACnC,MAAI,MAAM,UAAU,CAAC,SAAS,YAAY,GAAG;AACzC,WAAO,QAAQ,KAAK;AAAA,EACxB;AAEA,WAAS,YAAY,EAAE,KAAK,OAAO;AACvC,CAAC;AAKL,IAAM,WAAW,kBAAgB;AAC7B,MAAI,aAAa,MAAO,QAAO,aAAa,MAAM,SAAS;AAC3D,SAAO;AACX;AAKA,IAAM,WAAW,kBACb,IAAI,QAAQ,CAAC,SAAS,WAAW;AAE7B,QAAM,iBAAiB,aAAa,QAAQ,MAAM,KAAK,aAAa,KAAK,IAAI,CAAC,GAGzE,OAAO,CAAAE,UAAQ,iBAAiBA,KAAI,CAAC,EAGrC,IAAI,CAAAA,UAAQ,iBAAiBA,KAAI,CAAC;AAGvC,MAAI,CAAC,cAAc,QAAQ;AAGvB,YAAQ,aAAa,QAAQ,MAAM,KAAK,aAAa,KAAK,IAAI,CAAC,CAAC;AAChE;AAAA,EACJ;AAGA,UAAQ,IAAI,aAAa,EACpB,KAAK,wBAAsB;AAExB,UAAM,QAAQ,CAAC;AACf,uBAAmB,QAAQ,WAAS;AAChC,YAAM,KAAK,MAAM,OAAO,KAAK;AAAA,IACjC,CAAC;AAGD;AAAA,MACI,MACK,OAAO,CAAAK,UAAQA,KAAI,EACnB,IAAI,CAAAA,UAAQ;AACT,YAAI,CAACA,MAAK,cAAe,CAAAA,MAAK,gBAAgBA,MAAK;AACnD,eAAOA;AAAA,MACX,CAAC;AAAA,IACT;AAAA,EACJ,CAAC,EACA,MAAM,QAAQ,KAAK;AAC5B,CAAC;AAEL,IAAM,mBAAmB,CAAAL,UAAQ;AAC7B,MAAI,QAAQA,KAAI,GAAG;AACf,UAAM,QAAQ,WAAWA,KAAI;AAC7B,QAAI,OAAO;AACP,aAAO,MAAM,UAAU,MAAM;AAAA,IACjC;AAAA,EACJ;AACA,SAAOA,MAAK,SAAS;AACzB;AAEA,IAAM,mBAAmB,CAAAA,UACrB,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC7B,MAAI,iBAAiBA,KAAI,GAAG;AACxB,wBAAoB,WAAWA,KAAI,CAAC,EAC/B,KAAK,OAAO,EACZ,MAAM,MAAM;AACjB;AAAA,EACJ;AAEA,UAAQ,CAACA,MAAK,UAAU,CAAC,CAAC;AAC9B,CAAC;AAEL,IAAM,sBAAsB,WACxB,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC7B,QAAM,QAAQ,CAAC;AAGf,MAAI,aAAa;AACjB,MAAI,cAAc;AAElB,QAAM,gBAAgB,MAAM;AACxB,QAAI,gBAAgB,KAAK,eAAe,GAAG;AACvC,cAAQ,KAAK;AAAA,IACjB;AAAA,EACJ;AAGA,QAAM,cAAc,cAAY;AAC5B;AAEA,UAAM,kBAAkB,SAAS,aAAa;AAG9C,UAAM,YAAY,MAAM;AACpB,sBAAgB,YAAY,aAAW;AACnC,YAAI,QAAQ,WAAW,GAAG;AACtB;AACA,wBAAc;AACd;AAAA,QACJ;AAEA,gBAAQ,QAAQ,CAAA4B,WAAS;AAErB,cAAIA,OAAM,aAAa;AACnB,wBAAYA,MAAK;AAAA,UACrB,OAAO;AAEH;AAEA,YAAAA,OAAM,KAAK,CAAAvB,UAAQ;AACf,oBAAM,gBAAgB,uBAAuBA,KAAI;AACjD,kBAAIuB,OAAM,SAAU,eAAc,gBAAgBA,OAAM;AACxD,oBAAM,KAAK,aAAa;AACxB;AACA,4BAAc;AAAA,YAClB,CAAC;AAAA,UACL;AAAA,QACJ,CAAC;AAGD,kBAAU;AAAA,MACd,GAAG,MAAM;AAAA,IACb;AAGA,cAAU;AAAA,EACd;AAGA,cAAY,KAAK;AACrB,CAAC;AAEL,IAAM,yBAAyB,CAAAvB,UAAQ;AACnC,MAAIA,MAAK,KAAK,OAAQ,QAAOA;AAC7B,QAAM,OAAOA,MAAK;AAClB,QAAMpB,QAAOoB,MAAK;AAClB,QAAM,OAAO,oBAAoB,yBAAyBA,MAAK,IAAI,CAAC;AACpE,MAAI,CAAC,KAAK,OAAQ,QAAOA;AACzB,EAAAA,QAAOA,MAAK,MAAM,GAAGA,MAAK,MAAM,IAAI;AACpC,EAAAA,MAAK,OAAOpB;AACZ,EAAAoB,MAAK,mBAAmB;AACxB,SAAOA;AACX;AAEA,IAAM,mBAAmB,CAAAL,UAAQ,QAAQA,KAAI,MAAM,WAAWA,KAAI,KAAK,CAAC,GAAG;AAE3E,IAAM,UAAU,CAAAA,UAAQ,sBAAsBA;AAE9C,IAAM,aAAa,CAAAA,UAAQA,MAAK,iBAAiB;AAKjD,IAAM,WAAW,kBAAgB;AAC7B,MAAI,QAAQ,CAAC;AACb,MAAI;AAEA,YAAQ,6BAA6B,YAAY;AACjD,QAAI,MAAM,QAAQ;AACd,aAAO;AAAA,IACX;AACA,YAAQ,4BAA4B,YAAY;AAAA,EACpD,SAAS,GAAG;AAAA,EAEZ;AACA,SAAO;AACX;AAEA,IAAM,8BAA8B,kBAAgB;AAChD,MAAIM,QAAO,aAAa,QAAQ,KAAK;AACrC,MAAI,OAAOA,UAAS,YAAYA,MAAK,QAAQ;AACzC,WAAO,CAACA,KAAI;AAAA,EAChB;AACA,SAAO,CAAC;AACZ;AAEA,IAAM,+BAA+B,kBAAgB;AACjD,MAAIA,QAAO,aAAa,QAAQ,WAAW;AAC3C,MAAI,OAAOA,UAAS,YAAYA,MAAK,QAAQ;AACzC,UAAM,UAAUA,MAAK,MAAM,mBAAmB;AAC9C,QAAI,SAAS;AACT,aAAO,CAAC,QAAQ,CAAC,CAAC;AAAA,IACtB;AAAA,EACJ;AACA,SAAO,CAAC;AACZ;AAEA,IAAM,qBAAqB,CAAC;AAE5B,IAAM,gBAAgB,QAAM;AAAA,EACxB,UAAU,EAAE;AAAA,EACZ,SAAS,EAAE;AAAA,EACX,WAAW,EAAE,WAAW,EAAE;AAAA,EAC1B,UAAU,EAAE,WAAW,EAAE;AAC7B;AAEA,IAAM,wBAAwB,CAAC,SAAS,gBAAgB,kBAAkB;AACtE,QAAM,WAAW,qBAAqB,cAAc;AAEpD,QAAM,SAAS;AAAA,IACX;AAAA,IACA;AAAA,IACA,OAAO;AAAA,IACP,QAAQ,MAAM;AAAA,IAAC;AAAA,IACf,SAAS,MAAM;AAAA,IAAC;AAAA,IAChB,QAAQ,MAAM;AAAA,IAAC;AAAA,IACf,QAAQ,MAAM;AAAA,IAAC;AAAA,IACf,QAAQ,MAAM;AAAA,IAAC;AAAA,IACf,WAAW,MAAM;AAAA,IAAC;AAAA,EACtB;AAEA,SAAO,UAAU,SAAS,YAAY,MAAM;AAE5C,SAAO;AACX;AAEA,IAAM,uBAAuB,aAAW;AAEpC,QAAM,WAAW,mBAAmB,KAAK,CAAAN,UAAQA,MAAK,YAAY,OAAO;AACzE,MAAI,UAAU;AACV,WAAO;AAAA,EACX;AAGA,QAAM,cAAc,wBAAwB,OAAO;AACnD,qBAAmB,KAAK,WAAW;AACnC,SAAO;AACX;AAEA,IAAM,0BAA0B,aAAW;AACvC,QAAM,UAAU,CAAC;AAEjB,QAAM,SAAS;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAEA,QAAM,WAAW,CAAC;AAElB,QAAM,QAAQ,CAAC,OAAO,kBAAkB;AACpC,aAAS,KAAK,IAAI,cAAc,SAAS,OAAO;AAChD,YAAQ,iBAAiB,OAAO,SAAS,KAAK,GAAG,KAAK;AAAA,EAC1D,CAAC;AAED,QAAM,WAAW;AAAA,IACb;AAAA,IACA,aAAa,YAAU;AAEnB,cAAQ,KAAK,MAAM;AAGnB,aAAO,MAAM;AAET,gBAAQ,OAAO,QAAQ,QAAQ,MAAM,GAAG,CAAC;AAGzC,YAAI,QAAQ,WAAW,GAAG;AACtB,6BAAmB,OAAO,mBAAmB,QAAQ,QAAQ,GAAG,CAAC;AAEjE,gBAAM,QAAQ,WAAS;AACnB,oBAAQ,oBAAoB,OAAO,SAAS,KAAK,GAAG,KAAK;AAAA,UAC7D,CAAC;AAAA,QACL;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAEA,SAAO;AACX;AAEA,IAAM,mBAAmB,CAACF,OAAM,UAAU;AACtC,MAAI,EAAE,sBAAsBA,QAAO;AAC/B,IAAAA,QAAO;AAAA,EACX;AACA,SAAOA,MAAK,iBAAiB,MAAM,GAAG,MAAM,CAAC;AACjD;AAEA,IAAM,gBAAgB,CAAC,GAAG,WAAW;AAEjC,QAAMA,QAAO,YAAY,MAAM;AAI/B,QAAM,oBAAoB,iBAAiBA,OAAM;AAAA,IAC7C,GAAG,EAAE,QAAQ,OAAO;AAAA,IACpB,GAAG,EAAE,QAAQ,OAAO;AAAA,EACxB,CAAC;AAGD,SAAO,sBAAsB,UAAU,OAAO,SAAS,iBAAiB;AAC5E;AAEA,IAAI,gBAAgB;AAEpB,IAAM,gBAAgB,CAAC,cAAc,WAAW;AAE5C,MAAI;AACA,iBAAa,aAAa;AAAA,EAC9B,SAAS,GAAG;AAAA,EAAC;AACjB;AAEA,IAAM,YAAY,CAACA,OAAM,YAAY,OAAK;AACtC,IAAE,eAAe;AAEjB,kBAAgB,EAAE;AAElB,UAAQ,QAAQ,YAAU;AACtB,UAAM,EAAE,SAAS,QAAQ,IAAI;AAE7B,QAAI,cAAc,GAAG,OAAO,GAAG;AAC3B,aAAO,QAAQ;AAGf,cAAQ,cAAc,CAAC,CAAC;AAAA,IAC5B;AAAA,EACJ,CAAC;AACL;AAEA,IAAM,WAAW,CAACA,OAAM,YAAY,OAAK;AACrC,IAAE,eAAe;AAEjB,QAAM,eAAe,EAAE;AAEvB,2BAAyB,YAAY,EAAE,KAAK,WAAS;AACjD,QAAI,iBAAiB;AAErB,YAAQ,KAAK,YAAU;AACnB,YAAM,EAAE,eAAe,SAAS,SAAS,QAAQ,QAAQ,UAAU,IAAI;AAGvE,oBAAc,cAAc,MAAM;AAGlC,YAAM,iBAAiB,UAAU,KAAK;AAGtC,UAAI,CAAC,gBAAgB;AACjB,sBAAc,cAAc,MAAM;AAClC;AAAA,MACJ;AAGA,UAAI,cAAc,GAAG,OAAO,GAAG;AAC3B,yBAAiB;AAGjB,YAAI,OAAO,UAAU,MAAM;AACvB,iBAAO,QAAQ;AACf,kBAAQ,cAAc,CAAC,CAAC;AACxB;AAAA,QACJ;AAGA,eAAO,QAAQ;AAGf,YAAI,iBAAiB,CAAC,gBAAgB;AAClC,wBAAc,cAAc,MAAM;AAClC;AAAA,QACJ;AAGA,eAAO,cAAc,CAAC,CAAC;AAAA,MAC3B,OAAO;AAEH,YAAI,iBAAiB,CAAC,gBAAgB;AAClC,wBAAc,cAAc,MAAM;AAAA,QACtC;AAGA,YAAI,OAAO,OAAO;AACd,iBAAO,QAAQ;AACf,iBAAO,cAAc,CAAC,CAAC;AAAA,QAC3B;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL,CAAC;AACL;AAEA,IAAM,OAAO,CAACA,OAAM,YAAY,OAAK;AACjC,IAAE,eAAe;AAEjB,QAAM,eAAe,EAAE;AAEvB,2BAAyB,YAAY,EAAE,KAAK,WAAS;AACjD,YAAQ,QAAQ,YAAU;AACtB,YAAM,EAAE,eAAe,SAAS,QAAQ,QAAQ,UAAU,IAAI;AAE9D,aAAO,QAAQ;AAGf,UAAI,iBAAiB,CAAC,cAAc,GAAG,OAAO,EAAG;AAGjD,UAAI,CAAC,UAAU,KAAK,EAAG,QAAO,OAAO,cAAc,CAAC,CAAC;AAGrD,aAAO,cAAc,CAAC,GAAG,KAAK;AAAA,IAClC,CAAC;AAAA,EACL,CAAC;AACL;AAEA,IAAM,YAAY,CAACA,OAAM,YAAY,OAAK;AACtC,MAAI,kBAAkB,EAAE,QAAQ;AAC5B;AAAA,EACJ;AAEA,UAAQ,QAAQ,YAAU;AACtB,UAAM,EAAE,OAAO,IAAI;AAEnB,WAAO,QAAQ;AAEf,WAAO,cAAc,CAAC,CAAC;AAAA,EAC3B,CAAC;AACL;AAEA,IAAM,eAAe,CAAC,OAAO,eAAe,YAAY;AAEpD,QAAM,UAAU,IAAI,kBAAkB;AAGtC,QAAM,EAAE,oBAAoB,uBAAuB,cAAc,WAAS,MAAM,IAAI;AAGpF,QAAM,SAAS;AAAA,IACX;AAAA,IACA,qBAAqB,SAAS,kBAAkB;AAAA,IAChD;AAAA,EACJ;AAGA,MAAI,YAAY;AAChB,MAAI,eAAe;AAGnB,SAAO,YAAY,WAAS;AAGxB,WAAO,cAAc,YAAY,KAAK,CAAC;AAAA,EAC3C;AAEA,SAAO,SAAS,CAAC,UAAU,UAAU;AACjC,UAAM,gBAAgB,YAAY,KAAK;AAEvC,QAAI,CAAC,cAAc,aAAa,GAAG;AAC/B,UAAI,UAAU,QAAQ;AACtB;AAAA,IACJ;AAEA,mBAAe;AAEf,QAAI,OAAO,eAAe,QAAQ;AAAA,EACtC;AAEA,SAAO,SAAS,cAAY;AACxB,QAAI,OAAO,QAAQ;AAAA,EACvB;AAEA,SAAO,UAAU,cAAY;AACzB,mBAAe;AAEf,QAAI,YAAY,QAAQ;AAAA,EAC5B;AAEA,SAAO,SAAS,cAAY;AACxB,mBAAe;AAEf,QAAI,UAAU,QAAQ;AAAA,EAC1B;AAEA,QAAM,MAAM;AAAA,IACR,mBAAmB,MAAM;AACrB,UAAI,cAAc,cAAc;AAC5B,cAAM,QAAQ,cAAc;AAC5B,oBAAY;AAAA,MAChB;AAAA,IACJ;AAAA,IACA,QAAQ,MAAM;AAAA,IAAC;AAAA,IACf,aAAa,MAAM;AAAA,IAAC;AAAA,IACpB,QAAQ,MAAM;AAAA,IAAC;AAAA,IACf,WAAW,MAAM;AAAA,IAAC;AAAA,IAClB,SAAS,MAAM;AAEX,aAAO,QAAQ;AAAA,IACnB;AAAA,EACJ;AAEA,SAAO;AACX;AAEA,IAAI,YAAY;AAChB,IAAM,cAAc,CAAC;AAErB,IAAM,cAAc,OAAK;AAErB,QAAM,WAAW,SAAS;AAC1B,QAAM,0BACF,aACC,kBAAkB,KAAK,SAAS,QAAQ,KACrC,SAAS,aAAa,iBAAiB,MAAM;AAErD,MAAI,yBAAyB;AAEzB,QAAI,UAAU;AACd,QAAI,UAAU;AACd,WAAO,YAAY,SAAS,MAAM;AAC9B,UAAI,QAAQ,UAAU,SAAS,gBAAgB,GAAG;AAC9C,kBAAU;AACV;AAAA,MACJ;AACA,gBAAU,QAAQ;AAAA,IACtB;AAEA,QAAI,CAAC,QAAS;AAAA,EAClB;AAEA,2BAAyB,EAAE,aAAa,EAAE,KAAK,WAAS;AAEpD,QAAI,CAAC,MAAM,QAAQ;AACf;AAAA,IACJ;AAGA,gBAAY,QAAQ,cAAY,SAAS,KAAK,CAAC;AAAA,EACnD,CAAC;AACL;AAEA,IAAM,SAAS,QAAM;AAEjB,MAAI,YAAY,SAAS,EAAE,GAAG;AAC1B;AAAA,EACJ;AAGA,cAAY,KAAK,EAAE;AAGnB,MAAI,WAAW;AACX;AAAA,EACJ;AAEA,cAAY;AACZ,WAAS,iBAAiB,SAAS,WAAW;AAClD;AAEA,IAAM,WAAW,cAAY;AACzB,cAAY,aAAa,YAAY,QAAQ,QAAQ,CAAC;AAGtD,MAAI,YAAY,WAAW,GAAG;AAC1B,aAAS,oBAAoB,SAAS,WAAW;AACjD,gBAAY;AAAA,EAChB;AACJ;AAEA,IAAM,eAAe,MAAM;AACvB,QAAM,KAAK,WAAS;AAChB,QAAI,OAAO,KAAK;AAAA,EACpB;AAEA,QAAM,MAAM;AAAA,IACR,SAAS,MAAM;AACX,eAAS,EAAE;AAAA,IACf;AAAA,IACA,QAAQ,MAAM;AAAA,IAAC;AAAA,EACnB;AAEA,SAAO,EAAE;AAET,SAAO;AACX;AAKA,IAAM,WAAW,CAAC,EAAE,MAAAA,OAAM,MAAM,MAAM;AAClC,EAAAA,MAAK,QAAQ,KAAK,uBAAuB,MAAM,EAAE;AACjD,OAAKA,MAAK,SAAS,QAAQ,OAAO;AAClC,OAAKA,MAAK,SAAS,aAAa,QAAQ;AACxC,OAAKA,MAAK,SAAS,iBAAiB,WAAW;AACnD;AAEA,IAAI,8BAA8B;AAClC,IAAI,2BAA2B;AAE/B,IAAM,YAAY,CAAC;AAEnB,IAAM,SAAS,CAACA,OAAM,YAAY;AAC9B,EAAAA,MAAK,QAAQ,cAAc;AAC/B;AAEA,IAAM,UAAU,CAAAA,UAAQ;AACpB,EAAAA,MAAK,QAAQ,cAAc;AAC/B;AAEA,IAAM,eAAe,CAACA,OAAM,UAAU,UAAU;AAC5C,QAAM,QAAQA,MAAK,MAAM,iBAAiB;AAC1C;AAAA,IACIA;AAAA,IACA,GAAG,KAAK,IAAI,QAAQ,KAAK,KAAK,IAC1B,UAAU,IACJA,MAAK,MAAM,+BAA+B,IAC1CA,MAAK,MAAM,6BAA6B,CAClD;AAAA,EACJ;AAGA,eAAa,wBAAwB;AACrC,6BAA2B,WAAW,MAAM;AACxC,YAAQA,KAAI;AAAA,EAChB,GAAG,IAAI;AACX;AAEA,IAAM,kBAAkB,CAAAA,UAAQA,MAAK,QAAQ,WAAW,SAAS,SAAS,aAAa;AAEvF,IAAM,YAAY,CAAC,EAAE,MAAAA,OAAM,OAAO,MAAM;AACpC,MAAI,CAAC,gBAAgBA,KAAI,GAAG;AACxB;AAAA,EACJ;AAEA,EAAAA,MAAK,QAAQ,cAAc;AAC3B,QAAME,QAAOF,MAAK,MAAM,YAAY,OAAO,EAAE;AAC7C,YAAU,KAAKE,MAAK,QAAQ;AAE5B,eAAa,2BAA2B;AACxC,gCAA8B,WAAW,MAAM;AAC3C,iBAAaF,OAAM,UAAU,KAAK,IAAI,GAAGA,MAAK,MAAM,sBAAsB,CAAC;AAC3E,cAAU,SAAS;AAAA,EACvB,GAAG,GAAG;AACV;AAEA,IAAM,cAAc,CAAC,EAAE,MAAAA,OAAM,OAAO,MAAM;AACtC,MAAI,CAAC,gBAAgBA,KAAI,GAAG;AACxB;AAAA,EACJ;AAEA,QAAME,QAAO,OAAO;AACpB,eAAaF,OAAME,MAAK,UAAUF,MAAK,MAAM,wBAAwB,CAAC;AAC1E;AAEA,IAAM,gBAAgB,CAAC,EAAE,MAAAA,OAAM,OAAO,MAAM;AAGxC,QAAME,QAAOF,MAAK,MAAM,YAAY,OAAO,EAAE;AAC7C,QAAM,WAAWE,MAAK;AACtB,QAAM,QAAQF,MAAK,MAAM,oCAAoC;AAE7D,SAAOA,OAAM,GAAG,QAAQ,IAAI,KAAK,EAAE;AACvC;AAEA,IAAM,oBAAoB,CAAC,EAAE,MAAAA,OAAM,OAAO,MAAM;AAC5C,QAAME,QAAOF,MAAK,MAAM,YAAY,OAAO,EAAE;AAC7C,QAAM,WAAWE,MAAK;AACtB,QAAM,QAAQF,MAAK,MAAM,mCAAmC;AAE5D,SAAOA,OAAM,GAAG,QAAQ,IAAI,KAAK,EAAE;AACvC;AAEA,IAAM,YAAY,CAAC,EAAE,MAAAA,OAAM,OAAO,MAAM;AACpC,QAAME,QAAOF,MAAK,MAAM,YAAY,OAAO,EAAE;AAC7C,QAAM,WAAWE,MAAK;AAItB,SAAOF,OAAM,GAAG,OAAO,OAAO,IAAI,IAAI,QAAQ,IAAI,OAAO,OAAO,GAAG,EAAE;AACzE;AAEA,IAAM,YAAY,WAAW;AAAA,EACzB,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,OAAO,YAAY;AAAA,IACf,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,8BAA8B;AAAA,IAE9B,2BAA2B;AAAA,IAC3B,4BAA4B;AAAA,IAE5B,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,wBAAwB;AAAA,IACxB,iCAAiC;AAAA,EACrC,CAAC;AAAA,EACD,KAAK;AAAA,EACL,MAAM;AACV,CAAC;AAED,IAAM,WAAW,CAAC,QAAQ,YAAY,QAClC,OAAO,QAAQ,IAAI,OAAO,GAAG,SAAS,KAAK,GAAG,GAAG,SAAO,IAAI,OAAO,CAAC,EAAE,YAAY,CAAC;AAEvF,IAAM,WAAW,CAAC,MAAM,WAAW,IAAI,gBAAgB,SAAS;AAC5D,MAAI,OAAO,KAAK,IAAI;AACpB,MAAI,UAAU;AAEd,SAAO,IAAI,SAAS;AAChB,iBAAa,OAAO;AAEpB,UAAM,OAAO,KAAK,IAAI,IAAI;AAE1B,UAAMV,MAAK,MAAM;AACb,aAAO,KAAK,IAAI;AAChB,WAAK,GAAG,IAAI;AAAA,IAChB;AAEA,QAAI,OAAO,UAAU;AAIjB,UAAI,CAAC,eAAe;AAChB,kBAAU,WAAWA,KAAI,WAAW,IAAI;AAAA,MAC5C;AAAA,IACJ,OAAO;AAEH,MAAAA,IAAG;AAAA,IACP;AAAA,EACJ;AACJ;AAEA,IAAM,kBAAkB;AAExB,IAAM,UAAU,OAAK,EAAE,eAAe;AAEtC,IAAM,WAAW,CAAC,EAAE,MAAAU,OAAM,MAAM,MAAM;AAElC,QAAM,KAAKA,MAAK,MAAM,QAAQ;AAC9B,MAAI,IAAI;AACJ,IAAAA,MAAK,QAAQ,KAAK;AAAA,EACtB;AAGA,QAAM,YAAYA,MAAK,MAAM,gBAAgB;AAC7C,MAAI,WAAW;AACX,cACK,MAAM,GAAG,EACT,OAAO,CAAAb,UAAQA,MAAK,MAAM,EAC1B,QAAQ,CAAAA,UAAQ;AACb,MAAAa,MAAK,QAAQ,UAAU,IAAIb,KAAI;AAAA,IACnC,CAAC;AAAA,EACT;AAGA,EAAAa,MAAK,IAAI,QAAQA,MAAK;AAAA,IAClBA,MAAK,gBAAgB,WAAW;AAAA,MAC5B,GAAG;AAAA,MACH,YAAY;AAAA,MACZ,SAASA,MAAK,MAAM,gBAAgB;AAAA,IACxC,CAAC;AAAA,EACL;AAGA,EAAAA,MAAK,IAAI,OAAOA,MAAK,gBAAgBA,MAAK,gBAAgB,cAAc,EAAE,YAAY,KAAK,CAAC,CAAC;AAG7F,EAAAA,MAAK,IAAI,QAAQA,MAAK,gBAAgBA,MAAK,gBAAgB,OAAO,EAAE,MAAM,aAAa,CAAC,CAAC;AAGzF,EAAAA,MAAK,IAAI,YAAYA,MAAK,gBAAgBA,MAAK,gBAAgB,WAAW,EAAE,GAAG,MAAM,CAAC,CAAC;AAGvF,EAAAA,MAAK,IAAI,OAAOA,MAAK,gBAAgBA,MAAK,gBAAgB,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC;AAI7E,EAAAA,MAAK,IAAI,UAAU,gBAAgB,KAAK;AACxC,EAAAA,MAAK,IAAI,QAAQ,MAAM,SAAS;AAChC,EAAAA,MAAK,QAAQ,YAAYA,MAAK,IAAI,OAAO;AAGzC,EAAAA,MAAK,IAAI,SAAS;AAGlB,EAAAA,MAAK,MAAM,YAAY,EAClB,OAAO,WAAS,CAAC,QAAQ,MAAM,KAAK,CAAC,EACrC,IAAI,CAAC,EAAE,MAAAb,OAAM,MAAM,MAAM;AACtB,IAAAa,MAAK,QAAQ,QAAQb,KAAI,IAAI;AAAA,EACjC,CAAC;AAGL,EAAAa,MAAK,IAAI,gBAAgB;AACzB,EAAAA,MAAK,IAAI,eAAe,SAAS,MAAM;AACnC,IAAAA,MAAK,IAAI,gBAAgB,CAAC;AAC1B,IAAAA,MAAK,SAAS,iBAAiB;AAAA,EACnC,GAAG,GAAG;AAGN,EAAAA,MAAK,IAAI,sBAAsB;AAC/B,EAAAA,MAAK,IAAI,gBAAgB,CAAC;AAG1B,QAAM,WAAW,OAAO,WAAW,oCAAoC,EAAE;AACzE,QAAM,mBAAmB,kBAAkB;AAC3C,MAAIA,MAAK,MAAM,mBAAmB,KAAK,oBAAoB,CAAC,UAAU;AAClE,IAAAA,MAAK,QAAQ,iBAAiB,aAAa,SAAS,EAAE,SAAS,MAAM,CAAC;AACtE,IAAAA,MAAK,QAAQ,iBAAiB,gBAAgB,OAAO;AAAA,EACzD;AAGA,QAAM,UAAUA,MAAK,MAAM,aAAa;AACxC,QAAM,aAAa,QAAQ,WAAW;AACtC,MAAI,YAAY;AACZ,UAAM,OAAO,SAAS,cAAc,GAAG;AACvC,SAAK,YAAY;AACjB,SAAK,OAAO,QAAQ,CAAC;AACrB,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,MAAM;AACX,SAAK,cAAc,QAAQ,CAAC;AAC5B,IAAAA,MAAK,QAAQ,YAAY,IAAI;AAC7B,IAAAA,MAAK,IAAI,UAAU;AAAA,EACvB;AACJ;AAEA,IAAM,UAAU,CAAC,EAAE,MAAAA,OAAM,OAAO,SAAAL,SAAQ,MAAM;AAE1C,UAAQ,EAAE,MAAAK,OAAM,OAAO,SAAAL,SAAQ,CAAC;AAGhC,EAAAA,SACK,OAAO,YAAU,kBAAkB,KAAK,OAAO,IAAI,CAAC,EACpD,OAAO,YAAU,CAAC,QAAQ,OAAO,KAAK,KAAK,CAAC,EAC5C,IAAI,CAAC,EAAE,MAAM,MAAAa,MAAK,MAAM;AACrB,UAAMrB,QAAO,SAAS,KAAK,UAAU,CAAC,EAAE,YAAY,GAAG,GAAG;AAC1D,IAAAa,MAAK,QAAQ,QAAQb,KAAI,IAAIqB,MAAK;AAClC,IAAAR,MAAK,iBAAiB;AAAA,EAC1B,CAAC;AAEL,MAAIA,MAAK,KAAK,QAAQ,OAAQ;AAE9B,MAAIA,MAAK,KAAK,QAAQ,UAAUA,MAAK,IAAI,eAAe;AACpD,IAAAA,MAAK,IAAI,gBAAgBA,MAAK,KAAK,QAAQ;AAC3C,IAAAA,MAAK,IAAI,aAAa;AAAA,EAC1B;AAGA,MAAI,SAASA,MAAK,IAAI;AACtB,MAAI,CAAC,QAAQ;AACT,aAASA,MAAK,IAAI,SAAS,+BAA+BA,KAAI;AAG9D,IAAAA,MAAK,QAAQ,YAAYA,MAAK,IAAI,OAAO;AACzC,IAAAA,MAAK,IAAI,UAAU;AAAA,EACvB;AAGA,QAAM,EAAE,QAAQ,OAAO,MAAA+B,OAAM,OAAAC,OAAM,IAAIhC,MAAK;AAG5C,MAAI,QAAQ;AACR,WAAO,kBAAkB;AAAA,EAC7B;AAGA,QAAM,cAAcA,MAAK,MAAM,wBAAwB;AACvD,QAAM,cAAcA,MAAK,MAAM,oBAAoB;AACnD,QAAM,aAAaA,MAAK,MAAM,iBAAiB;AAC/C,QAAM,WAAW,cAAcA,MAAK,MAAM,eAAe,KAAK,kBAAkB;AAChF,QAAM,gBAAgB,eAAe;AAGrC,QAAM,YAAYL,SAAQ,KAAK,YAAU,OAAO,SAAS,cAAc;AAGvE,MAAI,iBAAiB,WAAW;AAE5B,UAAM,oBAAoB,UAAU,KAAK;AAGzC,UAAM,UAAU;AAEhB,QAAI,aAAa;AACb,YAAM,aAAa;AAAA,IACvB,OAAO;AACH,UAAI,sBAAsB,kBAAkB,KAAK;AAC7C,cAAM,aAAa;AAAA,MACvB,WAAW,sBAAsB,kBAAkB,QAAQ;AACvD,cAAM,aAAa;AAAA,MACvB,OAAO;AACH,cAAM,aAAa;AAAA,MACvB;AAAA,IACJ;AAAA,EACJ,WAAW,CAAC,eAAe;AACvB,UAAM,UAAU;AAChB,UAAM,aAAa;AACnB,UAAM,aAAa;AAAA,EACvB;AAEA,QAAM,iBAAiB,wBAAwBK,KAAI;AAEnD,QAAM,aAAa,oBAAoBA,KAAI;AAE3C,QAAM,cAAc,MAAM,KAAK,QAAQ;AACvC,QAAM,qBAAqB,CAAC,eAAe,gBAAgB,IAAI;AAE/D,QAAM,gBAAgB,gBAAgB+B,MAAK,KAAK,QAAQ,YAAY;AACpE,QAAM,mBAAmB,eAAe,IAAI,IAAIA,MAAK,KAAK,QAAQ;AAElE,QAAM,eAAe,qBAAqB,gBAAgB,WAAW,SAAS;AAC9E,QAAM,eAAe,qBAAqB,gBAAgB,WAAW,SAAS;AAG9E,EAAAA,MAAK,aACD,KAAK,IAAI,GAAG,qBAAqBA,MAAK,KAAK,QAAQ,SAAS,IAAI,eAAe;AAEnF,MAAI,aAAa;AAIb,UAAM,QAAQ/B,MAAK,KAAK,QAAQ;AAChC,UAAM,SAAS,QAAQ;AAGvB,QAAI,gBAAgBA,MAAK,IAAI,qBAAqB;AAC9C,MAAAA,MAAK,IAAI,sBAAsB;AAC/B,MAAAA,MAAK,IAAI,gBAAgB,CAAC;AAAA,IAC9B;AAGA,UAAM,UAAUA,MAAK,IAAI;AACzB,YAAQ,KAAK,KAAK;AAElB,UAAM,cAAc;AACpB,QAAI,QAAQ,SAAS,cAAc,GAAG;AAClC,YAAM,IAAI,QAAQ;AAClB,YAAM,SAAS,IAAI;AACnB,UAAI,UAAU;AACd,eAAS,IAAI,GAAG,KAAK,QAAQ,KAAK;AAC9B,YAAI,QAAQ,CAAC,MAAM,QAAQ,IAAI,CAAC,GAAG;AAC/B;AAAA,QACJ;AAEA,YAAI,WAAW,aAAa;AAExB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAGA,IAAAgC,OAAM,WAAW;AACjB,IAAAA,OAAM,SAAS;AAGf,UAAM;AAAA;AAAA,MAEF,SACA;AAAA,OAEC,mBAAmB,eAAe;AAAA,OAElC,gBAAgB,gBAAgB;AAAA;AAErC,QAAI,WAAW,SAAS,qBAAqB;AACzC,MAAAD,MAAK,WAAW;AAAA,IACpB,OAAO;AACH,MAAAA,MAAK,WAAW;AAAA,IACpB;AAGA,IAAA/B,MAAK,SAAS;AAAA,EAClB,WAAW,OAAO,aAAa;AAI3B,IAAAgC,OAAM,WAAW;AAGjB,UAAM;AAAA;AAAA,MAEF,OAAO,cACP;AAAA,OAEC,mBAAmB,eAAe;AAAA,OAElC,gBAAgB,gBAAgB;AAAA;AAGrC,QAAI,WAAW,SAAS,qBAAqB;AACzC,MAAAD,MAAK,WAAW;AAAA,IACpB,OAAO;AACH,MAAAA,MAAK,WAAW;AAAA,IACpB;AAAA,EAGJ,WAAW,OAAO,cAAc;AAI5B,UAAM,iBAAiB,gBAAgB,OAAO;AAC9C,UAAM,cAAc,KAAK,IAAI,OAAO,cAAc,YAAY;AAC9D,IAAAC,OAAM,WAAW;AACjB,IAAAA,OAAM,SAAS,iBACT,cACA,cAAc,eAAe,MAAM,eAAe;AAGxD,UAAM;AAAA;AAAA,MAEF,cACA;AAAA,OAEC,mBAAmB,eAAe;AAAA,OAElC,gBAAgB,gBAAgB;AAAA;AAGrC,QAAI,eAAe,OAAO,gBAAgB,WAAW,SAAS,qBAAqB;AAC/E,MAAAD,MAAK,WAAW;AAAA,IACpB,OAAO;AACH,MAAAA,MAAK,WAAW;AAAA,IACpB;AAGA,IAAA/B,MAAK,SAAS,KAAK;AAAA,MACf,OAAO;AAAA,MACP,eAAe,eAAe,MAAM,eAAe;AAAA,IACvD;AAAA,EACJ,OAAO;AAIH,UAAM,aAAa,aAAa,IAAI,eAAe,MAAM,eAAe,SAAS;AACjF,IAAAgC,OAAM,WAAW;AACjB,IAAAA,OAAM,SAAS,KAAK,IAAI,aAAa,eAAe,UAAU;AAG9D,IAAAhC,MAAK,SAAS,KAAK,IAAI,aAAa,eAAe,UAAU;AAAA,EACjE;AAGA,MAAIA,MAAK,IAAI,WAAWgC,OAAM;AAC1B,IAAAhC,MAAK,IAAI,QAAQ,MAAM,YAAY,cAAcgC,OAAM,aAAa;AAC5E;AAEA,IAAM,0BAA0B,CAAAhC,UAAQ;AACpC,QAAME,QAAOF,MAAK,IAAI,KAAK,WAAW,CAAC,EAAE,WAAW,CAAC;AACrD,SAAOE,QACD;AAAA,IACI,KAAKA,MAAK,KAAK,QAAQ;AAAA,IACvB,QAAQA,MAAK,KAAK,QAAQ;AAAA,EAC9B,IACA;AAAA,IACI,KAAK;AAAA,IACL,QAAQ;AAAA,EACZ;AACV;AAEA,IAAM,sBAAsB,CAAAF,UAAQ;AAChC,MAAI,SAAS;AACb,MAAI,SAAS;AAGb,QAAM,aAAaA,MAAK,IAAI;AAC5B,QAAM,WAAW,WAAW,WAAW,CAAC;AACxC,QAAM,kBAAkB,SAAS,WAAW,OAAO,WAAS,MAAM,KAAK,QAAQ,MAAM;AACrF,QAAM,WAAWA,MACZ,MAAM,kBAAkB,EACxB,IAAI,CAAAE,UAAQ,gBAAgB,KAAK,WAAS,MAAM,OAAOA,MAAK,EAAE,CAAC,EAC/D,OAAO,CAAAA,UAAQA,KAAI;AAGxB,MAAI,SAAS,WAAW,EAAG,QAAO,EAAE,QAAQ,OAAO;AAEnD,QAAM,kBAAkB,SAAS,KAAK,QAAQ;AAC9C,QAAM,YAAY,uBAAuB,UAAU,UAAU,WAAW,eAAe;AAEvF,QAAM,YAAY,SAAS,CAAC,EAAE,KAAK;AAEnC,QAAM,qBAAqB,UAAU,YAAY,UAAU;AAC3D,QAAM,uBAAuB,UAAU,aAAa,UAAU;AAE9D,QAAM,YAAY,UAAU,QAAQ;AACpC,QAAM,aAAa,UAAU,SAAS;AAEtC,QAAM,UAAU,OAAO,cAAc,eAAe,aAAa,IAAI,IAAI;AACzE,QAAM,cAAc,SAAS,KAAK,WAAS,MAAM,oBAAoB,MAAM,UAAU,IAAI,IACnF,KACA;AACN,QAAM,oBAAoB,SAAS,SAAS,UAAU;AACtD,QAAM,cAAc,eAAe,iBAAiB,SAAS;AAG7D,MAAI,gBAAgB,GAAG;AACnB,aAAS,QAAQ,CAAAA,UAAQ;AACrB,YAAM,SAASA,MAAK,KAAK,QAAQ,SAAS;AAC1C,gBAAU;AACV,gBAAU,SAASA,MAAK;AAAA,IAC5B,CAAC;AAAA,EACL,OAEK;AACD,aAAS,KAAK,KAAK,oBAAoB,WAAW,IAAI;AACtD,aAAS;AAAA,EACb;AAEA,SAAO,EAAE,QAAQ,OAAO;AAC5B;AAEA,IAAM,iCAAiC,CAAAF,UAAQ;AAC3C,QAAM,SAASA,MAAK,IAAI,iBAAiB;AACzC,QAAM,eAAe,SAASA,MAAK,MAAM,WAAW,EAAE,KAAK;AAC3D,QAAM,cAAc,WAAW,IAAI,OAAO;AAE1C,SAAO;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AACJ;AAEA,IAAM,kBAAkB,CAACA,OAAM,UAAU;AACrC,QAAM,eAAeA,MAAK,MAAM,mBAAmB;AACnD,QAAM,gBAAgBA,MAAK,MAAM,oBAAoB;AACrD,QAAM,aAAaA,MAAK,MAAM,iBAAiB;AAC/C,MAAI,WAAWA,MAAK,MAAM,eAAe;AAGzC,QAAM,mBAAmB,MAAM;AAG/B,MAAI,CAAC,iBAAiB,mBAAmB,GAAG;AACxC,IAAAA,MAAK,SAAS,uBAAuB;AAAA,MACjC,QAAQ;AAAA,MACR,OAAO,eAAe,WAAW,GAAG,WAAW;AAAA,IACnD,CAAC;AACD,WAAO;AAAA,EACX;AAGA,aAAW,gBAAgB,WAAW;AAEtC,MAAI,CAAC,iBAAiB,cAAc;AAEhC,WAAO;AAAA,EACX;AAGA,QAAM,cAAc,MAAM,QAAQ;AAClC,MAAI,eAAe,aAAa,mBAAmB,UAAU;AACzD,IAAAA,MAAK,SAAS,uBAAuB;AAAA,MACjC,QAAQ;AAAA,MACR,OAAO,eAAe,WAAW,GAAG,WAAW;AAAA,IACnD,CAAC;AACD,WAAO;AAAA,EACX;AAEA,SAAO;AACX;AAEA,IAAM,eAAe,CAAC+B,OAAM,UAAU,aAAa;AAC/C,QAAM,WAAWA,MAAK,WAAW,CAAC;AAClC,SAAO,uBAAuB,UAAU,UAAU;AAAA,IAC9C,MAAM,SAAS,YAAY,SAAS,KAAK,QAAQ;AAAA,IACjD,KACI,SAAS,YACRA,MAAK,KAAK,MAAM,MAAMA,MAAK,KAAK,QAAQ,YAAYA,MAAK,KAAK,QAAQ;AAAA,EAC/E,CAAC;AACL;AAKA,IAAM,aAAa,CAAA/B,UAAQ;AACvB,QAAM,YAAYA,MAAK,MAAM,gBAAgB;AAC7C,QAAM,aAAaA,MAAK,MAAM,cAAc;AAC5C,QAAM,UAAU,aAAa,CAAC;AAC9B,MAAI,WAAW,CAACA,MAAK,IAAI,QAAQ;AAC7B,UAAM,SAAS;AAAA,MACXA,MAAK;AAAA,MACL,WAAS;AAEL,cAAM,iBAAiBA,MAAK,MAAM,sBAAsB,MAAM,MAAM;AAGpE,cAAM,iBAAiBA,MAAK,MAAM,qBAAqB;AACvD,eAAO,iBACD,MAAM;AAAA,UACF,CAAAE,UACI,aAAa,qBAAqBA,OAAM;AAAA,YACpC,OAAOF,MAAK;AAAA,UAChB,CAAC,EAAE,MAAM,YAAU,WAAW,IAAI,KAAK,eAAeE,KAAI;AAAA,QAClE,IACA;AAAA,MACV;AAAA,MACA;AAAA,QACI,aAAa,WAAS;AAClB,gBAAM,eAAeF,MAAK,MAAM,mBAAmB;AACnD,iBAAO,MAAM,OAAO,CAAAE,UAAQ;AACxB,gBAAI,OAAOA,KAAI,GAAG;AACd,qBAAO,CAAC,aAAa,SAASA,MAAK,KAAK,YAAY,CAAC;AAAA,YACzD;AACA,mBAAO;AAAA,UACX,CAAC;AAAA,QACL;AAAA,QACA,oBAAoBF,MAAK,MAAM,kBAAkB;AAAA,QACjD,uBAAuBA,MAAK,MAAM,qBAAqB;AAAA,MAC3D;AAAA,IACJ;AAEA,WAAO,SAAS,CAAC,OAAO,aAAa;AAEjC,YAAM+B,QAAO/B,MAAK,IAAI,KAAK,WAAW,CAAC;AACvC,YAAM,kBAAkB+B,MAAK,WAAW,OAAO,WAAS,MAAM,KAAK,QAAQ,MAAM;AACjF,YAAM,WAAW/B,MACZ,MAAM,kBAAkB,EACxB,IAAI,CAAAE,UAAQ,gBAAgB,KAAK,WAAS,MAAM,OAAOA,MAAK,EAAE,CAAC,EAC/D,OAAO,CAAAA,UAAQA,KAAI;AAExB,uBAAiB,aAAa,OAAO,EAAE,UAAUF,MAAK,SAAS,CAAC,EAAE,KAAK,WAAS;AAE5E,YAAI,gBAAgBA,OAAM,KAAK,EAAG,QAAO;AAGzC,QAAAA,MAAK,SAAS,aAAa;AAAA,UACvB,OAAO;AAAA,UACP,OAAO,aAAaA,MAAK,IAAI,MAAM,UAAU,QAAQ;AAAA,UACrD,mBAAmB,kBAAkB;AAAA,QACzC,CAAC;AAAA,MACL,CAAC;AAED,MAAAA,MAAK,SAAS,YAAY,EAAE,SAAS,CAAC;AAEtC,MAAAA,MAAK,SAAS,gBAAgB,EAAE,SAAS,CAAC;AAAA,IAC9C;AAEA,WAAO,cAAc,cAAY;AAC7B,MAAAA,MAAK,SAAS,kBAAkB,EAAE,SAAS,CAAC;AAAA,IAChD;AAEA,WAAO,SAAS,SAAS,cAAY;AACjC,MAAAA,MAAK,SAAS,YAAY,EAAE,SAAS,CAAC;AAAA,IAC1C,CAAC;AAED,WAAO,YAAY,cAAY;AAC3B,MAAAA,MAAK,SAAS,gBAAgB,EAAE,SAAS,CAAC;AAAA,IAC9C;AAEA,IAAAA,MAAK,IAAI,SAAS;AAElB,IAAAA,MAAK,IAAI,OAAOA,MAAK,gBAAgBA,MAAK,gBAAgB,IAAI,CAAC;AAAA,EACnE,WAAW,CAAC,WAAWA,MAAK,IAAI,QAAQ;AACpC,IAAAA,MAAK,IAAI,OAAO,QAAQ;AACxB,IAAAA,MAAK,IAAI,SAAS;AAClB,IAAAA,MAAK,gBAAgBA,MAAK,IAAI,IAAI;AAAA,EACtC;AACJ;AAKA,IAAM,eAAe,CAACA,OAAM,UAAU;AAClC,QAAM,YAAYA,MAAK,MAAM,kBAAkB;AAC/C,QAAM,aAAaA,MAAK,MAAM,cAAc;AAC5C,QAAM,UAAU,aAAa,CAAC;AAC9B,MAAI,WAAW,CAACA,MAAK,IAAI,SAAS;AAC9B,IAAAA,MAAK,IAAI,UAAUA,MAAK;AAAA,MACpBA,MAAK,gBAAgB,SAAS;AAAA,QAC1B,GAAG;AAAA,QACH,QAAQ,WAAS;AACb,2BAAiB,aAAa,OAAO;AAAA,YACjC,UAAUA,MAAK;AAAA,UACnB,CAAC,EAAE,KAAK,WAAS;AAEb,gBAAI,gBAAgBA,OAAM,KAAK,EAAG,QAAO;AAGzC,YAAAA,MAAK,SAAS,aAAa;AAAA,cACvB,OAAO;AAAA,cACP,OAAO;AAAA,cACP,mBAAmB,kBAAkB;AAAA,YACzC,CAAC;AAAA,UACL,CAAC;AAAA,QACL;AAAA,MACJ,CAAC;AAAA,MACD;AAAA,IACJ;AAAA,EACJ,WAAW,CAAC,WAAWA,MAAK,IAAI,SAAS;AACrC,IAAAA,MAAK,gBAAgBA,MAAK,IAAI,OAAO;AACrC,IAAAA,MAAK,IAAI,UAAU;AAAA,EACvB;AACJ;AAKA,IAAM,cAAc,CAAAA,UAAQ;AACxB,QAAM,YAAYA,MAAK,MAAM,iBAAiB;AAC9C,QAAM,aAAaA,MAAK,MAAM,cAAc;AAC5C,QAAM,UAAU,aAAa,CAAC;AAC9B,MAAI,WAAW,CAACA,MAAK,IAAI,QAAQ;AAC7B,IAAAA,MAAK,IAAI,SAAS,aAAa;AAC/B,IAAAA,MAAK,IAAI,OAAO,SAAS,WAAS;AAC9B,uBAAiB,aAAa,OAAO,EAAE,UAAUA,MAAK,SAAS,CAAC,EAAE,KAAK,WAAS;AAE5E,YAAI,gBAAgBA,OAAM,KAAK,EAAG,QAAO;AAGzC,QAAAA,MAAK,SAAS,aAAa;AAAA,UACvB,OAAO;AAAA,UACP,OAAO;AAAA,UACP,mBAAmB,kBAAkB;AAAA,QACzC,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AAAA,EACJ,WAAW,CAAC,WAAWA,MAAK,IAAI,QAAQ;AACpC,IAAAA,MAAK,IAAI,OAAO,QAAQ;AACxB,IAAAA,MAAK,IAAI,SAAS;AAAA,EACtB;AACJ;AAKA,IAAM,UAAU,YAAY;AAAA,EACxB,sBAAsB,CAAC,EAAE,MAAAA,OAAM,MAAM,MAAM;AACvC,iBAAaA,OAAM,KAAK;AAAA,EAC5B;AAAA,EACA,oBAAoB,CAAC,EAAE,MAAAA,MAAK,MAAM;AAC9B,eAAWA,KAAI;AAAA,EACnB;AAAA,EACA,qBAAqB,CAAC,EAAE,MAAAA,MAAK,MAAM;AAC/B,gBAAYA,KAAI;AAAA,EACpB;AAAA,EACA,kBAAkB,CAAC,EAAE,MAAAA,OAAM,MAAM,MAAM;AACnC,eAAWA,KAAI;AACf,gBAAYA,KAAI;AAChB,iBAAaA,OAAM,KAAK;AACxB,UAAM,aAAaA,MAAK,MAAM,cAAc;AAC5C,QAAI,YAAY;AACZ,MAAAA,MAAK,QAAQ,QAAQ,WAAW;AAAA,IACpC,OAAO;AAEH,MAAAA,MAAK,QAAQ,gBAAgB,eAAe;AAAA,IAChD;AAAA,EACJ;AACJ,CAAC;AAED,IAAM,OAAO,WAAW;AAAA,EACpB,MAAM;AAAA,EACN,MAAM,CAAC,EAAE,MAAAA,MAAK,MAAM;AAChB,QAAIA,MAAK,IAAI,SAAS;AAClB,MAAAA,MAAK,IAAI,gBAAgBA,MAAK,IAAI,QAAQ;AAAA,IAC9C;AAAA,EACJ;AAAA,EACA,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,SAAS,CAAC,EAAE,MAAAA,MAAK,MAAM;AACnB,QAAIA,MAAK,IAAI,QAAQ;AACjB,MAAAA,MAAK,IAAI,OAAO,QAAQ;AAAA,IAC5B;AACA,QAAIA,MAAK,IAAI,QAAQ;AACjB,MAAAA,MAAK,IAAI,OAAO,QAAQ;AAAA,IAC5B;AACA,IAAAA,MAAK,QAAQ,oBAAoB,aAAa,OAAO;AACrD,IAAAA,MAAK,QAAQ,oBAAoB,gBAAgB,OAAO;AAAA,EAC5D;AAAA,EACA,QAAQ;AAAA,IACJ,QAAQ,CAAC,QAAQ;AAAA,EACrB;AACJ,CAAC;AAGD,IAAM,YAAY,CAAC,iBAAiB,CAAC,MAAM;AAEvC,MAAI,kBAAkB;AAGtB,QAAMiC,kBAAiB,WAAW;AAGlC,QAAM,QAAQ;AAAA;AAAA,IAEV,mBAAmBA,eAAc;AAAA;AAAA,IAGjC,CAAC,SAAS,oBAAoBA,eAAc,CAAC;AAAA;AAAA,IAG7C,CAAC,SAAS,oBAAoBA,eAAc,CAAC;AAAA,EACjD;AAGA,QAAM,SAAS,eAAe,EAAE,SAAS,eAAe,CAAC;AAGzD,QAAM,oBAAoB,MAAM;AAC5B,QAAI,SAAS,OAAQ;AACrB,UAAM,SAAS,MAAM;AAAA,EACzB;AACA,WAAS,iBAAiB,oBAAoB,iBAAiB;AAG/D,MAAI,kBAAkB;AACtB,MAAI,aAAa;AACjB,MAAI,yBAAyB;AAC7B,MAAI,qBAAqB;AACzB,MAAI,qBAAqB;AACzB,QAAM,gBAAgB,MAAM;AACxB,QAAI,CAAC,YAAY;AACb,mBAAa;AAAA,IACjB;AACA,iBAAa,eAAe;AAC5B,sBAAkB,WAAW,MAAM;AAC/B,mBAAa;AACb,2BAAqB;AACrB,2BAAqB;AACrB,UAAI,wBAAwB;AACxB,iCAAyB;AACzB,cAAM,SAAS,iBAAiB;AAAA,MACpC;AAAA,IACJ,GAAG,GAAG;AAAA,EACV;AACA,SAAO,iBAAiB,UAAU,aAAa;AAG/C,QAAM,OAAO,KAAK,OAAO,EAAE,IAAI,YAAY,EAAE,CAAC;AAK9C,MAAI,YAAY;AAChB,MAAI,WAAW;AAEf,QAAM,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOjB,OAAO,MAAM;AAGT,UAAI,YAAY;AACZ,6BAAqB,OAAO;AAC5B,YAAI,CAAC,oBAAoB;AACrB,+BAAqB;AAAA,QACzB;AAEA,YAAI,CAAC,0BAA0B,uBAAuB,oBAAoB;AACtE,gBAAM,SAAS,kBAAkB;AACjC,mCAAyB;AAAA,QAC7B;AAAA,MACJ;AAEA,UAAI,YAAY,WAAW;AAEvB,oBAAY,KAAK,QAAQ,iBAAiB;AAAA,MAC9C;AAGA,UAAI,UAAW;AAGf,WAAK,MAAM;AAGX,iBAAW,KAAK,KAAK,QAAQ;AAAA,IACjC;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,QAAQ,QAAM;AAEV,YAAMtC,WAAU,MACX,mBAAmB,EAGnB,OAAO,YAAU,CAAC,QAAQ,KAAK,OAAO,IAAI,CAAC;AAGhD,UAAI,aAAa,CAACA,SAAQ,OAAQ;AAGlC,2BAAqBA,QAAO;AAG5B,kBAAY,KAAK,OAAO,IAAIA,UAAS,sBAAsB;AAG3D,0BAAoB,MAAM,MAAM,WAAW,CAAC;AAG5C,UAAI,WAAW;AACX,cAAM,qBAAqB;AAAA,MAC/B;AAAA,IACJ;AAAA,EACJ;AAKA,QAAM,cAAc,CAAAR,UAAQ,CAAAqB,UAAQ;AAEhC,UAAM,QAAQ;AAAA,MACV,MAAMrB;AAAA,IACV;AAGA,QAAI,CAACqB,OAAM;AACP,aAAO;AAAA,IACX;AAGA,QAAIA,MAAK,eAAe,OAAO,GAAG;AAC9B,YAAM,QAAQA,MAAK,QAAQ,EAAE,GAAGA,MAAK,MAAM,IAAI;AAAA,IACnD;AAEA,QAAIA,MAAK,QAAQ;AACb,YAAM,SAAS,EAAE,GAAGA,MAAK,OAAO;AAAA,IACpC;AAEA,QAAIA,MAAK,MAAM;AACX,YAAM,SAASA,MAAK;AAAA,IACxB;AAGA,QAAIA,MAAK,QAAQ;AACb,YAAM,OAAOA,MAAK;AAAA,IACtB,WAAWA,MAAK,QAAQA,MAAK,IAAI;AAC7B,YAAMN,QAAOM,MAAK,OAAOA,MAAK,OAAO,MAAM,MAAM,YAAYA,MAAK,EAAE;AACpE,YAAM,OAAON,QAAO,cAAcA,KAAI,IAAI;AAAA,IAC9C;AAGA,QAAIM,MAAK,OAAO;AACZ,YAAM,QAAQA,MAAK,MAAM,IAAI,aAAa;AAAA,IAC9C;AAGA,QAAI,WAAW,KAAKrB,KAAI,GAAG;AACvB,YAAM,WAAWqB,MAAK;AAAA,IAC1B;AAGA,QAAIA,MAAK,eAAe,QAAQ,KAAKA,MAAK,eAAe,QAAQ,GAAG;AAChE,YAAM,SAASA,MAAK;AACpB,YAAM,SAASA,MAAK;AAAA,IACxB;AAEA,WAAO;AAAA,EACX;AAEA,QAAM,cAAc;AAAA,IAChB,aAAa,YAAY,SAAS;AAAA,IAElC,UAAU,YAAY,MAAM;AAAA,IAE5B,qBAAqB,YAAY,SAAS;AAAA,IAE1C,eAAe,YAAY,UAAU;AAAA,IACrC,qBAAqB,YAAY,cAAc;AAAA,IAC/C,+BAA+B,YAAY,iBAAiB;AAAA,IAC5D,eAAe,YAAY,SAAS;AAAA,IAEpC,wBAAwB,CAAC,YAAY,OAAO,GAAG,YAAY,SAAS,CAAC;AAAA,IAErE,2BAA2B,CAAC,YAAY,OAAO,GAAG,YAAY,SAAS,CAAC;AAAA,IAExE,6BAA6B,CAAC,YAAY,OAAO,GAAG,YAAY,YAAY,CAAC;AAAA,IAE7E,oBAAoB,YAAY,aAAa;AAAA,IAE7C,2BAA2B,YAAY,kBAAkB;AAAA,IACzD,kCAAkC,YAAY,qBAAqB;AAAA,IACnE,2BAA2B,YAAY,kBAAkB;AAAA,IACzD,8BAA8B,YAAY,aAAa;AAAA,IACvD,kCAAkC,YAAY,cAAc;AAAA,IAC5D,4BAA4B,YAAY,mBAAmB;AAAA,IAE3D,iCAAiC,CAAC,YAAY,OAAO,GAAG,YAAY,aAAa,CAAC;AAAA,IAElF,iBAAiB,YAAY,YAAY;AAAA,IAEzC,kBAAkB,YAAY,aAAa;AAAA,IAE3C,mBAAmB,YAAY,cAAc;AAAA,IAE7C,mBAAmB,YAAY,cAAc;AAAA,EACjD;AAEA,QAAM,cAAc,WAAS;AAEzB,UAAM,SAAS,EAAE,MAAM,SAAS,GAAG,MAAM;AACzC,WAAO,OAAO;AACd,SAAK,QAAQ;AAAA,MACT,IAAI,YAAY,YAAY,MAAM,IAAI,IAAI;AAAA;AAAA,QAEtC;AAAA;AAAA,QAGA,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,UAAU;AAAA;AAAA,MACd,CAAC;AAAA,IACL;AAGA,UAAM,SAAS,CAAC;AAGhB,QAAI,MAAM,eAAe,OAAO,GAAG;AAC/B,aAAO,KAAK,MAAM,KAAK;AAAA,IAC3B;AAGA,QAAI,MAAM,eAAe,MAAM,GAAG;AAC9B,aAAO,KAAK,MAAM,IAAI;AAAA,IAC1B;AAGA,UAAM,WAAW,CAAC,QAAQ,SAAS,MAAM;AACzC,WAAO,KAAK,KAAK,EACZ,OAAO,SAAO,CAAC,SAAS,SAAS,GAAG,CAAC,EACrC,QAAQ,SAAO,OAAO,KAAK,MAAM,GAAG,CAAC,CAAC;AAG3C,YAAQ,KAAK,MAAM,MAAM,GAAG,MAAM;AAGlC,UAAM,UAAU,MAAM,MAAM,SAAS,MAAM,KAAK,YAAY,CAAC,EAAE;AAC/D,QAAI,SAAS;AACT,cAAQ,GAAG,MAAM;AAAA,IACrB;AAAA,EACJ;AAEA,QAAM,uBAAuB,CAAAb,aAAW;AACpC,QAAI,CAACA,SAAQ,OAAQ;AACrB,IAAAA,SACK,OAAO,YAAU,YAAY,OAAO,IAAI,CAAC,EACzC,QAAQ,YAAU;AACf,YAAM,SAAS,YAAY,OAAO,IAAI;AACtC,OAAC,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAC,MAAM,GAAG,QAAQ,CAAAuC,WAAS;AAEzD,YAAI,OAAO,SAAS,iBAAiB;AACjC,sBAAYA,OAAM,OAAO,IAAI,CAAC;AAAA,QAClC,OAAO;AACH,qBAAW,MAAM;AACb,wBAAYA,OAAM,OAAO,IAAI,CAAC;AAAA,UAClC,GAAG,CAAC;AAAA,QACR;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAAA,EACT;AAKA,QAAMC,cAAa,aAAW,MAAM,SAAS,eAAe,EAAE,QAAQ,CAAC;AAEvE,QAAM,UAAU,WAAS,MAAM,MAAM,mBAAmB,KAAK;AAE7D,QAAM,cAAc,WAChB,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC7B,UAAM,SAAS,wBAAwB;AAAA,MACnC;AAAA,MACA,SAAS,CAAAjC,UAAQ;AACb,gBAAQA,KAAI;AAAA,MAChB;AAAA,MACA,SAAS,CAAAE,WAAS;AACd,eAAOA,MAAK;AAAA,MAChB;AAAA,IACJ,CAAC;AAAA,EACL,CAAC;AAEL,QAAM,UAAU,CAAC,QAAQ,UAAU,CAAC,MAChC,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC7B,aAAS,CAAC,EAAE,QAAQ,QAAQ,CAAC,GAAG,EAAE,OAAO,QAAQ,MAAM,CAAC,EACnD,KAAK,WAAS,QAAQ,SAAS,MAAM,CAAC,CAAC,CAAC,EACxC,MAAM,MAAM;AAAA,EACrB,CAAC;AAEL,QAAM,iBAAiB,SAAO,IAAI,QAAQ,IAAI;AAE9C,QAAM,aAAa,CAAC,OAAO,YAAY;AAEnC,QAAI,OAAO,UAAU,YAAY,CAAC,eAAe,KAAK,KAAK,CAAC,SAAS;AACjE,gBAAU;AACV,cAAQ;AAAA,IACZ;AAGA,UAAM,SAAS,eAAe,EAAE,GAAG,SAAS,MAAM,CAAC;AAGnD,WAAO,MAAM,MAAM,mBAAmB,KAAK,MAAM;AAAA,EACrD;AAEA,QAAM,WAAW,IAAI,SACjB,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC7B,UAAM,UAAU,CAAC;AACjB,UAAM,UAAU,CAAC;AAGjB,QAAI,QAAQ,KAAK,CAAC,CAAC,GAAG;AAClB,cAAQ,KAAK,MAAM,SAAS,KAAK,CAAC,CAAC;AACnC,aAAO,OAAO,SAAS,KAAK,CAAC,KAAK,CAAC,CAAC;AAAA,IACxC,OAAO;AAEH,YAAM,eAAe,KAAK,KAAK,SAAS,CAAC;AACzC,UAAI,OAAO,iBAAiB,YAAY,EAAE,wBAAwB,OAAO;AACrE,eAAO,OAAO,SAAS,KAAK,IAAI,CAAC;AAAA,MACrC;AAGA,cAAQ,KAAK,GAAG,IAAI;AAAA,IACxB;AAEA,UAAM,SAAS,aAAa;AAAA,MACxB,OAAO;AAAA,MACP,OAAO,QAAQ;AAAA,MACf,mBAAmB,kBAAkB;AAAA,MACrC,SAAS;AAAA,MACT,SAAS;AAAA,IACb,CAAC;AAAA,EACL,CAAC;AAEL,QAAMgC,YAAW,MAAM,MAAM,MAAM,kBAAkB;AAErD,QAAM,cAAc,WAChB,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC7B,UAAM,SAAS,2BAA2B;AAAA,MACtC;AAAA,MACA,SAAS,CAAAlC,UAAQ;AACb,gBAAQA,KAAI;AAAA,MAChB;AAAA,MACA,SAAS,CAAAE,WAAS;AACd,eAAOA,MAAK;AAAA,MAChB;AAAA,IACJ,CAAC;AAAA,EACL,CAAC;AAEL,QAAM,eAAe,IAAI,SAAS;AAC9B,UAAMiC,WAAU,MAAM,QAAQ,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI;AACnD,UAAM,QAAQA,SAAQ,SAASA,WAAUD,UAAS;AAClD,WAAO,QAAQ,IAAI,MAAM,IAAI,WAAW,CAAC;AAAA,EAC7C;AAEA,QAAM,eAAe,IAAI,SAAS;AAC9B,UAAMC,WAAU,MAAM,QAAQ,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI;AACnD,QAAI,CAACA,SAAQ,QAAQ;AACjB,YAAM,QAAQD,UAAS,EAAE;AAAA,QACrB,CAAAlC,UACI,EAAEA,MAAK,WAAW,WAAW,QAAQA,MAAK,WAAW,WAAW,UAChEA,MAAK,WAAW,WAAW,cAC3BA,MAAK,WAAW,WAAW,uBAC3BA,MAAK,WAAW,WAAW;AAAA,MACnC;AACA,aAAO,QAAQ,IAAI,MAAM,IAAI,WAAW,CAAC;AAAA,IAC7C;AACA,WAAO,QAAQ,IAAImC,SAAQ,IAAI,WAAW,CAAC;AAAA,EAC/C;AAEA,QAAM,cAAc,IAAI,SAAS;AAC7B,UAAMA,WAAU,MAAM,QAAQ,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI;AAEnD,QAAI;AACJ,QAAI,OAAOA,SAAQA,SAAQ,SAAS,CAAC,MAAM,UAAU;AACjD,gBAAUA,SAAQ,IAAI;AAAA,IAC1B,WAAW,MAAM,QAAQ,KAAK,CAAC,CAAC,GAAG;AAC/B,gBAAU,KAAK,CAAC;AAAA,IACpB;AAEA,UAAM,QAAQD,UAAS;AAEvB,QAAI,CAACC,SAAQ,OAAQ,QAAO,QAAQ,IAAI,MAAM,IAAI,CAAA9B,UAAQ,WAAWA,OAAM,OAAO,CAAC,CAAC;AAGpF,UAAM,gBAAgB8B,SACjB,IAAI,WAAU,SAAS,KAAK,IAAK,MAAM,KAAK,IAAI,MAAM,KAAK,EAAE,KAAK,OAAQ,KAAM,EAChF,OAAO,WAAS,KAAK;AAE1B,WAAO,cAAc,IAAI,OAAK,WAAW,GAAG,OAAO,CAAC;AAAA,EACxD;AAEA,QAAM,UAAU;AAAA;AAAA,IAEZ,GAAG,GAAG;AAAA;AAAA,IAGN,GAAG;AAAA;AAAA,IAGH,GAAG,gBAAgB,OAAOJ,eAAc;AAAA;AAAA;AAAA;AAAA;AAAA,IAMxC,YAAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA;AAAA;AAAA;AAAA;AAAA,IAKA,UAAU,CAAC,OAAO,UAAU,MAAM,SAAS,aAAa,EAAE,OAAO,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,IAKxE,UAAAC;AAAA;AAAA;AAAA;AAAA,IAKA;AAAA;AAAA;AAAA;AAAA,IAKA;AAAA;AAAA;AAAA;AAAA,IAKA;AAAA;AAAA;AAAA;AAAA,IAKA,MAAM,aAAW,MAAM,SAAS,QAAQ,EAAE,QAAQ,CAAC;AAAA;AAAA;AAAA;AAAA,IAKnD,QAAQ,MAAM;AAEV,UAAI,QAAQ,KAAK,QAAQ,cAAc,kBAAkB;AACzD,UAAI,OAAO;AACP,cAAM,MAAM;AAAA,MAChB;AAAA,IACJ;AAAA;AAAA;AAAA;AAAA,IAKA,SAAS,MAAM;AAEX,cAAQ,KAAK,WAAW,KAAK,OAAO;AAIpC,YAAM,SAAS,WAAW;AAG1B,WAAK,SAAS;AAGd,aAAO,oBAAoB,UAAU,aAAa;AAGlD,eAAS,oBAAoB,oBAAoB,iBAAiB;AAGlE,YAAM,SAAS,aAAa;AAAA,IAChC;AAAA;AAAA;AAAA;AAAA,IAKA,cAAc,aAAW,aAAa,KAAK,SAAS,OAAO;AAAA;AAAA;AAAA;AAAA,IAK3D,aAAa,aAAW,YAAY,KAAK,SAAS,OAAO;AAAA;AAAA;AAAA;AAAA,IAKzD,UAAU,aAAW,QAAQ,YAAY,KAAK,OAAO;AAAA;AAAA;AAAA;AAAA,IAKrD,gBAAgB,aAAW;AAEvB,mBAAa,KAAK,SAAS,OAAO;AAGlC,cAAQ,WAAW,YAAY,OAAO;AAGtC,wBAAkB;AAAA,IACtB;AAAA;AAAA;AAAA;AAAA,IAKA,gBAAgB,MAAM;AAClB,UAAI,CAAC,iBAAiB;AAClB;AAAA,MACJ;AAGA,kBAAY,iBAAiB,KAAK,OAAO;AAGzC,WAAK,QAAQ,WAAW,YAAY,KAAK,OAAO;AAGhD,wBAAkB;AAAA,IACtB;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,cAAc,aAAW,KAAK,YAAY,WAAW,oBAAoB;AAAA;AAAA;AAAA;AAAA,IAKzE,SAAS;AAAA,MACL,KAAK,MAAM,KAAK;AAAA,IACpB;AAAA;AAAA;AAAA;AAAA,IAKA,QAAQ;AAAA,MACJ,KAAK,MAAM,MAAM,MAAM,YAAY;AAAA,IACvC;AAAA,EACJ;AAGA,QAAM,SAAS,UAAU;AAGzB,SAAO,aAAa,OAAO;AAC/B;AAEA,IAAM,kBAAkB,CAAC,gBAAgB,CAAC,MAAM;AAE5C,QAAMH,kBAAiB,CAAC;AACxB,QAAM,WAAW,GAAG,CAAC,KAAK,UAAU;AAChC,IAAAA,gBAAe,GAAG,IAAI,MAAM,CAAC;AAAA,EACjC,CAAC;AAGD,QAAM,MAAM,UAAU;AAAA;AAAA,IAElB,GAAGA;AAAA;AAAA,IAGH,GAAG;AAAA,EACP,CAAC;AAGD,SAAO;AACX;AAEA,IAAM,uBAAuB,YAAU,OAAO,OAAO,CAAC,EAAE,YAAY,IAAI,OAAO,MAAM,CAAC;AAEtF,IAAM,8BAA8B,mBAAiB,SAAS,cAAc,QAAQ,UAAU,EAAE,CAAC;AAEjG,IAAM,YAAY,CAAC,QAAQ,gBAAgB;AAEvC,QAAM,aAAa,CAAC,UAAU,YAAY;AACtC,UAAM,QAAQ,CAAC,UAAU,UAAU;AAE/B,YAAM,iBAAiB,IAAI,OAAO,QAAQ;AAG1C,YAAM,UAAU,eAAe,KAAK,QAAQ;AAG5C,UAAI,CAAC,SAAS;AACV;AAAA,MACJ;AAGA,aAAO,OAAO,QAAQ;AAGtB,UAAI,YAAY,OAAO;AACnB;AAAA,MACJ;AAGA,UAAI,SAAS,OAAO,GAAG;AACnB,eAAO,OAAO,IAAI;AAClB;AAAA,MACJ;AAGA,YAAM,QAAQ,QAAQ;AACtB,UAAI,SAAS,OAAO,KAAK,CAAC,OAAO,KAAK,GAAG;AACrC,eAAO,KAAK,IAAI,CAAC;AAAA,MACrB;AAEA,aAAO,KAAK,EAAE,qBAAqB,SAAS,QAAQ,gBAAgB,EAAE,CAAC,CAAC,IAAI;AAAA,IAChF,CAAC;AAGD,QAAI,QAAQ,SAAS;AACjB,gBAAU,OAAO,QAAQ,KAAK,GAAG,QAAQ,OAAO;AAAA,IACpD;AAAA,EACJ,CAAC;AACL;AAEA,IAAM,wBAAwB,CAAC,MAAM,mBAAmB,CAAC,MAAM;AAE3D,QAAM,aAAa,CAAC;AACpB,QAAM,KAAK,YAAY,WAAS;AAC5B,eAAW,KAAK,KAAK,WAAW,KAAK,CAAC;AAAA,EAC1C,CAAC;AAED,QAAM,SAAS,WACV,OAAO,eAAa,UAAU,IAAI,EAClC,OAAO,CAAC,KAAK,cAAc;AACxB,UAAM,QAAQ,KAAK,MAAM,UAAU,IAAI;AAEvC,QAAI,4BAA4B,UAAU,IAAI,CAAC,IAC3C,UAAU,UAAU,OAAO,OAAO;AACtC,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AAGT,YAAU,QAAQ,gBAAgB;AAElC,SAAO;AACX;AAEA,IAAM,qBAAqB,CAAC,SAAS,UAAU,CAAC,MAAM;AAElD,QAAM,mBAAmB;AAAA;AAAA,IAErB,WAAW;AAAA,IACX,cAAc;AAAA,IACd,aAAa;AAAA,IACb,qBAAqB;AAAA;AAAA,IAGrB,WAAW;AAAA,MACP,OAAO;AAAA,MACP,SAAS;AAAA,QACL,YAAY;AAAA,UACR,OAAO;AAAA,QACX;AAAA,QACA,WAAW;AAAA,UACP,OAAO;AAAA,QACX;AAAA,QACA,UAAU;AAAA,UACN,OAAO;AAAA,QACX;AAAA,QACA,YAAY;AAAA,UACR,OAAO;AAAA,QACX;AAAA,QACA,SAAS;AAAA,UACL,OAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AAAA;AAAA,IAGA,UAAU;AAAA,IACV,WAAW;AAAA,EACf;AAGA,eAAa,+BAA+B,gBAAgB;AAG5D,QAAM,gBAAgB;AAAA,IAClB,GAAG;AAAA,EACP;AAEA,QAAM,mBAAmB;AAAA,IACrB,QAAQ,aAAa,aAAa,QAAQ,cAAc,kBAAkB,IAAI;AAAA,IAC9E;AAAA,EACJ;AAGA,SAAO,KAAK,gBAAgB,EAAE,QAAQ,SAAO;AACzC,QAAI,SAAS,iBAAiB,GAAG,CAAC,GAAG;AACjC,UAAI,CAAC,SAAS,cAAc,GAAG,CAAC,GAAG;AAC/B,sBAAc,GAAG,IAAI,CAAC;AAAA,MAC1B;AACA,aAAO,OAAO,cAAc,GAAG,GAAG,iBAAiB,GAAG,CAAC;AAAA,IAC3D,OAAO;AACH,oBAAc,GAAG,IAAI,iBAAiB,GAAG;AAAA,IAC7C;AAAA,EACJ,CAAC;AAID,gBAAc,SAAS,QAAQ,SAAS,CAAC,GAAG;AAAA,IACxC,MAAM,KAAK,QAAQ,iBAAiB,wBAAwB,CAAC,EAAE,IAAI,YAAU;AAAA,MACzE,QAAQ,MAAM;AAAA,MACd,SAAS;AAAA,QACL,MAAM,MAAM,QAAQ;AAAA,MACxB;AAAA,IACJ,EAAE;AAAA,EACN;AAGA,QAAM,MAAM,gBAAgB,aAAa;AAGzC,MAAI,QAAQ,OAAO;AACf,UAAM,KAAK,QAAQ,KAAK,EAAE,QAAQ,CAAA1B,UAAQ;AACtC,UAAI,QAAQA,KAAI;AAAA,IACpB,CAAC;AAAA,EACL;AAGA,MAAI,eAAe,OAAO;AAG1B,SAAO;AACX;AAGA,IAAM,cAAc,IAAI,SACpB,OAAO,KAAK,CAAC,CAAC,IAAI,mBAAmB,GAAG,IAAI,IAAI,gBAAgB,GAAG,IAAI;AAE3E,IAAM,kBAAkB,CAAC,QAAQ,SAAS,QAAQ;AAElD,IAAM,eAAe,SAAO;AACxB,QAAM,MAAM,CAAC;AAEb,+BAA6B,KAAK,KAAK,eAAe;AAEtD,SAAO;AACX;AAOA,IAAM,kBAAkB,CAAC,QAAQ,iBAC7B,OAAO,QAAQ,sBAAsB,CAAC,OAAO,UAAU,aAAa,KAAK,CAAC;AAE9E,IAAM,eAAe,CAAAjB,QAAM;AACvB,QAAM,aAAa,IAAI,KAAK,CAAC,KAAKA,IAAG,SAAS,GAAG,KAAK,GAAG;AAAA,IACrD,MAAM;AAAA,EACV,CAAC;AACD,QAAM,YAAY,IAAI,gBAAgB,UAAU;AAChD,QAAM,SAAS,IAAI,OAAO,SAAS;AAEnC,SAAO;AAAA,IACH,UAAU,CAAC,SAAS,OAAO;AAAA,IAAC;AAAA,IAC5B,MAAM,CAAC,SAAS,IAAI,iBAAiB;AACjC,YAAM,KAAK,YAAY;AAEvB,aAAO,YAAY,OAAK;AACpB,YAAI,EAAE,KAAK,OAAO,IAAI;AAClB,aAAG,EAAE,KAAK,OAAO;AAAA,QACrB;AAAA,MACJ;AAEA,aAAO;AAAA,QACH;AAAA,UACI;AAAA,UACA;AAAA,QACJ;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,WAAW,MAAM;AACb,aAAO,UAAU;AACjB,UAAI,gBAAgB,SAAS;AAAA,IACjC;AAAA,EACJ;AACJ;AAEA,IAAM,YAAY,SACd,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC7B,QAAM,MAAM,IAAI,MAAM;AACtB,MAAI,SAAS,MAAM;AACf,YAAQ,GAAG;AAAA,EACf;AACA,MAAI,UAAU,OAAK;AACf,WAAO,CAAC;AAAA,EACZ;AACA,MAAI,MAAM;AACd,CAAC;AAEL,IAAM,aAAa,CAACiB,OAAMpB,UAAS;AAC/B,QAAM,cAAcoB,MAAK,MAAM,GAAGA,MAAK,MAAMA,MAAK,IAAI;AACtD,cAAY,mBAAmBA,MAAK;AACpC,cAAY,OAAOpB;AACnB,SAAO;AACX;AAEA,IAAM,WAAW,CAAAoB,UAAQ,WAAWA,OAAMA,MAAK,IAAI;AAGnD,IAAM,oBAAoB,CAAC;AAG3B,IAAM,kBAAkB,YAAU;AAE9B,MAAI,kBAAkB,SAAS,MAAM,GAAG;AACpC;AAAA,EACJ;AAGA,oBAAkB,KAAK,MAAM;AAG7B,QAAM,gBAAgB,OAAO;AAAA,IACzB;AAAA,IACA,OAAO;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,IACA,OAAO;AAAA,MACH;AAAA,IACJ;AAAA,EACJ,CAAC;AAGD,uBAAqB,cAAc,OAAO;AAC9C;AAGA,IAAM,cAAc,MAAM,OAAO,UAAU,SAAS,KAAK,OAAO,SAAS,MAAM;AAC/E,IAAM,cAAc,MAAM,aAAa;AACvC,IAAM,eAAe,MAAM,WAAW,KAAK;AAC3C,IAAM,qBAAqB,MAAM,SAAS,UAAU,qBAAqB,OAAO;AAChF,IAAM,gBAAgB,MAAM,qBAAqB;AACjD,IAAM,YAAY,MAAM,iBAAiB;AACzC,IAAM,iBAAiB,MAAM,eAAe,OAAO,OAAO,CAAC;AAC3D,IAAM,SAAS,MAAM,eAAe,KAAK,OAAO,UAAU,SAAS;AAEnE,IAAM,aAAa,MAAM;AAErB,QAAM+B;AAAA;AAAA,IAEF,UAAU;AAAA,IAEV,CAAC,YAAY;AAAA,IAEb,cAAc,KACd,YAAY,KACZ,aAAa,KACb,mBAAmB,KACnB,UAAU;AAAA,KAET,eAAe,KAAK,OAAO;AAAA;AAEhC,SAAO,MAAMA;AACjB,GAAG;AAKH,IAAM,QAAQ;AAAA;AAAA,EAEV,MAAM,CAAC;AACX;AAGA,IAAM,OAAO;AAKb,IAAM,KAAK,MAAM;AAAC;AAClB,IAAI,WAAW,CAAC;AAChB,IAAI,aAAa,CAAC;AAClB,IAAI,eAAe,CAAC;AACpB,IAAI,cAAc,CAAC;AACnB,IAAI,WAAW;AACf,IAAI,UAAU;AACd,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,iBAAiB;AACrB,IAAI,eAAe;AACnB,IAAI,eAAe;AAGnB,IAAI,UAAU,GAAG;AAEb;AAAA,IACI,MAAM;AACF,YAAM,KAAK,QAAQ,SAAO,IAAI,MAAM,CAAC;AAAA,IACzC;AAAA,IACA,QAAM;AACF,YAAM,KAAK,QAAQ,SAAO,IAAI,OAAO,EAAE,CAAC;AAAA,IAC5C;AAAA,EACJ;AAGA,QAAM,WAAW,MAAM;AAEnB,aAAS;AAAA,MACL,IAAI,YAAY,mBAAmB;AAAA,QAC/B,QAAQ;AAAA,UACJ;AAAA,UACA,QAAQ;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,YAAY;AAAA,QAChB;AAAA,MACJ,CAAC;AAAA,IACL;AAGA,aAAS,oBAAoB,oBAAoB,QAAQ;AAAA,EAC7D;AAEA,MAAI,SAAS,eAAe,WAAW;AAEnC,eAAW,MAAM,SAAS,GAAG,CAAC;AAAA,EAClC,OAAO;AACH,aAAS,iBAAiB,oBAAoB,QAAQ;AAAA,EAC1D;AAGA,QAAM,oBAAoB,MACtB,MAAM,WAAW,GAAG,CAAC,KAAK,UAAU;AAChC,gBAAY,GAAG,IAAI,MAAM,CAAC;AAAA,EAC9B,CAAC;AAEL,aAAW,EAAE,GAAG,OAAO;AACvB,iBAAe,EAAE,GAAG,WAAW;AAC/B,eAAa,EAAE,GAAG,WAAW;AAE7B,gBAAc,CAAC;AACf,oBAAkB;AAGlB,aAAW,IAAI,SAAS;AACpB,UAAM,MAAM,YAAY,GAAG,IAAI;AAC/B,QAAI,GAAG,WAAW,OAAO;AACzB,UAAM,KAAK,KAAK,GAAG;AACnB,WAAO,aAAa,GAAG;AAAA,EAC3B;AAGA,YAAU,UAAQ;AAEd,UAAM,gBAAgB,MAAM,KAAK,UAAU,SAAO,IAAI,aAAa,IAAI,CAAC;AACxE,QAAI,iBAAiB,GAAG;AAEpB,YAAM,MAAM,MAAM,KAAK,OAAO,eAAe,CAAC,EAAE,CAAC;AAGjD,UAAI,eAAe;AAEnB,aAAO;AAAA,IACX;AAEA,WAAO;AAAA,EACX;AAGA,UAAQ,aAAW;AAEf,UAAM,eAAe,MAAM,KAAK,QAAQ,iBAAiB,IAAI,IAAI,EAAE,CAAC;AAGpE,UAAM,WAAW,aAAa;AAAA,MAC1B,aAAW,CAAC,MAAM,KAAK,KAAK,SAAO,IAAI,aAAa,OAAO,CAAC;AAAA,IAChE;AAGA,WAAO,SAAS,IAAI,UAAQ,SAAS,IAAI,CAAC;AAAA,EAC9C;AAGA,SAAO,UAAQ;AACX,UAAM,MAAM,MAAM,KAAK,KAAK,CAAAC,SAAOA,KAAI,aAAa,IAAI,CAAC;AACzD,QAAI,CAAC,KAAK;AACN,aAAO;AAAA,IACX;AACA,WAAO,aAAa,GAAG;AAAA,EAC3B;AAGA,mBAAiB,IAAI,YAAY;AAE7B,YAAQ,QAAQ,eAAe;AAG/B,sBAAkB;AAAA,EACtB;AAEA,iBAAe,MAAM;AACjB,UAAM,OAAO,CAAC;AACd,UAAM,WAAW,GAAG,CAAC,KAAK,UAAU;AAChC,WAAK,GAAG,IAAI,MAAM,CAAC;AAAA,IACvB,CAAC;AACD,WAAO;AAAA,EACX;AAEA,iBAAe,UAAQ;AACnB,QAAI,SAAS,IAAI,GAAG;AAEhB,YAAM,KAAK,QAAQ,SAAO;AACtB,YAAI,WAAW,IAAI;AAAA,MACvB,CAAC;AAGD,iBAAW,IAAI;AAAA,IACnB;AAGA,WAAO,aAAa;AAAA,EACxB;AACJ;;;ADngTA,IAAM,cAAc,UAAU;AAG9B,IAAM,kBAAkB;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAGO,IAAM,WAAN,cAAuB,aAAAC,QAAM,UAAU;AAAA,EAC5C,YAAY,OAAO;AACjB,UAAM,KAAK;AACX,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA,EAGA,oBAAoB;AAElB,SAAK,SAAS,KAAK,SAAS,cAAc,oBAAoB;AAC9D,SAAK,cAAc,KAAK,OAAO,UAAU;AAGzC,QAAI,CAAC,YAAa;AAElB,UAAM,UAAU,OAAO,OAAO,CAAC,GAAG,KAAK,KAAK;AAG5C,QAAI,QAAQ,eAAe;AACzB,YAAM,KAAK,QAAQ;AACnB,cAAQ,gBAAgB,CAAC,UAAU;AACjC,aAAK,iBAAiB;AACtB,WAAG,KAAK;AAAA,MACV;AAAA,IACF;AAGA,SAAK,QAAQ,SAAO,KAAK,QAAQ,OAAO;AAGxC,WAAO,KAAK,KAAK,KAAK,EACnB,OAAO,CAAC,QAAQ,CAAC,gBAAgB,SAAS,GAAG,CAAC,EAC9C,QAAQ,CAAC,QAAQ;AAChB,WAAK,GAAG,IAAI,KAAK,MAAM,GAAG;AAAA,IAC5B,CAAC;AAAA,EACL;AAAA;AAAA,EAGA,uBAAuB;AAErB,QAAI,CAAC,KAAK,MAAO;AAKjB,UAAM,MAAM,SAAS,cAAc,KAAK;AACxC,QAAI,OAAO,KAAK,MAAM,OAAO;AAC7B,QAAI,KAAK;AAGT,SAAK,MAAM,QAAQ;AACnB,SAAK,QAAQ;AAGb,SAAK,SAAS,OAAO,KAAK,WAAW;AAAA,EACvC;AAAA,EAEA,wBAAwB;AACtB,QAAI,CAAC,KAAK,gBAAgB;AACxB,WAAK,iBAAiB;AACtB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,qBAAqB;AAEnB,QAAI,CAAC,KAAK,MAAO;AAEjB,UAAM,UAAU,OAAO,OAAO,CAAC,GAAG,KAAK,KAAK;AAG5C,WAAO,QAAQ;AAGf,SAAK,MAAM,WAAW,OAAO;AAAA,EAC/B;AAAA;AAAA,EAGA,SAAS;AACP,UAAM;AAAA,MACJ;AAAA,MACA,MAAAC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AACT,eAAO;AAAA,MACL;AAAA,MACA;AAAA,QACE,WAAW;AAAA,QACX,KAAK,CAAC,YAAa,KAAK,WAAW;AAAA,MACrC;AAAA,UACA,4BAAc,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,MAAAA;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,QACR,UAAU;AAAA,QACV;AAAA,QACA;AAAA,QACA,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AACF;", "names": ["queries", "actions", "state", "data", "query", "name", "name", "animations", "animator", "fn", "styles", "write", "create", "destroy", "actions", "state", "store", "props", "read", "root", "listeners", "item", "value", "error", "option", "blob", "file", "data", "res", "headers", "chunk", "ondata", "onerror", "onload", "serverFileReference", "key", "query", "success", "result", "failure", "isAsync", "map", "action", "e", "drop", "location", "currentIndex", "dragHeight", "itemHeight", "entry", "list", "panel", "defaultOptions", "route", "setOptions", "getFiles", "queries", "isSupported", "app", "React", "name"]}