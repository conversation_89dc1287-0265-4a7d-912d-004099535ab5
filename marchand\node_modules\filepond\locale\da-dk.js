export default {
    labelIdle:
        'Træk & slip filer eller <span class = "filepond - label-action"> Gennemse </span>',
    labelInvalidField: 'Felt indeholder ugyldige filer',
    labelFileWaitingForSize: 'Venter på størrelse',
    labelFileSizeNotAvailable: 'Størrelse ikke tilgængelig',
    labelFileLoading: 'Loader',
    labelFileLoadError: 'Load fejlede',
    labelFileProcessing: 'Uploader',
    labelFileProcessingComplete: 'Upload færdig',
    labelFileProcessingAborted: 'Upload annulleret',
    labelFileProcessingError: 'Upload fejlede',
    labelFileProcessingRevertError: 'Fortryd fejlede',
    labelFileRemoveError: 'Fjern fejlede',
    labelTapToCancel: 'tryk for at annullere',
    labelTapToRetry: 'tryk for at prøve igen',
    labelTapToUndo: 'tryk for at fortryde',
    labelButtonRemoveItem: 'Fjern',
    labelButtonAbortItemLoad: 'Annuller',
    labelButtonRetryItemLoad: 'Forsøg igen',
    labelButtonAbortItemProcessing: 'Annuller',
    labelButtonUndoItemProcessing: 'Fortryd',
    labelButtonRetryItemProcessing: 'Prøv igen',
    labelButtonProcessItem: 'Upload',
    labelMaxFileSizeExceeded: 'Filen er for stor',
    labelMaxFileSize: 'Maksimal filstørrelse er {filesize}',
    labelMaxTotalFileSizeExceeded: 'Maksimal totalstørrelse overskredet',
    labelMaxTotalFileSize: 'Maksimal total filstørrelse er {filesize}',
    labelFileTypeNotAllowed: 'Ugyldig filtype',
    fileValidateTypeLabelExpectedTypes: 'Forventer {allButLastType} eller {lastType}',
    imageValidateSizeLabelFormatError: 'Ugyldigt format',
    imageValidateSizeLabelImageSizeTooSmall: 'Billedet er for lille',
    imageValidateSizeLabelImageSizeTooBig: 'Billedet er for stort',
    imageValidateSizeLabelExpectedMinSize: 'Minimum størrelse er {minBredde} × {minHøjde}',
    imageValidateSizeLabelExpectedMaxSize: 'Maksimal størrelse er {maxWidth} × {maxHeight}',
    imageValidateSizeLabelImageResolutionTooLow: 'For lav opløsning',
    imageValidateSizeLabelImageResolutionTooHigh: 'For høj opløsning',
    imageValidateSizeLabelExpectedMinResolution: 'Minimum opløsning er {minResolution}',
    imageValidateSizeLabelExpectedMaxResolution: 'Maksimal opløsning er {maxResolution}',
};
