{"version": 3, "sources": ["../../filepond-plugin-image-preview/dist/filepond-plugin-image-preview.esm.js"], "sourcesContent": ["/*!\n * FilePondPluginImagePreview 4.6.12\n * Licensed under MIT, https://opensource.org/licenses/MIT/\n * Please visit https://pqina.nl/filepond/ for details.\n */\n\n/* eslint-disable */\n\n// test if file is of type image and can be viewed in canvas\nconst isPreviewableImage = file => /^image/.test(file.type);\n\nconst vectorMultiply = (v, amount) => createVector(v.x * amount, v.y * amount);\n\nconst vectorAdd = (a, b) => createVector(a.x + b.x, a.y + b.y);\n\nconst vectorNormalize = v => {\n  const l = Math.sqrt(v.x * v.x + v.y * v.y);\n  if (l === 0) {\n    return {\n      x: 0,\n      y: 0\n    };\n  }\n  return createVector(v.x / l, v.y / l);\n};\n\nconst vectorRotate = (v, radians, origin) => {\n  const cos = Math.cos(radians);\n  const sin = Math.sin(radians);\n  const t = createVector(v.x - origin.x, v.y - origin.y);\n  return createVector(\n    origin.x + cos * t.x - sin * t.y,\n    origin.y + sin * t.x + cos * t.y\n  );\n};\n\nconst createVector = (x = 0, y = 0) => ({ x, y });\n\nconst getMarkupValue = (value, size, scalar = 1, axis) => {\n  if (typeof value === 'string') {\n    return parseFloat(value) * scalar;\n  }\n  if (typeof value === 'number') {\n    return value * (axis ? size[axis] : Math.min(size.width, size.height));\n  }\n  return;\n};\n\nconst getMarkupStyles = (markup, size, scale) => {\n  const lineStyle = markup.borderStyle || markup.lineStyle || 'solid';\n  const fill = markup.backgroundColor || markup.fontColor || 'transparent';\n  const stroke = markup.borderColor || markup.lineColor || 'transparent';\n  const strokeWidth = getMarkupValue(\n    markup.borderWidth || markup.lineWidth,\n    size,\n    scale\n  );\n  const lineCap = markup.lineCap || 'round';\n  const lineJoin = markup.lineJoin || 'round';\n  const dashes =\n    typeof lineStyle === 'string'\n      ? ''\n      : lineStyle.map(v => getMarkupValue(v, size, scale)).join(',');\n  const opacity = markup.opacity || 1;\n  return {\n    'stroke-linecap': lineCap,\n    'stroke-linejoin': lineJoin,\n    'stroke-width': strokeWidth || 0,\n    'stroke-dasharray': dashes,\n    stroke,\n    fill,\n    opacity\n  };\n};\n\nconst isDefined = value => value != null;\n\nconst getMarkupRect = (rect, size, scalar = 1) => {\n  let left =\n    getMarkupValue(rect.x, size, scalar, 'width') ||\n    getMarkupValue(rect.left, size, scalar, 'width');\n  let top =\n    getMarkupValue(rect.y, size, scalar, 'height') ||\n    getMarkupValue(rect.top, size, scalar, 'height');\n  let width = getMarkupValue(rect.width, size, scalar, 'width');\n  let height = getMarkupValue(rect.height, size, scalar, 'height');\n  let right = getMarkupValue(rect.right, size, scalar, 'width');\n  let bottom = getMarkupValue(rect.bottom, size, scalar, 'height');\n\n  if (!isDefined(top)) {\n    if (isDefined(height) && isDefined(bottom)) {\n      top = size.height - height - bottom;\n    } else {\n      top = bottom;\n    }\n  }\n\n  if (!isDefined(left)) {\n    if (isDefined(width) && isDefined(right)) {\n      left = size.width - width - right;\n    } else {\n      left = right;\n    }\n  }\n\n  if (!isDefined(width)) {\n    if (isDefined(left) && isDefined(right)) {\n      width = size.width - left - right;\n    } else {\n      width = 0;\n    }\n  }\n\n  if (!isDefined(height)) {\n    if (isDefined(top) && isDefined(bottom)) {\n      height = size.height - top - bottom;\n    } else {\n      height = 0;\n    }\n  }\n\n  return {\n    x: left || 0,\n    y: top || 0,\n    width: width || 0,\n    height: height || 0\n  };\n};\n\nconst pointsToPathShape = points =>\n  points\n    .map((point, index) => `${index === 0 ? 'M' : 'L'} ${point.x} ${point.y}`)\n    .join(' ');\n\nconst setAttributes = (element, attr) =>\n  Object.keys(attr).forEach(key => element.setAttribute(key, attr[key]));\n\nconst ns = 'http://www.w3.org/2000/svg';\nconst svg = (tag, attr) => {\n  const element = document.createElementNS(ns, tag);\n  if (attr) {\n    setAttributes(element, attr);\n  }\n  return element;\n};\n\nconst updateRect = element =>\n  setAttributes(element, {\n    ...element.rect,\n    ...element.styles\n  });\n\nconst updateEllipse = element => {\n  const cx = element.rect.x + element.rect.width * 0.5;\n  const cy = element.rect.y + element.rect.height * 0.5;\n  const rx = element.rect.width * 0.5;\n  const ry = element.rect.height * 0.5;\n  return setAttributes(element, {\n    cx,\n    cy,\n    rx,\n    ry,\n    ...element.styles\n  });\n};\n\nconst IMAGE_FIT_STYLE = {\n  contain: 'xMidYMid meet',\n  cover: 'xMidYMid slice'\n};\n\nconst updateImage = (element, markup) => {\n  setAttributes(element, {\n    ...element.rect,\n    ...element.styles,\n    preserveAspectRatio: IMAGE_FIT_STYLE[markup.fit] || 'none'\n  });\n};\n\nconst TEXT_ANCHOR = {\n  left: 'start',\n  center: 'middle',\n  right: 'end'\n};\n\nconst updateText = (element, markup, size, scale) => {\n  const fontSize = getMarkupValue(markup.fontSize, size, scale);\n  const fontFamily = markup.fontFamily || 'sans-serif';\n  const fontWeight = markup.fontWeight || 'normal';\n  const textAlign = TEXT_ANCHOR[markup.textAlign] || 'start';\n\n  setAttributes(element, {\n    ...element.rect,\n    ...element.styles,\n    'stroke-width': 0,\n    'font-weight': fontWeight,\n    'font-size': fontSize,\n    'font-family': fontFamily,\n    'text-anchor': textAlign\n  });\n\n  // update text\n  if (element.text !== markup.text) {\n    element.text = markup.text;\n    element.textContent = markup.text.length ? markup.text : ' ';\n  }\n};\n\nconst updateLine = (element, markup, size, scale) => {\n  setAttributes(element, {\n    ...element.rect,\n    ...element.styles,\n    fill: 'none'\n  });\n\n  const line = element.childNodes[0];\n  const begin = element.childNodes[1];\n  const end = element.childNodes[2];\n\n  const origin = element.rect;\n\n  const target = {\n    x: element.rect.x + element.rect.width,\n    y: element.rect.y + element.rect.height\n  };\n\n  setAttributes(line, {\n    x1: origin.x,\n    y1: origin.y,\n    x2: target.x,\n    y2: target.y\n  });\n\n  if (!markup.lineDecoration) return;\n\n  begin.style.display = 'none';\n  end.style.display = 'none';\n\n  const v = vectorNormalize({\n    x: target.x - origin.x,\n    y: target.y - origin.y\n  });\n\n  const l = getMarkupValue(0.05, size, scale);\n\n  if (markup.lineDecoration.indexOf('arrow-begin') !== -1) {\n    const arrowBeginRotationPoint = vectorMultiply(v, l);\n    const arrowBeginCenter = vectorAdd(origin, arrowBeginRotationPoint);\n    const arrowBeginA = vectorRotate(origin, 2, arrowBeginCenter);\n    const arrowBeginB = vectorRotate(origin, -2, arrowBeginCenter);\n\n    setAttributes(begin, {\n      style: 'display:block;',\n      d: `M${arrowBeginA.x},${arrowBeginA.y} L${origin.x},${origin.y} L${\n        arrowBeginB.x\n      },${arrowBeginB.y}`\n    });\n  }\n\n  if (markup.lineDecoration.indexOf('arrow-end') !== -1) {\n    const arrowEndRotationPoint = vectorMultiply(v, -l);\n    const arrowEndCenter = vectorAdd(target, arrowEndRotationPoint);\n    const arrowEndA = vectorRotate(target, 2, arrowEndCenter);\n    const arrowEndB = vectorRotate(target, -2, arrowEndCenter);\n\n    setAttributes(end, {\n      style: 'display:block;',\n      d: `M${arrowEndA.x},${arrowEndA.y} L${target.x},${target.y} L${\n        arrowEndB.x\n      },${arrowEndB.y}`\n    });\n  }\n};\n\nconst updatePath = (element, markup, size, scale) => {\n  setAttributes(element, {\n    ...element.styles,\n    fill: 'none',\n    d: pointsToPathShape(\n      markup.points.map(point => ({\n        x: getMarkupValue(point.x, size, scale, 'width'),\n        y: getMarkupValue(point.y, size, scale, 'height')\n      }))\n    )\n  });\n};\n\nconst createShape = node => markup => svg(node, { id: markup.id });\n\nconst createImage = markup => {\n  const shape = svg('image', {\n    id: markup.id,\n    'stroke-linecap': 'round',\n    'stroke-linejoin': 'round',\n    opacity: '0'\n  });\n  shape.onload = () => {\n    shape.setAttribute('opacity', markup.opacity || 1);\n  };\n  shape.setAttributeNS(\n    'http://www.w3.org/1999/xlink',\n    'xlink:href',\n    markup.src\n  );\n  return shape;\n};\n\nconst createLine = markup => {\n  const shape = svg('g', {\n    id: markup.id,\n    'stroke-linecap': 'round',\n    'stroke-linejoin': 'round'\n  });\n\n  const line = svg('line');\n  shape.appendChild(line);\n\n  const begin = svg('path');\n  shape.appendChild(begin);\n\n  const end = svg('path');\n  shape.appendChild(end);\n\n  return shape;\n};\n\nconst CREATE_TYPE_ROUTES = {\n  image: createImage,\n  rect: createShape('rect'),\n  ellipse: createShape('ellipse'),\n  text: createShape('text'),\n  path: createShape('path'),\n  line: createLine\n};\n\nconst UPDATE_TYPE_ROUTES = {\n  rect: updateRect,\n  ellipse: updateEllipse,\n  image: updateImage,\n  text: updateText,\n  path: updatePath,\n  line: updateLine\n};\n\nconst createMarkupByType = (type, markup) => CREATE_TYPE_ROUTES[type](markup);\n\nconst updateMarkupByType = (element, type, markup, size, scale) => {\n  if (type !== 'path') {\n    element.rect = getMarkupRect(markup, size, scale);\n  }\n  element.styles = getMarkupStyles(markup, size, scale);\n  UPDATE_TYPE_ROUTES[type](element, markup, size, scale);\n};\n\nconst MARKUP_RECT = [\n  'x',\n  'y',\n  'left',\n  'top',\n  'right',\n  'bottom',\n  'width',\n  'height'\n];\n\nconst toOptionalFraction = value =>\n  typeof value === 'string' && /%/.test(value)\n    ? parseFloat(value) / 100\n    : value;\n\n// adds default markup properties, clones markup\nconst prepareMarkup = markup => {\n  const [type, props] = markup;\n\n  const rect = props.points\n    ? {}\n    : MARKUP_RECT.reduce((prev, curr) => {\n        prev[curr] = toOptionalFraction(props[curr]);\n        return prev;\n      }, {});\n\n  return [\n    type,\n    {\n      zIndex: 0,\n      ...props,\n      ...rect\n    }\n  ];\n};\n\nconst sortMarkupByZIndex = (a, b) => {\n  if (a[1].zIndex > b[1].zIndex) {\n    return 1;\n  }\n  if (a[1].zIndex < b[1].zIndex) {\n    return -1;\n  }\n  return 0;\n};\n\nconst createMarkupView = _ =>\n  _.utils.createView({\n    name: 'image-preview-markup',\n    tag: 'svg',\n    ignoreRect: true,\n    mixins: {\n      apis: ['width', 'height', 'crop', 'markup', 'resize', 'dirty']\n    },\n    write: ({ root, props }) => {\n      if (!props.dirty) return;\n\n      const { crop, resize, markup } = props;\n\n      const viewWidth = props.width;\n      const viewHeight = props.height;\n\n      let cropWidth = crop.width;\n      let cropHeight = crop.height;\n\n      if (resize) {\n        const { size } = resize;\n\n        let outputWidth = size && size.width;\n        let outputHeight = size && size.height;\n        const outputFit = resize.mode;\n        const outputUpscale = resize.upscale;\n\n        if (outputWidth && !outputHeight) outputHeight = outputWidth;\n        if (outputHeight && !outputWidth) outputWidth = outputHeight;\n\n        const shouldUpscale =\n          cropWidth < outputWidth && cropHeight < outputHeight;\n\n        if (!shouldUpscale || (shouldUpscale && outputUpscale)) {\n          let scalarWidth = outputWidth / cropWidth;\n          let scalarHeight = outputHeight / cropHeight;\n\n          if (outputFit === 'force') {\n            cropWidth = outputWidth;\n            cropHeight = outputHeight;\n          } else {\n            let scalar;\n            if (outputFit === 'cover') {\n              scalar = Math.max(scalarWidth, scalarHeight);\n            } else if (outputFit === 'contain') {\n              scalar = Math.min(scalarWidth, scalarHeight);\n            }\n            cropWidth = cropWidth * scalar;\n            cropHeight = cropHeight * scalar;\n          }\n        }\n      }\n\n      const size = {\n        width: viewWidth,\n        height: viewHeight\n      };\n\n      root.element.setAttribute('width', size.width);\n      root.element.setAttribute('height', size.height);\n\n      const scale = Math.min(viewWidth / cropWidth, viewHeight / cropHeight);\n\n      // clear\n      root.element.innerHTML = '';\n\n      // get filter\n      const markupFilter = root.query('GET_IMAGE_PREVIEW_MARKUP_FILTER');\n\n      // draw new\n      markup\n        .filter(markupFilter)\n        .map(prepareMarkup)\n        .sort(sortMarkupByZIndex)\n        .forEach(markup => {\n          const [type, settings] = markup;\n\n          // create\n          const element = createMarkupByType(type, settings);\n\n          // update\n          updateMarkupByType(element, type, settings, size, scale);\n\n          // add\n          root.element.appendChild(element);\n        });\n    }\n  });\n\nconst createVector$1 = (x, y) => ({ x, y });\n\nconst vectorDot = (a, b) => a.x * b.x + a.y * b.y;\n\nconst vectorSubtract = (a, b) => createVector$1(a.x - b.x, a.y - b.y);\n\nconst vectorDistanceSquared = (a, b) =>\n  vectorDot(vectorSubtract(a, b), vectorSubtract(a, b));\n\nconst vectorDistance = (a, b) => Math.sqrt(vectorDistanceSquared(a, b));\n\nconst getOffsetPointOnEdge = (length, rotation) => {\n  const a = length;\n\n  const A = 1.5707963267948966;\n  const B = rotation;\n  const C = 1.5707963267948966 - rotation;\n\n  const sinA = Math.sin(A);\n  const sinB = Math.sin(B);\n  const sinC = Math.sin(C);\n  const cosC = Math.cos(C);\n  const ratio = a / sinA;\n  const b = ratio * sinB;\n  const c = ratio * sinC;\n\n  return createVector$1(cosC * b, cosC * c);\n};\n\nconst getRotatedRectSize = (rect, rotation) => {\n  const w = rect.width;\n  const h = rect.height;\n\n  const hor = getOffsetPointOnEdge(w, rotation);\n  const ver = getOffsetPointOnEdge(h, rotation);\n\n  const tl = createVector$1(rect.x + Math.abs(hor.x), rect.y - Math.abs(hor.y));\n\n  const tr = createVector$1(\n    rect.x + rect.width + Math.abs(ver.y),\n    rect.y + Math.abs(ver.x)\n  );\n\n  const bl = createVector$1(\n    rect.x - Math.abs(ver.y),\n    rect.y + rect.height - Math.abs(ver.x)\n  );\n\n  return {\n    width: vectorDistance(tl, tr),\n    height: vectorDistance(tl, bl)\n  };\n};\n\nconst calculateCanvasSize = (image, canvasAspectRatio, zoom = 1) => {\n  const imageAspectRatio = image.height / image.width;\n\n  // determine actual pixels on x and y axis\n  let canvasWidth = 1;\n  let canvasHeight = canvasAspectRatio;\n  let imgWidth = 1;\n  let imgHeight = imageAspectRatio;\n  if (imgHeight > canvasHeight) {\n    imgHeight = canvasHeight;\n    imgWidth = imgHeight / imageAspectRatio;\n  }\n\n  const scalar = Math.max(canvasWidth / imgWidth, canvasHeight / imgHeight);\n  const width = image.width / (zoom * scalar * imgWidth);\n  const height = width * canvasAspectRatio;\n\n  return {\n    width: width,\n    height: height\n  };\n};\n\nconst getImageRectZoomFactor = (imageRect, cropRect, rotation, center) => {\n  // calculate available space round image center position\n  const cx = center.x > 0.5 ? 1 - center.x : center.x;\n  const cy = center.y > 0.5 ? 1 - center.y : center.y;\n  const imageWidth = cx * 2 * imageRect.width;\n  const imageHeight = cy * 2 * imageRect.height;\n\n  // calculate rotated crop rectangle size\n  const rotatedCropSize = getRotatedRectSize(cropRect, rotation);\n\n  // calculate scalar required to fit image\n  return Math.max(\n    rotatedCropSize.width / imageWidth,\n    rotatedCropSize.height / imageHeight\n  );\n};\n\nconst getCenteredCropRect = (container, aspectRatio) => {\n  let width = container.width;\n  let height = width * aspectRatio;\n  if (height > container.height) {\n    height = container.height;\n    width = height / aspectRatio;\n  }\n  const x = (container.width - width) * 0.5;\n  const y = (container.height - height) * 0.5;\n\n  return {\n    x,\n    y,\n    width,\n    height\n  };\n};\n\nconst getCurrentCropSize = (imageSize, crop = {}) => {\n  let { zoom, rotation, center, aspectRatio } = crop;\n\n  if (!aspectRatio) aspectRatio = imageSize.height / imageSize.width;\n\n  const canvasSize = calculateCanvasSize(imageSize, aspectRatio, zoom);\n\n  const canvasCenter = {\n    x: canvasSize.width * 0.5,\n    y: canvasSize.height * 0.5\n  };\n\n  const stage = {\n    x: 0,\n    y: 0,\n    width: canvasSize.width,\n    height: canvasSize.height,\n    center: canvasCenter\n  };\n\n  const shouldLimit = typeof crop.scaleToFit === 'undefined' || crop.scaleToFit;\n\n  const stageZoomFactor = getImageRectZoomFactor(\n    imageSize,\n    getCenteredCropRect(stage, aspectRatio),\n    rotation,\n    shouldLimit ? center : { x: 0.5, y: 0.5 }\n  );\n\n  const scale = zoom * stageZoomFactor;\n\n  // start drawing\n  return {\n    widthFloat: canvasSize.width / scale,\n    heightFloat: canvasSize.height / scale,\n    width: Math.round(canvasSize.width / scale),\n    height: Math.round(canvasSize.height / scale)\n  };\n};\n\nconst IMAGE_SCALE_SPRING_PROPS = {\n  type: 'spring',\n  stiffness: 0.5,\n  damping: 0.45,\n  mass: 10\n};\n\n// does horizontal and vertical flipping\nconst createBitmapView = _ =>\n  _.utils.createView({\n    name: 'image-bitmap',\n    ignoreRect: true,\n    mixins: { styles: ['scaleX', 'scaleY'] },\n    create: ({ root, props }) => {\n      root.appendChild(props.image);\n    }\n  });\n\n// shifts and rotates image\nconst createImageCanvasWrapper = _ =>\n  _.utils.createView({\n    name: 'image-canvas-wrapper',\n    tag: 'div',\n    ignoreRect: true,\n    mixins: {\n      apis: ['crop', 'width', 'height'],\n      styles: [\n        'originX',\n        'originY',\n        'translateX',\n        'translateY',\n        'scaleX',\n        'scaleY',\n        'rotateZ'\n      ],\n      animations: {\n        originX: IMAGE_SCALE_SPRING_PROPS,\n        originY: IMAGE_SCALE_SPRING_PROPS,\n        scaleX: IMAGE_SCALE_SPRING_PROPS,\n        scaleY: IMAGE_SCALE_SPRING_PROPS,\n        translateX: IMAGE_SCALE_SPRING_PROPS,\n        translateY: IMAGE_SCALE_SPRING_PROPS,\n        rotateZ: IMAGE_SCALE_SPRING_PROPS\n      }\n    },\n    create: ({ root, props }) => {\n      props.width = props.image.width;\n      props.height = props.image.height;\n      root.ref.bitmap = root.appendChildView(\n        root.createChildView(createBitmapView(_), { image: props.image })\n      );\n    },\n    write: ({ root, props }) => {\n      const { flip } = props.crop;\n      const { bitmap } = root.ref;\n      bitmap.scaleX = flip.horizontal ? -1 : 1;\n      bitmap.scaleY = flip.vertical ? -1 : 1;\n    }\n  });\n\n// clips canvas to correct aspect ratio\nconst createClipView = _ =>\n  _.utils.createView({\n    name: 'image-clip',\n    tag: 'div',\n    ignoreRect: true,\n    mixins: {\n      apis: [\n        'crop',\n        'markup',\n        'resize',\n        'width',\n        'height',\n        'dirty',\n        'background'\n      ],\n      styles: ['width', 'height', 'opacity'],\n      animations: {\n        opacity: { type: 'tween', duration: 250 }\n      }\n    },\n    didWriteView: function({ root, props }) {\n      if (!props.background) return;\n      root.element.style.backgroundColor = props.background;\n    },\n    create: ({ root, props }) => {\n      root.ref.image = root.appendChildView(\n        root.createChildView(\n          createImageCanvasWrapper(_),\n          Object.assign({}, props)\n        )\n      );\n\n      root.ref.createMarkup = () => {\n        if (root.ref.markup) return;\n        root.ref.markup = root.appendChildView(\n          root.createChildView(createMarkupView(_), Object.assign({}, props))\n        );\n      };\n\n      root.ref.destroyMarkup = () => {\n        if (!root.ref.markup) return;\n        root.removeChildView(root.ref.markup);\n        root.ref.markup = null;\n      };\n\n      // set up transparency grid\n      const transparencyIndicator = root.query(\n        'GET_IMAGE_PREVIEW_TRANSPARENCY_INDICATOR'\n      );\n      if (transparencyIndicator === null) return;\n\n      // grid pattern\n      if (transparencyIndicator === 'grid') {\n        root.element.dataset.transparencyIndicator = transparencyIndicator;\n      }\n      // basic color\n      else {\n        root.element.dataset.transparencyIndicator = 'color';\n      }\n    },\n    write: ({ root, props, shouldOptimize }) => {\n      const { crop, markup, resize, dirty, width, height } = props;\n\n      root.ref.image.crop = crop;\n\n      const stage = {\n        x: 0,\n        y: 0,\n        width,\n        height,\n        center: {\n          x: width * 0.5,\n          y: height * 0.5\n        }\n      };\n\n      const image = {\n        width: root.ref.image.width,\n        height: root.ref.image.height\n      };\n\n      const origin = {\n        x: crop.center.x * image.width,\n        y: crop.center.y * image.height\n      };\n\n      const translation = {\n        x: stage.center.x - image.width * crop.center.x,\n        y: stage.center.y - image.height * crop.center.y\n      };\n\n      const rotation = Math.PI * 2 + (crop.rotation % (Math.PI * 2));\n\n      const cropAspectRatio = crop.aspectRatio || image.height / image.width;\n\n      const shouldLimit =\n        typeof crop.scaleToFit === 'undefined' || crop.scaleToFit;\n\n      const stageZoomFactor = getImageRectZoomFactor(\n        image,\n        getCenteredCropRect(stage, cropAspectRatio),\n        rotation,\n        shouldLimit ? crop.center : { x: 0.5, y: 0.5 }\n      );\n\n      const scale = crop.zoom * stageZoomFactor;\n\n      // update markup view\n      if (markup && markup.length) {\n        root.ref.createMarkup();\n        root.ref.markup.width = width;\n        root.ref.markup.height = height;\n        root.ref.markup.resize = resize;\n        root.ref.markup.dirty = dirty;\n        root.ref.markup.markup = markup;\n        root.ref.markup.crop = getCurrentCropSize(image, crop);\n      } else if (root.ref.markup) {\n        root.ref.destroyMarkup();\n      }\n\n      // update image view\n      const imageView = root.ref.image;\n\n      // don't update clip layout\n      if (shouldOptimize) {\n        imageView.originX = null;\n        imageView.originY = null;\n        imageView.translateX = null;\n        imageView.translateY = null;\n        imageView.rotateZ = null;\n        imageView.scaleX = null;\n        imageView.scaleY = null;\n        return;\n      }\n\n      imageView.originX = origin.x;\n      imageView.originY = origin.y;\n      imageView.translateX = translation.x;\n      imageView.translateY = translation.y;\n      imageView.rotateZ = rotation;\n      imageView.scaleX = scale;\n      imageView.scaleY = scale;\n    }\n  });\n\nconst createImageView = _ =>\n  _.utils.createView({\n    name: 'image-preview',\n    tag: 'div',\n    ignoreRect: true,\n    mixins: {\n      apis: ['image', 'crop', 'markup', 'resize', 'dirty', 'background'],\n      styles: ['translateY', 'scaleX', 'scaleY', 'opacity'],\n      animations: {\n        scaleX: IMAGE_SCALE_SPRING_PROPS,\n        scaleY: IMAGE_SCALE_SPRING_PROPS,\n        translateY: IMAGE_SCALE_SPRING_PROPS,\n        opacity: { type: 'tween', duration: 400 }\n      }\n    },\n    create: ({ root, props }) => {\n      root.ref.clip = root.appendChildView(\n        root.createChildView(createClipView(_), {\n          id: props.id,\n          image: props.image,\n          crop: props.crop,\n          markup: props.markup,\n          resize: props.resize,\n          dirty: props.dirty,\n          background: props.background\n        })\n      );\n    },\n    write: ({ root, props, shouldOptimize }) => {\n      const { clip } = root.ref;\n\n      const { image, crop, markup, resize, dirty } = props;\n\n      clip.crop = crop;\n      clip.markup = markup;\n      clip.resize = resize;\n      clip.dirty = dirty;\n\n      // don't update clip layout\n      clip.opacity = shouldOptimize ? 0 : 1;\n\n      // don't re-render if optimizing or hidden (width will be zero resulting in weird animations)\n      if (shouldOptimize || root.rect.element.hidden) return;\n\n      // calculate scaled preview image size\n      const imageAspectRatio = image.height / image.width;\n      let aspectRatio = crop.aspectRatio || imageAspectRatio;\n\n      // calculate container size\n      const containerWidth = root.rect.inner.width;\n      const containerHeight = root.rect.inner.height;\n\n      let fixedPreviewHeight = root.query('GET_IMAGE_PREVIEW_HEIGHT');\n      const minPreviewHeight = root.query('GET_IMAGE_PREVIEW_MIN_HEIGHT');\n      const maxPreviewHeight = root.query('GET_IMAGE_PREVIEW_MAX_HEIGHT');\n\n      const panelAspectRatio = root.query('GET_PANEL_ASPECT_RATIO');\n      const allowMultiple = root.query('GET_ALLOW_MULTIPLE');\n\n      if (panelAspectRatio && !allowMultiple) {\n        fixedPreviewHeight = containerWidth * panelAspectRatio;\n        aspectRatio = panelAspectRatio;\n      }\n\n      // determine clip width and height\n      let clipHeight =\n        fixedPreviewHeight !== null\n          ? fixedPreviewHeight\n          : Math.max(\n              minPreviewHeight,\n              Math.min(containerWidth * aspectRatio, maxPreviewHeight)\n            );\n\n      let clipWidth = clipHeight / aspectRatio;\n      if (clipWidth > containerWidth) {\n        clipWidth = containerWidth;\n        clipHeight = clipWidth * aspectRatio;\n      }\n\n      if (clipHeight > containerHeight) {\n        clipHeight = containerHeight;\n        clipWidth = containerHeight / aspectRatio;\n      }\n\n      clip.width = clipWidth;\n      clip.height = clipHeight;\n    }\n  });\n\nlet SVG_MASK = `<svg width=\"500\" height=\"200\" viewBox=\"0 0 500 200\" preserveAspectRatio=\"none\">\n    <defs>\n        <radialGradient id=\"gradient-__UID__\" cx=\".5\" cy=\"1.25\" r=\"1.15\">\n            <stop offset='50%' stop-color='#000000'/>\n            <stop offset='56%' stop-color='#0a0a0a'/>\n            <stop offset='63%' stop-color='#262626'/>\n            <stop offset='69%' stop-color='#4f4f4f'/>\n            <stop offset='75%' stop-color='#808080'/>\n            <stop offset='81%' stop-color='#b1b1b1'/>\n            <stop offset='88%' stop-color='#dadada'/>\n            <stop offset='94%' stop-color='#f6f6f6'/>\n            <stop offset='100%' stop-color='#ffffff'/>\n        </radialGradient>\n        <mask id=\"mask-__UID__\">\n            <rect x=\"0\" y=\"0\" width=\"500\" height=\"200\" fill=\"url(#gradient-__UID__)\"></rect>\n        </mask>\n    </defs>\n    <rect x=\"0\" width=\"500\" height=\"200\" fill=\"currentColor\" mask=\"url(#mask-__UID__)\"></rect>\n</svg>`;\n\nlet SVGMaskUniqueId = 0;\n\nconst createImageOverlayView = fpAPI =>\n  fpAPI.utils.createView({\n    name: 'image-preview-overlay',\n    tag: 'div',\n    ignoreRect: true,\n    create: ({ root, props }) => {\n      let mask = SVG_MASK;\n      if (document.querySelector('base')) {\n        const url = new URL(\n          window.location.href.replace(window.location.hash, '')\n        ).href;\n        mask = mask.replace(/url\\(\\#/g, 'url(' + url + '#');\n      }\n\n      SVGMaskUniqueId++;\n      root.element.classList.add(\n        `filepond--image-preview-overlay-${props.status}`\n      );\n      root.element.innerHTML = mask.replace(/__UID__/g, SVGMaskUniqueId);\n    },\n    mixins: {\n      styles: ['opacity'],\n      animations: {\n        opacity: { type: 'spring', mass: 25 }\n      }\n    }\n  });\n\n/**\n * Bitmap Worker\n */\nconst BitmapWorker = function() {\n  self.onmessage = e => {\n    createImageBitmap(e.data.message.file).then(bitmap => {\n      self.postMessage({ id: e.data.id, message: bitmap }, [bitmap]);\n    });\n  };\n};\n\n/**\n * ColorMatrix Worker\n */\nconst ColorMatrixWorker = function() {\n  self.onmessage = e => {\n    const imageData = e.data.message.imageData;\n    const matrix = e.data.message.colorMatrix;\n\n    const data = imageData.data;\n    const l = data.length;\n\n    const m11 = matrix[0];\n    const m12 = matrix[1];\n    const m13 = matrix[2];\n    const m14 = matrix[3];\n    const m15 = matrix[4];\n\n    const m21 = matrix[5];\n    const m22 = matrix[6];\n    const m23 = matrix[7];\n    const m24 = matrix[8];\n    const m25 = matrix[9];\n\n    const m31 = matrix[10];\n    const m32 = matrix[11];\n    const m33 = matrix[12];\n    const m34 = matrix[13];\n    const m35 = matrix[14];\n\n    const m41 = matrix[15];\n    const m42 = matrix[16];\n    const m43 = matrix[17];\n    const m44 = matrix[18];\n    const m45 = matrix[19];\n\n    let index = 0,\n      r = 0.0,\n      g = 0.0,\n      b = 0.0,\n      a = 0.0;\n\n    for (; index < l; index += 4) {\n      r = data[index] / 255;\n      g = data[index + 1] / 255;\n      b = data[index + 2] / 255;\n      a = data[index + 3] / 255;\n      data[index] = Math.max(\n        0,\n        Math.min((r * m11 + g * m12 + b * m13 + a * m14 + m15) * 255, 255)\n      );\n      data[index + 1] = Math.max(\n        0,\n        Math.min((r * m21 + g * m22 + b * m23 + a * m24 + m25) * 255, 255)\n      );\n      data[index + 2] = Math.max(\n        0,\n        Math.min((r * m31 + g * m32 + b * m33 + a * m34 + m35) * 255, 255)\n      );\n      data[index + 3] = Math.max(\n        0,\n        Math.min((r * m41 + g * m42 + b * m43 + a * m44 + m45) * 255, 255)\n      );\n    }\n\n    self.postMessage({ id: e.data.id, message: imageData }, [\n      imageData.data.buffer\n    ]);\n  };\n};\n\nconst getImageSize = (url, cb) => {\n  let image = new Image();\n  image.onload = () => {\n    const width = image.naturalWidth;\n    const height = image.naturalHeight;\n    image = null;\n    cb(width, height);\n  };\n  image.src = url;\n};\n\nconst transforms = {\n  1: () => [1, 0, 0, 1, 0, 0],\n  2: width => [-1, 0, 0, 1, width, 0],\n  3: (width, height) => [-1, 0, 0, -1, width, height],\n  4: (width, height) => [1, 0, 0, -1, 0, height],\n  5: () => [0, 1, 1, 0, 0, 0],\n  6: (width, height) => [0, 1, -1, 0, height, 0],\n  7: (width, height) => [0, -1, -1, 0, height, width],\n  8: width => [0, -1, 1, 0, 0, width]\n};\n\nconst fixImageOrientation = (ctx, width, height, orientation) => {\n  // no orientation supplied\n  if (orientation === -1) {\n    return;\n  }\n\n  ctx.transform.apply(ctx, transforms[orientation](width, height));\n};\n\n// draws the preview image to canvas\nconst createPreviewImage = (data, width, height, orientation) => {\n  // can't draw on half pixels\n  width = Math.round(width);\n  height = Math.round(height);\n\n  // draw image\n  const canvas = document.createElement('canvas');\n  canvas.width = width;\n  canvas.height = height;\n  const ctx = canvas.getContext('2d');\n\n  // if is rotated incorrectly swap width and height\n  if (orientation >= 5 && orientation <= 8) {\n    [width, height] = [height, width];\n  }\n\n  // correct image orientation\n  fixImageOrientation(ctx, width, height, orientation);\n\n  // draw the image\n  ctx.drawImage(data, 0, 0, width, height);\n\n  return canvas;\n};\n\nconst isBitmap = file => /^image/.test(file.type) && !/svg/.test(file.type);\n\nconst MAX_WIDTH = 10;\nconst MAX_HEIGHT = 10;\n\nconst calculateAverageColor = image => {\n  const scalar = Math.min(MAX_WIDTH / image.width, MAX_HEIGHT / image.height);\n\n  const canvas = document.createElement('canvas');\n  const ctx = canvas.getContext('2d');\n  const width = (canvas.width = Math.ceil(image.width * scalar));\n  const height = (canvas.height = Math.ceil(image.height * scalar));\n  ctx.drawImage(image, 0, 0, width, height);\n  let data = null;\n  try {\n    data = ctx.getImageData(0, 0, width, height).data;\n  } catch (e) {\n    return null;\n  }\n  const l = data.length;\n\n  let r = 0;\n  let g = 0;\n  let b = 0;\n  let i = 0;\n\n  for (; i < l; i += 4) {\n    r += data[i] * data[i];\n    g += data[i + 1] * data[i + 1];\n    b += data[i + 2] * data[i + 2];\n  }\n\n  r = averageColor(r, l);\n  g = averageColor(g, l);\n  b = averageColor(b, l);\n\n  return { r, g, b };\n};\n\nconst averageColor = (c, l) => Math.floor(Math.sqrt(c / (l / 4)));\n\nconst cloneCanvas = (origin, target) => {\n  target = target || document.createElement('canvas');\n  target.width = origin.width;\n  target.height = origin.height;\n  const ctx = target.getContext('2d');\n  ctx.drawImage(origin, 0, 0);\n  return target;\n};\n\nconst cloneImageData = imageData => {\n  let id;\n  try {\n    id = new ImageData(imageData.width, imageData.height);\n  } catch (e) {\n    const canvas = document.createElement('canvas');\n    const ctx = canvas.getContext('2d');\n    id = ctx.createImageData(imageData.width, imageData.height);\n  }\n  id.data.set(new Uint8ClampedArray(imageData.data));\n  return id;\n};\n\nconst loadImage = url =>\n  new Promise((resolve, reject) => {\n    const img = new Image();\n    img.crossOrigin = 'Anonymous';\n    img.onload = () => {\n      resolve(img);\n    };\n    img.onerror = e => {\n      reject(e);\n    };\n    img.src = url;\n  });\n\nconst createImageWrapperView = _ => {\n  // create overlay view\n  const OverlayView = createImageOverlayView(_);\n\n  const ImageView = createImageView(_);\n\n  const { createWorker } = _.utils;\n\n  const applyFilter = (root, filter, target) =>\n    new Promise(resolve => {\n      // will store image data for future filter updates\n      if (!root.ref.imageData) {\n        root.ref.imageData = target\n          .getContext('2d')\n          .getImageData(0, 0, target.width, target.height);\n      }\n\n      // get image data reference\n      const imageData = cloneImageData(root.ref.imageData);\n\n      if (!filter || filter.length !== 20) {\n        target.getContext('2d').putImageData(imageData, 0, 0);\n        return resolve();\n      }\n\n      const worker = createWorker(ColorMatrixWorker);\n      worker.post(\n        {\n          imageData,\n          colorMatrix: filter\n        },\n        response => {\n          // apply filtered colors\n          target.getContext('2d').putImageData(response, 0, 0);\n\n          // stop worker\n          worker.terminate();\n\n          // done!\n          resolve();\n        },\n        [imageData.data.buffer]\n      );\n    });\n\n  const removeImageView = (root, imageView) => {\n    root.removeChildView(imageView);\n    imageView.image.width = 1;\n    imageView.image.height = 1;\n    imageView._destroy();\n  };\n\n  // remove an image\n  const shiftImage = ({ root }) => {\n    const imageView = root.ref.images.shift();\n    imageView.opacity = 0;\n    imageView.translateY = -15;\n    root.ref.imageViewBin.push(imageView);\n    return imageView;\n  };\n\n  // add new image\n  const pushImage = ({ root, props, image }) => {\n    const id = props.id;\n    const item = root.query('GET_ITEM', { id });\n    if (!item) return;\n\n    const crop = item.getMetadata('crop') || {\n      center: {\n        x: 0.5,\n        y: 0.5\n      },\n      flip: {\n        horizontal: false,\n        vertical: false\n      },\n      zoom: 1,\n      rotation: 0,\n      aspectRatio: null\n    };\n\n    const background = root.query(\n      'GET_IMAGE_TRANSFORM_CANVAS_BACKGROUND_COLOR'\n    );\n\n    let markup;\n    let resize;\n    let dirty = false;\n    if (root.query('GET_IMAGE_PREVIEW_MARKUP_SHOW')) {\n      markup = item.getMetadata('markup') || [];\n      resize = item.getMetadata('resize');\n      dirty = true;\n    }\n\n    // append image presenter\n    const imageView = root.appendChildView(\n      root.createChildView(ImageView, {\n        id,\n        image,\n        crop,\n        resize,\n        markup,\n        dirty,\n        background,\n        opacity: 0,\n        scaleX: 1.15,\n        scaleY: 1.15,\n        translateY: 15\n      }),\n      root.childViews.length\n    );\n    root.ref.images.push(imageView);\n\n    // reveal the preview image\n    imageView.opacity = 1;\n    imageView.scaleX = 1;\n    imageView.scaleY = 1;\n    imageView.translateY = 0;\n\n    // the preview is now ready to be drawn\n    setTimeout(() => {\n      root.dispatch('DID_IMAGE_PREVIEW_SHOW', { id });\n    }, 250);\n  };\n\n  const updateImage = ({ root, props }) => {\n    const item = root.query('GET_ITEM', { id: props.id });\n    if (!item) return;\n    const imageView = root.ref.images[root.ref.images.length - 1];\n    imageView.crop = item.getMetadata('crop');\n    imageView.background = root.query(\n      'GET_IMAGE_TRANSFORM_CANVAS_BACKGROUND_COLOR'\n    );\n    if (root.query('GET_IMAGE_PREVIEW_MARKUP_SHOW')) {\n      imageView.dirty = true;\n      imageView.resize = item.getMetadata('resize');\n      imageView.markup = item.getMetadata('markup');\n    }\n  };\n\n  // replace image preview\n  const didUpdateItemMetadata = ({ root, props, action }) => {\n    // only filter and crop trigger redraw\n    if (!/crop|filter|markup|resize/.test(action.change.key)) return;\n\n    // no images to update, exit\n    if (!root.ref.images.length) return;\n\n    // no item found, exit\n    const item = root.query('GET_ITEM', { id: props.id });\n    if (!item) return;\n\n    // for now, update existing image when filtering\n    if (/filter/.test(action.change.key)) {\n      const imageView = root.ref.images[root.ref.images.length - 1];\n      applyFilter(root, action.change.value, imageView.image);\n      return;\n    }\n\n    if (/crop|markup|resize/.test(action.change.key)) {\n      const crop = item.getMetadata('crop');\n      const image = root.ref.images[root.ref.images.length - 1];\n\n      // if aspect ratio has changed, we need to create a new image\n      if (\n        crop &&\n        crop.aspectRatio &&\n        image.crop &&\n        image.crop.aspectRatio &&\n        Math.abs(crop.aspectRatio - image.crop.aspectRatio) > 0.00001\n      ) {\n        const imageView = shiftImage({ root });\n        pushImage({ root, props, image: cloneCanvas(imageView.image) });\n      }\n      // if not, we can update the current image\n      else {\n        updateImage({ root, props });\n      }\n    }\n  };\n\n  const canCreateImageBitmap = file => {\n    // Firefox versions before 58 will freeze when running createImageBitmap\n    // in a Web Worker so we detect those versions and return false for support\n    const userAgent = window.navigator.userAgent;\n    const isFirefox = userAgent.match(/Firefox\\/([0-9]+)\\./);\n    const firefoxVersion = isFirefox ? parseInt(isFirefox[1]) : null;\n    if (firefoxVersion !== null && firefoxVersion <= 58) return false;\n\n    return 'createImageBitmap' in window && isBitmap(file);\n  };\n\n  /**\n   * Write handler for when preview container has been created\n   */\n  const didCreatePreviewContainer = ({ root, props }) => {\n    const { id } = props;\n\n    // we need to get the file data to determine the eventual image size\n    const item = root.query('GET_ITEM', id);\n    if (!item) return;\n\n    // get url to file (we'll revoke it later on when done)\n    const fileURL = URL.createObjectURL(item.file);\n\n    // determine image size of this item\n    getImageSize(fileURL, (width, height) => {\n      // we can now scale the panel to the final size\n      root.dispatch('DID_IMAGE_PREVIEW_CALCULATE_SIZE', {\n        id,\n        width,\n        height\n      });\n    });\n  };\n\n  const drawPreview = ({ root, props }) => {\n    const { id } = props;\n\n    // we need to get the file data to determine the eventual image size\n    const item = root.query('GET_ITEM', id);\n    if (!item) return;\n\n    // get url to file (we'll revoke it later on when done)\n    const fileURL = URL.createObjectURL(item.file);\n\n    // fallback\n    const loadPreviewFallback = () => {\n      // let's scale the image in the main thread :(\n      loadImage(fileURL).then(previewImageLoaded);\n    };\n\n    // image is now ready\n    const previewImageLoaded = imageData => {\n      // the file url is no longer needed\n      URL.revokeObjectURL(fileURL);\n\n      // draw the scaled down version here and use that as source so bitmapdata can be closed\n      // orientation info\n      const exif = item.getMetadata('exif') || {};\n      const orientation = exif.orientation || -1;\n\n      // get width and height from action, and swap if orientation is incorrect\n      let { width, height } = imageData;\n\n      // if no width or height, just return early.\n      if (!width || !height) return;\n\n      if (orientation >= 5 && orientation <= 8) {\n        [width, height] = [height, width];\n      }\n\n      // scale canvas based on pixel density\n      // we multiply by .75 as that creates smaller but still clear images on screens with high res displays\n      const pixelDensityFactor = Math.max(1, window.devicePixelRatio * 0.75);\n\n      // we want as much pixels to work with as possible,\n      // this multiplies the minimum image resolution,\n      // so when zooming in it doesn't get too blurry\n      const zoomFactor = root.query('GET_IMAGE_PREVIEW_ZOOM_FACTOR');\n\n      // imaeg scale factor\n      const scaleFactor = zoomFactor * pixelDensityFactor;\n\n      // calculate scaled preview image size\n      const previewImageRatio = height / width;\n\n      // calculate image preview height and width\n      const previewContainerWidth = root.rect.element.width;\n      const previewContainerHeight = root.rect.element.height;\n\n      let imageWidth = previewContainerWidth;\n      let imageHeight = imageWidth * previewImageRatio;\n\n      if (previewImageRatio > 1) {\n        imageWidth = Math.min(width, previewContainerWidth * scaleFactor);\n        imageHeight = imageWidth * previewImageRatio;\n      } else {\n        imageHeight = Math.min(height, previewContainerHeight * scaleFactor);\n        imageWidth = imageHeight / previewImageRatio;\n      }\n\n      // transfer to image tag so no canvas memory wasted on iOS\n      const previewImage = createPreviewImage(\n        imageData,\n        imageWidth,\n        imageHeight,\n        orientation\n      );\n\n      // done\n      const done = () => {\n        // calculate average image color, disabled for now\n        const averageColor = root.query(\n          'GET_IMAGE_PREVIEW_CALCULATE_AVERAGE_IMAGE_COLOR'\n        )\n          ? calculateAverageColor(data)\n          : null;\n        item.setMetadata('color', averageColor, true);\n\n        // data has been transferred to canvas ( if was ImageBitmap )\n        if ('close' in imageData) {\n          imageData.close();\n        }\n\n        // show the overlay\n        root.ref.overlayShadow.opacity = 1;\n\n        // create the first image\n        pushImage({ root, props, image: previewImage });\n      };\n\n      // apply filter\n      const filter = item.getMetadata('filter');\n      if (filter) {\n        applyFilter(root, filter, previewImage).then(done);\n      } else {\n        done();\n      }\n    };\n\n    // if we support scaling using createImageBitmap we use a worker\n    if (canCreateImageBitmap(item.file)) {\n      // let's scale the image in a worker\n      const worker = createWorker(BitmapWorker);\n\n      worker.post(\n        {\n          file: item.file\n        },\n        imageBitmap => {\n          // destroy worker\n          worker.terminate();\n\n          // no bitmap returned, must be something wrong,\n          // try the oldschool way\n          if (!imageBitmap) {\n            loadPreviewFallback();\n            return;\n          }\n\n          // yay we got our bitmap, let's continue showing the preview\n          previewImageLoaded(imageBitmap);\n        }\n      );\n    } else {\n      // create fallback preview\n      loadPreviewFallback();\n    }\n  };\n\n  /**\n   * Write handler for when the preview image is ready to be animated\n   */\n  const didDrawPreview = ({ root }) => {\n    // get last added image\n    const image = root.ref.images[root.ref.images.length - 1];\n    image.translateY = 0;\n    image.scaleX = 1.0;\n    image.scaleY = 1.0;\n    image.opacity = 1;\n  };\n\n  /**\n   * Write handler for when the preview has been loaded\n   */\n  const restoreOverlay = ({ root }) => {\n    root.ref.overlayShadow.opacity = 1;\n    root.ref.overlayError.opacity = 0;\n    root.ref.overlaySuccess.opacity = 0;\n  };\n\n  const didThrowError = ({ root }) => {\n    root.ref.overlayShadow.opacity = 0.25;\n    root.ref.overlayError.opacity = 1;\n  };\n\n  const didCompleteProcessing = ({ root }) => {\n    root.ref.overlayShadow.opacity = 0.25;\n    root.ref.overlaySuccess.opacity = 1;\n  };\n\n  /**\n   * Constructor\n   */\n  const create = ({ root }) => {\n    // image view\n    root.ref.images = [];\n\n    // the preview image data (we need this to filter the image)\n    root.ref.imageData = null;\n\n    // image bin\n    root.ref.imageViewBin = [];\n\n    // image overlays\n    root.ref.overlayShadow = root.appendChildView(\n      root.createChildView(OverlayView, {\n        opacity: 0,\n        status: 'idle'\n      })\n    );\n\n    root.ref.overlaySuccess = root.appendChildView(\n      root.createChildView(OverlayView, {\n        opacity: 0,\n        status: 'success'\n      })\n    );\n\n    root.ref.overlayError = root.appendChildView(\n      root.createChildView(OverlayView, {\n        opacity: 0,\n        status: 'failure'\n      })\n    );\n  };\n\n  return _.utils.createView({\n    name: 'image-preview-wrapper',\n    create,\n    styles: ['height'],\n    apis: ['height'],\n    destroy: ({ root }) => {\n      // we resize the image so memory on iOS 12 is released more quickly (it seems)\n      root.ref.images.forEach(imageView => {\n        imageView.image.width = 1;\n        imageView.image.height = 1;\n      });\n    },\n    didWriteView: ({ root }) => {\n      root.ref.images.forEach(imageView => {\n        imageView.dirty = false;\n      });\n    },\n    write: _.utils.createRoute(\n      {\n        // image preview stated\n        DID_IMAGE_PREVIEW_DRAW: didDrawPreview,\n        DID_IMAGE_PREVIEW_CONTAINER_CREATE: didCreatePreviewContainer,\n        DID_FINISH_CALCULATE_PREVIEWSIZE: drawPreview,\n        DID_UPDATE_ITEM_METADATA: didUpdateItemMetadata,\n\n        // file states\n        DID_THROW_ITEM_LOAD_ERROR: didThrowError,\n        DID_THROW_ITEM_PROCESSING_ERROR: didThrowError,\n        DID_THROW_ITEM_INVALID: didThrowError,\n        DID_COMPLETE_ITEM_PROCESSING: didCompleteProcessing,\n        DID_START_ITEM_PROCESSING: restoreOverlay,\n        DID_REVERT_ITEM_PROCESSING: restoreOverlay\n      },\n      ({ root }) => {\n        // views on death row\n        const viewsToRemove = root.ref.imageViewBin.filter(\n          imageView => imageView.opacity === 0\n        );\n\n        // views to retain\n        root.ref.imageViewBin = root.ref.imageViewBin.filter(\n          imageView => imageView.opacity > 0\n        );\n\n        // remove these views\n        viewsToRemove.forEach(imageView => removeImageView(root, imageView));\n        viewsToRemove.length = 0;\n      }\n    )\n  });\n};\n\n/**\n * Image Preview Plugin\n */\nconst plugin = fpAPI => {\n  const { addFilter, utils } = fpAPI;\n  const { Type, createRoute, isFile } = utils;\n\n  // imagePreviewView\n  const imagePreviewView = createImageWrapperView(fpAPI);\n\n  // called for each view that is created right after the 'create' method\n  addFilter('CREATE_VIEW', viewAPI => {\n    // get reference to created view\n    const { is, view, query } = viewAPI;\n\n    // only hook up to item view and only if is enabled for this cropper\n    if (!is('file') || !query('GET_ALLOW_IMAGE_PREVIEW')) return;\n\n    // create the image preview plugin, but only do so if the item is an image\n    const didLoadItem = ({ root, props }) => {\n      const { id } = props;\n      const item = query('GET_ITEM', id);\n\n      // item could theoretically have been removed in the mean time\n      if (!item || !isFile(item.file) || item.archived) return;\n\n      // get the file object\n      const file = item.file;\n\n      // exit if this is not an image\n      if (!isPreviewableImage(file)) return;\n\n      // test if is filtered\n      if (!query('GET_IMAGE_PREVIEW_FILTER_ITEM')(item)) return;\n\n      // exit if image size is too high and no createImageBitmap support\n      // this would simply bring the browser to its knees and that is not what we want\n      const supportsCreateImageBitmap = 'createImageBitmap' in (window || {});\n      const maxPreviewFileSize = query('GET_IMAGE_PREVIEW_MAX_FILE_SIZE');\n      if (\n        !supportsCreateImageBitmap &&\n        (maxPreviewFileSize && file.size > maxPreviewFileSize)\n      )\n        return;\n\n      // set preview view\n      root.ref.imagePreview = view.appendChildView(\n        view.createChildView(imagePreviewView, { id })\n      );\n\n      // update height if is fixed\n      const fixedPreviewHeight = root.query('GET_IMAGE_PREVIEW_HEIGHT');\n      if (fixedPreviewHeight) {\n        root.dispatch('DID_UPDATE_PANEL_HEIGHT', {\n          id: item.id,\n          height: fixedPreviewHeight\n        });\n      }\n\n      // now ready\n      const queue =\n        !supportsCreateImageBitmap &&\n        file.size > query('GET_IMAGE_PREVIEW_MAX_INSTANT_PREVIEW_FILE_SIZE');\n      root.dispatch('DID_IMAGE_PREVIEW_CONTAINER_CREATE', { id }, queue);\n    };\n\n    const rescaleItem = (root, props) => {\n      if (!root.ref.imagePreview) return;\n\n      let { id } = props;\n\n      // get item\n      const item = root.query('GET_ITEM', { id });\n      if (!item) return;\n\n      // if is fixed height or panel has aspect ratio, exit here, height has already been defined\n      const panelAspectRatio = root.query('GET_PANEL_ASPECT_RATIO');\n      const itemPanelAspectRatio = root.query('GET_ITEM_PANEL_ASPECT_RATIO');\n      const fixedHeight = root.query('GET_IMAGE_PREVIEW_HEIGHT');\n      if (panelAspectRatio || itemPanelAspectRatio || fixedHeight) return;\n\n      // no data!\n      let { imageWidth, imageHeight } = root.ref;\n      if (!imageWidth || !imageHeight) return;\n\n      // get height min and max\n      const minPreviewHeight = root.query('GET_IMAGE_PREVIEW_MIN_HEIGHT');\n      const maxPreviewHeight = root.query('GET_IMAGE_PREVIEW_MAX_HEIGHT');\n\n      // orientation info\n      const exif = item.getMetadata('exif') || {};\n      const orientation = exif.orientation || -1;\n\n      // get width and height from action, and swap of orientation is incorrect\n      if (orientation >= 5 && orientation <= 8)\n        [imageWidth, imageHeight] = [imageHeight, imageWidth];\n\n      // scale up width and height when we're dealing with an SVG\n      if (!isBitmap(item.file) || root.query('GET_IMAGE_PREVIEW_UPSCALE')) {\n        const scalar = 2048 / imageWidth;\n        imageWidth *= scalar;\n        imageHeight *= scalar;\n      }\n\n      // image aspect ratio\n      const imageAspectRatio = imageHeight / imageWidth;\n\n      // we need the item to get to the crop size\n      const previewAspectRatio =\n        (item.getMetadata('crop') || {}).aspectRatio || imageAspectRatio;\n\n      // preview height range\n      let previewHeightMax = Math.max(\n        minPreviewHeight,\n        Math.min(imageHeight, maxPreviewHeight)\n      );\n      const itemWidth = root.rect.element.width;\n      const previewHeight = Math.min(\n        itemWidth * previewAspectRatio,\n        previewHeightMax\n      );\n\n      // request update to panel height\n      root.dispatch('DID_UPDATE_PANEL_HEIGHT', {\n        id: item.id,\n        height: previewHeight\n      });\n    };\n\n    const didResizeView = ({ root }) => {\n      // actions in next write operation\n      root.ref.shouldRescale = true;\n    };\n\n    const didUpdateItemMetadata = ({ root, action }) => {\n      if (action.change.key !== 'crop') return;\n\n      // actions in next write operation\n      root.ref.shouldRescale = true;\n    };\n\n    const didCalculatePreviewSize = ({ root, action }) => {\n      // remember dimensions\n      root.ref.imageWidth = action.width;\n      root.ref.imageHeight = action.height;\n\n      // actions in next write operation\n      root.ref.shouldRescale = true;\n      root.ref.shouldDrawPreview = true;\n\n      // as image load could take a while and fire when draw loop is resting we need to give it a kick\n      root.dispatch('KICK');\n    };\n\n    // start writing\n    view.registerWriter(\n      createRoute(\n        {\n          DID_RESIZE_ROOT: didResizeView,\n          DID_STOP_RESIZE: didResizeView,\n          DID_LOAD_ITEM: didLoadItem,\n          DID_IMAGE_PREVIEW_CALCULATE_SIZE: didCalculatePreviewSize,\n          DID_UPDATE_ITEM_METADATA: didUpdateItemMetadata\n        },\n        ({ root, props }) => {\n          // no preview view attached\n          if (!root.ref.imagePreview) return;\n\n          // don't do anything while hidden\n          if (root.rect.element.hidden) return;\n\n          // resize the item panel\n          if (root.ref.shouldRescale) {\n            rescaleItem(root, props);\n            root.ref.shouldRescale = false;\n          }\n\n          if (root.ref.shouldDrawPreview) {\n            // queue till next frame so we're sure the height has been applied this forces the draw image call inside the wrapper view to use the correct height\n            requestAnimationFrame(() => {\n              // this requestAnimationFrame nesting is horrible but it fixes an issue with 100hz displays on Chrome\n              // https://github.com/pqina/filepond-plugin-image-preview/issues/57\n              requestAnimationFrame(() => {\n                root.dispatch('DID_FINISH_CALCULATE_PREVIEWSIZE', {\n                  id: props.id\n                });\n              });\n            });\n\n            root.ref.shouldDrawPreview = false;\n          }\n        }\n      )\n    );\n  });\n\n  // expose plugin\n  return {\n    options: {\n      // Enable or disable image preview\n      allowImagePreview: [true, Type.BOOLEAN],\n\n      // filters file items to determine which are shown as preview\n      imagePreviewFilterItem: [() => true, Type.FUNCTION],\n\n      // Fixed preview height\n      imagePreviewHeight: [null, Type.INT],\n\n      // Min image height\n      imagePreviewMinHeight: [44, Type.INT],\n\n      // Max image height\n      imagePreviewMaxHeight: [256, Type.INT],\n\n      // Max size of preview file for when createImageBitmap is not supported\n      imagePreviewMaxFileSize: [null, Type.INT],\n\n      // The amount of extra pixels added to the image preview to allow comfortable zooming\n      imagePreviewZoomFactor: [2, Type.INT],\n\n      // Should we upscale small images to fit the max bounding box of the preview area\n      imagePreviewUpscale: [false, Type.BOOLEAN],\n\n      // Max size of preview file that we allow to try to instant preview if createImageBitmap is not supported, else image is queued for loading\n      imagePreviewMaxInstantPreviewFileSize: [1000000, Type.INT],\n\n      // Style of the transparancy indicator used behind images\n      imagePreviewTransparencyIndicator: [null, Type.STRING],\n\n      // Enables or disables reading average image color\n      imagePreviewCalculateAverageImageColor: [false, Type.BOOLEAN],\n\n      // Enables or disables the previewing of markup\n      imagePreviewMarkupShow: [true, Type.BOOLEAN],\n\n      // Allows filtering of markup to only show certain shapes\n      imagePreviewMarkupFilter: [() => true, Type.FUNCTION]\n    }\n  };\n};\n\n// fire pluginloaded event if running in browser, this allows registering the plugin when using async script tags\nconst isBrowser =\n  typeof window !== 'undefined' && typeof window.document !== 'undefined';\nif (isBrowser) {\n  document.dispatchEvent(\n    new CustomEvent('FilePond:pluginloaded', { detail: plugin })\n  );\n}\n\nexport default plugin;\n"], "mappings": ";;;AASA,IAAM,qBAAqB,UAAQ,SAAS,KAAK,KAAK,IAAI;AAE1D,IAAM,iBAAiB,CAAC,GAAG,WAAW,aAAa,EAAE,IAAI,QAAQ,EAAE,IAAI,MAAM;AAE7E,IAAM,YAAY,CAAC,GAAG,MAAM,aAAa,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;AAE7D,IAAM,kBAAkB,OAAK;AAC3B,QAAM,IAAI,KAAK,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AACzC,MAAI,MAAM,GAAG;AACX,WAAO;AAAA,MACL,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,EACF;AACA,SAAO,aAAa,EAAE,IAAI,GAAG,EAAE,IAAI,CAAC;AACtC;AAEA,IAAM,eAAe,CAAC,GAAG,SAAS,WAAW;AAC3C,QAAM,MAAM,KAAK,IAAI,OAAO;AAC5B,QAAM,MAAM,KAAK,IAAI,OAAO;AAC5B,QAAM,IAAI,aAAa,EAAE,IAAI,OAAO,GAAG,EAAE,IAAI,OAAO,CAAC;AACrD,SAAO;AAAA,IACL,OAAO,IAAI,MAAM,EAAE,IAAI,MAAM,EAAE;AAAA,IAC/B,OAAO,IAAI,MAAM,EAAE,IAAI,MAAM,EAAE;AAAA,EACjC;AACF;AAEA,IAAM,eAAe,CAAC,IAAI,GAAG,IAAI,OAAO,EAAE,GAAG,EAAE;AAE/C,IAAM,iBAAiB,CAAC,OAAO,MAAM,SAAS,GAAG,SAAS;AACxD,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO,WAAW,KAAK,IAAI;AAAA,EAC7B;AACA,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO,SAAS,OAAO,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,OAAO,KAAK,MAAM;AAAA,EACtE;AACA;AACF;AAEA,IAAM,kBAAkB,CAAC,QAAQ,MAAM,UAAU;AAC/C,QAAM,YAAY,OAAO,eAAe,OAAO,aAAa;AAC5D,QAAM,OAAO,OAAO,mBAAmB,OAAO,aAAa;AAC3D,QAAM,SAAS,OAAO,eAAe,OAAO,aAAa;AACzD,QAAM,cAAc;AAAA,IAClB,OAAO,eAAe,OAAO;AAAA,IAC7B;AAAA,IACA;AAAA,EACF;AACA,QAAM,UAAU,OAAO,WAAW;AAClC,QAAM,WAAW,OAAO,YAAY;AACpC,QAAM,SACJ,OAAO,cAAc,WACjB,KACA,UAAU,IAAI,OAAK,eAAe,GAAG,MAAM,KAAK,CAAC,EAAE,KAAK,GAAG;AACjE,QAAM,UAAU,OAAO,WAAW;AAClC,SAAO;AAAA,IACL,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,gBAAgB,eAAe;AAAA,IAC/B,oBAAoB;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,YAAY,WAAS,SAAS;AAEpC,IAAM,gBAAgB,CAAC,MAAM,MAAM,SAAS,MAAM;AAChD,MAAI,OACF,eAAe,KAAK,GAAG,MAAM,QAAQ,OAAO,KAC5C,eAAe,KAAK,MAAM,MAAM,QAAQ,OAAO;AACjD,MAAI,MACF,eAAe,KAAK,GAAG,MAAM,QAAQ,QAAQ,KAC7C,eAAe,KAAK,KAAK,MAAM,QAAQ,QAAQ;AACjD,MAAI,QAAQ,eAAe,KAAK,OAAO,MAAM,QAAQ,OAAO;AAC5D,MAAI,SAAS,eAAe,KAAK,QAAQ,MAAM,QAAQ,QAAQ;AAC/D,MAAI,QAAQ,eAAe,KAAK,OAAO,MAAM,QAAQ,OAAO;AAC5D,MAAI,SAAS,eAAe,KAAK,QAAQ,MAAM,QAAQ,QAAQ;AAE/D,MAAI,CAAC,UAAU,GAAG,GAAG;AACnB,QAAI,UAAU,MAAM,KAAK,UAAU,MAAM,GAAG;AAC1C,YAAM,KAAK,SAAS,SAAS;AAAA,IAC/B,OAAO;AACL,YAAM;AAAA,IACR;AAAA,EACF;AAEA,MAAI,CAAC,UAAU,IAAI,GAAG;AACpB,QAAI,UAAU,KAAK,KAAK,UAAU,KAAK,GAAG;AACxC,aAAO,KAAK,QAAQ,QAAQ;AAAA,IAC9B,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAEA,MAAI,CAAC,UAAU,KAAK,GAAG;AACrB,QAAI,UAAU,IAAI,KAAK,UAAU,KAAK,GAAG;AACvC,cAAQ,KAAK,QAAQ,OAAO;AAAA,IAC9B,OAAO;AACL,cAAQ;AAAA,IACV;AAAA,EACF;AAEA,MAAI,CAAC,UAAU,MAAM,GAAG;AACtB,QAAI,UAAU,GAAG,KAAK,UAAU,MAAM,GAAG;AACvC,eAAS,KAAK,SAAS,MAAM;AAAA,IAC/B,OAAO;AACL,eAAS;AAAA,IACX;AAAA,EACF;AAEA,SAAO;AAAA,IACL,GAAG,QAAQ;AAAA,IACX,GAAG,OAAO;AAAA,IACV,OAAO,SAAS;AAAA,IAChB,QAAQ,UAAU;AAAA,EACpB;AACF;AAEA,IAAM,oBAAoB,YACxB,OACG,IAAI,CAAC,OAAO,UAAU,GAAG,UAAU,IAAI,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,MAAM,CAAC,EAAE,EACxE,KAAK,GAAG;AAEb,IAAM,gBAAgB,CAAC,SAAS,SAC9B,OAAO,KAAK,IAAI,EAAE,QAAQ,SAAO,QAAQ,aAAa,KAAK,KAAK,GAAG,CAAC,CAAC;AAEvE,IAAM,KAAK;AACX,IAAM,MAAM,CAAC,KAAK,SAAS;AACzB,QAAM,UAAU,SAAS,gBAAgB,IAAI,GAAG;AAChD,MAAI,MAAM;AACR,kBAAc,SAAS,IAAI;AAAA,EAC7B;AACA,SAAO;AACT;AAEA,IAAM,aAAa,aACjB,cAAc,SAAS;AAAA,EACrB,GAAG,QAAQ;AAAA,EACX,GAAG,QAAQ;AACb,CAAC;AAEH,IAAM,gBAAgB,aAAW;AAC/B,QAAM,KAAK,QAAQ,KAAK,IAAI,QAAQ,KAAK,QAAQ;AACjD,QAAM,KAAK,QAAQ,KAAK,IAAI,QAAQ,KAAK,SAAS;AAClD,QAAM,KAAK,QAAQ,KAAK,QAAQ;AAChC,QAAM,KAAK,QAAQ,KAAK,SAAS;AACjC,SAAO,cAAc,SAAS;AAAA,IAC5B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG,QAAQ;AAAA,EACb,CAAC;AACH;AAEA,IAAM,kBAAkB;AAAA,EACtB,SAAS;AAAA,EACT,OAAO;AACT;AAEA,IAAM,cAAc,CAAC,SAAS,WAAW;AACvC,gBAAc,SAAS;AAAA,IACrB,GAAG,QAAQ;AAAA,IACX,GAAG,QAAQ;AAAA,IACX,qBAAqB,gBAAgB,OAAO,GAAG,KAAK;AAAA,EACtD,CAAC;AACH;AAEA,IAAM,cAAc;AAAA,EAClB,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AAEA,IAAM,aAAa,CAAC,SAAS,QAAQ,MAAM,UAAU;AACnD,QAAM,WAAW,eAAe,OAAO,UAAU,MAAM,KAAK;AAC5D,QAAM,aAAa,OAAO,cAAc;AACxC,QAAM,aAAa,OAAO,cAAc;AACxC,QAAM,YAAY,YAAY,OAAO,SAAS,KAAK;AAEnD,gBAAc,SAAS;AAAA,IACrB,GAAG,QAAQ;AAAA,IACX,GAAG,QAAQ;AAAA,IACX,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,aAAa;AAAA,IACb,eAAe;AAAA,IACf,eAAe;AAAA,EACjB,CAAC;AAGD,MAAI,QAAQ,SAAS,OAAO,MAAM;AAChC,YAAQ,OAAO,OAAO;AACtB,YAAQ,cAAc,OAAO,KAAK,SAAS,OAAO,OAAO;AAAA,EAC3D;AACF;AAEA,IAAM,aAAa,CAAC,SAAS,QAAQ,MAAM,UAAU;AACnD,gBAAc,SAAS;AAAA,IACrB,GAAG,QAAQ;AAAA,IACX,GAAG,QAAQ;AAAA,IACX,MAAM;AAAA,EACR,CAAC;AAED,QAAM,OAAO,QAAQ,WAAW,CAAC;AACjC,QAAM,QAAQ,QAAQ,WAAW,CAAC;AAClC,QAAM,MAAM,QAAQ,WAAW,CAAC;AAEhC,QAAM,SAAS,QAAQ;AAEvB,QAAM,SAAS;AAAA,IACb,GAAG,QAAQ,KAAK,IAAI,QAAQ,KAAK;AAAA,IACjC,GAAG,QAAQ,KAAK,IAAI,QAAQ,KAAK;AAAA,EACnC;AAEA,gBAAc,MAAM;AAAA,IAClB,IAAI,OAAO;AAAA,IACX,IAAI,OAAO;AAAA,IACX,IAAI,OAAO;AAAA,IACX,IAAI,OAAO;AAAA,EACb,CAAC;AAED,MAAI,CAAC,OAAO,eAAgB;AAE5B,QAAM,MAAM,UAAU;AACtB,MAAI,MAAM,UAAU;AAEpB,QAAM,IAAI,gBAAgB;AAAA,IACxB,GAAG,OAAO,IAAI,OAAO;AAAA,IACrB,GAAG,OAAO,IAAI,OAAO;AAAA,EACvB,CAAC;AAED,QAAM,IAAI,eAAe,MAAM,MAAM,KAAK;AAE1C,MAAI,OAAO,eAAe,QAAQ,aAAa,MAAM,IAAI;AACvD,UAAM,0BAA0B,eAAe,GAAG,CAAC;AACnD,UAAM,mBAAmB,UAAU,QAAQ,uBAAuB;AAClE,UAAM,cAAc,aAAa,QAAQ,GAAG,gBAAgB;AAC5D,UAAM,cAAc,aAAa,QAAQ,IAAI,gBAAgB;AAE7D,kBAAc,OAAO;AAAA,MACnB,OAAO;AAAA,MACP,GAAG,IAAI,YAAY,CAAC,IAAI,YAAY,CAAC,KAAK,OAAO,CAAC,IAAI,OAAO,CAAC,KAC5D,YAAY,CACd,IAAI,YAAY,CAAC;AAAA,IACnB,CAAC;AAAA,EACH;AAEA,MAAI,OAAO,eAAe,QAAQ,WAAW,MAAM,IAAI;AACrD,UAAM,wBAAwB,eAAe,GAAG,CAAC,CAAC;AAClD,UAAM,iBAAiB,UAAU,QAAQ,qBAAqB;AAC9D,UAAM,YAAY,aAAa,QAAQ,GAAG,cAAc;AACxD,UAAM,YAAY,aAAa,QAAQ,IAAI,cAAc;AAEzD,kBAAc,KAAK;AAAA,MACjB,OAAO;AAAA,MACP,GAAG,IAAI,UAAU,CAAC,IAAI,UAAU,CAAC,KAAK,OAAO,CAAC,IAAI,OAAO,CAAC,KACxD,UAAU,CACZ,IAAI,UAAU,CAAC;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAEA,IAAM,aAAa,CAAC,SAAS,QAAQ,MAAM,UAAU;AACnD,gBAAc,SAAS;AAAA,IACrB,GAAG,QAAQ;AAAA,IACX,MAAM;AAAA,IACN,GAAG;AAAA,MACD,OAAO,OAAO,IAAI,YAAU;AAAA,QAC1B,GAAG,eAAe,MAAM,GAAG,MAAM,OAAO,OAAO;AAAA,QAC/C,GAAG,eAAe,MAAM,GAAG,MAAM,OAAO,QAAQ;AAAA,MAClD,EAAE;AAAA,IACJ;AAAA,EACF,CAAC;AACH;AAEA,IAAM,cAAc,UAAQ,YAAU,IAAI,MAAM,EAAE,IAAI,OAAO,GAAG,CAAC;AAEjE,IAAM,cAAc,YAAU;AAC5B,QAAM,QAAQ,IAAI,SAAS;AAAA,IACzB,IAAI,OAAO;AAAA,IACX,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,SAAS;AAAA,EACX,CAAC;AACD,QAAM,SAAS,MAAM;AACnB,UAAM,aAAa,WAAW,OAAO,WAAW,CAAC;AAAA,EACnD;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,OAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,IAAM,aAAa,YAAU;AAC3B,QAAM,QAAQ,IAAI,KAAK;AAAA,IACrB,IAAI,OAAO;AAAA,IACX,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,EACrB,CAAC;AAED,QAAM,OAAO,IAAI,MAAM;AACvB,QAAM,YAAY,IAAI;AAEtB,QAAM,QAAQ,IAAI,MAAM;AACxB,QAAM,YAAY,KAAK;AAEvB,QAAM,MAAM,IAAI,MAAM;AACtB,QAAM,YAAY,GAAG;AAErB,SAAO;AACT;AAEA,IAAM,qBAAqB;AAAA,EACzB,OAAO;AAAA,EACP,MAAM,YAAY,MAAM;AAAA,EACxB,SAAS,YAAY,SAAS;AAAA,EAC9B,MAAM,YAAY,MAAM;AAAA,EACxB,MAAM,YAAY,MAAM;AAAA,EACxB,MAAM;AACR;AAEA,IAAM,qBAAqB;AAAA,EACzB,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AACR;AAEA,IAAM,qBAAqB,CAAC,MAAM,WAAW,mBAAmB,IAAI,EAAE,MAAM;AAE5E,IAAM,qBAAqB,CAAC,SAAS,MAAM,QAAQ,MAAM,UAAU;AACjE,MAAI,SAAS,QAAQ;AACnB,YAAQ,OAAO,cAAc,QAAQ,MAAM,KAAK;AAAA,EAClD;AACA,UAAQ,SAAS,gBAAgB,QAAQ,MAAM,KAAK;AACpD,qBAAmB,IAAI,EAAE,SAAS,QAAQ,MAAM,KAAK;AACvD;AAEA,IAAM,cAAc;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAM,qBAAqB,WACzB,OAAO,UAAU,YAAY,IAAI,KAAK,KAAK,IACvC,WAAW,KAAK,IAAI,MACpB;AAGN,IAAM,gBAAgB,YAAU;AAC9B,QAAM,CAAC,MAAM,KAAK,IAAI;AAEtB,QAAM,OAAO,MAAM,SACf,CAAC,IACD,YAAY,OAAO,CAAC,MAAM,SAAS;AACjC,SAAK,IAAI,IAAI,mBAAmB,MAAM,IAAI,CAAC;AAC3C,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AAET,SAAO;AAAA,IACL;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,MACR,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,EACF;AACF;AAEA,IAAM,qBAAqB,CAAC,GAAG,MAAM;AACnC,MAAI,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,QAAQ;AAC7B,WAAO;AAAA,EACT;AACA,MAAI,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,QAAQ;AAC7B,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,IAAM,mBAAmB,OACvB,EAAE,MAAM,WAAW;AAAA,EACjB,MAAM;AAAA,EACN,KAAK;AAAA,EACL,YAAY;AAAA,EACZ,QAAQ;AAAA,IACN,MAAM,CAAC,SAAS,UAAU,QAAQ,UAAU,UAAU,OAAO;AAAA,EAC/D;AAAA,EACA,OAAO,CAAC,EAAE,MAAM,MAAM,MAAM;AAC1B,QAAI,CAAC,MAAM,MAAO;AAElB,UAAM,EAAE,MAAM,QAAQ,OAAO,IAAI;AAEjC,UAAM,YAAY,MAAM;AACxB,UAAM,aAAa,MAAM;AAEzB,QAAI,YAAY,KAAK;AACrB,QAAI,aAAa,KAAK;AAEtB,QAAI,QAAQ;AACV,YAAM,EAAE,MAAAA,MAAK,IAAI;AAEjB,UAAI,cAAcA,SAAQA,MAAK;AAC/B,UAAI,eAAeA,SAAQA,MAAK;AAChC,YAAM,YAAY,OAAO;AACzB,YAAM,gBAAgB,OAAO;AAE7B,UAAI,eAAe,CAAC,aAAc,gBAAe;AACjD,UAAI,gBAAgB,CAAC,YAAa,eAAc;AAEhD,YAAM,gBACJ,YAAY,eAAe,aAAa;AAE1C,UAAI,CAAC,iBAAkB,iBAAiB,eAAgB;AACtD,YAAI,cAAc,cAAc;AAChC,YAAI,eAAe,eAAe;AAElC,YAAI,cAAc,SAAS;AACzB,sBAAY;AACZ,uBAAa;AAAA,QACf,OAAO;AACL,cAAI;AACJ,cAAI,cAAc,SAAS;AACzB,qBAAS,KAAK,IAAI,aAAa,YAAY;AAAA,UAC7C,WAAW,cAAc,WAAW;AAClC,qBAAS,KAAK,IAAI,aAAa,YAAY;AAAA,UAC7C;AACA,sBAAY,YAAY;AACxB,uBAAa,aAAa;AAAA,QAC5B;AAAA,MACF;AAAA,IACF;AAEA,UAAM,OAAO;AAAA,MACX,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AAEA,SAAK,QAAQ,aAAa,SAAS,KAAK,KAAK;AAC7C,SAAK,QAAQ,aAAa,UAAU,KAAK,MAAM;AAE/C,UAAM,QAAQ,KAAK,IAAI,YAAY,WAAW,aAAa,UAAU;AAGrE,SAAK,QAAQ,YAAY;AAGzB,UAAM,eAAe,KAAK,MAAM,iCAAiC;AAGjE,WACG,OAAO,YAAY,EACnB,IAAI,aAAa,EACjB,KAAK,kBAAkB,EACvB,QAAQ,CAAAC,YAAU;AACjB,YAAM,CAAC,MAAM,QAAQ,IAAIA;AAGzB,YAAM,UAAU,mBAAmB,MAAM,QAAQ;AAGjD,yBAAmB,SAAS,MAAM,UAAU,MAAM,KAAK;AAGvD,WAAK,QAAQ,YAAY,OAAO;AAAA,IAClC,CAAC;AAAA,EACL;AACF,CAAC;AAEH,IAAM,iBAAiB,CAAC,GAAG,OAAO,EAAE,GAAG,EAAE;AAEzC,IAAM,YAAY,CAAC,GAAG,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAEhD,IAAM,iBAAiB,CAAC,GAAG,MAAM,eAAe,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;AAEpE,IAAM,wBAAwB,CAAC,GAAG,MAChC,UAAU,eAAe,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC,CAAC;AAEtD,IAAM,iBAAiB,CAAC,GAAG,MAAM,KAAK,KAAK,sBAAsB,GAAG,CAAC,CAAC;AAEtE,IAAM,uBAAuB,CAAC,QAAQ,aAAa;AACjD,QAAM,IAAI;AAEV,QAAM,IAAI;AACV,QAAM,IAAI;AACV,QAAM,IAAI,qBAAqB;AAE/B,QAAM,OAAO,KAAK,IAAI,CAAC;AACvB,QAAM,OAAO,KAAK,IAAI,CAAC;AACvB,QAAM,OAAO,KAAK,IAAI,CAAC;AACvB,QAAM,OAAO,KAAK,IAAI,CAAC;AACvB,QAAM,QAAQ,IAAI;AAClB,QAAM,IAAI,QAAQ;AAClB,QAAM,IAAI,QAAQ;AAElB,SAAO,eAAe,OAAO,GAAG,OAAO,CAAC;AAC1C;AAEA,IAAM,qBAAqB,CAAC,MAAM,aAAa;AAC7C,QAAM,IAAI,KAAK;AACf,QAAM,IAAI,KAAK;AAEf,QAAM,MAAM,qBAAqB,GAAG,QAAQ;AAC5C,QAAM,MAAM,qBAAqB,GAAG,QAAQ;AAE5C,QAAM,KAAK,eAAe,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC;AAE5E,QAAM,KAAK;AAAA,IACT,KAAK,IAAI,KAAK,QAAQ,KAAK,IAAI,IAAI,CAAC;AAAA,IACpC,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC;AAAA,EACzB;AAEA,QAAM,KAAK;AAAA,IACT,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC;AAAA,IACvB,KAAK,IAAI,KAAK,SAAS,KAAK,IAAI,IAAI,CAAC;AAAA,EACvC;AAEA,SAAO;AAAA,IACL,OAAO,eAAe,IAAI,EAAE;AAAA,IAC5B,QAAQ,eAAe,IAAI,EAAE;AAAA,EAC/B;AACF;AAEA,IAAM,sBAAsB,CAAC,OAAO,mBAAmB,OAAO,MAAM;AAClE,QAAM,mBAAmB,MAAM,SAAS,MAAM;AAG9C,MAAI,cAAc;AAClB,MAAI,eAAe;AACnB,MAAI,WAAW;AACf,MAAI,YAAY;AAChB,MAAI,YAAY,cAAc;AAC5B,gBAAY;AACZ,eAAW,YAAY;AAAA,EACzB;AAEA,QAAM,SAAS,KAAK,IAAI,cAAc,UAAU,eAAe,SAAS;AACxE,QAAM,QAAQ,MAAM,SAAS,OAAO,SAAS;AAC7C,QAAM,SAAS,QAAQ;AAEvB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,yBAAyB,CAAC,WAAW,UAAU,UAAU,WAAW;AAExE,QAAM,KAAK,OAAO,IAAI,MAAM,IAAI,OAAO,IAAI,OAAO;AAClD,QAAM,KAAK,OAAO,IAAI,MAAM,IAAI,OAAO,IAAI,OAAO;AAClD,QAAM,aAAa,KAAK,IAAI,UAAU;AACtC,QAAM,cAAc,KAAK,IAAI,UAAU;AAGvC,QAAM,kBAAkB,mBAAmB,UAAU,QAAQ;AAG7D,SAAO,KAAK;AAAA,IACV,gBAAgB,QAAQ;AAAA,IACxB,gBAAgB,SAAS;AAAA,EAC3B;AACF;AAEA,IAAM,sBAAsB,CAAC,WAAW,gBAAgB;AACtD,MAAI,QAAQ,UAAU;AACtB,MAAI,SAAS,QAAQ;AACrB,MAAI,SAAS,UAAU,QAAQ;AAC7B,aAAS,UAAU;AACnB,YAAQ,SAAS;AAAA,EACnB;AACA,QAAM,KAAK,UAAU,QAAQ,SAAS;AACtC,QAAM,KAAK,UAAU,SAAS,UAAU;AAExC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,qBAAqB,CAAC,WAAW,OAAO,CAAC,MAAM;AACnD,MAAI,EAAE,MAAM,UAAU,QAAQ,YAAY,IAAI;AAE9C,MAAI,CAAC,YAAa,eAAc,UAAU,SAAS,UAAU;AAE7D,QAAM,aAAa,oBAAoB,WAAW,aAAa,IAAI;AAEnE,QAAM,eAAe;AAAA,IACnB,GAAG,WAAW,QAAQ;AAAA,IACtB,GAAG,WAAW,SAAS;AAAA,EACzB;AAEA,QAAM,QAAQ;AAAA,IACZ,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO,WAAW;AAAA,IAClB,QAAQ,WAAW;AAAA,IACnB,QAAQ;AAAA,EACV;AAEA,QAAM,cAAc,OAAO,KAAK,eAAe,eAAe,KAAK;AAEnE,QAAM,kBAAkB;AAAA,IACtB;AAAA,IACA,oBAAoB,OAAO,WAAW;AAAA,IACtC;AAAA,IACA,cAAc,SAAS,EAAE,GAAG,KAAK,GAAG,IAAI;AAAA,EAC1C;AAEA,QAAM,QAAQ,OAAO;AAGrB,SAAO;AAAA,IACL,YAAY,WAAW,QAAQ;AAAA,IAC/B,aAAa,WAAW,SAAS;AAAA,IACjC,OAAO,KAAK,MAAM,WAAW,QAAQ,KAAK;AAAA,IAC1C,QAAQ,KAAK,MAAM,WAAW,SAAS,KAAK;AAAA,EAC9C;AACF;AAEA,IAAM,2BAA2B;AAAA,EAC/B,MAAM;AAAA,EACN,WAAW;AAAA,EACX,SAAS;AAAA,EACT,MAAM;AACR;AAGA,IAAM,mBAAmB,OACvB,EAAE,MAAM,WAAW;AAAA,EACjB,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,QAAQ,EAAE,QAAQ,CAAC,UAAU,QAAQ,EAAE;AAAA,EACvC,QAAQ,CAAC,EAAE,MAAM,MAAM,MAAM;AAC3B,SAAK,YAAY,MAAM,KAAK;AAAA,EAC9B;AACF,CAAC;AAGH,IAAM,2BAA2B,OAC/B,EAAE,MAAM,WAAW;AAAA,EACjB,MAAM;AAAA,EACN,KAAK;AAAA,EACL,YAAY;AAAA,EACZ,QAAQ;AAAA,IACN,MAAM,CAAC,QAAQ,SAAS,QAAQ;AAAA,IAChC,QAAQ;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,YAAY;AAAA,MACV,SAAS;AAAA,MACT,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,QAAQ,CAAC,EAAE,MAAM,MAAM,MAAM;AAC3B,UAAM,QAAQ,MAAM,MAAM;AAC1B,UAAM,SAAS,MAAM,MAAM;AAC3B,SAAK,IAAI,SAAS,KAAK;AAAA,MACrB,KAAK,gBAAgB,iBAAiB,CAAC,GAAG,EAAE,OAAO,MAAM,MAAM,CAAC;AAAA,IAClE;AAAA,EACF;AAAA,EACA,OAAO,CAAC,EAAE,MAAM,MAAM,MAAM;AAC1B,UAAM,EAAE,KAAK,IAAI,MAAM;AACvB,UAAM,EAAE,OAAO,IAAI,KAAK;AACxB,WAAO,SAAS,KAAK,aAAa,KAAK;AACvC,WAAO,SAAS,KAAK,WAAW,KAAK;AAAA,EACvC;AACF,CAAC;AAGH,IAAM,iBAAiB,OACrB,EAAE,MAAM,WAAW;AAAA,EACjB,MAAM;AAAA,EACN,KAAK;AAAA,EACL,YAAY;AAAA,EACZ,QAAQ;AAAA,IACN,MAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,QAAQ,CAAC,SAAS,UAAU,SAAS;AAAA,IACrC,YAAY;AAAA,MACV,SAAS,EAAE,MAAM,SAAS,UAAU,IAAI;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,cAAc,SAAS,EAAE,MAAM,MAAM,GAAG;AACtC,QAAI,CAAC,MAAM,WAAY;AACvB,SAAK,QAAQ,MAAM,kBAAkB,MAAM;AAAA,EAC7C;AAAA,EACA,QAAQ,CAAC,EAAE,MAAM,MAAM,MAAM;AAC3B,SAAK,IAAI,QAAQ,KAAK;AAAA,MACpB,KAAK;AAAA,QACH,yBAAyB,CAAC;AAAA,QAC1B,OAAO,OAAO,CAAC,GAAG,KAAK;AAAA,MACzB;AAAA,IACF;AAEA,SAAK,IAAI,eAAe,MAAM;AAC5B,UAAI,KAAK,IAAI,OAAQ;AACrB,WAAK,IAAI,SAAS,KAAK;AAAA,QACrB,KAAK,gBAAgB,iBAAiB,CAAC,GAAG,OAAO,OAAO,CAAC,GAAG,KAAK,CAAC;AAAA,MACpE;AAAA,IACF;AAEA,SAAK,IAAI,gBAAgB,MAAM;AAC7B,UAAI,CAAC,KAAK,IAAI,OAAQ;AACtB,WAAK,gBAAgB,KAAK,IAAI,MAAM;AACpC,WAAK,IAAI,SAAS;AAAA,IACpB;AAGA,UAAM,wBAAwB,KAAK;AAAA,MACjC;AAAA,IACF;AACA,QAAI,0BAA0B,KAAM;AAGpC,QAAI,0BAA0B,QAAQ;AACpC,WAAK,QAAQ,QAAQ,wBAAwB;AAAA,IAC/C,OAEK;AACH,WAAK,QAAQ,QAAQ,wBAAwB;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,OAAO,CAAC,EAAE,MAAM,OAAO,eAAe,MAAM;AAC1C,UAAM,EAAE,MAAM,QAAQ,QAAQ,OAAO,OAAO,OAAO,IAAI;AAEvD,SAAK,IAAI,MAAM,OAAO;AAEtB,UAAM,QAAQ;AAAA,MACZ,GAAG;AAAA,MACH,GAAG;AAAA,MACH;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,QACN,GAAG,QAAQ;AAAA,QACX,GAAG,SAAS;AAAA,MACd;AAAA,IACF;AAEA,UAAM,QAAQ;AAAA,MACZ,OAAO,KAAK,IAAI,MAAM;AAAA,MACtB,QAAQ,KAAK,IAAI,MAAM;AAAA,IACzB;AAEA,UAAM,SAAS;AAAA,MACb,GAAG,KAAK,OAAO,IAAI,MAAM;AAAA,MACzB,GAAG,KAAK,OAAO,IAAI,MAAM;AAAA,IAC3B;AAEA,UAAM,cAAc;AAAA,MAClB,GAAG,MAAM,OAAO,IAAI,MAAM,QAAQ,KAAK,OAAO;AAAA,MAC9C,GAAG,MAAM,OAAO,IAAI,MAAM,SAAS,KAAK,OAAO;AAAA,IACjD;AAEA,UAAM,WAAW,KAAK,KAAK,IAAK,KAAK,YAAY,KAAK,KAAK;AAE3D,UAAM,kBAAkB,KAAK,eAAe,MAAM,SAAS,MAAM;AAEjE,UAAM,cACJ,OAAO,KAAK,eAAe,eAAe,KAAK;AAEjD,UAAM,kBAAkB;AAAA,MACtB;AAAA,MACA,oBAAoB,OAAO,eAAe;AAAA,MAC1C;AAAA,MACA,cAAc,KAAK,SAAS,EAAE,GAAG,KAAK,GAAG,IAAI;AAAA,IAC/C;AAEA,UAAM,QAAQ,KAAK,OAAO;AAG1B,QAAI,UAAU,OAAO,QAAQ;AAC3B,WAAK,IAAI,aAAa;AACtB,WAAK,IAAI,OAAO,QAAQ;AACxB,WAAK,IAAI,OAAO,SAAS;AACzB,WAAK,IAAI,OAAO,SAAS;AACzB,WAAK,IAAI,OAAO,QAAQ;AACxB,WAAK,IAAI,OAAO,SAAS;AACzB,WAAK,IAAI,OAAO,OAAO,mBAAmB,OAAO,IAAI;AAAA,IACvD,WAAW,KAAK,IAAI,QAAQ;AAC1B,WAAK,IAAI,cAAc;AAAA,IACzB;AAGA,UAAM,YAAY,KAAK,IAAI;AAG3B,QAAI,gBAAgB;AAClB,gBAAU,UAAU;AACpB,gBAAU,UAAU;AACpB,gBAAU,aAAa;AACvB,gBAAU,aAAa;AACvB,gBAAU,UAAU;AACpB,gBAAU,SAAS;AACnB,gBAAU,SAAS;AACnB;AAAA,IACF;AAEA,cAAU,UAAU,OAAO;AAC3B,cAAU,UAAU,OAAO;AAC3B,cAAU,aAAa,YAAY;AACnC,cAAU,aAAa,YAAY;AACnC,cAAU,UAAU;AACpB,cAAU,SAAS;AACnB,cAAU,SAAS;AAAA,EACrB;AACF,CAAC;AAEH,IAAM,kBAAkB,OACtB,EAAE,MAAM,WAAW;AAAA,EACjB,MAAM;AAAA,EACN,KAAK;AAAA,EACL,YAAY;AAAA,EACZ,QAAQ;AAAA,IACN,MAAM,CAAC,SAAS,QAAQ,UAAU,UAAU,SAAS,YAAY;AAAA,IACjE,QAAQ,CAAC,cAAc,UAAU,UAAU,SAAS;AAAA,IACpD,YAAY;AAAA,MACV,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,SAAS,EAAE,MAAM,SAAS,UAAU,IAAI;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,QAAQ,CAAC,EAAE,MAAM,MAAM,MAAM;AAC3B,SAAK,IAAI,OAAO,KAAK;AAAA,MACnB,KAAK,gBAAgB,eAAe,CAAC,GAAG;AAAA,QACtC,IAAI,MAAM;AAAA,QACV,OAAO,MAAM;AAAA,QACb,MAAM,MAAM;AAAA,QACZ,QAAQ,MAAM;AAAA,QACd,QAAQ,MAAM;AAAA,QACd,OAAO,MAAM;AAAA,QACb,YAAY,MAAM;AAAA,MACpB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO,CAAC,EAAE,MAAM,OAAO,eAAe,MAAM;AAC1C,UAAM,EAAE,KAAK,IAAI,KAAK;AAEtB,UAAM,EAAE,OAAO,MAAM,QAAQ,QAAQ,MAAM,IAAI;AAE/C,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,QAAQ;AAGb,SAAK,UAAU,iBAAiB,IAAI;AAGpC,QAAI,kBAAkB,KAAK,KAAK,QAAQ,OAAQ;AAGhD,UAAM,mBAAmB,MAAM,SAAS,MAAM;AAC9C,QAAI,cAAc,KAAK,eAAe;AAGtC,UAAM,iBAAiB,KAAK,KAAK,MAAM;AACvC,UAAM,kBAAkB,KAAK,KAAK,MAAM;AAExC,QAAI,qBAAqB,KAAK,MAAM,0BAA0B;AAC9D,UAAM,mBAAmB,KAAK,MAAM,8BAA8B;AAClE,UAAM,mBAAmB,KAAK,MAAM,8BAA8B;AAElE,UAAM,mBAAmB,KAAK,MAAM,wBAAwB;AAC5D,UAAM,gBAAgB,KAAK,MAAM,oBAAoB;AAErD,QAAI,oBAAoB,CAAC,eAAe;AACtC,2BAAqB,iBAAiB;AACtC,oBAAc;AAAA,IAChB;AAGA,QAAI,aACF,uBAAuB,OACnB,qBACA,KAAK;AAAA,MACH;AAAA,MACA,KAAK,IAAI,iBAAiB,aAAa,gBAAgB;AAAA,IACzD;AAEN,QAAI,YAAY,aAAa;AAC7B,QAAI,YAAY,gBAAgB;AAC9B,kBAAY;AACZ,mBAAa,YAAY;AAAA,IAC3B;AAEA,QAAI,aAAa,iBAAiB;AAChC,mBAAa;AACb,kBAAY,kBAAkB;AAAA,IAChC;AAEA,SAAK,QAAQ;AACb,SAAK,SAAS;AAAA,EAChB;AACF,CAAC;AAEH,IAAI,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoBf,IAAI,kBAAkB;AAEtB,IAAM,yBAAyB,WAC7B,MAAM,MAAM,WAAW;AAAA,EACrB,MAAM;AAAA,EACN,KAAK;AAAA,EACL,YAAY;AAAA,EACZ,QAAQ,CAAC,EAAE,MAAM,MAAM,MAAM;AAC3B,QAAI,OAAO;AACX,QAAI,SAAS,cAAc,MAAM,GAAG;AAClC,YAAM,MAAM,IAAI;AAAA,QACd,OAAO,SAAS,KAAK,QAAQ,OAAO,SAAS,MAAM,EAAE;AAAA,MACvD,EAAE;AACF,aAAO,KAAK,QAAQ,YAAY,SAAS,MAAM,GAAG;AAAA,IACpD;AAEA;AACA,SAAK,QAAQ,UAAU;AAAA,MACrB,mCAAmC,MAAM,MAAM;AAAA,IACjD;AACA,SAAK,QAAQ,YAAY,KAAK,QAAQ,YAAY,eAAe;AAAA,EACnE;AAAA,EACA,QAAQ;AAAA,IACN,QAAQ,CAAC,SAAS;AAAA,IAClB,YAAY;AAAA,MACV,SAAS,EAAE,MAAM,UAAU,MAAM,GAAG;AAAA,IACtC;AAAA,EACF;AACF,CAAC;AAKH,IAAM,eAAe,WAAW;AAC9B,OAAK,YAAY,OAAK;AACpB,sBAAkB,EAAE,KAAK,QAAQ,IAAI,EAAE,KAAK,YAAU;AACpD,WAAK,YAAY,EAAE,IAAI,EAAE,KAAK,IAAI,SAAS,OAAO,GAAG,CAAC,MAAM,CAAC;AAAA,IAC/D,CAAC;AAAA,EACH;AACF;AAKA,IAAM,oBAAoB,WAAW;AACnC,OAAK,YAAY,OAAK;AACpB,UAAM,YAAY,EAAE,KAAK,QAAQ;AACjC,UAAM,SAAS,EAAE,KAAK,QAAQ;AAE9B,UAAMC,QAAO,UAAU;AACvB,UAAM,IAAIA,MAAK;AAEf,UAAM,MAAM,OAAO,CAAC;AACpB,UAAM,MAAM,OAAO,CAAC;AACpB,UAAM,MAAM,OAAO,CAAC;AACpB,UAAM,MAAM,OAAO,CAAC;AACpB,UAAM,MAAM,OAAO,CAAC;AAEpB,UAAM,MAAM,OAAO,CAAC;AACpB,UAAM,MAAM,OAAO,CAAC;AACpB,UAAM,MAAM,OAAO,CAAC;AACpB,UAAM,MAAM,OAAO,CAAC;AACpB,UAAM,MAAM,OAAO,CAAC;AAEpB,UAAM,MAAM,OAAO,EAAE;AACrB,UAAM,MAAM,OAAO,EAAE;AACrB,UAAM,MAAM,OAAO,EAAE;AACrB,UAAM,MAAM,OAAO,EAAE;AACrB,UAAM,MAAM,OAAO,EAAE;AAErB,UAAM,MAAM,OAAO,EAAE;AACrB,UAAM,MAAM,OAAO,EAAE;AACrB,UAAM,MAAM,OAAO,EAAE;AACrB,UAAM,MAAM,OAAO,EAAE;AACrB,UAAM,MAAM,OAAO,EAAE;AAErB,QAAI,QAAQ,GACV,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI;AAEN,WAAO,QAAQ,GAAG,SAAS,GAAG;AAC5B,UAAIA,MAAK,KAAK,IAAI;AAClB,UAAIA,MAAK,QAAQ,CAAC,IAAI;AACtB,UAAIA,MAAK,QAAQ,CAAC,IAAI;AACtB,UAAIA,MAAK,QAAQ,CAAC,IAAI;AACtB,MAAAA,MAAK,KAAK,IAAI,KAAK;AAAA,QACjB;AAAA,QACA,KAAK,KAAK,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,OAAO,KAAK,GAAG;AAAA,MACnE;AACA,MAAAA,MAAK,QAAQ,CAAC,IAAI,KAAK;AAAA,QACrB;AAAA,QACA,KAAK,KAAK,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,OAAO,KAAK,GAAG;AAAA,MACnE;AACA,MAAAA,MAAK,QAAQ,CAAC,IAAI,KAAK;AAAA,QACrB;AAAA,QACA,KAAK,KAAK,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,OAAO,KAAK,GAAG;AAAA,MACnE;AACA,MAAAA,MAAK,QAAQ,CAAC,IAAI,KAAK;AAAA,QACrB;AAAA,QACA,KAAK,KAAK,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,OAAO,KAAK,GAAG;AAAA,MACnE;AAAA,IACF;AAEA,SAAK,YAAY,EAAE,IAAI,EAAE,KAAK,IAAI,SAAS,UAAU,GAAG;AAAA,MACtD,UAAU,KAAK;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAEA,IAAM,eAAe,CAAC,KAAK,OAAO;AAChC,MAAI,QAAQ,IAAI,MAAM;AACtB,QAAM,SAAS,MAAM;AACnB,UAAM,QAAQ,MAAM;AACpB,UAAM,SAAS,MAAM;AACrB,YAAQ;AACR,OAAG,OAAO,MAAM;AAAA,EAClB;AACA,QAAM,MAAM;AACd;AAEA,IAAM,aAAa;AAAA,EACjB,GAAG,MAAM,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,EAC1B,GAAG,WAAS,CAAC,IAAI,GAAG,GAAG,GAAG,OAAO,CAAC;AAAA,EAClC,GAAG,CAAC,OAAO,WAAW,CAAC,IAAI,GAAG,GAAG,IAAI,OAAO,MAAM;AAAA,EAClD,GAAG,CAAC,OAAO,WAAW,CAAC,GAAG,GAAG,GAAG,IAAI,GAAG,MAAM;AAAA,EAC7C,GAAG,MAAM,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,EAC1B,GAAG,CAAC,OAAO,WAAW,CAAC,GAAG,GAAG,IAAI,GAAG,QAAQ,CAAC;AAAA,EAC7C,GAAG,CAAC,OAAO,WAAW,CAAC,GAAG,IAAI,IAAI,GAAG,QAAQ,KAAK;AAAA,EAClD,GAAG,WAAS,CAAC,GAAG,IAAI,GAAG,GAAG,GAAG,KAAK;AACpC;AAEA,IAAM,sBAAsB,CAAC,KAAK,OAAO,QAAQ,gBAAgB;AAE/D,MAAI,gBAAgB,IAAI;AACtB;AAAA,EACF;AAEA,MAAI,UAAU,MAAM,KAAK,WAAW,WAAW,EAAE,OAAO,MAAM,CAAC;AACjE;AAGA,IAAM,qBAAqB,CAACA,OAAM,OAAO,QAAQ,gBAAgB;AAE/D,UAAQ,KAAK,MAAM,KAAK;AACxB,WAAS,KAAK,MAAM,MAAM;AAG1B,QAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,SAAO,QAAQ;AACf,SAAO,SAAS;AAChB,QAAM,MAAM,OAAO,WAAW,IAAI;AAGlC,MAAI,eAAe,KAAK,eAAe,GAAG;AACxC,KAAC,OAAO,MAAM,IAAI,CAAC,QAAQ,KAAK;AAAA,EAClC;AAGA,sBAAoB,KAAK,OAAO,QAAQ,WAAW;AAGnD,MAAI,UAAUA,OAAM,GAAG,GAAG,OAAO,MAAM;AAEvC,SAAO;AACT;AAEA,IAAM,WAAW,UAAQ,SAAS,KAAK,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,KAAK,IAAI;AAE1E,IAAM,YAAY;AAClB,IAAM,aAAa;AAEnB,IAAM,wBAAwB,WAAS;AACrC,QAAM,SAAS,KAAK,IAAI,YAAY,MAAM,OAAO,aAAa,MAAM,MAAM;AAE1E,QAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,QAAM,MAAM,OAAO,WAAW,IAAI;AAClC,QAAM,QAAS,OAAO,QAAQ,KAAK,KAAK,MAAM,QAAQ,MAAM;AAC5D,QAAM,SAAU,OAAO,SAAS,KAAK,KAAK,MAAM,SAAS,MAAM;AAC/D,MAAI,UAAU,OAAO,GAAG,GAAG,OAAO,MAAM;AACxC,MAAIA,QAAO;AACX,MAAI;AACF,IAAAA,QAAO,IAAI,aAAa,GAAG,GAAG,OAAO,MAAM,EAAE;AAAA,EAC/C,SAAS,GAAG;AACV,WAAO;AAAA,EACT;AACA,QAAM,IAAIA,MAAK;AAEf,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AAER,SAAO,IAAI,GAAG,KAAK,GAAG;AACpB,SAAKA,MAAK,CAAC,IAAIA,MAAK,CAAC;AACrB,SAAKA,MAAK,IAAI,CAAC,IAAIA,MAAK,IAAI,CAAC;AAC7B,SAAKA,MAAK,IAAI,CAAC,IAAIA,MAAK,IAAI,CAAC;AAAA,EAC/B;AAEA,MAAI,aAAa,GAAG,CAAC;AACrB,MAAI,aAAa,GAAG,CAAC;AACrB,MAAI,aAAa,GAAG,CAAC;AAErB,SAAO,EAAE,GAAG,GAAG,EAAE;AACnB;AAEA,IAAM,eAAe,CAAC,GAAG,MAAM,KAAK,MAAM,KAAK,KAAK,KAAK,IAAI,EAAE,CAAC;AAEhE,IAAM,cAAc,CAAC,QAAQ,WAAW;AACtC,WAAS,UAAU,SAAS,cAAc,QAAQ;AAClD,SAAO,QAAQ,OAAO;AACtB,SAAO,SAAS,OAAO;AACvB,QAAM,MAAM,OAAO,WAAW,IAAI;AAClC,MAAI,UAAU,QAAQ,GAAG,CAAC;AAC1B,SAAO;AACT;AAEA,IAAM,iBAAiB,eAAa;AAClC,MAAI;AACJ,MAAI;AACF,SAAK,IAAI,UAAU,UAAU,OAAO,UAAU,MAAM;AAAA,EACtD,SAAS,GAAG;AACV,UAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,UAAM,MAAM,OAAO,WAAW,IAAI;AAClC,SAAK,IAAI,gBAAgB,UAAU,OAAO,UAAU,MAAM;AAAA,EAC5D;AACA,KAAG,KAAK,IAAI,IAAI,kBAAkB,UAAU,IAAI,CAAC;AACjD,SAAO;AACT;AAEA,IAAM,YAAY,SAChB,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC/B,QAAM,MAAM,IAAI,MAAM;AACtB,MAAI,cAAc;AAClB,MAAI,SAAS,MAAM;AACjB,YAAQ,GAAG;AAAA,EACb;AACA,MAAI,UAAU,OAAK;AACjB,WAAO,CAAC;AAAA,EACV;AACA,MAAI,MAAM;AACZ,CAAC;AAEH,IAAM,yBAAyB,OAAK;AAElC,QAAM,cAAc,uBAAuB,CAAC;AAE5C,QAAM,YAAY,gBAAgB,CAAC;AAEnC,QAAM,EAAE,aAAa,IAAI,EAAE;AAE3B,QAAM,cAAc,CAAC,MAAM,QAAQ,WACjC,IAAI,QAAQ,aAAW;AAErB,QAAI,CAAC,KAAK,IAAI,WAAW;AACvB,WAAK,IAAI,YAAY,OAClB,WAAW,IAAI,EACf,aAAa,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;AAAA,IACnD;AAGA,UAAM,YAAY,eAAe,KAAK,IAAI,SAAS;AAEnD,QAAI,CAAC,UAAU,OAAO,WAAW,IAAI;AACnC,aAAO,WAAW,IAAI,EAAE,aAAa,WAAW,GAAG,CAAC;AACpD,aAAO,QAAQ;AAAA,IACjB;AAEA,UAAM,SAAS,aAAa,iBAAiB;AAC7C,WAAO;AAAA,MACL;AAAA,QACE;AAAA,QACA,aAAa;AAAA,MACf;AAAA,MACA,cAAY;AAEV,eAAO,WAAW,IAAI,EAAE,aAAa,UAAU,GAAG,CAAC;AAGnD,eAAO,UAAU;AAGjB,gBAAQ;AAAA,MACV;AAAA,MACA,CAAC,UAAU,KAAK,MAAM;AAAA,IACxB;AAAA,EACF,CAAC;AAEH,QAAM,kBAAkB,CAAC,MAAM,cAAc;AAC3C,SAAK,gBAAgB,SAAS;AAC9B,cAAU,MAAM,QAAQ;AACxB,cAAU,MAAM,SAAS;AACzB,cAAU,SAAS;AAAA,EACrB;AAGA,QAAM,aAAa,CAAC,EAAE,KAAK,MAAM;AAC/B,UAAM,YAAY,KAAK,IAAI,OAAO,MAAM;AACxC,cAAU,UAAU;AACpB,cAAU,aAAa;AACvB,SAAK,IAAI,aAAa,KAAK,SAAS;AACpC,WAAO;AAAA,EACT;AAGA,QAAM,YAAY,CAAC,EAAE,MAAM,OAAO,MAAM,MAAM;AAC5C,UAAM,KAAK,MAAM;AACjB,UAAM,OAAO,KAAK,MAAM,YAAY,EAAE,GAAG,CAAC;AAC1C,QAAI,CAAC,KAAM;AAEX,UAAM,OAAO,KAAK,YAAY,MAAM,KAAK;AAAA,MACvC,QAAQ;AAAA,QACN,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AAAA,MACA,MAAM;AAAA,QACJ,YAAY;AAAA,QACZ,UAAU;AAAA,MACZ;AAAA,MACA,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAEA,UAAM,aAAa,KAAK;AAAA,MACtB;AAAA,IACF;AAEA,QAAI;AACJ,QAAI;AACJ,QAAI,QAAQ;AACZ,QAAI,KAAK,MAAM,+BAA+B,GAAG;AAC/C,eAAS,KAAK,YAAY,QAAQ,KAAK,CAAC;AACxC,eAAS,KAAK,YAAY,QAAQ;AAClC,cAAQ;AAAA,IACV;AAGA,UAAM,YAAY,KAAK;AAAA,MACrB,KAAK,gBAAgB,WAAW;AAAA,QAC9B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,YAAY;AAAA,MACd,CAAC;AAAA,MACD,KAAK,WAAW;AAAA,IAClB;AACA,SAAK,IAAI,OAAO,KAAK,SAAS;AAG9B,cAAU,UAAU;AACpB,cAAU,SAAS;AACnB,cAAU,SAAS;AACnB,cAAU,aAAa;AAGvB,eAAW,MAAM;AACf,WAAK,SAAS,0BAA0B,EAAE,GAAG,CAAC;AAAA,IAChD,GAAG,GAAG;AAAA,EACR;AAEA,QAAMC,eAAc,CAAC,EAAE,MAAM,MAAM,MAAM;AACvC,UAAM,OAAO,KAAK,MAAM,YAAY,EAAE,IAAI,MAAM,GAAG,CAAC;AACpD,QAAI,CAAC,KAAM;AACX,UAAM,YAAY,KAAK,IAAI,OAAO,KAAK,IAAI,OAAO,SAAS,CAAC;AAC5D,cAAU,OAAO,KAAK,YAAY,MAAM;AACxC,cAAU,aAAa,KAAK;AAAA,MAC1B;AAAA,IACF;AACA,QAAI,KAAK,MAAM,+BAA+B,GAAG;AAC/C,gBAAU,QAAQ;AAClB,gBAAU,SAAS,KAAK,YAAY,QAAQ;AAC5C,gBAAU,SAAS,KAAK,YAAY,QAAQ;AAAA,IAC9C;AAAA,EACF;AAGA,QAAM,wBAAwB,CAAC,EAAE,MAAM,OAAO,OAAO,MAAM;AAEzD,QAAI,CAAC,4BAA4B,KAAK,OAAO,OAAO,GAAG,EAAG;AAG1D,QAAI,CAAC,KAAK,IAAI,OAAO,OAAQ;AAG7B,UAAM,OAAO,KAAK,MAAM,YAAY,EAAE,IAAI,MAAM,GAAG,CAAC;AACpD,QAAI,CAAC,KAAM;AAGX,QAAI,SAAS,KAAK,OAAO,OAAO,GAAG,GAAG;AACpC,YAAM,YAAY,KAAK,IAAI,OAAO,KAAK,IAAI,OAAO,SAAS,CAAC;AAC5D,kBAAY,MAAM,OAAO,OAAO,OAAO,UAAU,KAAK;AACtD;AAAA,IACF;AAEA,QAAI,qBAAqB,KAAK,OAAO,OAAO,GAAG,GAAG;AAChD,YAAM,OAAO,KAAK,YAAY,MAAM;AACpC,YAAM,QAAQ,KAAK,IAAI,OAAO,KAAK,IAAI,OAAO,SAAS,CAAC;AAGxD,UACE,QACA,KAAK,eACL,MAAM,QACN,MAAM,KAAK,eACX,KAAK,IAAI,KAAK,cAAc,MAAM,KAAK,WAAW,IAAI,MACtD;AACA,cAAM,YAAY,WAAW,EAAE,KAAK,CAAC;AACrC,kBAAU,EAAE,MAAM,OAAO,OAAO,YAAY,UAAU,KAAK,EAAE,CAAC;AAAA,MAChE,OAEK;AACH,QAAAA,aAAY,EAAE,MAAM,MAAM,CAAC;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAEA,QAAM,uBAAuB,UAAQ;AAGnC,UAAM,YAAY,OAAO,UAAU;AACnC,UAAM,YAAY,UAAU,MAAM,qBAAqB;AACvD,UAAM,iBAAiB,YAAY,SAAS,UAAU,CAAC,CAAC,IAAI;AAC5D,QAAI,mBAAmB,QAAQ,kBAAkB,GAAI,QAAO;AAE5D,WAAO,uBAAuB,UAAU,SAAS,IAAI;AAAA,EACvD;AAKA,QAAM,4BAA4B,CAAC,EAAE,MAAM,MAAM,MAAM;AACrD,UAAM,EAAE,GAAG,IAAI;AAGf,UAAM,OAAO,KAAK,MAAM,YAAY,EAAE;AACtC,QAAI,CAAC,KAAM;AAGX,UAAM,UAAU,IAAI,gBAAgB,KAAK,IAAI;AAG7C,iBAAa,SAAS,CAAC,OAAO,WAAW;AAEvC,WAAK,SAAS,oCAAoC;AAAA,QAChD;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,QAAM,cAAc,CAAC,EAAE,MAAM,MAAM,MAAM;AACvC,UAAM,EAAE,GAAG,IAAI;AAGf,UAAM,OAAO,KAAK,MAAM,YAAY,EAAE;AACtC,QAAI,CAAC,KAAM;AAGX,UAAM,UAAU,IAAI,gBAAgB,KAAK,IAAI;AAG7C,UAAM,sBAAsB,MAAM;AAEhC,gBAAU,OAAO,EAAE,KAAK,kBAAkB;AAAA,IAC5C;AAGA,UAAM,qBAAqB,eAAa;AAEtC,UAAI,gBAAgB,OAAO;AAI3B,YAAM,OAAO,KAAK,YAAY,MAAM,KAAK,CAAC;AAC1C,YAAM,cAAc,KAAK,eAAe;AAGxC,UAAI,EAAE,OAAO,OAAO,IAAI;AAGxB,UAAI,CAAC,SAAS,CAAC,OAAQ;AAEvB,UAAI,eAAe,KAAK,eAAe,GAAG;AACxC,SAAC,OAAO,MAAM,IAAI,CAAC,QAAQ,KAAK;AAAA,MAClC;AAIA,YAAM,qBAAqB,KAAK,IAAI,GAAG,OAAO,mBAAmB,IAAI;AAKrE,YAAM,aAAa,KAAK,MAAM,+BAA+B;AAG7D,YAAM,cAAc,aAAa;AAGjC,YAAM,oBAAoB,SAAS;AAGnC,YAAM,wBAAwB,KAAK,KAAK,QAAQ;AAChD,YAAM,yBAAyB,KAAK,KAAK,QAAQ;AAEjD,UAAI,aAAa;AACjB,UAAI,cAAc,aAAa;AAE/B,UAAI,oBAAoB,GAAG;AACzB,qBAAa,KAAK,IAAI,OAAO,wBAAwB,WAAW;AAChE,sBAAc,aAAa;AAAA,MAC7B,OAAO;AACL,sBAAc,KAAK,IAAI,QAAQ,yBAAyB,WAAW;AACnE,qBAAa,cAAc;AAAA,MAC7B;AAGA,YAAM,eAAe;AAAA,QACnB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAGA,YAAM,OAAO,MAAM;AAEjB,cAAMC,gBAAe,KAAK;AAAA,UACxB;AAAA,QACF,IACI,sBAAsB,IAAI,IAC1B;AACJ,aAAK,YAAY,SAASA,eAAc,IAAI;AAG5C,YAAI,WAAW,WAAW;AACxB,oBAAU,MAAM;AAAA,QAClB;AAGA,aAAK,IAAI,cAAc,UAAU;AAGjC,kBAAU,EAAE,MAAM,OAAO,OAAO,aAAa,CAAC;AAAA,MAChD;AAGA,YAAM,SAAS,KAAK,YAAY,QAAQ;AACxC,UAAI,QAAQ;AACV,oBAAY,MAAM,QAAQ,YAAY,EAAE,KAAK,IAAI;AAAA,MACnD,OAAO;AACL,aAAK;AAAA,MACP;AAAA,IACF;AAGA,QAAI,qBAAqB,KAAK,IAAI,GAAG;AAEnC,YAAM,SAAS,aAAa,YAAY;AAExC,aAAO;AAAA,QACL;AAAA,UACE,MAAM,KAAK;AAAA,QACb;AAAA,QACA,iBAAe;AAEb,iBAAO,UAAU;AAIjB,cAAI,CAAC,aAAa;AAChB,gCAAoB;AACpB;AAAA,UACF;AAGA,6BAAmB,WAAW;AAAA,QAChC;AAAA,MACF;AAAA,IACF,OAAO;AAEL,0BAAoB;AAAA,IACtB;AAAA,EACF;AAKA,QAAM,iBAAiB,CAAC,EAAE,KAAK,MAAM;AAEnC,UAAM,QAAQ,KAAK,IAAI,OAAO,KAAK,IAAI,OAAO,SAAS,CAAC;AACxD,UAAM,aAAa;AACnB,UAAM,SAAS;AACf,UAAM,SAAS;AACf,UAAM,UAAU;AAAA,EAClB;AAKA,QAAM,iBAAiB,CAAC,EAAE,KAAK,MAAM;AACnC,SAAK,IAAI,cAAc,UAAU;AACjC,SAAK,IAAI,aAAa,UAAU;AAChC,SAAK,IAAI,eAAe,UAAU;AAAA,EACpC;AAEA,QAAM,gBAAgB,CAAC,EAAE,KAAK,MAAM;AAClC,SAAK,IAAI,cAAc,UAAU;AACjC,SAAK,IAAI,aAAa,UAAU;AAAA,EAClC;AAEA,QAAM,wBAAwB,CAAC,EAAE,KAAK,MAAM;AAC1C,SAAK,IAAI,cAAc,UAAU;AACjC,SAAK,IAAI,eAAe,UAAU;AAAA,EACpC;AAKA,QAAM,SAAS,CAAC,EAAE,KAAK,MAAM;AAE3B,SAAK,IAAI,SAAS,CAAC;AAGnB,SAAK,IAAI,YAAY;AAGrB,SAAK,IAAI,eAAe,CAAC;AAGzB,SAAK,IAAI,gBAAgB,KAAK;AAAA,MAC5B,KAAK,gBAAgB,aAAa;AAAA,QAChC,SAAS;AAAA,QACT,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AAEA,SAAK,IAAI,iBAAiB,KAAK;AAAA,MAC7B,KAAK,gBAAgB,aAAa;AAAA,QAChC,SAAS;AAAA,QACT,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AAEA,SAAK,IAAI,eAAe,KAAK;AAAA,MAC3B,KAAK,gBAAgB,aAAa;AAAA,QAChC,SAAS;AAAA,QACT,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO,EAAE,MAAM,WAAW;AAAA,IACxB,MAAM;AAAA,IACN;AAAA,IACA,QAAQ,CAAC,QAAQ;AAAA,IACjB,MAAM,CAAC,QAAQ;AAAA,IACf,SAAS,CAAC,EAAE,KAAK,MAAM;AAErB,WAAK,IAAI,OAAO,QAAQ,eAAa;AACnC,kBAAU,MAAM,QAAQ;AACxB,kBAAU,MAAM,SAAS;AAAA,MAC3B,CAAC;AAAA,IACH;AAAA,IACA,cAAc,CAAC,EAAE,KAAK,MAAM;AAC1B,WAAK,IAAI,OAAO,QAAQ,eAAa;AACnC,kBAAU,QAAQ;AAAA,MACpB,CAAC;AAAA,IACH;AAAA,IACA,OAAO,EAAE,MAAM;AAAA,MACb;AAAA;AAAA,QAEE,wBAAwB;AAAA,QACxB,oCAAoC;AAAA,QACpC,kCAAkC;AAAA,QAClC,0BAA0B;AAAA;AAAA,QAG1B,2BAA2B;AAAA,QAC3B,iCAAiC;AAAA,QACjC,wBAAwB;AAAA,QACxB,8BAA8B;AAAA,QAC9B,2BAA2B;AAAA,QAC3B,4BAA4B;AAAA,MAC9B;AAAA,MACA,CAAC,EAAE,KAAK,MAAM;AAEZ,cAAM,gBAAgB,KAAK,IAAI,aAAa;AAAA,UAC1C,eAAa,UAAU,YAAY;AAAA,QACrC;AAGA,aAAK,IAAI,eAAe,KAAK,IAAI,aAAa;AAAA,UAC5C,eAAa,UAAU,UAAU;AAAA,QACnC;AAGA,sBAAc,QAAQ,eAAa,gBAAgB,MAAM,SAAS,CAAC;AACnE,sBAAc,SAAS;AAAA,MACzB;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAKA,IAAM,SAAS,WAAS;AACtB,QAAM,EAAE,WAAW,MAAM,IAAI;AAC7B,QAAM,EAAE,MAAM,aAAa,OAAO,IAAI;AAGtC,QAAM,mBAAmB,uBAAuB,KAAK;AAGrD,YAAU,eAAe,aAAW;AAElC,UAAM,EAAE,IAAI,MAAM,MAAM,IAAI;AAG5B,QAAI,CAAC,GAAG,MAAM,KAAK,CAAC,MAAM,yBAAyB,EAAG;AAGtD,UAAM,cAAc,CAAC,EAAE,MAAM,MAAM,MAAM;AACvC,YAAM,EAAE,GAAG,IAAI;AACf,YAAM,OAAO,MAAM,YAAY,EAAE;AAGjC,UAAI,CAAC,QAAQ,CAAC,OAAO,KAAK,IAAI,KAAK,KAAK,SAAU;AAGlD,YAAM,OAAO,KAAK;AAGlB,UAAI,CAAC,mBAAmB,IAAI,EAAG;AAG/B,UAAI,CAAC,MAAM,+BAA+B,EAAE,IAAI,EAAG;AAInD,YAAM,4BAA4B,wBAAwB,UAAU,CAAC;AACrE,YAAM,qBAAqB,MAAM,iCAAiC;AAClE,UACE,CAAC,8BACA,sBAAsB,KAAK,OAAO;AAEnC;AAGF,WAAK,IAAI,eAAe,KAAK;AAAA,QAC3B,KAAK,gBAAgB,kBAAkB,EAAE,GAAG,CAAC;AAAA,MAC/C;AAGA,YAAM,qBAAqB,KAAK,MAAM,0BAA0B;AAChE,UAAI,oBAAoB;AACtB,aAAK,SAAS,2BAA2B;AAAA,UACvC,IAAI,KAAK;AAAA,UACT,QAAQ;AAAA,QACV,CAAC;AAAA,MACH;AAGA,YAAM,QACJ,CAAC,6BACD,KAAK,OAAO,MAAM,iDAAiD;AACrE,WAAK,SAAS,sCAAsC,EAAE,GAAG,GAAG,KAAK;AAAA,IACnE;AAEA,UAAM,cAAc,CAAC,MAAM,UAAU;AACnC,UAAI,CAAC,KAAK,IAAI,aAAc;AAE5B,UAAI,EAAE,GAAG,IAAI;AAGb,YAAM,OAAO,KAAK,MAAM,YAAY,EAAE,GAAG,CAAC;AAC1C,UAAI,CAAC,KAAM;AAGX,YAAM,mBAAmB,KAAK,MAAM,wBAAwB;AAC5D,YAAM,uBAAuB,KAAK,MAAM,6BAA6B;AACrE,YAAM,cAAc,KAAK,MAAM,0BAA0B;AACzD,UAAI,oBAAoB,wBAAwB,YAAa;AAG7D,UAAI,EAAE,YAAY,YAAY,IAAI,KAAK;AACvC,UAAI,CAAC,cAAc,CAAC,YAAa;AAGjC,YAAM,mBAAmB,KAAK,MAAM,8BAA8B;AAClE,YAAM,mBAAmB,KAAK,MAAM,8BAA8B;AAGlE,YAAM,OAAO,KAAK,YAAY,MAAM,KAAK,CAAC;AAC1C,YAAM,cAAc,KAAK,eAAe;AAGxC,UAAI,eAAe,KAAK,eAAe;AACrC,SAAC,YAAY,WAAW,IAAI,CAAC,aAAa,UAAU;AAGtD,UAAI,CAAC,SAAS,KAAK,IAAI,KAAK,KAAK,MAAM,2BAA2B,GAAG;AACnE,cAAM,SAAS,OAAO;AACtB,sBAAc;AACd,uBAAe;AAAA,MACjB;AAGA,YAAM,mBAAmB,cAAc;AAGvC,YAAM,sBACH,KAAK,YAAY,MAAM,KAAK,CAAC,GAAG,eAAe;AAGlD,UAAI,mBAAmB,KAAK;AAAA,QAC1B;AAAA,QACA,KAAK,IAAI,aAAa,gBAAgB;AAAA,MACxC;AACA,YAAM,YAAY,KAAK,KAAK,QAAQ;AACpC,YAAM,gBAAgB,KAAK;AAAA,QACzB,YAAY;AAAA,QACZ;AAAA,MACF;AAGA,WAAK,SAAS,2BAA2B;AAAA,QACvC,IAAI,KAAK;AAAA,QACT,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AAEA,UAAM,gBAAgB,CAAC,EAAE,KAAK,MAAM;AAElC,WAAK,IAAI,gBAAgB;AAAA,IAC3B;AAEA,UAAM,wBAAwB,CAAC,EAAE,MAAM,OAAO,MAAM;AAClD,UAAI,OAAO,OAAO,QAAQ,OAAQ;AAGlC,WAAK,IAAI,gBAAgB;AAAA,IAC3B;AAEA,UAAM,0BAA0B,CAAC,EAAE,MAAM,OAAO,MAAM;AAEpD,WAAK,IAAI,aAAa,OAAO;AAC7B,WAAK,IAAI,cAAc,OAAO;AAG9B,WAAK,IAAI,gBAAgB;AACzB,WAAK,IAAI,oBAAoB;AAG7B,WAAK,SAAS,MAAM;AAAA,IACtB;AAGA,SAAK;AAAA,MACH;AAAA,QACE;AAAA,UACE,iBAAiB;AAAA,UACjB,iBAAiB;AAAA,UACjB,eAAe;AAAA,UACf,kCAAkC;AAAA,UAClC,0BAA0B;AAAA,QAC5B;AAAA,QACA,CAAC,EAAE,MAAM,MAAM,MAAM;AAEnB,cAAI,CAAC,KAAK,IAAI,aAAc;AAG5B,cAAI,KAAK,KAAK,QAAQ,OAAQ;AAG9B,cAAI,KAAK,IAAI,eAAe;AAC1B,wBAAY,MAAM,KAAK;AACvB,iBAAK,IAAI,gBAAgB;AAAA,UAC3B;AAEA,cAAI,KAAK,IAAI,mBAAmB;AAE9B,kCAAsB,MAAM;AAG1B,oCAAsB,MAAM;AAC1B,qBAAK,SAAS,oCAAoC;AAAA,kBAChD,IAAI,MAAM;AAAA,gBACZ,CAAC;AAAA,cACH,CAAC;AAAA,YACH,CAAC;AAED,iBAAK,IAAI,oBAAoB;AAAA,UAC/B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AAGD,SAAO;AAAA,IACL,SAAS;AAAA;AAAA,MAEP,mBAAmB,CAAC,MAAM,KAAK,OAAO;AAAA;AAAA,MAGtC,wBAAwB,CAAC,MAAM,MAAM,KAAK,QAAQ;AAAA;AAAA,MAGlD,oBAAoB,CAAC,MAAM,KAAK,GAAG;AAAA;AAAA,MAGnC,uBAAuB,CAAC,IAAI,KAAK,GAAG;AAAA;AAAA,MAGpC,uBAAuB,CAAC,KAAK,KAAK,GAAG;AAAA;AAAA,MAGrC,yBAAyB,CAAC,MAAM,KAAK,GAAG;AAAA;AAAA,MAGxC,wBAAwB,CAAC,GAAG,KAAK,GAAG;AAAA;AAAA,MAGpC,qBAAqB,CAAC,OAAO,KAAK,OAAO;AAAA;AAAA,MAGzC,uCAAuC,CAAC,KAAS,KAAK,GAAG;AAAA;AAAA,MAGzD,mCAAmC,CAAC,MAAM,KAAK,MAAM;AAAA;AAAA,MAGrD,wCAAwC,CAAC,OAAO,KAAK,OAAO;AAAA;AAAA,MAG5D,wBAAwB,CAAC,MAAM,KAAK,OAAO;AAAA;AAAA,MAG3C,0BAA0B,CAAC,MAAM,MAAM,KAAK,QAAQ;AAAA,IACtD;AAAA,EACF;AACF;AAGA,IAAM,YACJ,OAAO,WAAW,eAAe,OAAO,OAAO,aAAa;AAC9D,IAAI,WAAW;AACb,WAAS;AAAA,IACP,IAAI,YAAY,yBAAyB,EAAE,QAAQ,OAAO,CAAC;AAAA,EAC7D;AACF;AAEA,IAAO,4CAAQ;", "names": ["size", "markup", "data", "updateImage", "averageColor"]}