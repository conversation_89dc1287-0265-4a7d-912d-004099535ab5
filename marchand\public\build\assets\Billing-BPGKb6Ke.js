import{m as y,j as e,L as C}from"./app-CZ-bNXzO.js";import{c as w,B as g}from"./createLucideIcon-DkVbiciO.js";import{C as S,a as k,b as F,c as L,d as A}from"./card-a7oWrl6x.js";import{L as n,I as c}from"./label-Dqs5U4J9.js";import{S as d,a as x,b as u,c as m,d as p}from"./select-Dj4gZH27.js";import{P as M}from"./progress-ChVphGtn.js";import{A as D,a as I}from"./alert-BQaw_4dw.js";import{u as Y}from"./use-translation-Bbr1NOyb.js";import{L as T,A as V}from"./appearance-dropdown-DYCFlxYQ.js";import{C as B}from"./credit-card-BRqlFN91.js";import{M as P}from"./map-pin-y-GvMobl.js";import"./index-k7GfkN7R.js";import"./index-uB82XUlN.js";import"./dropdown-menu-DUK2pmBw.js";import"./check-Cg80uYQX.js";import"./index-g7rG-vGS.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const E=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]],H=w("Info",E);function te({user:O,marchand:r,countries:j}){const{translate:l}=Y(),{data:i,setData:a,post:b,processing:_,errors:t}=y({numero_carte:(r==null?void 0:r.numero_carte)||"",nom_porteur_carte:(r==null?void 0:r.nom_porteur_carte)||"",mois_expiration:(r==null?void 0:r.mois_expiration)||"",annee_expiration:(r==null?void 0:r.annee_expiration)||"",adresse_facturation_ligne1:(r==null?void 0:r.adresse_facturation_ligne1)||"",adresse_facturation_ligne2:(r==null?void 0:r.adresse_facturation_ligne2)||"",ville_facturation:(r==null?void 0:r.ville_facturation)||"",region_facturation:(r==null?void 0:r.region_facturation)||"",code_postal_facturation:(r==null?void 0:r.code_postal_facturation)||"",pays_facturation:(r==null?void 0:r.pays_facturation)||""}),h=s=>{s.preventDefault(),b(route("seller.billing.store"))},v=Array.from({length:12},(s,o)=>({value:String(o+1).padStart(2,"0"),label:String(o+1).padStart(2,"0")})),f=new Date().getFullYear(),N=Array.from({length:20},(s,o)=>({value:String(f+o),label:String(f+o)}));return e.jsxs(e.Fragment,{children:[e.jsx(C,{title:l("seller.billing.title")}),e.jsxs("div",{className:"min-h-screen bg-background text-foreground",children:[e.jsx("header",{className:"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("nav",{className:"flex h-16 items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-2xl font-bold text-primary",children:"Lorelei"}),e.jsx("span",{className:"text-xl font-medium text-muted-foreground",children:"Marchand"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(T,{}),e.jsx(V,{})]})]})})}),e.jsxs("div",{className:"container mx-auto px-4 py-8 max-w-4xl",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("div",{className:"flex items-center justify-center mb-6",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("div",{className:"w-10 h-10 bg-primary text-primary-foreground rounded-full flex items-center justify-center",children:"✓"}),e.jsx("span",{className:"text-sm font-medium text-foreground mt-2",children:l("seller.steps.information")})]}),e.jsx("div",{className:"w-16 h-0.5 bg-primary"}),e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("div",{className:"w-10 h-10 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-semibold",children:"2"}),e.jsx("span",{className:"text-sm font-medium text-foreground mt-2",children:l("seller.steps.billing")})]}),e.jsx("div",{className:"w-16 h-0.5 bg-border"}),e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("div",{className:"w-10 h-10 bg-muted text-muted-foreground rounded-full flex items-center justify-center font-semibold",children:"3"}),e.jsx("span",{className:"text-sm text-muted-foreground mt-2",children:l("seller.steps.store")})]}),e.jsx("div",{className:"w-16 h-0.5 bg-border"}),e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("div",{className:"w-10 h-10 bg-muted text-muted-foreground rounded-full flex items-center justify-center font-semibold",children:"4"}),e.jsx("span",{className:"text-sm text-muted-foreground mt-2",children:l("seller.steps.verification")})]})]})}),e.jsx(M,{value:50,className:"h-2"})]}),e.jsxs(S,{className:"border-border bg-card",children:[e.jsxs(k,{children:[e.jsxs(F,{className:"flex items-center space-x-2 text-card-foreground",children:[e.jsx(B,{className:"w-5 h-5 text-primary"}),e.jsx("span",{children:l("seller.billing.form_title")})]}),e.jsx(L,{className:"text-muted-foreground",children:l("seller.billing.form_description")})]}),e.jsx(A,{children:e.jsxs("form",{onSubmit:h,className:"space-y-8",children:[e.jsxs(D,{className:"border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950",children:[e.jsx(H,{className:"h-4 w-4 text-blue-600 dark:text-blue-400"}),e.jsx(I,{className:"text-blue-800 dark:text-blue-200",children:l("seller.billing.subscription_info")})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-foreground border-b border-border pb-2",children:l("seller.billing.card_details")}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs(n,{htmlFor:"numero_carte",children:[l("seller.billing.card_number")," *"]}),e.jsx(c,{id:"numero_carte",value:i.numero_carte,onChange:s=>a("numero_carte",s.target.value),placeholder:"1234 5678 9012 3456",className:t.numero_carte?"border-destructive":""}),t.numero_carte&&e.jsx("p",{className:"text-sm text-destructive",children:t.numero_carte})]}),e.jsxs("div",{className:"grid md:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"space-y-2 md:col-span-2",children:[e.jsxs(n,{htmlFor:"nom_porteur_carte",children:[l("seller.billing.cardholder_name")," *"]}),e.jsx(c,{id:"nom_porteur_carte",value:i.nom_porteur_carte,onChange:s=>a("nom_porteur_carte",s.target.value),placeholder:l("seller.billing.cardholder_name_placeholder"),className:t.nom_porteur_carte?"border-destructive":""}),t.nom_porteur_carte&&e.jsx("p",{className:"text-sm text-destructive",children:t.nom_porteur_carte})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(n,{children:[l("seller.billing.expiry_date")," *"]}),e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.jsxs(d,{value:i.mois_expiration,onValueChange:s=>a("mois_expiration",s),children:[e.jsx(x,{className:t.mois_expiration?"border-destructive":"",children:e.jsx(u,{placeholder:"MM"})}),e.jsx(m,{children:v.map(s=>e.jsx(p,{value:s.value,children:s.label},s.value))})]}),e.jsxs(d,{value:i.annee_expiration,onValueChange:s=>a("annee_expiration",s),children:[e.jsx(x,{className:t.annee_expiration?"border-destructive":"",children:e.jsx(u,{placeholder:"YYYY"})}),e.jsx(m,{children:N.map(s=>e.jsx(p,{value:s.value,children:s.label},s.value))})]})]}),(t.mois_expiration||t.annee_expiration)&&e.jsx("p",{className:"text-sm text-destructive",children:t.mois_expiration||t.annee_expiration})]})]})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("h3",{className:"text-lg font-semibold text-foreground border-b border-border pb-2 flex items-center space-x-2",children:[e.jsx(P,{className:"w-4 h-4"}),e.jsx("span",{children:l("seller.billing.billing_address")})]}),e.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs(n,{htmlFor:"adresse_facturation_ligne1",children:[l("seller.billing.address_line1")," *"]}),e.jsx(c,{id:"adresse_facturation_ligne1",value:i.adresse_facturation_ligne1,onChange:s=>a("adresse_facturation_ligne1",s.target.value),placeholder:l("seller.billing.address_line1_placeholder"),className:t.adresse_facturation_ligne1?"border-destructive":""}),t.adresse_facturation_ligne1&&e.jsx("p",{className:"text-sm text-destructive",children:t.adresse_facturation_ligne1})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"adresse_facturation_ligne2",children:l("seller.billing.address_line2")}),e.jsx(c,{id:"adresse_facturation_ligne2",value:i.adresse_facturation_ligne2,onChange:s=>a("adresse_facturation_ligne2",s.target.value),placeholder:l("seller.billing.address_line2_placeholder")})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(n,{htmlFor:"ville_facturation",children:[l("seller.billing.city")," *"]}),e.jsx(c,{id:"ville_facturation",value:i.ville_facturation,onChange:s=>a("ville_facturation",s.target.value),placeholder:l("seller.billing.city_placeholder"),className:t.ville_facturation?"border-destructive":""}),t.ville_facturation&&e.jsx("p",{className:"text-sm text-destructive",children:t.ville_facturation})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(n,{htmlFor:"region_facturation",children:[l("seller.billing.region")," *"]}),e.jsx(c,{id:"region_facturation",value:i.region_facturation,onChange:s=>a("region_facturation",s.target.value),placeholder:l("seller.billing.region_placeholder"),className:t.region_facturation?"border-destructive":""}),t.region_facturation&&e.jsx("p",{className:"text-sm text-destructive",children:t.region_facturation})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(n,{htmlFor:"code_postal_facturation",children:[l("seller.billing.postal_code")," *"]}),e.jsx(c,{id:"code_postal_facturation",value:i.code_postal_facturation,onChange:s=>a("code_postal_facturation",s.target.value),placeholder:l("seller.billing.postal_code_placeholder"),className:t.code_postal_facturation?"border-destructive":""}),t.code_postal_facturation&&e.jsx("p",{className:"text-sm text-destructive",children:t.code_postal_facturation})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(n,{htmlFor:"pays_facturation",children:[l("seller.billing.country")," *"]}),e.jsxs(d,{value:i.pays_facturation,onValueChange:s=>a("pays_facturation",s),children:[e.jsx(x,{className:t.pays_facturation?"border-destructive":"",children:e.jsx(u,{placeholder:l("seller.billing.select_country")})}),e.jsx(m,{children:Object.entries(j).map(([s,o])=>e.jsx(p,{value:s,children:o},s))})]}),t.pays_facturation&&e.jsx("p",{className:"text-sm text-destructive",children:t.pays_facturation})]})]})]}),e.jsxs("div",{className:"flex justify-between pt-6 border-t border-border",children:[e.jsx(g,{type:"button",variant:"outline",asChild:!0,children:e.jsx("a",{href:route("seller.information"),children:l("seller.billing.previous")})}),e.jsx(g,{type:"submit",disabled:_,className:"min-w-[150px]",children:l(_?"seller.billing.saving":"seller.billing.continue")})]})]})})]})]})]})]})}export{te as default};
