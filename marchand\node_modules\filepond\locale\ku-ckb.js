export default {
    labelIdle: 'پەڕگەکان فڕێ بدە ئێرە بۆ بارکردن یان <span class="filepond--label-action"> هەڵبژێرە </span>',
    labelInvalidField: 'پەڕگەی نادروستی تێدایە',
    labelFileWaitingForSize: 'چاوەڕوانیی قەبارە',
    labelFileSizeNotAvailable: 'قەبارە بەردەست نیە',
    labelFileLoading: 'بارکردن',
    labelFileLoadError: 'هەڵە لەماوەی بارکردن',
    labelFileProcessing: 'بارکردن',
    labelFileProcessingComplete: 'بارکردن تەواو بوو',
    labelFileProcessingAborted: 'بارکردن هەڵوەشایەوە',
    labelFileProcessingError: 'هەڵە لەکاتی بارکردندا',
    labelFileProcessingRevertError: 'هەڵە لە کاتی گەڕانەوە',
    labelFileRemoveError: 'هەڵە لە کاتی سڕینەوە',
    labelTapToCancel: 'بۆ هەڵوەشاندنەوە Tab دابگرە',
    labelTapToRetry: 'tap دابگرە بۆ دووبارەکردنەوە',
    labelTapToUndo: 'tap دابگرە بۆ گەڕاندنەوە',
    labelButtonRemoveItem: 'سڕینەوە',
    labelButtonAbortItemLoad: 'هەڵوەشاندنەوە',
    labelButtonRetryItemLoad: 'هەوڵدانەوە',
    labelButtonAbortItemProcessing: 'پەشیمانبوونەوە',
    labelButtonUndoItemProcessing: 'گەڕاندنەوە',
    labelButtonRetryItemProcessing: 'هەوڵدانەوە',
    labelButtonProcessItem: 'بارکردن',
    labelMaxFileSizeExceeded: 'پەڕگە زۆر گەورەیە',
    labelMaxFileSize: 'زۆرترین قەبارە {filesize}',
    labelMaxTotalFileSizeExceeded: 'زۆرترین قەبارەی کۆی گشتی تێپەڕێندرا',
    labelMaxTotalFileSize: 'زۆرترین قەبارەی کۆی پەڕگە {filesize}',
    labelFileTypeNotAllowed: 'جۆری پەڕگەکە نادروستە',
    fileValidateTypeLabelExpectedTypes: 'جگە لە {allButLastType} یان {lastType}',
    imageValidateSizeLabelFormatError: 'جۆری وێنە پاڵپشتیی نەکراوە',
    imageValidateSizeLabelImageSizeTooSmall: 'وێنەکە زۆر بچووکە',
    imageValidateSizeLabelImageSizeTooBig: 'وێنەکە زۆر گەورەیە',
    imageValidateSizeLabelExpectedMinSize: 'کەمترین قەبارە {minWidth} × {minHeight}',
    imageValidateSizeLabelExpectedMaxSize: 'زۆرترین قەبارە {maxWidth} × {maxHeight}',
    imageValidateSizeLabelImageResolutionTooLow: 'وردبینییەکەی زۆر کەمە',
    imageValidateSizeLabelImageResolutionTooHigh: 'وردبینییەکەی زۆر بەرزە',
    imageValidateSizeLabelExpectedMinResolution: 'کەمترین وردبینیی {minResolution}',
    imageValidateSizeLabelExpectedMaxResolution: 'زۆرترین وردبینی {maxResolution}'
};
