import{j as e,u as j}from"./app-CZ-bNXzO.js";import{c as g,B as h}from"./createLucideIcon-DkVbiciO.js";import{l as m,m as p,n as x,k as l}from"./dropdown-menu-DUK2pmBw.js";import{u,S as t,M as i,a as d}from"./use-translation-Bbr1NOyb.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const f=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]],N=g("Globe",f);function M(){var a;const{currentLocale:r,setLocale:o}=u(),c=[{code:"fr",name:"Français",flag:"🇫🇷",display:"FR"},{code:"en",name:"English",flag:"🇬🇧",display:"EN"}],n=s=>{o(s),window.location.reload()};return e.jsxs(m,{children:[e.jsx(p,{asChild:!0,children:e.jsxs(h,{variant:"ghost",size:"icon",className:"relative",children:[e.jsx(N,{className:"h-5 w-5"}),e.jsx("span",{className:"sr-only",children:"Changer de langue"}),e.jsx("span",{className:"absolute -bottom-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-primary text-[10px] font-bold text-primary-foreground",children:((a=c.find(s=>s.code===r))==null?void 0:a.display)||"FR"})]})}),e.jsx(x,{align:"end",children:c.map(s=>e.jsxs(l,{onClick:()=>n(s.code),className:`flex cursor-pointer items-center gap-2 ${r===s.code?"bg-muted font-medium":""}`,children:[e.jsx("span",{className:"text-lg",children:s.flag}),e.jsx("span",{children:s.name})]},s.code))})]})}function b({className:r="",...o}){const{appearance:c,updateAppearance:n}=j(),{translate:a}=u(),s=()=>{switch(c){case"dark":return e.jsx(i,{className:"h-5 w-5"});case"light":return e.jsx(t,{className:"h-5 w-5"});default:return e.jsx(d,{className:"h-5 w-5"})}};return e.jsx("div",{className:r,...o,children:e.jsxs(m,{children:[e.jsx(p,{asChild:!0,children:e.jsxs(h,{variant:"ghost",size:"icon",className:"h-9 w-9 rounded-md",children:[s(),e.jsx("span",{className:"sr-only",children:a("header.theme")})]})}),e.jsxs(x,{align:"end",children:[e.jsx(l,{onClick:()=>n("light"),children:e.jsxs("span",{className:"flex items-center gap-2",children:[e.jsx(t,{className:"h-5 w-5"}),a("header.light_mode")]})}),e.jsx(l,{onClick:()=>n("dark"),children:e.jsxs("span",{className:"flex items-center gap-2",children:[e.jsx(i,{className:"h-5 w-5"}),a("header.dark_mode")]})}),e.jsx(l,{onClick:()=>n("system"),children:e.jsxs("span",{className:"flex items-center gap-2",children:[e.jsx(d,{className:"h-5 w-5"}),a("header.system_mode")]})})]})]})})}export{b as A,N as G,M as L};
