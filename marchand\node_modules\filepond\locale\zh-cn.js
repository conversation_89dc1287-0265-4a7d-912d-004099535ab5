export default {
    labelIdle: '拖放文件，或者 <span class="filepond--label-action"> 浏览 </span>',
    labelInvalidField: '字段包含无效文件',
    labelFileWaitingForSize: '计算文件大小',
    labelFileSizeNotAvailable: '文件大小不可用',
    labelFileLoading: '加载',
    labelFileLoadError: '加载错误',
    labelFileProcessing: '上传',
    labelFileProcessingComplete: '已上传',
    labelFileProcessingAborted: '上传已取消',
    labelFileProcessingError: '上传出错',
    labelFileProcessingRevertError: '还原出错',
    labelFileRemoveError: '删除出错',
    labelTapToCancel: '点击取消',
    labelTapToRetry: '点击重试',
    labelTapToUndo: '点击撤消',
    labelButtonRemoveItem: '删除',
    labelButtonAbortItemLoad: '中止',
    labelButtonRetryItemLoad: '重试',
    labelButtonAbortItemProcessing: '取消',
    labelButtonUndoItemProcessing: '撤消',
    labelButtonRetryItemProcessing: '重试',
    labelButtonProcessItem: '上传',
    labelMaxFileSizeExceeded: '文件太大',
    labelMaxFileSize: '最大值: {filesize}',
    labelMaxTotalFileSizeExceeded: '超过最大文件大小',
    labelMaxTotalFileSize: '最大文件大小：{filesize}',
    labelFileTypeNotAllowed: '文件类型无效',
    fileValidateTypeLabelExpectedTypes: '应为 {allButLastType} 或 {lastType}',
    imageValidateSizeLabelFormatError: '不支持图像类型',
    imageValidateSizeLabelImageSizeTooSmall: '图像太小',
    imageValidateSizeLabelImageSizeTooBig: '图像太大',
    imageValidateSizeLabelExpectedMinSize: '最小值: {minWidth} × {minHeight}',
    imageValidateSizeLabelExpectedMaxSize: '最大值: {maxWidth} × {maxHeight}',
    imageValidateSizeLabelImageResolutionTooLow: '分辨率太低',
    imageValidateSizeLabelImageResolutionTooHigh: '分辨率太高',
    imageValidateSizeLabelExpectedMinResolution: '最小分辨率：{minResolution}',
    imageValidateSizeLabelExpectedMaxResolution: '最大分辨率：{maxResolution}'
  };
