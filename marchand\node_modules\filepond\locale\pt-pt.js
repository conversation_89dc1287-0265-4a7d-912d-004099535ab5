export default {
    labelIdle: '<PERSON><PERSON><PERSON> & Largue os ficheiros ou <span class="filepond--label-action"> Seleccione </span>',
    labelInvalidField: 'O campo contém ficheiros inválidos',
    labelFileWaitingForSize: 'A aguardar tamanho',
    labelFileSizeNotAvailable: 'Tamanho não disponível',
    labelFileLoading: 'A carregar',
    labelFileLoadError: 'Erro ao carregar',
    labelFileProcessing: 'A carregar',
    labelFileProcessingComplete: 'Carregamento completo',
    labelFileProcessingAborted: 'Carregamento cancelado',
    labelFileProcessingError: 'Erro ao carregar',
    labelFileProcessingRevertError: 'Erro ao reverter',
    labelFileRemoveError: 'Erro ao remover',
    labelTapToCancel: 'carregue para cancelar',
    labelTapToRetry: 'carregue para tentar novamente',
    labelTapToUndo: 'carregue para desfazer',
    labelButtonRemoveItem: 'Remover',
    labelButtonAbortItemLoad: 'Abortar',
    labelButtonRetryItemLoad: 'Tentar novamente',
    labelButtonAbortItemProcessing: 'Cancelar',
    labelButtonUndoItemProcessing: 'Desfazer',
    labelButtonRetryItemProcessing: 'Tentar novamente',
    labelButtonProcessItem: 'Carregar',
    labelMaxFileSizeExceeded: 'Ficheiro demasiado grande',
    labelMaxFileSize: 'O tamanho máximo do ficheiro é de {filesize}',
    labelMaxTotalFileSizeExceeded: 'Tamanho máximo total excedido',
    labelMaxTotalFileSize: 'O tamanho máximo total do ficheiro é de {filesize}',
    labelFileTypeNotAllowed: 'Tipo de ficheiro inválido',
    fileValidateTypeLabelExpectedTypes: 'É esperado {allButLastType} ou {lastType}',
    imageValidateSizeLabelFormatError: 'Tipo de imagem não suportada',
    imageValidateSizeLabelImageSizeTooSmall: 'A imagem é demasiado pequena',
    imageValidateSizeLabelImageSizeTooBig: 'A imagem é demasiado grande',
    imageValidateSizeLabelExpectedMinSize: 'O tamanho mínimo é de {minWidth} × {minHeight}',
    imageValidateSizeLabelExpectedMaxSize: 'O tamanho máximo é de {maxWidth} × {maxHeight}',
    imageValidateSizeLabelImageResolutionTooLow: 'A resolução é demasiado baixa',
    imageValidateSizeLabelImageResolutionTooHigh: 'A resolução é demasiado grande',
    imageValidateSizeLabelExpectedMinResolution: 'A resolução mínima é de {minResolution}',
    imageValidateSizeLabelExpectedMaxResolution: 'A resolução máxima é de {maxResolution}'
};
