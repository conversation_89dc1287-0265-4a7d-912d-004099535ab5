<?php

use App\Http\Controllers\SellerRegistrationController;
use App\Http\Controllers\SellerDashboardController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

/*
|--------------------------------------------------------------------------
| Web Routes - Plateforme Marchand (seller.lorelei.com)
|--------------------------------------------------------------------------
|
| Ce fichier contient les routes pour la plateforme marchand séparée.
|
| Structure des URLs :
| - / : Page d'accueil
| - /seller/* : Processus d'inscription marchand
| - /dashboard/* : Dashboard Inertia pour les marchands
| - /marchand/* : Panel Filament pour les marchands (géré par Filament)
| - /admin/* : Panel Filament pour les administrateurs (géré par Filament)
|
*/

/*
|--------------------------------------------------------------------------
| Page d'accueil
|--------------------------------------------------------------------------
*/
Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

/*
|--------------------------------------------------------------------------
| Routes d'inscription marchand (Inertia)
|--------------------------------------------------------------------------
*/
Route::middleware(['auth'])->prefix('seller')->name('seller.')->group(function () {
    // Processus d'inscription en 4 étapes
    Route::get('/welcome', [SellerRegistrationController::class, 'welcome'])->name('welcome');

    // Étape 1: Informations personnelles
    Route::get('/information', [SellerRegistrationController::class, 'information'])->name('information');
    Route::post('/information', [SellerRegistrationController::class, 'storeInformation'])->name('information.store');

    // Étape 2: Informations de facturation
    Route::get('/billing', [SellerRegistrationController::class, 'billing'])->name('billing');
    Route::post('/billing', [SellerRegistrationController::class, 'storeBilling'])->name('billing.store');

    // Étape 3: Informations boutique
    Route::get('/store', [SellerRegistrationController::class, 'store'])->name('store');
    Route::post('/store', [SellerRegistrationController::class, 'storeStore'])->name('store.store');

    // Étape 4: Documents et vérification
    Route::get('/documents', [SellerRegistrationController::class, 'documents'])->name('documents');
    Route::post('/documents/upload', [SellerRegistrationController::class, 'uploadDocument'])->name('documents.upload');
    Route::post('/finalize', [SellerRegistrationController::class, 'finalize'])->name('finalize');

    // Pages d'état
    Route::get('/rejected', function () {
        return Inertia::render('SellerRegistration/Rejected');
    })->name('rejected');

    Route::get('/suspended', function () {
        return Inertia::render('SellerRegistration/Suspended');
    })->name('suspended');
});

/*
|--------------------------------------------------------------------------
| Dashboard marchand (Inertia) - Interface moderne
|--------------------------------------------------------------------------
*/
Route::middleware(['auth', 'seller'])->prefix('dashboard')->name('dashboard.')->group(function () {
    // Dashboard principal
    Route::get('/', [SellerDashboardController::class, 'index'])->name('index');

    // Profil marchand
    Route::get('/profile', [SellerDashboardController::class, 'profile'])->name('profile');
    Route::patch('/profile', [SellerDashboardController::class, 'updateProfile'])->name('profile.update');

    // Abonnements
    Route::get('/subscriptions', [SellerDashboardController::class, 'subscriptions'])->name('subscriptions');

    // Documents
    Route::get('/documents', [SellerDashboardController::class, 'documents'])->name('documents');
});

/*
|--------------------------------------------------------------------------
| Redirections et routes de compatibilité
|--------------------------------------------------------------------------
*/

// Redirection du dashboard par défaut vers le dashboard marchand
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return redirect()->route('dashboard.index');
    })->name('dashboard');
});

// Redirection pour les marchands qui tentent d'accéder à l'ancien dashboard
Route::middleware(['auth'])->get('/old-dashboard', function () {
    return redirect()->route('dashboard.index');
});

/*
|--------------------------------------------------------------------------
| Inclusion des autres fichiers de routes
|--------------------------------------------------------------------------
*/
require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
require __DIR__.'/admin.php';
