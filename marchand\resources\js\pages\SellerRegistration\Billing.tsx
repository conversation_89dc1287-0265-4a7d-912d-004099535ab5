import { Head, useForm } from '@inertiajs/react';
import { FormEventHandler } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CreditCard, Info, MapPin } from 'lucide-react';
import { useTranslation } from '@/hooks/use-translation';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import AppearanceToggleDropdown from '@/components/appearance-dropdown';

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
    };
    marchand?: {
        numero_carte?: string;
        nom_porteur_carte?: string;
        mois_expiration?: string;
        annee_expiration?: string;
        adresse_facturation_ligne1?: string;
        adresse_facturation_ligne2?: string;
        ville_facturation?: string;
        region_facturation?: string;
        code_postal_facturation?: string;
        pays_facturation?: string;
    };
    countries: Record<string, string>;
}

export default function Billing({ user, marchand, countries }: Props) {
    const { translate } = useTranslation();
    
    const { data, setData, post, processing, errors } = useForm({
        numero_carte: marchand?.numero_carte || '',
        nom_porteur_carte: marchand?.nom_porteur_carte || '',
        mois_expiration: marchand?.mois_expiration || '',
        annee_expiration: marchand?.annee_expiration || '',
        adresse_facturation_ligne1: marchand?.adresse_facturation_ligne1 || '',
        adresse_facturation_ligne2: marchand?.adresse_facturation_ligne2 || '',
        ville_facturation: marchand?.ville_facturation || '',
        region_facturation: marchand?.region_facturation || '',
        code_postal_facturation: marchand?.code_postal_facturation || '',
        pays_facturation: marchand?.pays_facturation || '',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('seller.billing.store'));
    };

    const months = Array.from({ length: 12 }, (_, i) => ({
        value: String(i + 1).padStart(2, '0'),
        label: String(i + 1).padStart(2, '0')
    }));

    const currentYear = new Date().getFullYear();
    const years = Array.from({ length: 20 }, (_, i) => ({
        value: String(currentYear + i),
        label: String(currentYear + i)
    }));

    return (
        <>
            <Head title={translate('seller.billing.title')} />
            
            <div className="min-h-screen bg-background text-foreground">
                {/* Header avec contrôles */}
                <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                    <div className="container mx-auto px-4">
                        <nav className="flex h-16 items-center justify-between">
                            <div className="flex items-center space-x-2">
                                <span className="text-2xl font-bold text-primary">Lorelei</span>
                                <span className="text-xl font-medium text-muted-foreground">Marchand</span>
                            </div>
                            <div className="flex items-center space-x-2">
                                <LanguageSwitcher />
                                <AppearanceToggleDropdown />
                            </div>
                        </nav>
                    </div>
                </header>

                <div className="container mx-auto px-4 py-8 max-w-4xl">
                    {/* Progress Bar */}
                    <div className="mb-8">
                        <div className="flex items-center justify-center mb-6">
                            <div className="flex items-center space-x-4">
                                {/* Étape 1 - Complétée */}
                                <div className="flex flex-col items-center">
                                    <div className="w-10 h-10 bg-primary text-primary-foreground rounded-full flex items-center justify-center">
                                        ✓
                                    </div>
                                    <span className="text-sm font-medium text-foreground mt-2">
                                        {translate('seller.steps.information')}
                                    </span>
                                </div>
                                <div className="w-16 h-0.5 bg-primary"></div>
                                
                                {/* Étape 2 - Active */}
                                <div className="flex flex-col items-center">
                                    <div className="w-10 h-10 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-semibold">
                                        2
                                    </div>
                                    <span className="text-sm font-medium text-foreground mt-2">
                                        {translate('seller.steps.billing')}
                                    </span>
                                </div>
                                <div className="w-16 h-0.5 bg-border"></div>
                                
                                {/* Étape 3 */}
                                <div className="flex flex-col items-center">
                                    <div className="w-10 h-10 bg-muted text-muted-foreground rounded-full flex items-center justify-center font-semibold">
                                        3
                                    </div>
                                    <span className="text-sm text-muted-foreground mt-2">
                                        {translate('seller.steps.store')}
                                    </span>
                                </div>
                                <div className="w-16 h-0.5 bg-border"></div>
                                
                                {/* Étape 4 */}
                                <div className="flex flex-col items-center">
                                    <div className="w-10 h-10 bg-muted text-muted-foreground rounded-full flex items-center justify-center font-semibold">
                                        4
                                    </div>
                                    <span className="text-sm text-muted-foreground mt-2">
                                        {translate('seller.steps.verification')}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <Progress value={50} className="h-2" />
                    </div>

                    <Card className="border-border bg-card">
                        <CardHeader>
                            <CardTitle className="flex items-center space-x-2 text-card-foreground">
                                <CreditCard className="w-5 h-5 text-primary" />
                                <span>{translate('seller.billing.form_title')}</span>
                            </CardTitle>
                            <CardDescription className="text-muted-foreground">
                                {translate('seller.billing.form_description')}
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={submit} className="space-y-8">
                                {/* Alerte d'information sur les frais */}
                                <Alert className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
                                    <Info className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                                    <AlertDescription className="text-blue-800 dark:text-blue-200">
                                        {translate('seller.billing.subscription_info')}
                                    </AlertDescription>
                                </Alert>

                                {/* Informations de carte */}
                                <div className="space-y-6">
                                    <h3 className="text-lg font-semibold text-foreground border-b border-border pb-2">
                                        {translate('seller.billing.card_details')}
                                    </h3>
                                    
                                    <div className="space-y-6">
                                        <div className="space-y-2">
                                            <Label htmlFor="numero_carte">{translate('seller.billing.card_number')} *</Label>
                                            <Input
                                                id="numero_carte"
                                                value={data.numero_carte}
                                                onChange={(e) => setData('numero_carte', e.target.value)}
                                                placeholder="1234 5678 9012 3456"
                                                className={errors.numero_carte ? 'border-destructive' : ''}
                                            />
                                            {errors.numero_carte && (
                                                <p className="text-sm text-destructive">{errors.numero_carte}</p>
                                            )}
                                        </div>

                                        <div className="grid md:grid-cols-3 gap-6">
                                            <div className="space-y-2 md:col-span-2">
                                                <Label htmlFor="nom_porteur_carte">{translate('seller.billing.cardholder_name')} *</Label>
                                                <Input
                                                    id="nom_porteur_carte"
                                                    value={data.nom_porteur_carte}
                                                    onChange={(e) => setData('nom_porteur_carte', e.target.value)}
                                                    placeholder={translate('seller.billing.cardholder_name_placeholder')}
                                                    className={errors.nom_porteur_carte ? 'border-destructive' : ''}
                                                />
                                                {errors.nom_porteur_carte && (
                                                    <p className="text-sm text-destructive">{errors.nom_porteur_carte}</p>
                                                )}
                                            </div>

                                            <div className="space-y-2">
                                                <Label>{translate('seller.billing.expiry_date')} *</Label>
                                                <div className="grid grid-cols-2 gap-2">
                                                    <Select value={data.mois_expiration} onValueChange={(value) => setData('mois_expiration', value)}>
                                                        <SelectTrigger className={errors.mois_expiration ? 'border-destructive' : ''}>
                                                            <SelectValue placeholder="MM" />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            {months.map((month) => (
                                                                <SelectItem key={month.value} value={month.value}>
                                                                    {month.label}
                                                                </SelectItem>
                                                            ))}
                                                        </SelectContent>
                                                    </Select>
                                                    
                                                    <Select value={data.annee_expiration} onValueChange={(value) => setData('annee_expiration', value)}>
                                                        <SelectTrigger className={errors.annee_expiration ? 'border-destructive' : ''}>
                                                            <SelectValue placeholder="YYYY" />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            {years.map((year) => (
                                                                <SelectItem key={year.value} value={year.value}>
                                                                    {year.label}
                                                                </SelectItem>
                                                            ))}
                                                        </SelectContent>
                                                    </Select>
                                                </div>
                                                {(errors.mois_expiration || errors.annee_expiration) && (
                                                    <p className="text-sm text-destructive">
                                                        {errors.mois_expiration || errors.annee_expiration}
                                                    </p>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Adresse de facturation */}
                                <div className="space-y-6">
                                    <h3 className="text-lg font-semibold text-foreground border-b border-border pb-2 flex items-center space-x-2">
                                        <MapPin className="w-4 h-4" />
                                        <span>{translate('seller.billing.billing_address')}</span>
                                    </h3>
                                    
                                    <div className="grid md:grid-cols-2 gap-6">
                                        <div className="space-y-2">
                                            <Label htmlFor="adresse_facturation_ligne1">{translate('seller.billing.address_line1')} *</Label>
                                            <Input
                                                id="adresse_facturation_ligne1"
                                                value={data.adresse_facturation_ligne1}
                                                onChange={(e) => setData('adresse_facturation_ligne1', e.target.value)}
                                                placeholder={translate('seller.billing.address_line1_placeholder')}
                                                className={errors.adresse_facturation_ligne1 ? 'border-destructive' : ''}
                                            />
                                            {errors.adresse_facturation_ligne1 && (
                                                <p className="text-sm text-destructive">{errors.adresse_facturation_ligne1}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="adresse_facturation_ligne2">{translate('seller.billing.address_line2')}</Label>
                                            <Input
                                                id="adresse_facturation_ligne2"
                                                value={data.adresse_facturation_ligne2}
                                                onChange={(e) => setData('adresse_facturation_ligne2', e.target.value)}
                                                placeholder={translate('seller.billing.address_line2_placeholder')}
                                            />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="ville_facturation">{translate('seller.billing.city')} *</Label>
                                            <Input
                                                id="ville_facturation"
                                                value={data.ville_facturation}
                                                onChange={(e) => setData('ville_facturation', e.target.value)}
                                                placeholder={translate('seller.billing.city_placeholder')}
                                                className={errors.ville_facturation ? 'border-destructive' : ''}
                                            />
                                            {errors.ville_facturation && (
                                                <p className="text-sm text-destructive">{errors.ville_facturation}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="region_facturation">{translate('seller.billing.region')} *</Label>
                                            <Input
                                                id="region_facturation"
                                                value={data.region_facturation}
                                                onChange={(e) => setData('region_facturation', e.target.value)}
                                                placeholder={translate('seller.billing.region_placeholder')}
                                                className={errors.region_facturation ? 'border-destructive' : ''}
                                            />
                                            {errors.region_facturation && (
                                                <p className="text-sm text-destructive">{errors.region_facturation}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="code_postal_facturation">{translate('seller.billing.postal_code')} *</Label>
                                            <Input
                                                id="code_postal_facturation"
                                                value={data.code_postal_facturation}
                                                onChange={(e) => setData('code_postal_facturation', e.target.value)}
                                                placeholder={translate('seller.billing.postal_code_placeholder')}
                                                className={errors.code_postal_facturation ? 'border-destructive' : ''}
                                            />
                                            {errors.code_postal_facturation && (
                                                <p className="text-sm text-destructive">{errors.code_postal_facturation}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="pays_facturation">{translate('seller.billing.country')} *</Label>
                                            <Select value={data.pays_facturation} onValueChange={(value) => setData('pays_facturation', value)}>
                                                <SelectTrigger className={errors.pays_facturation ? 'border-destructive' : ''}>
                                                    <SelectValue placeholder={translate('seller.billing.select_country')} />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {Object.entries(countries).map(([code, name]) => (
                                                        <SelectItem key={code} value={code}>
                                                            {name}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            {errors.pays_facturation && (
                                                <p className="text-sm text-destructive">{errors.pays_facturation}</p>
                                            )}
                                        </div>
                                    </div>
                                </div>

                                {/* Actions */}
                                <div className="flex justify-between pt-6 border-t border-border">
                                    <Button type="button" variant="outline" asChild>
                                        <a href={route('seller.information')}>
                                            {translate('seller.billing.previous')}
                                        </a>
                                    </Button>
                                    
                                    <Button type="submit" disabled={processing} className="min-w-[150px]">
                                        {processing 
                                            ? translate('seller.billing.saving') 
                                            : translate('seller.billing.continue')
                                        }
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </>
    );
}
